module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es2021: true,
  },
  parser: "vue-eslint-parser",
  extends: [
    "eslint:recommended",
    // "plugin:vue/vue3-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "prettier",
  ],
  parserOptions: {
    ecmaVersion: 12,
    parser: "@typescript-eslint/parser",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["vue", "@typescript-eslint", "prettier", "simple-import-sort"],
  rules: {
    "simple-import-sort/imports": [
      "error",
      {
        groups: [
          // 2. `react` and packages: Things that start with a letter (or digit or underscore), or `@` followed by a letter.
          ["^react$", "^@?\\w"],
          // 3. Absolute imports and other imports such as Vue-style `@/foo`.
          // Anything not matched in another group. (also relative imports starting with "../")
          ["^@", "^"],
          // 4. relative imports from same folder "./" (I like to have them grouped together)
          ["^\\./"],
          // 5. style module imports always come last, this helps to avoid CSS order issues
          ["^.+\\.(module.css|module.scss)$"],
          // 6. media imports
          ["^.+\\.(gif|png|svg|jpg)$"],
        ],
      },
    ],
    "no-undef": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-empty-function": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/ban-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "vue/multi-word-component-names": 0,
    "no-var": "off",
    "prettier/prettier": "off",
    "no-console": "off",
    "no-debugger": "off",
    "no-duplicate-case": "off",
    "no-empty": "off",
    "no-extra-parens": "off",
    "no-func-assign": "off",
    "no-unreachable": "off",
    "default-case": "off",
    "dot-notation": "off",
    eqeqeq: "off",
    "no-else-return": "off",
    "no-empty-function": "off",
    "no-lone-blocks": "off",
    "no-multi-spaces": "off",
    "no-redeclare": "off",
    "no-return-assign": "off",
    "no-return-await": "off",
    "no-self-assign": "off",
    "no-self-compare": "off",
    "no-useless-catch": "off",
    "no-useless-return": "off",
    "no-shadow": "off",
    "no-delete-var": "off",
    "array-bracket-spacing": "off",
    "brace-style": "off",
    indent: "off",
    "max-statements": ["off", 20],
    "max-nested-callbacks": ["off", 3],
    "max-params": ["off", 3],
    "max-statements-per-line": ["off", { max: 1 }],
    "newline-per-chained-call": ["off", { ignoreChainWithDepth: 3 }],
    "no-lonely-if": "off",
    "no-mixed-spaces-and-tabs": "off",
    "no-multiple-empty-lines": "off",
    semi: ["off", "always"],
    singleQuote: [0],
    "space-before-blocks": "off",
    "space-in-parens": "off",
    "space-infix-ops": "off",
    "space-unary-ops": "off",
    "switch-colon-spacing": "off",
    "arrow-spacing": "off",
    "prefer-const": "off",
    "prefer-rest-params": "off",
    "no-useless-escape": "off",
    "no-irregular-whitespace": "off",
    "no-prototype-builtins": "off",
    "no-fallthrough": "off",
    "no-extra-boolean-cast": "off",
    "no-case-declarations": "off",
    "no-async-promise-executor": "off",
  },
  globals: {
    defineProps: "readonly",
    defineEmits: "readonly",
    defineExpose: "readonly",
    withDefaults: "readonly",
  },
};
