{"name": "enigma-vue", "type": "module", "private": true, "version": "0.0.1", "scripts": {"dev": "bunx --bun vite", "build": "bunx --bun vite build --mode up", "build:san": "bunx --bun vite build --mode san", "build:up": "bunx --bun vite build --mode up", "preview": "bunx --bun vite preview", "typecheck": "bun run vue-tsc -noEmit", "lint": "bun run eslint src --fix --ext .ts,.tsx,.vue,.js,.jsx", "prettier": "bun run prettier --write .", "prepare": "bun run husky install"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/vue": "^2.2.2", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/list": "^6.1.10", "@fullcalendar/resource": "^6.1.10", "@fullcalendar/resource-timegrid": "^6.1.10", "@fullcalendar/resource-timeline": "^6.1.10", "@fullcalendar/scrollgrid": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@fullcalendar/vue3": "^6.1.10", "@headlessui/vue": "^1.7.13", "@primevue/themes": "4.2.2", "@tiptap-pro/extension-file-handler": "^2.13.0", "@tiptap/extension-bubble-menu": "^2.9.1", "@tiptap/extension-image": "^2.9.1", "@tiptap/extension-mention": "^2.9.1", "@tiptap/pm": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@tiptap/suggestion": "^2.9.1", "@tiptap/vue-3": "^2.8.0", "@voicenter-team/vsip": "^1.1.63", "@vuelidate/core": "^2.0.0", "@vuelidate/validators": "^2.0.0", "@vueuse/components": "^11.0.1", "@vueuse/core": "^11.0.1", "@vueuse/integrations": "^11.2.0", "axios": "1.8.2", "chart.js": "^3.9.1", "color": "^4.2.3", "cron-parser": "^4.9.0", "dayjs": "^1.11.5", "dompurify": "^3.2.3", "dropzone": "^6.0.0-beta.2", "flat": "^5.0.2", "highlight.js": "^11.6.0", "json-editor-vue": "^0.17.3", "jssip": "^3.10.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.3", "leaflet.markercluster": "^1.5.3", "litepicker": "^2.0.12", "lucide-vue-next": "^0.95.0", "maska": "^2.1.11", "nprogress": "^0", "pinia": "^2.0.23", "primeicons": "^7.0.0", "primevue": "4.2.5", "simplebar": "^5.3.9", "tailwindcss-primeui": "^0.3.3", "tiny-slider": "^2.9.4", "tippy.js": "^6.3.7", "toastify-js": "^1.12.0", "tom-select": "^2.2.0", "vue": "^3.4.38", "vue-easy-lightbox": "^1.19.0", "vue-json-pretty": "^2.4.0", "vue-query": "^1.26.0", "vue-router": "^4.1.6", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@primevue/auto-import-resolver": "4.2.2", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.15", "@types/color": "^3.0.6", "@types/dropzone": "^5.7.4", "@types/flat": "^5.0.2", "@types/js-cookie": "^3.0.4", "@types/leaflet": "^1.9.3", "@types/leaflet.markercluster": "^1.5.1", "@types/lodash": "^4.14.195", "@types/node": "^20.7.0", "@types/toastify-js": "^1.11.1", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.14", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^9.24.0", "husky": "^8.0.0", "js-cookie": "^3.0.5", "lint-staged": "^15.2.2", "lodash": "^4.17.21", "postcss": "8.4.31", "postcss-advanced-variables": "^3.0.1", "postcss-import": "^15.1.0", "postcss-nesting": "^12.0.0", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "rollup-plugin-visualizer": "^5.14.0", "tailwind-merge": "^1.13.2", "tailwindcss": "^3.4.10", "typescript": "^5.1.6", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^0.27.2", "vite": "5.4.6", "vite-plugin-pwa": "^0.17.5", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^1.8.4"}, "lint-staged": {"*.{ts,tsx,vue,js,jsx}": ["prettier --write", "eslint --cache --fix", "vue-tsc -noEmit"], "*.json": ["prettier --write"]}, "packageManager": "pnpm@8.8.0+sha1.9922e8b650d393700209ccd81e0ebdbcbe43b0d3"}