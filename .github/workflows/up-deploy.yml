name: Deploy to Server

on:
  push:
    branches:
      - 'main'
      - 'up/uat'
      - 'up/release'
  workflow_dispatch: { }

env:
  SSH_HOST: up.blazy.vn
  SSH_PORT: 22
  SSH_USERNAME: root
  APP_NAME: updental

jobs:
  deploy:
    name: Deploy to Server
    runs-on: ubuntu-latest

    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.1.43

      - name: Install Dependencies
        run: bun install

      - name: Build Application
        env:
          NODE_OPTIONS: --max_old_space_size=4096
        run: |
          echo "Starting build process..."
          bun run build:up

          if [[ $? -ne 0 ]]; then
            echo "Build failed with exit code $?"
            exit 1
          fi
          echo "Build completed successfully."

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.VHLAM_SSH_KEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -p ${{ env.SSH_PORT }} -H ${{ env.SSH_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to Server
        run: |
          if [[ "${{ github.ref }}" == refs/heads/main ]]; then
            rsync -avz -e "ssh -p ${{ env.SSH_PORT }}" ./dist/* ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }}:/opt/bcare/${{ env.APP_NAME }}/fe
          elif [[ "${{ github.ref }}" == refs/heads/${{ env.APP_NAME }}/release ]]; then
            rsync -avz -e "ssh -p ${{ env.SSH_PORT }}" ./dist/* ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }}:/opt/bcare/${{ env.APP_NAME }}/fe
          else
            rsync -avz -e "ssh -p ${{ env.SSH_PORT }}" ./dist/* ${{ env.SSH_USERNAME }}@${{ env.SSH_HOST }}:/opt/bcare/${{ env.APP_NAME }}/uatfe
          fi
