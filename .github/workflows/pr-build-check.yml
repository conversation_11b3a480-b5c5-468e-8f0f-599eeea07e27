name: PR Build Check

on:
  pull_request:
    branches:
      - 'main'

jobs:
  build:
    name: Build Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.1.43

      - name: Install Dependencies
        run: bun install

      - name: Build Application
        env:
          NODE_OPTIONS: --max_old_space_size=4096
        run: bun run build
