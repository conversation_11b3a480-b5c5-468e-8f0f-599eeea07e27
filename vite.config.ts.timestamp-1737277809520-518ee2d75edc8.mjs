// vite.config.ts
import { PrimeVueResolver } from "file:///Users/<USER>/Documents/go/github.com/bcare-fe/node_modules/@primevue/auto-import-resolver/index.mjs";
import vue from "file:///Users/<USER>/Documents/go/github.com/bcare-fe/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import { visualizer } from "file:///Users/<USER>/Documents/go/github.com/bcare-fe/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import Components from "file:///Users/<USER>/Documents/go/github.com/bcare-fe/node_modules/unplugin-vue-components/dist/vite.js";
import { fileURLToPath } from "url";
import { defineConfig } from "file:///Users/<USER>/Documents/go/github.com/bcare-fe/node_modules/vite/dist/node/index.js";
var __vite_injected_original_import_meta_url = "file:///Users/<USER>/Documents/go/github.com/bcare-fe/vite.config.ts";
var __dirname = path.dirname(fileURLToPath(__vite_injected_original_import_meta_url));
var vite_config_default = defineConfig({
  server: {
    host: "localhost",
    port: 7777,
    open: true,
    proxy: {}
  },
  build: {
    commonjsOptions: {
      include: ["tailwind.config.js", "node_modules/**"]
    }
  },
  optimizeDeps: {
    include: ["tailwind-config"]
  },
  plugins: [
    vue(),
    visualizer({
      filename: "dist/stats.html",
      open: true,
      gzipSize: true,
      brotliSize: true,
      template: "treemap"
      // Hiển thị dạng treemap dễ nhìn nhất
    }),
    Components({
      resolvers: [PrimeVueResolver()]
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "tailwind-config": path.resolve(__dirname, "./tailwind.config.js")
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
