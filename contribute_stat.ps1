# Define multiple authors
$authors = @("linhmh", "linhmh8")
$since = "14 days ago"

# Initialize counters
$totalAdded = 0
$totalRemoved = 0

# Loop through each author to get their git stats
foreach ($author in $authors) {
    # Added --all flag to check all branches
    $output = git log --all --author="$author" --since="$since" --numstat --pretty="%H"

    $output | ForEach-Object {
        if ($_ -match "^(\d+)\s+(\d+)\s+") {
            $totalAdded += [int]$Matches[1]
            $totalRemoved += [int]$Matches[2]
        }
    }
}

# Calculate total changes
$totalChanges = $totalAdded + $totalRemoved

# Display results
Write-Host "Total changes for authors: $($authors -join ', ')"
Write-Host "Time period: since $since"
Write-Host "Added: $totalAdded, Removed: $totalRemoved, Total changes: $totalChanges"
