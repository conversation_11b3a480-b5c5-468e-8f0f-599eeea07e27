import { createRouter, createWebHistory } from "vue-router";

import { useAuth } from "@/pages/auth/config/auth-composable";

import mainRoute from "./main-route";
import mobileRoute from "./mobile-route";
import printRoute from "./print-route";

const routes = [
  mainRoute,
  mobileRoute,
  printRoute,
  {
    path: "/tiptap-test",
    name: "tiptap-test",
    component: () => import("@/components/WysiwgEditor/TiptapTest.vue"),
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/pages/auth/Login.vue"),
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/pages/templates/Register.vue"),
  },
  {
    path: "/error-page",
    name: "error-page",
    component: () => import("@/pages/templates/ErrorPage.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/pages/templates/ErrorPage.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    const isTabChange = to.query.tab !== from.query.tab;
    if (isTabChange) {
      return savedPosition || { left: 0 };
    }
    return { left: 0, top: 0 };
  },
});

router.beforeEach((to, from, next) => {
  const isAuthenticated = useAuth().isAuthenticated;

  // Allow access to tiptap-test without authentication
  if (to.name === "tiptap-test") {
    return next();
  }

  if (to.name === "login" && isAuthenticated.value) {
    return next("/");
  }

  if (to.meta.requiresAuth && !isAuthenticated.value) {
    return next("/login");
  }

  next();
});

export default router;
