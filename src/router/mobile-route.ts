// router/mobile-route.ts
import { RouteRecordRaw } from "vue-router";

const mobileRoute: Readonly<RouteRecordRaw> = {
  path: "/mobile",
  meta: { requiresAuth: true },
  children: [
    {
      path: "search",
      name: "mobile-search",
      component: () => import("@/pages/mobile/MobileSearchView.vue"),
      meta: {
        breadcrumb: "Tìm kiếm",
      },
    },
    {
      path: "revenue-attachment",
      name: "mobile-revenue-attachment",
      component: () => import("@/pages/statistics/RevenueAttachment/RevenueAttachmentMobile.vue"),
      meta: {
        breadcrumb: "Báo cáo điều trị",
      },
    },
  ],
};

export default mobileRoute;
