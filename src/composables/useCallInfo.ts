import { effectScope,ref } from "vue";

import type { PersonResponse } from "@/api/bcare-types-v2";

interface CallInfo {
  personId: number;
  name: string;
  phone: string;
  address: string;
}

// Create a scope outside the function
const scope = effectScope(true);

// Create shared state
const callInfo = ref<CallInfo | null>(null);
const isVisible = ref(false);

export function useCallInfo() {
  // Use scope to ensure state is shared
  scope.run(() => {
    // State is now shared between all instances
  });

  const showWithPerson = (person: PersonResponse) => {
    callInfo.value = {
      personId: person.id ?? 0,
      name: person.full_name ?? "",
      phone: person.phone ?? "",
      address: person.address_number ?? "",
    };
    isVisible.value = true;

    console.log("callInfo", callInfo.value);
    console.log("isVisible", isVisible.value);
  };

  const clear = () => {
    callInfo.value = null;
    isVisible.value = false;
  };

  return {
    callInfo,
    isVisible,
    showW<PERSON><PERSON><PERSON>,
    clear,
  };
}
