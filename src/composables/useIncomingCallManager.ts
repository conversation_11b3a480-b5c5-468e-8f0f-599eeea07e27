import { RTCSession } from "jssip/lib/RTCSession";
import { computed, onUnmounted, ref } from "vue";

import { Call } from "@/api/bcare-types-v2";
import { useCall } from "@/hooks/useCall";
import usePerson from "@/hooks/usePerson";
import { normalizeVietnamesePhone } from "@/utils/string";

import { useRingtone } from "./useRingtone";

interface CallInfo {
  sessionId: string;
  phone: string;
  personInfo: {
    id: number;
    name: string;
    phone: string;
    address?: string;
  } | null;
  status: "ringing" | "accepted" | "rejected" | "missed" | "ended";
  timestamp: number;
  session: RTCSession;
  key: string;
  callRecord?: Call | null;
  isConfirmed?: boolean;
}

// Singleton instance
const activeCalls = ref<Map<string, CallInfo>>(new Map());
const sortedCalls = computed(() =>
  Array.from(activeCalls.value.values())
    .sort((a, b) => b.timestamp - a.timestamp)
    .map((call) => ({
      ...call,
      key: `call-${call.sessionId}-${call.status}`,
    })),
);

export function useIncomingCallManager() {
  const { getPerson } = usePerson();
  const { createCall, endCall } = useCall();
  const { playRingtone, stopRingtone } = useRingtone();

  // Add new call
  const addCall = async (session: RTCSession) => {
    const phone = normalizeVietnamesePhone(session.remote_identity.uri.user);

    // Kiểm tra xem đã có cuộc gọi nào đang hoạt động từ số điện thoại này chưa
    const existingCall = Array.from(activeCalls.value.values()).find(
      (call) => call.phone === phone && call.status === "ringing",
    );

    if (existingCall) {
      console.log(`Ignoring duplicate incoming call from ${phone}`);
      return;
    }

    try {
      const personInfo = await getPerson({ phone, id: 0 }, false).catch(() => null);

      // Create backend record
      const callRecord =
        (await createCall({
          call_id: session.id ?? "",
          person_id: personInfo?.id ?? 0,
          phone: personInfo?.phone ?? phone,
          direction: "inbound",
        })) || null;

      // Add to active calls
      activeCalls.value.set(session.id, {
        sessionId: session.id,
        key: `call-${session.id}-ringing`,
        phone,
        personInfo: personInfo
          ? {
              id: personInfo.id,
              name: personInfo.full_name,
              phone: personInfo.phone,
              address: personInfo.address_number,
            }
          : null,
        status: "ringing",
        timestamp: Date.now(),
        session,
        callRecord,
        isConfirmed: false,
      });

      // Setup session handlers
      setupSessionHandlers(session, callRecord);

      playRingtone();
    } catch (error) {
      console.error("Failed to process incoming call:", error);
    }
  };

  // Tách logic xử lý sự kiện session ra thành hàm riêng để code sạch hơn
  const setupSessionHandlers = (session: RTCSession, callRecord: Call | null | undefined) => {
    session.on("ended", () => {
      if (callRecord) {
        endCall({
          id: callRecord.id,
          call_id: session.id,
          is_miss: false,
        });
      }
      activeCalls.value.delete(session.id);
    });

    session.on("failed", () => {
      const call = activeCalls.value.get(session.id);
      if (call) call.status = "missed";
      setTimeout(() => activeCalls.value.delete(session.id), 2000);
    });

    session.on("confirmed", () => {
      const call = activeCalls.value.get(session.id);
      if (call) {
        call.isConfirmed = true;
      }
    });
  };

  // Update call status
  const updateCallStatus = (sessionId: string, status: CallInfo["status"]) => {
    const call = activeCalls.value.get(sessionId);
    if (call) {
      call.status = status;
      call.key = `call-${sessionId}-${status}`;

      // Stop ringtone when call is not in ringing state
      if (status !== "ringing") {
        stopRingtone();
      }
    }
  };

  // Remove call from manager
  const removeCall = (sessionId: string) => {
    activeCalls.value.delete(sessionId);
  };

  const handleSkipCall = (sessionId: string) => {
    const call = activeCalls.value.get(sessionId);
    if (call && call.status === "ringing") {
      stopRingtone();
      removeCall(sessionId); // Will sync across tabs
    }
  };

  // Cleanup when unmount
  onUnmounted(() => {
    stopRingtone();
  });

  // Helper to check if any call is confirmed
  const hasConfirmedCall = computed(() => {
    return Array.from(activeCalls.value.values()).some((call) => call.isConfirmed);
  });

  return {
    activeCalls,
    sortedCalls,
    addCall,
    updateCallStatus,
    removeCall,
    handleSkipCall,
    hasConfirmedCall,
  };
}
