import { WsMessage } from "@/realtime/websocket";
import { useWsStore } from "@/stores/ws-store";

interface CallInParams {
  callId: string;
  phoneNumber: string;
  timestamp: number;
}

export function useCallBroadcast() {
  const wsStore = useWsStore();

  const joinCallLineRoom = async (lineId: string) => {
    const room = `call_line_${lineId}`;

    return new Promise<void>((resolve, reject) => {
      console.log("WS State:", wsStore.isReady ? "Ready" : "Not Ready");

      if (!wsStore.isReady) {
        wsStore.connect();
      }

      const joinRoom = async () => {
        try {
          // Gửi join room message và đợi response
          const response = await wsStore.realtime.getWs().ask("join", room);
          console.log("Join room response:", response);

          if (response.code === 0) {
            console.log(`Successfully joined room: ${room}`);
            resolve();
          } else {
            console.error(`Failed to join room: ${response.message}`);
            reject(new Error(response.message));
          }
        } catch (error) {
          console.error("Error joining room:", error);
          reject(error);
        }
      };

      if (wsStore.isReady) {
        joinRoom();
      } else {
        const readyHandler = () => {
          joinRoom();
          wsStore.removeEvent("ready", readyHandler);
        };
        wsStore.addEvent("ready", readyHandler);
      }
    });
  };

  const broadcastCallIn = (lineId: number, callParams: CallInParams) => {
    if (!wsStore.isReady) {
      console.warn("WebSocket not initialized, skipping broadcast");
      return;
    }

    wsStore.realtime.broadcast.to(`call_line_${lineId}`).emit("call_in", callParams);
  };

  const subscribeToCallIn = (callback: (msg: WsMessage) => void) => {
    if (!wsStore) {
      console.warn("WebSocket store not initialized, skipping subscription");
      return () => {};
    }

    wsStore.addEvent("call_in", callback);
    return () => {
      wsStore.removeEvent("call_in", callback);
    };
  };

  const leaveCallLineRoom = (lineId: number) => {
    if (!wsStore.realtime) {
      console.warn("WebSocket not initialized, skipping room leave");
      return;
    }

    const room = `call_line_${lineId}`;
    wsStore.realtime.leave(room);
  };

  return {
    joinCallLineRoom,
    broadcastCallIn,
    subscribeToCallIn,
    leaveCallLineRoom,
  };
}
