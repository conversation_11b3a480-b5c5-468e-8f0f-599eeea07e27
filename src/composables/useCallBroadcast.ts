import { ref, watch } from "vue";

import type { RTCSessionApp } from "@/stores/call-store";
import { useWsStore } from "@/stores/ws-store";

export function useCallBroadcast() {
  const wsStore = useWsStore();
  const { realtime } = wsStore;
  const activeCalls = ref<Record<string, RTCSessionApp>>({});

  const broadcastCallIn = (callId?: string, callData?: Partial<RTCSessionApp>) => {
    realtime.broadcast.to(`call_line_${callId}`).emit("call_start", {
      callId,
      session: callData,
    });
  };

  return {
    activeCalls,
    broadcastCallIn,
  };
}
