import type { UA } from "jssip";
import JsSIP from "jssip";
import { RTCSession } from "jssip/lib/RTCSession";
import { onUnmounted, ref, toRaw } from "vue";

import { useCall } from "@/hooks/useCall";
import usePerson from "@/hooks/usePerson";
import { useCallInfoStore } from "@/stores/call-info";
import { normalizeVietnamesePhone } from "@/utils/string";

import { useAudioStreams } from "./useAudioStreams";
import { useIncomingCallManager } from "./useIncomingCallManager";

interface SipConfig {
  socketUrl: string; // wss://sbcwrtchcm.ccall.vn:8080/ws
  sipUri: string; // 000
  password: string; // 000
  uri?: string; // updentalhcm035.ccall.vn
}

export function useSipClient() {
  const { getPerson } = usePerson();
  const { createCall, endCall, acceptCall } = useCall();
  const callInfoStore = useCallInfoStore();
  const ua = ref<UA | null>(null);
  const currentSession = ref<RTCSession | null>(null);
  const isRegistered = ref(false);
  const isInCall = ref(false);
  const isCallConfirmed = ref(false);

  // Call duration
  const callDuration = ref(0);
  const timerInterval = ref<ReturnType<typeof setInterval> | null>(null);

  const audioStreams = useAudioStreams();
  const callManager = useIncomingCallManager();

  const initializeSip = async (config: SipConfig) => {
    try {
      // Clone config để tránh proxy issues
      const sipConfig = {
        socketUrl: config.socketUrl,
        sipUri: config.sipUri,
        password: config.password,
        uri: config.uri,
      };

      const socket = new JsSIP.WebSocketInterface(sipConfig.socketUrl);

      const configuration = {
        sockets: [socket],
        uri: `sip:${sipConfig.sipUri}@${sipConfig.uri}`,
        password: sipConfig.password,
        realm: sipConfig.uri,
        use_preloaded_route: true,
        connection_recovery_min_interval: 1000, // Giảm xuống 1s cho phản hồi nhanh hơn
        connection_recovery_max_interval: 30000, // Tăng lên 30s để chịu được mạng yếu
      };

      ua.value = new JsSIP.UA(toRaw(configuration));

      // Register events
      ua.value.on("registered", () => {
        isRegistered.value = true;
      });

      ua.value.on("unregistered", () => {
        isRegistered.value = false;
      });

      // Handle new calls here
      ua.value.on("newRTCSession", ({ session }: { session: RTCSession }) => {
        handleNewSession(toRaw(session));
      });

      ua.value.start();
      setupRegistrationRenewal();
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : "Unknown error";
      throw new Error(`SIP initialization failed: ${message}`);
    }
  };

  const handleNewSession = (session: RTCSession) => {
    if (currentSession.value || callManager.hasConfirmedCall.value) return;

    if (session.direction === "incoming") {
      // Thêm vào call manager và lưu session luôn
      callManager.addCall(session);

      session.on("failed", () => {
        callManager.updateCallStatus(session.id, "missed");
        currentSession.value = null;
        setTimeout(() => callManager.removeCall(session.id), 2000);
      });

      session.on("ended", () => {
        currentSession.value = null;
        callManager.removeCall(session.id);
      });
    } else {
      // Outgoing call - chỉ setup media và events
      setupMediaForCall(session);
    }
  };

  const setupMediaForCall = (session: RTCSession) => {
    currentSession.value = session;
    isInCall.value = true;

    const remoteAudio = audioStreams.setupRemoteAudio();

    session.connection.addEventListener("addstream", (event: any) => {
      remoteAudio.srcObject = event.stream;
    });

    const cleanupSession = () => {
      stopTimer();
      clearMediaStreams();
      audioStreams.cleanup();
      currentSession.value = null;
      isInCall.value = false;
      isCallConfirmed.value = false;
      callInfoStore.clear();
    };

    session.on("failed", cleanupSession);
    session.on("ended", cleanupSession);
    session.on("getusermediafailed", cleanupSession);
    session.on("peerconnection:setlocaldescriptionfailed", cleanupSession);
    session.on("peerconnection:setremotedescriptionfailed", cleanupSession);

    // Update existing event handlers
    session.on("connecting", () => {
      console.log("Session connecting");
    });

    session.on("progress", () => {
      console.log("Call progress");
    });

    session.on("accepted", () => {
      console.log("Call accepted");
    });

    // Track call progress
    session.on("progress", () => {
      // Handle ringing state
      console.log("Call is ringing...");
    });

    session.on("confirmed", () => {
      // Xảy ra khi nhận được ACK cho 200 OK
      // Nghĩa là cả 2 bên đã sẵn sàng
      // Media streams đã được thiết lập
      // Cuộc gọi chính thức bắt đầu
      console.log("Call is established - Media connection ready");
      isCallConfirmed.value = true;
      startTimer();
    });

    // Thêm xử lý sự kiện session:update
    session.on("update", () => {
      console.log("Session update/refresh occurred");
    });
  };

  const clearMediaStreams = () => {
    try {
      // Clear current session's media streams
      if (currentSession.value) {
        const peerConnection = currentSession.value.connection;
        const senders = peerConnection?.getSenders() || [];
        senders.forEach((sender) => {
          if (sender.track) {
            sender.track.stop();
          }
        });
      }
    } catch (e) {
      console.warn("Error clearing media streams:", e);
    }
  };

  const startTimer = () => {
    callDuration.value = 0;
    timerInterval.value = setInterval(() => {
      callDuration.value++;
    }, 1000);
  };

  const stopTimer = () => {
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
    }
    callDuration.value = 0;
  };

  const makeCall = async (number: string) => {
    if (currentSession.value) return;

    const currentUA = toRaw(ua.value);
    if (!currentUA || !isRegistered.value) return;

    try {
      const stream = await audioStreams.setupLocalAudio();
      const options = toRaw({
        mediaStream: stream,
        mediaConstraints: { audio: true, video: false },
        rtcOfferConstraints: {
          offerToReceiveAudio: true,
          offerToReceiveVideo: false,
        },
        sessionTimersExpires: 3600, // Tăng lên 1 giờ
      });

      const session = currentUA.call(String(number), options);

      if (session) {
        const phone = normalizeVietnamesePhone(number);
        const personInfo = await getPerson({ phone, id: 0 }, false).catch(() => null);

        // Store the backend call record
        const callRecord = await createCall({
          call_id: session.id,
          person_id: personInfo?.id ?? 0,
          phone: phone,
          direction: "outbound",
        });

        // Setup session end handler if we have a backend record
        if (callRecord) {
          session.on("ended", () => {
            endCall({
              id: callRecord.id,
              call_id: session.id,
              is_miss: false,
            });
          });
        }
      }
    } catch (e: unknown) {
      audioStreams.cleanup();
      const message = e instanceof Error ? e.message : "Call failed";
      console.error(message);
      throw e;
    }
  };

  const answerIncomingCall = async (sessionId: string) => {
    const call = callManager.activeCalls.value.get(sessionId);
    if (!call || call.status !== "ringing") return;

    try {
      const stream = await audioStreams.setupLocalAudio();
      const session = (call as any).session as RTCSession;

      if (!session) throw new Error("Session not found");
      session.answer({
        mediaConstraints: { audio: true, video: false },
        mediaStream: stream,
        sessionTimersExpires: 3600, // Add session timer config here
      });
      callManager.updateCallStatus(sessionId, "accepted");
      setupMediaForCall(session);

      // Handle backend accept
      if (call.callRecord) {
        try {
          await acceptCall({
            id: call.callRecord.id,
            call_id: sessionId,
          });
        } catch (e) {
          console.error("Backend accept failed:", e);
          // Có thể retry hoặc continue với warning
        }
      }
    } catch (e) {
      console.error("Answer failed:", e);
      callManager.updateCallStatus(sessionId, "rejected");
      throw e;
    }
  };

  const rejectIncomingCall = (sessionId: string) => {
    const call = callManager.activeCalls.value.get(sessionId);
    if (!call || call.status !== "ringing") return;

    const session = currentSession.value;
    if (session) {
      session.terminate();
      callManager.updateCallStatus(sessionId, "rejected");
      setTimeout(() => callManager.removeCall(sessionId), 2000);
    }
  };

  const hangUp = () => {
    const session = toRaw(currentSession.value);
    if (session) session.terminate();
    currentSession.value = null;
    isInCall.value = false;
    clearMediaStreams();
  };

  const hangUpIncomingCall = (sessionId: string) => {
    const call = callManager.activeCalls.value.get(sessionId);
    if (call) {
      call.session.terminate();
      callManager.updateCallStatus(sessionId, "ended");
      setTimeout(() => callManager.removeCall(sessionId), 2000);
    }
  };

  // Thêm hàm để tự động gia hạn đăng ký SIP
  const setupRegistrationRenewal = () => {
    if (!ua.value) return;

    // Gia hạn đăng ký trước khi hết hạn
    const renewalInterval = setInterval(() => {
      if (ua.value && isRegistered.value) {
        console.log("Renewing SIP registration");
        ua.value.register();
      }
    }, 3000 * 1000); // 50 phút (3000 giây)

    // Dọn dẹp khi component unmount
    onUnmounted(() => {
      clearInterval(renewalInterval);
    });
  };

  onUnmounted(() => {
    stopTimer();
    clearMediaStreams();
    audioStreams.cleanup();
    const session = toRaw(currentSession.value);
    if (session) session.terminate();

    const currentUA = toRaw(ua.value);
    if (currentUA) {
      currentUA.unregister(); // Unregister
      currentUA.stop(); // Stop
    }
  });

  return {
    isRegistered,
    isInCall,
    initializeSip,
    makeCall,
    hangUp,
    hangUpIncomingCall,
    answerIncomingCall,
    rejectIncomingCall,
    callDuration,
    isCallConfirmed,
  };
}
