import type { ChartData, ChartDataset as ChartJsDataset, ChartOptions, ChartType } from "chart.js";
import { computed, type ComputedRef } from "vue";

import { COLOR_COLLECTIONS, useHashColor } from "./useHashColor";

/**
 * Interface for chart dataset configuration
 * @template T - Type of data items in the dataset
 */
interface ChartDataset<T = any> {
  key: string; // Unique identifier for the dataset
  label: string; // Display label for the dataset
  data: T[]; // Array of data points
  dataKey?: keyof T; // Optional key to extract values from complex data objects
  type?: ChartType; // Optional chart type override for mixed charts
  colorKey?: string; // Optional key for color mapping
  color?: string; // Thêm trường color
}

/**
 * Configuration options for chart customization
 */
interface UseChartOptions {
  type?: ChartType; // Chart type (line, bar, etc.)
  colorScheme?: keyof typeof COLOR_COLLECTIONS; // Color scheme from predefined collections
  enableAnimation?: boolean; // Toggle chart animations
  legendPosition?: "top" | "bottom" | "left" | "right"; // Position of the chart legend
  chartOptions?: Partial<ChartOptions>; // Additional Chart.js options for full customization
}

/**
 * Default chart configuration
 * These settings can be overridden through UseChartOptions
 */
const defaultOptions: ChartOptions = {
  responsive: true,
  maintainAspectRatio: true,
  plugins: {
    tooltip: {
      mode: "index",
      intersect: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        stepSize: 1,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: "index",
  },
};

/**
 * Composable for creating and configuring charts
 *
 * @template TType - Chart type (defaults to 'line')
 * @param datasets - Reactive array of dataset configurations
 * @param labels - Reactive array of chart labels
 * @param options - Chart configuration options
 *
 * @returns {Object} Chart data and options ready for Chart.js initialization
 *
 * @example
 * ```ts
 * // Basic usage
 * const { chartData, chartOptions } = useChart(datasets, labels, {
 *   type: 'line',
 *   legendPosition: 'bottom'
 * });
 *
 * // Advanced usage with custom options
 * const { chartData, chartOptions } = useChart(datasets, labels, {
 *   type: 'line',
 *   chartOptions: {
 *     plugins: {
 *       legend: {
 *         labels: { usePointStyle: true }
 *       }
 *     }
 *   }
 * });
 * ```
 */
export function useChart<TType extends ChartType = "line">(
  datasets: ComputedRef<ChartDataset[]>,
  labels: ComputedRef<string[]>,
  options: UseChartOptions = {},
) {
  // Extract and set default options
  const {
    type = "line",
    colorScheme = "Colorful",
    enableAnimation = true,
    legendPosition = "top",
    chartOptions: customOptions = {},
  } = options;

  /**
   * Transform input datasets into Chart.js compatible format
   * - Handles data extraction from complex objects
   * - Applies color schemes
   * - Sets visual styles
   */
  const chartData = computed<ChartData<TType>>(() => ({
    labels: labels.value,
    datasets: datasets.value.map(
      (dataset) =>
        ({
          type: (dataset.type ?? type) as TType,
          label: dataset.label,
          data: dataset.dataKey
            ? dataset.data.map((item) => Number(item[dataset.dataKey as keyof typeof item]))
            : (dataset.data as number[]),
          borderColor: dataset.color || useHashColor(dataset.colorKey ?? dataset.key, colorScheme),
          backgroundColor: `${dataset.color || useHashColor(dataset.colorKey ?? dataset.key, colorScheme)}15`,
          tension: 0.4,
          fill: true,
        }) as unknown as ChartJsDataset<TType, number[]>,
    ) as ChartJsDataset<TType, number[]>[],
  }));

  /**
   * Merge default options with user-provided customizations
   * Custom options take precedence over defaults
   */
  const chartOptions = computed<ChartOptions>(() => ({
    ...defaultOptions,
    animation: {
      duration: enableAnimation ? 750 : 0,
    },
    plugins: {
      ...defaultOptions.plugins,
      legend: {
        position: legendPosition,
      },
      ...customOptions.plugins,
    },
    ...customOptions,
  }));

  return {
    chartData,
    chartOptions,
  };
}
