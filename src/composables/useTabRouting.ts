import { useRoute } from "vue-router";

import { usePermissions } from "@/composables/usePermissions";
import { tabList } from "@/config/person-tabs.config";

export const useTabRouting = () => {
  const route = useRoute();
  const { ability } = usePermissions();

  const getInitialTab = (): string => {
    // Priority 1: Query param
    if (route.query.tab) return route.query.tab.toString();

    // Priority 2: Route meta (if user has permission)
    if (route.meta.defaultTab) {
      const defaultTab = route.meta.defaultTab as string;
      const tabConfig = tabList.find((tab) => tab.key === defaultTab);

      if (tabConfig && ability.value?.can("read", tabConfig.subject)) {
        return defaultTab;
      }
    }

    // Priority 3: First permitted tab
    const firstPermittedTab = tabList.find((tab) => ability.value?.can("read", tab.subject))?.key;

    return firstPermittedTab || "deals";
  };

  return {
    getInitialTab,
  };
};
