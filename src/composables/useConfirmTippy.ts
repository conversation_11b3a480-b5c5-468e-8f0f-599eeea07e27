import tippy, { Instance } from "tippy.js";
import { onBeforeUnmount, ref } from "vue";

import "tippy.js/dist/tippy.css";

interface ConfirmOptions {
  title?: string;
  icon?: string;
  acceptLabel?: string;
  rejectLabel?: string;
  onAccept?: () => void;
  onReject?: () => void;
  skipPreventDefault?: boolean;
}

export function useConfirmTippy() {
  const tippyInstance = ref<Instance | null>(null);
  let isDestroying = false;

  const destroyInstance = () => {
    if (tippyInstance.value && !isDestroying) {
      isDestroying = true;
      tippyInstance.value.destroy();
      tippyInstance.value = null;
      isDestroying = false;
    }
  };

  const createConfirmContent = (options: ConfirmOptions) => {
    const wrapper = document.createElement("div");
    wrapper.className = "confirm-tippy-wrapper";

    // Add inline styles for proper background and animations
    wrapper.style.cssText = `
      background: white;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(0, 0, 0, 0.08);
      overflow: hidden;
      transform: scale(0.95);
      opacity: 0;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      animation: confirmTippyFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    `;

    wrapper.innerHTML = `
      <style>
        @keyframes confirmTippyFadeIn {
          from {
            opacity: 0;
            transform: scale(0.95) translateY(-4px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
        .confirm-tippy-wrapper .reject-btn {
          transition: all 0.15s ease;
        }
        .confirm-tippy-wrapper .accept-btn {
          transition: all 0.15s ease;
        }
        .confirm-tippy-wrapper .reject-btn:hover {
          background-color: #f3f4f6 !important;
          transform: translateY(-1px);
        }
        .confirm-tippy-wrapper .accept-btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .dark .confirm-tippy-wrapper {
          background: #1f2937 !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
          color: #f9fafb !important;
        }
      </style>
      <div class="flex flex-col gap-3 p-4">
        <div class="flex items-center gap-2">
          <i class="${options.icon || "pi pi-exclamation-triangle"} text-warning text-xl"></i>
          <span class="font-medium text-gray-800 dark:text-gray-200">${options.title || "Xác nhận"}</span>
        </div>
        <div class="flex justify-end gap-2">
          <button class="reject-btn px-3 py-2 rounded border border-gray-300 hover:bg-gray-100 text-sm font-medium text-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
            ${options.rejectLabel || "Hủy"}
          </button>
          <button class="accept-btn px-3 py-2 rounded bg-primary text-white hover:bg-primary-600 text-sm font-medium shadow-sm">
            ${options.acceptLabel || "Xác nhận"}
          </button>
        </div>
      </div>
    `;

    return wrapper;
  };

  const confirm = (event: MouseEvent | undefined, options: ConfirmOptions) => {
    // Handle event if exists
    if (event && !options.skipPreventDefault) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Get target element
    const target = (event?.currentTarget as Element) || document.body;

    // Destroy existing instance
    destroyInstance();

    const content = createConfirmContent(options);

    // Create new instance
    tippyInstance.value = tippy(target, {
      content,
      trigger: "manual",
      interactive: true,
      arrow: false,
      placement: "auto",
      appendTo: () => document.body,
      theme: "light",
      allowHTML: true,
      animation: "fade",
      duration: [200, 150],
      delay: [0, 0],
      maxWidth: "none",
      hideOnClick: false,
      popperOptions: {
        strategy: "fixed",
        modifiers: [
          {
            name: "flip",
            enabled: true,
            options: {
              fallbackPlacements: ["top", "bottom", "left", "right"],
            },
          },
          {
            name: "preventOverflow",
            enabled: true,
            options: {
              boundary: "viewport",
              altAxis: true,
            },
          },
        ],
      },
      onMount(instance) {
        const acceptBtn = content.querySelector(".accept-btn");
        const rejectBtn = content.querySelector(".reject-btn");

        const hideAndDestroy = () => {
          instance.hide();
          setTimeout(() => {
            if (tippyInstance.value === instance) {
              destroyInstance();
            }
          }, 150);
        };

        acceptBtn?.addEventListener("click", () => {
          options.onAccept?.();
          hideAndDestroy();
        });

        rejectBtn?.addEventListener("click", () => {
          options.onReject?.();
          hideAndDestroy();
        });

        const handleClickOutside = (e: MouseEvent) => {
          if (!content.contains(e.target as Node)) {
            hideAndDestroy();
          }
        };
        document.addEventListener("click", handleClickOutside);

        instance.setProps({
          onDestroy: () => {
            document.removeEventListener("click", handleClickOutside);
          },
        });
      },
    });

    tippyInstance.value.show();
  };

  onBeforeUnmount(() => {
    destroyInstance();
  });

  return {
    confirm,
  };
}
