import { useBreakpoints, breakpointsTailwind } from "@vueuse/core";

const MOBILE_BREAKPOINT = "md";
const TABLET_BREAKPOINT_START = "md";
const TABLET_BREAKPOINT_END = "lg";
const DESKTOP_BREAKPOINT_START = "lg";

export function useResponsive() {
  const breakpoints = useBreakpoints(breakpointsTailwind);

  const isMobile = breakpoints.smaller(MO<PERSON>LE_BREAKPOINT);
  const isTablet = breakpoints.between(TABLET_BREAKPOINT_START, TABLET_BREAKPOINT_END);
  const isDesktop = breakpoints.greaterOrEqual(DESKTOP_BREAKPOINT_START);

  return {
    isMobile,
    isTablet,
    isDesktop,
  };
}
