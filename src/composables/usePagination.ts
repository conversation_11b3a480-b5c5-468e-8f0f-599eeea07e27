import { Ref,ref } from 'vue';

interface PaginationOptions {
  initialPage?: number;
  initialRowsPerPage?: number;
  rowsPerPageOptions?: number[];
}

export function usePagination(options: PaginationOptions = {}) {
  const page = ref(options.initialPage || 1);
  const rowsPerPage = ref(options.initialRowsPerPage || 10);
  const rowsPerPageOptions = options.rowsPerPageOptions || [10, 20, 50, 100];
  
  const resetPagination = () => {
    page.value = 1;
  };
  
  const handlePageChange = (event: { first: number; rows: number }) => {
    page.value = Math.floor(event.first / event.rows) + 1;
    rowsPerPage.value = event.rows;
  };
  
  const getPaginationParams = () => ({
    offset: (page.value - 1) * rowsPerPage.value,
    limit: rowsPerPage.value,
  });
  
  return {
    page,
    rowsPerPage,
    rowsPerPageOptions,
    resetPagination,
    handlePageChange,
    getPaginationParams,
  };
} 