// composables/usePrint.ts
import { useRouter } from 'vue-router'

export type PrintEntity = 'payment' | 'deal' | 'bill' | 'plan'

interface PrintOptions {
  entity: PrintEntity
  id: string | number
  callback?: () => void
}

export const usePrint = () => {
  const router = useRouter()

  const printX = async ({ entity, id, callback }: PrintOptions) => {
    try {
      const routeName = `print-${entity}`
      const url = router.resolve({
        name: routeName,
        params: { id }
      }).href

      // Tạo và cấu hình iframe
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      document.body.appendChild(iframe)

      // Xử lý khi iframe load xong
      iframe.onload = () => {
        // Tạo MutationObserver để theo dõi khi content render xong
        const observer = new MutationObserver((mutations, obs) => {
          const contentElement = iframe.contentWindow?.document.querySelector('.layout-content')
          if (contentElement && contentElement.children.length > 0) {
            obs.disconnect() // Ngừng theo dõi

            try {
              iframe.contentWindow?.print()

              // Cleanup sau khi in xong
              const cleanup = () => {
                document.body.removeChild(iframe)
                callback?.()
              }

              if (iframe.contentWindow?.onafterprint !== undefined) {
                iframe.contentWindow.onafterprint = cleanup
              } else {
                setTimeout(cleanup, 100)
              }
            } catch (err) {
              console.error('Print execution error:', err)
              document.body.removeChild(iframe)
            }
          }
        })

        // Bắt đầu theo dõi DOM của iframe
        if (iframe.contentWindow?.document.body) {
          observer.observe(iframe.contentWindow.document.body, {
            childList: true,
            subtree: true
          })
        }
      }

      // Set source cho iframe
      iframe.src = url

    } catch (error) {
      console.error('Print error:', error)
    }
  }

  const printPayment = (id: string | number, callback?: () => void) => {
    printX({ entity: 'payment', id, callback })
  }

  const printDeal = (id: string | number, callback?: () => void) => {
    printX({ entity: 'deal', id, callback })
  }

  const printBill = (id: string | number, callback?: () => void) => {
    printX({ entity: 'bill', id, callback })
  }

  const printPlan = (id: string | number, callback?: () => void) => {
    printX({ entity: 'plan', id, callback })
  }

  return {
    printX,
    printPayment,
    printDeal,
    printBill,
    printPlan
  }
}
