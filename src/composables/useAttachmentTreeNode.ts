import { TreeNode } from 'primevue/tree';
import { computed,ref } from 'vue';

import {AttachmentResponse} from "@/api/bcare-types-v2";

// Giả sử bạn đã import interface AttachmentResponse

export function useAttachmentTreeNode(attachment: AttachmentResponse) {
  const attachmentRef = ref(attachment);

  const treeNode = computed<TreeNode>(() => ({
    key: attachmentRef.value.id.toString(),
    label: attachmentRef.value.product.name,
    data: attachmentRef.value,
    children: attachmentRef.value.children.map(child => useAttachmentTreeNode(child).treeNode.value),
    leaf: attachmentRef.value.children.length === 0,
    // Thêm các thuộc tính khác của TreeNode nếu cần
  }));

  // Hàm để cập nhật dữ liệu attachment
  const updateAttachment = (newData: Partial<AttachmentResponse>) => {
    attachmentRef.value = { ...attachmentRef.value, ...newData };
  };

  return {
    attachment: attachmentRef,
    treeNode,
    updateAttachment
  };
}
