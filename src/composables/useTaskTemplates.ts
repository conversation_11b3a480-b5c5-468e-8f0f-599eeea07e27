import { ref, computed } from "vue";
import type { TaskAddRequest, UserShort } from "@/api/bcare-types-v2";
import { useConfigurations } from "@/hooks/useSetting";

export interface TaskTemplateDefinition {
  templateInternalId: string;
  templateDisplayName: string;
  taskData: TaskData;
}

export interface TaskData {
  title: string;
  note: string;
  type: string;
  priority: number;
  start_date?: string;
  due_date?: string;
  end_date?: string;
  parent_id?: number;
  deal_id?: number;
  department_id?: number;
  appointment_id?: number;
  cron_expression?: string;
}

export interface TaskTemplatesConfig {
  list_of_templates: TaskTemplateDefinition[];
}

export function useTaskTemplates() {
  const { getSetting, fetchSettings, isLoading } = useConfigurations();
  const templates = ref<TaskTemplateDefinition[]>([]);

  // Load templates from settings
  const loadTemplates = async () => {
    try {
      // Fetch all settings once
      await fetchSettings("", "");
      // Then get specific setting from cache
      const setting = getSetting("templates", "task_templates");

      if (setting?.value) {
        const config = setting.value as TaskTemplatesConfig;
        templates.value = config.list_of_templates || [];
      } else {
        templates.value = [];
      }
    } catch (error) {
      console.error("Error loading task templates:", error);
      templates.value = [];
    }
  };

  // Apply template to form data
  const applyTemplate = (
    templateId: string,
    formDataRef: TaskAddRequest,
    primaryRef: { value: UserShort[] },
    contributorRef: { value: UserShort[] },
    reviewerRef: { value: UserShort[] },
  ) => {
    const template = templates.value.find((t) => t.templateInternalId === templateId);
    if (!template) {
      console.warn(`Template with ID ${templateId} not found`);
      return false;
    }

    // Apply template data to form
    const { taskData } = template;

    // Apply basic fields
    if (taskData.title !== undefined) formDataRef.title = taskData.title;
    if (taskData.note !== undefined) formDataRef.note = taskData.note;
    if (taskData.type !== undefined) formDataRef.type = taskData.type;
    if (taskData.priority !== undefined) formDataRef.priority = taskData.priority;
    if (taskData.start_date !== undefined) formDataRef.start_date = taskData.start_date;
    if (taskData.due_date !== undefined) formDataRef.due_date = taskData.due_date;
    if (taskData.end_date !== undefined) formDataRef.end_date = taskData.end_date;
    if (taskData.parent_id !== undefined) formDataRef.parent_id = taskData.parent_id;
    if (taskData.deal_id !== undefined) formDataRef.deal_id = taskData.deal_id;
    if (taskData.department_id !== undefined) formDataRef.department_id = taskData.department_id;
    if (taskData.appointment_id !== undefined) formDataRef.appointment_id = taskData.appointment_id;
    if (taskData.cron_expression !== undefined)
      formDataRef.cron_expression = taskData.cron_expression;

    return true;
  };

  const templateOptions = computed(() => {
    return templates.value.map((template) => ({
      label: template.templateDisplayName,
      command: () => template.templateInternalId,
      templateId: template.templateInternalId,
    }));
  });

  const hasTemplates = computed(() => templates.value.length > 0);

  return {
    templates,
    templateOptions,
    hasTemplates,
    isLoading,
    loadTemplates,
    applyTemplate,
  };
}
