import { ref, computed } from "vue";
import { taskTaskIsLast } from "@/api/bcare-v2";
import type { TaskGetRequest } from "@/api/bcare-types-v2";
import { useTaskTemplates } from "@/composables/useTaskTemplates";
import { useAsyncAction } from "@/composables/useAsyncAction";
import usePerson from "@/hooks/usePerson";

export interface TaskTemplateSelectorOptions {
  onTemplateSelected?: (templateId: string) => void;
  onCancelled?: () => void;
  onTaskCreated?: () => void;
}

export function useTaskTemplateSelector(options: TaskTemplateSelectorOptions = {}) {
  // State
  const isDialogVisible = ref(false);
  const selectedTemplateId = ref<string>("");
  const currentTaskId = ref<number | null>(null);
  const currentTaskSerial = ref<number | undefined>(undefined);
  const currentPersonId = ref<number | null>(null);
  const currentPersonName = ref<string>("");

  // Task templates
  const { templates, loadTemplates, isLoading: templatesLoading } = useTaskTemplates();

  // Person hook
  const { getPerson } = usePerson();

  // Async action for API calls
  const { isLoading: checkingLastTask, performAsyncAction } = useAsyncAction();

  // Computed options for Select component
  const selectOptions = computed(() => {
    const templateOptions = templates.value.map((template) => ({
      label: template.templateDisplayName,
      value: template.templateInternalId,
      isDanger: false,
    }));

    // Add cancel option at the end
    const cancelOption = {
      label: "Xác nhận không tạo task mới",
      value: "cancel",
      isDanger: true,
    };

    return [...templateOptions, cancelOption];
  });

  // Check if task is last and show dialog if needed
  const checkAndShowTemplateSelector = async (taskId: number, serial?: number) => {
    currentTaskId.value = taskId;
    currentTaskSerial.value = serial;

    return performAsyncAction(async () => {
      const request: TaskGetRequest = {
        id: taskId,
        ...(serial && { serial }),
      };

      const response = await taskTaskIsLast(request);

      if (response.code === 0 && response.data?.is_last) {
        // Task is last, show template selector
        await openTemplateDialog();
        return true; // Indicates dialog was shown
      }

      return false; // Task is not last, no dialog shown
    });
  };

  // Open template selection dialog
  const openTemplateDialog = async () => {
    isDialogVisible.value = true;
    selectedTemplateId.value = "";

    // Load templates if not already loaded
    if (templates.value.length === 0) {
      await loadTemplates();
    }
  };

  // Handle template selection
  const handleTemplateSelection = async () => {
    if (!selectedTemplateId.value) return;

    if (selectedTemplateId.value === "cancel") {
      // Handle cancel selection
      console.log("User cancelled template selection");
      closeDialog();
      options.onCancelled?.();
      return;
    }

    // Find the selected template
    const template = templates.value.find((t) => t.templateInternalId === selectedTemplateId.value);
    if (!template) {
      console.warn(`Template with ID ${selectedTemplateId.value} not found`);
      return;
    }

    console.log("Selected template:", template);
    closeDialog();

    // Notify parent component about template selection
    options.onTemplateSelected?.(selectedTemplateId.value);
  };

  // Close dialog and reset state
  const closeDialog = () => {
    isDialogVisible.value = false;
    selectedTemplateId.value = "";
    currentTaskId.value = null;
    currentTaskSerial.value = undefined;
  };

  // Manual trigger for opening dialog (for testing or manual use)
  const showTemplateSelector = async () => {
    await openTemplateDialog();
  };

  // Set person and load person name
  const setPersonId = async (personId: number) => {
    currentPersonId.value = personId;

    // Load person data to get name
    try {
      const personData = await getPerson({ id: personId }, false);
      if (personData) {
        currentPersonName.value = personData.full_name || "";
      }
    } catch (error) {
      console.warn("Failed to load person data:", error);
      currentPersonName.value = "";
    }
  };

  return {
    // State
    isDialogVisible,
    selectedTemplateId,
    currentTaskId,
    currentTaskSerial,
    currentPersonId,
    currentPersonName,

    // Computed
    selectOptions,

    // Loading states
    isLoading: computed(() => templatesLoading.value || checkingLastTask.value),
    templatesLoading,
    checkingLastTask,

    // Templates
    templates,

    // Methods
    checkAndShowTemplateSelector,
    handleTemplateSelection,
    closeDialog,
    showTemplateSelector,
    loadTemplates,
    setPersonId,
  };
}
