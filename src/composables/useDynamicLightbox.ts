// composables/useDynamicLightbox.ts
import { onMounted, onUnmounted, type Ref } from 'vue'
import { createVNode, render } from 'vue'

import LightboxModal from "@/directives/Lightbox/LightboxModal.vue"

let lightboxInstance: any = null

const createLightboxModal = () => {
  const container = document.createElement('div')
  const vnode = createVNode(LightboxModal)
  render(vnode, container)
  document.body.appendChild(container)
  return vnode.component?.exposed
}

export function useDynamicLightbox(containerRef: Ref<HTMLElement | null>) {
  if (!lightboxInstance) {
    lightboxInstance = createLightboxModal()
  }

  const initializeLightbox = () => {
    if (!containerRef.value) return

    const images = containerRef.value.getElementsByTagName('img')
    Array.from(images).forEach((img) => {
      const imgElement = img as HTMLImageElement
      // Remove existing listeners first
      imgElement.removeEventListener('click', handleImageClick)
      // Add new listener
      imgElement.addEventListener('click', handleImageClick)
      imgElement.style.cursor = 'pointer'
    })
  }

  const handleImageClick = (e: Event) => {
    e.preventDefault()
    e.stopPropagation()
    const imgElement = e.target as HTMLImageElement
    lightboxInstance?.openModal(imgElement.src, imgElement.alt)
  }

  const cleanup = () => {
    if (!containerRef.value) return

    const images = containerRef.value.getElementsByTagName('img')
    Array.from(images).forEach((img) => {
      const imgElement = img as HTMLImageElement
      imgElement.removeEventListener('click', handleImageClick)
      imgElement.style.cursor = ''
    })
  }

  onMounted(() => {
    initializeLightbox()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    initializeLightbox
  }
}
