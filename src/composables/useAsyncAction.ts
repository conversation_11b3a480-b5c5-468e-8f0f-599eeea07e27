import { readonly,ref } from "vue";

export function useAsyncAction(options?: {
  onError?: (error: unknown) => void;
  onSuccess?: () => void;
}) {
  const isLoading = ref(false);
  const error = ref<Error | null>(null);

  const resetState = () => {
    isLoading.value = true;
    error.value = null;
  };

  async function performAsyncAction<T>(action: () => Promise<T>): Promise<T> {
    resetState();
    try {
      const result = await action();
      options?.onSuccess?.();
      return result;
    } catch (err) {
      const errorObject = err instanceof Error ? err : new Error('An unknown error occurred');
      error.value = errorObject;
      options?.onError?.(errorObject);
      throw errorObject;
    } finally {
      isLoading.value = false;
    }
  }

  // Trả về một đối tượng mới mỗi lần gọi
  return {
    isLoading: readonly(isLoading),
    error: readonly(error),
    performAsyncAction,
  };
}
