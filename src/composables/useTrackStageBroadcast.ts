import { config } from "@/config";
import { useWsStore } from "@/stores/ws-store";

export function useTrackStageBroadcast() {
  const wsStore = useWsStore();
  const { realtime } = wsStore;

  const broadcastTrackStageChange = (
    trackId: number,
    newStageId: number,
    oldStageId: number,
    newIndex: number = 0,
  ) => {
    realtime.broadcast.to("pipeline_" + config.clinic.offline_pipeline_id).emit("track_update", {
      trackId,
      newStageId,
      oldStageId,
      newIndex,
    });
  };

  const broadcastPaymentDone = (trackId: number) => {
    broadcastTrackStageChange(trackId, config.clinic.after_payment_stage_id, 0, 0);
  };

  return {
    broadcastTrackStageChange,
    broadcastPaymentDone,
  };
}
