import { onMounted, onUnmounted } from "vue";

export function useImageLazyLoad() {
  let observer: IntersectionObserver;

  const initLazyLoading = () => {
    if (observer) {
      observer.disconnect();
    }

    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.add("loaded");
              observer.unobserve(img);
            }
          }
        });
      },
      {
        rootMargin: "50px",
      },
    );

    setTimeout(() => {
      const lazyImages = document.querySelectorAll("img.lazy-image");
      lazyImages.forEach((img) => {
        const imgElement = img as HTMLImageElement;
        imgElement.classList.remove("loaded");
        imgElement.src =
          "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
        observer.observe(imgElement);
      });
    }, 0);
  };

  const vLazyLoad = {
    mounted: (el: HTMLImageElement) => {
      observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && el.dataset.src) {
              el.src = el.dataset.src;
              el.classList.add("loaded");
              observer.unobserve(el);
            }
          });
        },
        { rootMargin: "50px" },
      );

      observer.observe(el);
    },
    unmounted: (el: HTMLImageElement) => {
      observer?.unobserve(el);
    },
  };

  onMounted(() => {
    initLazyLoading();
  });

  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
    }
  });

  return {
    initLazyLoading,
    vLazyLoad,
  };
}
