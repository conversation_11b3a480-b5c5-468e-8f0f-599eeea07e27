import { ref, watch } from "vue";

import { AppAbility, defineAbility } from "@/config/ability";
import { useAuthStore } from "@/stores/auth-store";
import { MenuItem } from "@/config/menu-definitions";

export function usePermissions() {
  const authStore = useAuthStore();

  const ability = ref<AppAbility | null>(null);

  // Memoize ability check results
  const menuPermissionCache = new Map<string, boolean>();

  const initializeAbility = () => {
    const user = authStore.user?.user;
    if (!user?.roles) {
      ability.value = null;
      return;
    }

    const departmentPosition = authStore.currentUser?.department_position;
    ability.value = defineAbility(user.roles, departmentPosition);
  };

  initializeAbility();

  const canAccessMenu = (pageName?: string): boolean => {
    if (!ability.value || !pageName) return false;

    // Check cache first
    if (menuPermissionCache.has(pageName)) {
      return menuPermissionCache.get(pageName)!;
    }

    if (ability.value.can("manage", "all")) {
      menuPermissionCache.set(pageName, true);
      return true;
    }

    // Dùng trực tiếp pageName làm subject
    const result = ability.value.can("read", pageName);
    menuPermissionCache.set(pageName, result);
    return result;
  };

  // Check if user can manage a resource with specific ID (own resource)
  const canManageOwnResource = (resourceId: number): boolean => {
    if (!ability.value) return false;

    // If user has full admin access
    if (ability.value.can("manage", "all")) return true;

    // Check if the resource ID matches the current user's ID
    const currentUserId = authStore.currentUser?.id;
    return !!currentUserId && resourceId === currentUserId;
  };

  // Check if user is a department manager
  const isDepartmentManager = (): boolean => {
    if (!ability.value) return false;

    // If user has full admin access
    if (ability.value.can("manage", "all")) return true;

    // Check if the user has department_position === "manager"
    const currentUser = authStore.currentUser;
    return currentUser?.department_position === "manager";
  };

  const filterMenuByPermission = (menu: MenuItem[]): MenuItem[] => {
    if (!ability.value) return [];

    // Use iterative approach instead of recursion
    const stack = [...menu];
    const result: MenuItem[] = [];

    while (stack.length) {
      const item = stack.pop()!;

      if (item.subMenu?.length) {
        const filteredSubMenu = filterMenuByPermission(item.subMenu);
        if (filteredSubMenu.length) {
          result.unshift({ ...item, subMenu: filteredSubMenu });
        }
        continue;
      }

      if (canAccessMenu(item.pageName)) {
        result.unshift(item);
      }
    }

    return result;
  };

  const onlyAdmin = (): boolean => {
    if (!ability.value) return false;
    return ability.value.can("manage", "all");
  };

  const canViewPhone = (): boolean => {
    if (!ability.value) return false;
    return ability.value.can("read", "customer-phone");
  };

  const hasAdministrativePrivileges = (): boolean => {
    return isDepartmentManager() || onlyAdmin();
  };

  watch(
    [() => authStore.user?.user.roles, () => authStore.currentUser?.department_position],
    () => {
      menuPermissionCache.clear();
      initializeAbility();
    },
  );

  return {
    ability,
    filterMenuByPermission,
    initializeAbility,
    onlyAdmin,
    canViewPhone,
    canManageOwnResource,
    isDepartmentManager,
    hasAdministrativePrivileges,
  };
}
