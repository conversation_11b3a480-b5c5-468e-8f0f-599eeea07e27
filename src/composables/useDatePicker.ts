import { computed, ref } from "vue";

import { parseGoTime } from "@/utils/time-helper";

export function useDatePicker(
  getter: () => string | null | undefined,
  setter: (value: string | null) => void,
) {
  const dateValue = ref<Date | null>(null);

  const dateString = computed({
    get: () => getter(),
    set: (value) => setter(value || ""),
  });

  const handleDateUpdate = (value: Date | Date[] | (Date | null)[] | null | undefined) => {
    if (Array.isArray(value)) {
      // Xử lý trường hợp Date[]
      dateValue.value = value[0] || null;
    } else {
      // Xử lý trường hợp Date hoặc null hoặc undefined
      dateValue.value = value || null;
    }

    dateString.value = dateValue.value ? dateValue.value.toISOString() : "";
  };

  // Khởi tạo dateValue từ getter
  dateValue.value = getter() ? parseGoTime(getter()!) : null;

  const reset = () => {
    dateValue.value = null;
    dateString.value = "";
  };

  return {
    dateValue,
    dateString,
    handleDateUpdate,
    reset,
  };
}
