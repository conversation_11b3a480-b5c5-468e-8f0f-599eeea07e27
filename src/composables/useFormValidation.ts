import { ref } from "vue";

interface ValidationRule {
  validate: (value: any) => boolean;
  message: string;
}

export interface ValidationRules {
  [key: string]: ValidationRule[];
}

export function useFormValidation<T extends Record<string, any>>(rules: ValidationRules) {
  const errors = ref<Partial<Record<string, string>>>({});

  const validateForm = (formData: T): boolean => {
    errors.value = {};
    let isValid = true;

    Object.keys(rules).forEach((field) => {
      const fieldValue = formData[field as keyof T];
      const fieldRules = rules[field];

      for (const rule of fieldRules) {
        if (!rule.validate(fieldValue)) {
          errors.value[field] = rule.message;
          isValid = false;
          break;
        }
      }
    });

    return isValid;
  };

  const clearErrors = () => {
    errors.value = {};
  };

  return {
    errors,
    validateForm,
    clearErrors,
  };
}

// Predefined rules
export const rules = {
  required: (message = "Trường này là bắt buộc") => ({
    validate: (value: any) => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === "string") return value.trim().length > 0;
      return !!value;
    },
    message,
  }),

  dateComparison: (startDate: Date, endDate: Date, message: string) => ({
    validate: () => startDate <= endDate,
    message,
  }),
};
