import { ref } from "vue";

export function useAudioStreams() {
  const localAudio = ref<HTMLAudioElement | null>(null);
  const remoteAudio = ref<HTMLAudioElement | null>(null);
  const audioContext = ref<AudioContext | null>(null);
  const originalStream = ref<MediaStream | null>(null);

  const createAudioElement = (muted = false) => {
    const audio = new Audio();
    audio.autoplay = true;
    audio.muted = muted;
    document.body.appendChild(audio);
    return audio;
  };

  const setupLocalAudio = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: false,
        autoGainControl: false,
      },
      video: false,
    });

    // Lưu stream gốc để có thể dọn dẹp sau này
    originalStream.value = stream;

    audioContext.value = new AudioContext();
    const source = audioContext.value.createMediaStreamSource(stream);
    const gainNode = audioContext.value.createGain();
    gainNode.gain.value = 1.5;
    source.connect(gainNode);

    const destination = audioContext.value.createMediaStreamDestination();
    gainNode.connect(destination);

    const amplifiedStream = destination.stream;

    localAudio.value = createAudioElement(true);
    localAudio.value.srcObject = amplifiedStream;

    return amplifiedStream;
  };

  const setupRemoteAudio = () => {
    remoteAudio.value = createAudioElement();
    return remoteAudio.value;
  };

  const cleanup = () => {
    // Dọn dẹp các phần tử audio
    [localAudio.value, remoteAudio.value].forEach((audio) => {
      if (audio) {
        audio.srcObject = null;
        audio.remove();
      }
    });

    // Dừng tất cả các track trong stream gốc
    if (originalStream.value) {
      originalStream.value.getTracks().forEach((track) => {
        track.stop();
      });
      originalStream.value = null;
    }

    // Đóng AudioContext
    if (audioContext.value && audioContext.value.state !== "closed") {
      audioContext.value.close();
      audioContext.value = null;
    }

    localAudio.value = null;
    remoteAudio.value = null;
  };

  return {
    setupLocalAudio,
    setupRemoteAudio,
    cleanup,
  };
}
