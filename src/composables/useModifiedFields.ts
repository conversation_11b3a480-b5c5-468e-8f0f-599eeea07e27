import { ref, watch, WatchStopHandle } from "vue";

type NestedKeyOf<T> = {
  [K in keyof T & (string | number)]: T[K] extends object
    ? `${K}` | `${K}.${NestedKeyOf<T[K]>}`
    : `${K}`;
}[keyof T & (string | number)];

export function useModifiedFields<T extends Record<string, any>>(
  formData: T,
  fieldsToWatch?: NestedKeyOf<T>[],
) {
  const modified = ref<string[]>([]);
  let watchers: WatchStopHandle[] = [];

  const getNestedValue = (obj: any, path: string) => {
    return path.split(".").reduce((acc, part) => acc && acc[part], obj);
  };

  const getAllNestedFields = (obj: any, prefix = ""): string[] => {
    return Object.entries(obj).flatMap(([key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === "object" && value !== null) {
        return [newKey, ...getAllNestedFields(value, newKey)];
      }
      return [newKey];
    });
  };

  const track = () => {
    stopTracking();

    const expandedFields = fieldsToWatch
      ? fieldsToWatch.flatMap((field) => {
          const value = getNestedValue(formData, field as string);
          if (typeof value === "object" && value !== null) {
            return getAllNestedFields(value, field as string);
          }
          return [field];
        })
      : getAllNestedFields(formData);

    expandedFields.forEach((field) => {
      const watcher = watch(
        () => getNestedValue(formData, field as string),
        (newValue, oldValue) => {
          if (newValue !== oldValue) {
            const rootField = (field as string).split(".")[0];
            if (!modified.value.includes(rootField)) {
              modified.value.push(rootField);
            }
          }
        },
        { deep: true },
      );
      watchers.push(watcher);
    });
  };

  const stopTracking = () => {
    watchers.forEach((stop) => stop());
    watchers = [];
  };

  const reset = () => {
    stopTracking();
    modified.value = [];
  };

  return {
    modified,
    track,
    stopTracking,
    reset,
  };
}
