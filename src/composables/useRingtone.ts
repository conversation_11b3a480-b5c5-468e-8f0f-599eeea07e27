import { ref } from "vue";

export function useRingtone() {
  const audio = ref<HTMLAudioElement | null>(null);
  const playCount = ref(0);
  const MAX_PLAYS = 2;

  const initRingtone = () => {
    audio.value = new Audio("/audio/incoming-ringtone.mp3");
    audio.value.loop = false; // Không dùng loop mặc định
    audio.value.volume = 0.05; // Set volume to 5%

    // Handle khi audio kết thúc
    audio.value.addEventListener("ended", () => {
      playCount.value++;
      if (playCount.value < MAX_PLAYS) {
        audio.value?.play();
      }
    });
  };

  const playRingtone = () => {
    if (!audio.value) {
      initRingtone();
    }
    // Reset counter và play
    playCount.value = 0;
    audio.value?.play().catch((e) => console.error("Failed to play ringtone:", e));
  };

  const stopRingtone = () => {
    if (audio.value) {
      audio.value.pause();
      audio.value.currentTime = 0;
      playCount.value = MAX_PLAYS; // Prevent further plays
    }
  };

  return {
    playRingtone,
    stopRingtone,
  };
}
