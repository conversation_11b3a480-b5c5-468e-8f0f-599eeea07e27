import { reactive, readonly, toRaw, Ref, WritableComputedRef } from "vue";
import type { Filter } from "@/api/bcare-types-v2";

/**
 * Configuration for a single input field in the filter drawer.
 */
export interface DrawerFilterConfigEntry<InputValueType = any, AllInputsType = any> {
  /**
   * Determines if the input maps to the 'filters' array
   * or a direct field on the main query payload.
   */
  target: "filters" | "payload";
  /**
   * The backend field name.
   * - If target is 'filters', this is Filter['field'].
   * - If target is 'payload', this is the key on the main query object (e.g., 'doctor', 'person').
   */
  field: string;
  /**
   * The filter operator (e.g., 'EQ', 'LIKE').
   * Only used if target is 'filters' and valueTransform doesn't return a full Filter object.
   */
  operator?: Filter["operator"];
  /**
   * Optional function to transform the raw input value from the drawer
   * into the value expected by the backend or into a complete Filter object/array.
   *
   * @param inputValue The value of the current input field.
   * @param allFilterInputs An object containing all current filter input values.
   * @returns The transformed value.
   *          - For target: 'filters':
   *            - If it returns a Filter or Filter[], it's used directly.
   *            - Otherwise, the return value is used as Filter['value'].
   *          - For target: 'payload':
   *            - The return value is used as the value for the payload field.
   *          Return `null` or `undefined` from this function to skip this filter.
   */
  valueTransform?: (
    inputValue: InputValueType,
    allFilterInputs: AllInputsType,
  ) => any | Filter | Filter[] | null | undefined;
  /**
   * Logic for combining this filter (if target is 'filters' and it's a simple filter).
   * Defaults to 'AND'.
   */
  logic?: Filter["logic"];
}

/**
 * A map of input keys (from your filter drawer's state) to their configurations.
 */
export interface DrawerFilterConfigs<AllInputsType = any> {
  [inputKey: string]: DrawerFilterConfigEntry<any, AllInputsType>;
}

/**
 * The object returned by `buildQueryModifications`, containing
 * the `filters` array and any other direct payload parameters.
 */
export interface QueryModifications {
  filters?: Filter[];
  [payloadKey: string]: any;
}

/**
 * Composable for managing filter drawer inputs and constructing query modifications.
 *
 * @param initialFilterInputs An object representing the initial state of the filter inputs.
 * @param configs Configuration object mapping input keys to their backend query translation.
 */
export function useDrawerFilters<T extends Record<string, any>>(
  initialFilterInputs: T,
  configs: DrawerFilterConfigs<T>,
) {
  // Deep clone initial inputs to ensure fresh state on reset and avoid modifying the original.
  // Handles simple objects, arrays, dates, null. For more complex types (Map, Set, functions),
  // a more robust deep clone (like lodash.cloneDeep) might be needed.
  const getClonedInitialInputs = (): T => JSON.parse(JSON.stringify(initialFilterInputs));

  const filterInputs = reactive<T>({ ...getClonedInitialInputs() });

  /**
   * Builds the query modifications object based on the current filter inputs and configurations.
   * @returns A `QueryModifications` object.
   */
  const buildQueryModifications = (): QueryModifications => {
    const modifications: QueryModifications = { filters: [] };
    // Use 'as T' to assert the correct type after toRaw
    const rawInputs = toRaw(filterInputs) as T;

    for (const inputKey in rawInputs) {
      if (!Object.prototype.hasOwnProperty.call(rawInputs, inputKey)) continue;

      const inputValue = rawInputs[inputKey as keyof T]; // Added 'as keyof T' for stricter typing if needed
      const config = configs[inputKey];

      if (!config) {
        // console.warn(`No configuration found for filter input: ${inputKey}`);
        continue;
      }

      // Allow 0 and false as valid inputs.
      // Consider an empty array as empty for multi-selects etc.
      const isInputValueEmpty =
        inputValue === null ||
        inputValue === undefined ||
        inputValue === "" ||
        (Array.isArray(inputValue) && inputValue.length === 0);

      if (isInputValueEmpty && !config.valueTransform) {
        continue; // Skip if no value and no transform to potentially create one
      }

      let processedValue = inputValue;
      if (config.valueTransform) {
        processedValue = config.valueTransform(inputValue, rawInputs);
      }

      const isProcessedValueEmpty =
        processedValue === null ||
        processedValue === undefined ||
        (typeof processedValue === "string" && processedValue === "") ||
        (Array.isArray(processedValue) && processedValue.length === 0);

      if (isProcessedValueEmpty) {
        continue;
      }

      if (config.target === "filters") {
        const targetFilters = modifications.filters as Filter[];
        if (Array.isArray(processedValue)) {
          // Assumes valueTransform returned an array of Filter objects
          targetFilters.push(...(processedValue.filter(Boolean) as Filter[]));
        } else if (
          typeof processedValue === "object" &&
          (Object.prototype.hasOwnProperty.call(processedValue, "field") ||
            Object.prototype.hasOwnProperty.call(processedValue, "conditions"))
        ) {
          // Assumes valueTransform returned a single Filter object
          targetFilters.push(processedValue as Filter);
        } else {
          // Construct a simple Filter object
          targetFilters.push({
            field: config.field,
            operator: config.operator,
            value: processedValue, // Transformed value is used directly
            logic: config.logic || "AND",
          } as Filter);
        }
      } else if (config.target === "payload") {
        modifications[config.field] = processedValue;
      }
    }

    if (modifications.filters && modifications.filters.length === 0) {
      delete modifications.filters; // Clean up if no filters were added
    }

    return modifications;
  };

  /**
   * Resets all filter inputs to their initial states.
   */
  const resetInputs = () => {
    const clonedInitial = getClonedInitialInputs();
    for (const key in clonedInitial) {
      (filterInputs as any)[key] = clonedInitial[key];
    }
    // Also ensure keys not in initial (if any were dynamically added, though not typical) are removed
    for (const key in filterInputs) {
      if (!Object.prototype.hasOwnProperty.call(clonedInitial, key)) {
        delete (filterInputs as any)[key];
      }
    }
  };

  return {
    /**
     * Reactive state of the filter inputs.
     * It's recommended to bind this to your drawer's form elements using v-model.
     */
    filterInputs, // Made writable for easier v-model integration
    buildQueryModifications,
    resetInputs,
  };
}
