import { computed, ref } from "vue";

import { CallDynamicQuery, CallResponse } from "@/api/bcare-types-v2";
import { callQuery } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v3";

export const initialCallDynamicQuery: CallDynamicQuery = {
  table: "call",
  selects: [],
  filters: [
    {
      field: "status",
      operator: "NEQ",
      value: "-1",
    },
  ],
  sort: [
    {
      field: "start_time",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  person: "",
  creator: "",
  state: "all",
};

interface CallStateStats {
  all: number;
  inbound: number;
  outbound: number;
  miss_inbound: number;
  miss_outbound: number;
}

export function useCallQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error, getAggregationResult } =
    useQuery<CallDynamicQuery, CallResponse>({
      queryFn: callQuery,
      initialQuery: initialCallDynamicQuery,
    });

  const calls = computed(() => items.value as CallResponse[]);

  const rowsPerPage = ref(initialCallDynamicQuery.limit);

  const fetchCalls = (req: Partial<CallDynamicQuery>, getCount: boolean = true) => {
    if (req.limit && req.limit !== rowsPerPage.value) {
      rowsPerPage.value = req.limit;
    }
    return fetchQuery(req, getCount);
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, initialCallDynamicQuery);
  };

  const stateStats = ref<CallStateStats | null>(null);

  const fetchStateStats = async () => {
    const states = ["inbound", "outbound", "miss_inbound", "miss_outbound"] as const;
    const statsQueries = states.map((state) =>
      callQuery({
        ...initialCallDynamicQuery,
        sort: [{ field: "created_at", order: "DESC" }],
        group_by: [],
        aggregations: [{ field: "id", function: "COUNT", alias: "count", over: {} }],
        state,
        limit: 1,
        offset: 0,
        person: "",
        creator: "",
      }),
    );

    const results = await Promise.all(statsQueries);

    stateStats.value = results.reduce(
      (acc, result, index) => ({
        ...acc,
        [states[index]]: result?.data?.result?.count || 0,
      }),
      { all: 0 } as CallStateStats,
    );

    // Calculate total
    stateStats.value.all =
      Object.values(stateStats.value).reduce((sum, count) => sum + count, 0) - stateStats.value.all;

    return stateStats.value;
  };

  return {
    // data
    calls,
    total,
    dynamicQuery,
    // fetch
    fetchCalls,
    // state
    isLoading,
    error,
    resetQuery,
    // additional functionality
    getAggregationResult,
    stateStats: computed(() => stateStats.value),
    fetchStateStats,
    rowsPerPage: computed(() => rowsPerPage.value),
  };
}
