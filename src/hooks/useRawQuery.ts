// useRawQuery.ts
import dayjs from "dayjs";
import { computed, ComputedRef, reactive, Ref, ref } from "vue";

import {
  Parameter,
  RawQueryRequest,
  RawQueryResponse,
  ExportRequest,
  HeaderConfig,
  ExportJobResponse,
  GenericResponse,
} from "@/api/bcare-types-v2";
import { queryRawQuery, exportExport } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

// Đ<PERSON>nh nghĩa các kiểu dữ liệu
type ParamType =
  | "string"
  | "int"
  | "float"
  | "bool"
  | "date"
  | "time"
  | "datetime"
  | "timestamp"
  | "json"
  | "array"
  | "uuid"
  | "bytea"
  | "null";
type arrayElementType = "string" | "int" | "float" | "bool";

export interface ParamConfig {
  type: ParamType;
  placeholder?: string | number;
  template?: boolean;
  default?: any;
  isLimit?: boolean;
  isOffset?: boolean;
  arrayElementType?: arrayElementType;
}

interface RawQueryOptions {
  pageSize?: number;
  timeout?: number;
  onSuccess?: (data: RawQueryResponse) => void;
  onError?: (error: any) => void;
  countQuery?: string; // Tùy chọn để truyền vào câu query count
}

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  pageCount: number;
  goToPage: (page: number) => Promise<RawQueryResponse | undefined>;
  setPageSize: (size: number) => Promise<RawQueryResponse | undefined>;
}

interface UseRawQueryReturn {
  data: Ref<any[]>;
  loading: Ref<boolean>;
  error: Ref<any>;
  rowCount: Ref<number>;
  executionTime: Ref<number>;
  exportLoading: Ref<boolean>;
  params: Record<string, any>;
  execute: () => Promise<RawQueryResponse | undefined>;
  refresh: () => Promise<RawQueryResponse | undefined>;
  resetParams: () => void;
  setParams: (newParams: Record<string, any>) => void;
  pagination: PaginationState;
  executeCount: () => Promise<number>; // Hàm thực thi query count
  executeExport: (
    headers?: HeaderConfig[],
    totalCount?: number,
  ) => Promise<GenericResponse<ExportJobResponse> | undefined>;
}

/**
 * Composable để làm việc với Raw Query API
 *
 * @param sqlQuery - SQL query với các placeholders ($1, $2...) và template vars {{var}}
 * @param paramConfig - Cấu hình cho mỗi tham số
 * @param options - Các tùy chọn bổ sung
 * @returns - Các state và hàm để thao tác với query
 */
export function useRawQuery(
  sqlQuery: string,
  paramConfig: Record<string, ParamConfig>,
  options: RawQueryOptions = {},
): UseRawQueryReturn {
  // States
  const data = ref<any[]>([]) as Ref<any[]>;
  const loading = ref<boolean>(false);
  const countLoading = ref<boolean>(false);
  const exportLoading = ref<boolean>(false);
  const error = ref<any>(null);
  const rowCount = ref<number>(0);
  const executionTime = ref<number>(0);

  // Sử dụng useAsyncAction
  const { isLoading, error: asyncError, performAsyncAction } = useAsyncAction();

  // Parameters (reactive object)
  const params = reactive<Record<string, any>>({});

  // Pagination
  const pagination = reactive<PaginationState>({
    page: 1,
    pageSize: options.pageSize || 10,
    get total() {
      return rowCount.value;
    },
    get pageCount() {
      return Math.ceil(this.total / this.pageSize);
    },
    goToPage,
    setPageSize,
  });

  // Xử lý SQL query bằng cách thay thế template params
  const processedSql: ComputedRef<string> = computed(() => {
    let sql = sqlQuery;

    // Thay thế tất cả template variables
    Object.entries(paramConfig).forEach(([key, config]) => {
      if (config.template && params[key] !== undefined) {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}}`, "g");
        sql = sql.replace(regex, String(params[key]));
      }
    });

    return sql;
  });

  // Chuyển đổi params thành API parameters
  const apiParameters: ComputedRef<Parameter[]> = computed(() => {
    const parameters: Parameter[] = [];
    const parameterMap: Record<number, Parameter> = {};

    // Xử lý các tham số thường (không phải template)
    Object.entries(paramConfig).forEach(([key, config]) => {
      if (config.template) return;

      if (params[key] !== undefined) {
        // Lấy vị trí placeholder
        const position =
          typeof config.placeholder === "string"
            ? parseInt(config.placeholder.replace("$", ""))
            : (config.placeholder as number);

        if (position > 0) {
          // Kiểm tra nếu giá trị là null thì đổi type thành 'null'
          const paramType = params[key] === null ? "null" : config.type;

          parameterMap[position] = {
            type: paramType,
            value: params[key] === null ? "" : formatValue(params[key], config.type),
          };

          // Thêm arrayElementType nếu cần và không phải type null
          if (paramType === "array" && config.arrayElementType) {
            parameterMap[position].arrayElementType = config.arrayElementType;
          }
        }
      }
    });

    // Chuyển đổi object thành mảng có thứ tự
    const positions = Object.keys(parameterMap)
      .map(Number)
      .sort((a, b) => a - b);
    if (positions.length > 0) {
      const maxPosition = positions[positions.length - 1];

      for (let i = 1; i <= maxPosition; i++) {
        if (parameterMap[i]) {
          parameters.push(parameterMap[i]);
        } else {
          // Điền null cho các vị trí bị thiếu
          parameters.push({ type: "null", value: "" });
        }
      }
    }

    return parameters;
  });

  // Format giá trị dựa trên kiểu tham số
  function formatValue(value: any, type: ParamType): string {
    if (value === null || value === undefined) {
      return "";
    }

    switch (type) {
      case "string":
      case "uuid":
      case "bytea":
        return String(value);

      case "int":
      case "float":
      case "bool":
        return String(value);

      case "date":
        if (value instanceof Date) {
          // Format as YYYY-MM-DD for PostgreSQL
          return dayjs(value).format("YYYY-MM-DD");
        }
        return String(value);

      case "time":
        if (value instanceof Date) {
          return value.toTimeString().split(" ")[0];
        }
        return String(value);

      case "datetime":
      case "timestamp":
        if (value instanceof Date) {
          return `${value.toISOString().split("T")[0]} ${value.toTimeString().split(" ")[0]}`;
        }
        return String(value);

      case "json":
        return typeof value === "string" ? value : JSON.stringify(value);

      case "array":
        return Array.isArray(value) ? JSON.stringify(value) : JSON.stringify([value]);

      default:
        return String(value);
    }
  }

  // Thực thi query count để lấy tổng số bản ghi
  async function executeCount(): Promise<number> {
    if (!options.countQuery) {
      return 0;
    }

    countLoading.value = true;

    try {
      // Lọc ra các tham số không phải limit và offset
      const countParameters: Parameter[] = [];

      // Tìm các vị trí của limit và offset
      const limitOffsetParams = Object.entries(paramConfig)
        .filter(([_, config]) => config.isLimit || config.isOffset)
        .map(([key, _]) => key);

      // Lấy tất cả các tham số từ apiParameters.value
      // nhưng không lấy các tham số limit và offset
      const request: RawQueryRequest = {
        sql: options.countQuery,
        parameters: apiParameters.value.filter((_, index) => {
          // Kiểm tra xem vị trí này có phải là limit hoặc offset không
          return !Object.entries(paramConfig).some(([key, config]) => {
            if (limitOffsetParams.includes(key)) {
              const position =
                typeof config.placeholder === "string"
                  ? parseInt(config.placeholder.replace("$", "")) - 1
                  : (config.placeholder as number) - 1;
              return position === index;
            }
            return false;
          });
        }),
        timeout: options.timeout || 30,
      };

      const response = await queryRawQuery(request);

      if (response.data?.results && response.data.results.length > 0) {
        const count = parseInt(response.data.results[0].total_count);
        rowCount.value = count;
        return count;
      }

      return 0;
    } catch (err) {
      console.error("Error executing count query:", err);
      return 0;
    } finally {
      countLoading.value = false;
    }
  }

  // Thực thi query
  async function execute(): Promise<RawQueryResponse | undefined> {
    loading.value = true;
    error.value = null;

    // Nếu có count query, thực thi song song
    if (options.countQuery) {
      executeCount().catch(console.error);
    }

    return await performAsyncAction(async () => {
      try {
        const request: RawQueryRequest = {
          sql: processedSql.value,
          parameters: apiParameters.value,
          timeout: options.timeout || 30,
        };

        const response = await queryRawQuery(request);

        data.value = response.data?.results || [];

        // Chỉ cập nhật rowCount nếu không sử dụng count query
        if (!options.countQuery) {
          rowCount.value = response.data?.rowCount || data.value.length;
        }

        executionTime.value = response.data?.executionTime || 0;

        if (options.onSuccess && response.data) {
          options.onSuccess(response.data);
        }

        return response.data || undefined;
      } catch (err) {
        error.value = err;

        if (options.onError) {
          options.onError(err);
        }

        throw err;
      } finally {
        loading.value = false;
      }
    });
  }

  // Làm mới query (alias cho execute)
  function refresh(): Promise<RawQueryResponse | undefined> {
    return execute();
  }

  // Reset tất cả parameters
  function resetParams(): void {
    Object.keys(params).forEach((key) => {
      delete params[key];
    });

    // Thiết lập lại giá trị mặc định
    Object.entries(paramConfig).forEach(([key, config]) => {
      if (config.default !== undefined) {
        params[key] = config.default;
      }
    });
  }

  // Thiết lập nhiều parameters cùng lúc
  function setParams(newParams: Record<string, any>): void {
    Object.entries(newParams).forEach(([key, value]) => {
      params[key] = value;
    });
  }

  // Đi đến trang cụ thể
  async function goToPage(page: number): Promise<RawQueryResponse | undefined> {
    pagination.page = page;

    // Cập nhật offset parameter nếu được cấu hình
    const offsetParamEntry = Object.entries(paramConfig).find(([_, config]) => config.isOffset);

    if (offsetParamEntry) {
      const [offsetParam] = offsetParamEntry;
      params[offsetParam] = (page - 1) * pagination.pageSize;
    }

    // Thực thi query chính (không cần gọi executeCount() vì tổng số bản ghi không thay đổi)
    return execute();
  }

  // Thay đổi số lượng items trên mỗi trang
  async function setPageSize(size: number): Promise<RawQueryResponse | undefined> {
    pagination.pageSize = size;
    pagination.page = 1;

    // Cập nhật limit và offset parameters nếu được cấu hình
    const limitParamEntry = Object.entries(paramConfig).find(([_, config]) => config.isLimit);

    const offsetParamEntry = Object.entries(paramConfig).find(([_, config]) => config.isOffset);

    if (limitParamEntry) {
      const [limitParam] = limitParamEntry;
      params[limitParam] = size;
    }

    if (offsetParamEntry) {
      const [offsetParam] = offsetParamEntry;
      params[offsetParam] = 0;
    }

    // Thực thi query chính
    return execute();
  }

  // Thiết lập giá trị mặc định
  Object.entries(paramConfig).forEach(([key, config]) => {
    if (config.default !== undefined) {
      params[key] = config.default;
    }
  });

  // Function to execute the export API call
  async function executeExport(
    headers?: HeaderConfig[],
    totalCount?: number,
  ): Promise<GenericResponse<ExportJobResponse> | undefined> {
    exportLoading.value = true;
    error.value = null;

    const effectiveHeaders = headers ?? [];
    const effectiveTotalCount = totalCount ?? rowCount.value;

    return await performAsyncAction(async () => {
      try {
        const exportParameters = [...apiParameters.value];

        const limitParamIndex = Object.entries(paramConfig).find(
          ([_, config]) => config.isLimit,
        )?.[1]?.placeholder;

        const limitIndex =
          typeof limitParamIndex === "string"
            ? parseInt(limitParamIndex.replace("$", "")) - 1
            : (limitParamIndex as number) - 1;

        if (limitIndex >= 0 && limitIndex < exportParameters.length) {
          const maxLimit = Math.min(effectiveTotalCount, 10000);
          exportParameters[limitIndex] = {
            type: "int",
            value: String(maxLimit),
          };
        }

        const request: ExportRequest = {
          sql: processedSql.value,
          parameters: exportParameters,
          headers: effectiveHeaders,
          total_record: effectiveTotalCount,
        };

        const response = await exportExport(request);
        return response;
      } catch (err) {
        error.value = err;
        if (options.onError) {
          options.onError(err);
        }
        throw err;
      } finally {
        exportLoading.value = false;
      }
    });
  }

  return {
    // Data và state
    data,
    loading,
    error: asyncError as Ref<any>,
    rowCount,
    executionTime,
    exportLoading,
    // Reactive params
    params,

    // Methods
    execute,
    refresh,
    resetParams,
    setParams,
    executeCount,
    executeExport,

    // Pagination
    pagination,
  };
}
