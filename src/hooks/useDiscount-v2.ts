import { computed,shallowRef } from "vue";

import type {
  CalculateDiscountRequest,
  CalculateDiscountResponse,
  Discount,
  DiscountAddRequest,
  DiscountDeleteRequest,
  DiscountGetRequest,
  DiscountListRequest,
  DiscountListResponse,
  DiscountUpdateRequest,
  DiscountUsageAddRequest,
  DiscountUsageDeleteRequest,
  DiscountUsageListRequest,
  DiscountUsageListResponse,
  DiscountUsageResponse,
  EligibleDiscountRequest,
  EligibleDiscountResponse,
} from "@/api/bcare-types-v2";
import {
  discount_usageAdd,
  discount_usageDelete,
  discount_usageList,
  discountAdd,
  discountCalculateDiscount,
  discountDelete,
  discountEligibleDiscount,
  discountGet,
  discountList,
  discountUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UseDiscountOptions {
  initialState?: {
    discounts?: Discount[];
    currentDiscount?: Discount | null;
  };
}

/**
 * useDiscount composable for managing discount-related state and actions.
 * @param options - Initial state options.
 * @returns An object containing discounts state, actions, and utility functions.
 */
export default function useDiscount(options: UseDiscountOptions = {}) {
  const { initialState = {} } = options;

  // Use shallowRef for better performance with complex objects
  const discounts = shallowRef<Discount[]>(initialState.discounts || []);
  const currentDiscount = shallowRef<Discount | null>(initialState.currentDiscount || null);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Computed properties
  const discountCount = computed(() => discounts.value.length);
  const getDiscountById = computed(() => (id: number) =>
    discounts.value.find(discount => discount.id === id)
  );

  /**
   * Adds a new discount and updates the local state.
   */
  const addDiscount = (req: DiscountAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountAdd(req);
        if (response.data) {
          discounts.value.push(response.data);
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to add discount");
      }
    });

  /**
   * Calculates the discount based on the provided request.
   */
  const calculateDiscount = (req: CalculateDiscountRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountCalculateDiscount(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to calculate discount");
      }
    });

  /**
   * Deletes a discount and updates the local state.
   */
  const deleteDiscount = (req: DiscountDeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountDelete(req);
        if (response.data) {
          discounts.value = discounts.value.filter(
            discount => discount.id !== req.id
          );
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete discount");
      }
    });

  /**
   * Retrieves eligible discounts based on the provided request.
   */
  const getEligibleDiscount = (req: EligibleDiscountRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountEligibleDiscount(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to get eligible discounts");
      }
    });

  /**
   * Retrieves a specific discount and updates the currentDiscount state.
   */
  const getDiscount = (req: DiscountGetRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountGet(req);
        currentDiscount.value = response.data ?? null;
        return response.data;
      } catch (err) {
        throw new Error("Failed to get discount");
      }
    });

  /**
   * Lists all discounts and updates the local state.
   */
  const listDiscounts = (req: DiscountListRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountList(req);
        discounts.value = response.data?.discounts ?? [];
        return response.data;
      } catch (err) {
        throw new Error("Failed to list discounts");
      }
    });

  /**
   * Updates an existing discount and updates the local state.
   */
  const updateDiscount = (req: DiscountUpdateRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discountUpdate(req);
        if (response.data) {
          const index = discounts.value.findIndex(
            discount => discount.id === req.id
          );
          if (index !== -1) {
            discounts.value[index] = response.data;
          }
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to update discount");
      }
    });

  /**
   * Adds a usage record for a discount.
   */
  const addDiscountUsage = (req: DiscountUsageAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discount_usageAdd(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to add discount usage");
      }
    });

  /**
   * Deletes a usage record for a discount.
   */
  const deleteDiscountUsage = (req: DiscountUsageDeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discount_usageDelete(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete discount usage");
      }
    });

  /**
   * Lists all usage records for discounts.
   */
  const listDiscountUsages = (req: DiscountUsageListRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await discount_usageList(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to list discount usages");
      }
    });

  return {
    // State
    discounts,
    currentDiscount,
    isLoading,
    error,

    // Computed
    discountCount,
    getDiscountById,

    // Discount actions
    addDiscount,
    calculateDiscount,
    deleteDiscount,
    getEligibleDiscount,
    getDiscount,
    listDiscounts,
    updateDiscount,

    // Discount usage actions
    addDiscountUsage,
    deleteDiscountUsage,
    listDiscountUsages,
  };
}
