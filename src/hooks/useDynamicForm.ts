// src/composables/useDynamicForm.ts

import { useDebounceFn } from "@vueuse/core";
import { onMounted, reactive } from "vue";

import usePersonData from "@/hooks/usePersonData";

export type Answer = Record<string, any>;

export default function useDynamicForm(
  formId: string,
  questions: Record<string, any>,
  personId: number,
  dynamicQuestions: boolean = false,
) {
  const personData = usePersonData();

  const answers = reactive<Record<string, Answer>>(
    Object.keys(questions).reduce(
      (acc, key) => {
        acc[key] = {};
        return acc;
      },
      {} as Record<string, Answer>,
    ),
  );

  const addDynamicQuestions = (data: Record<string, any>) => {
    Object.keys(data).forEach((key) => {
      if (!(key in answers)) {
        answers[key] = {};
      }
    });
  };

  const syncData = (questionId: string) => {
    const answer = answers[questionId];
    const value = JSON.stringify(answer);
    personData.setData({
      person_id: personId,
      kind: `examination__${formId}`,
      key: questionId,
      value: value,
    });
  };

  const debouncedSyncData = useDebounceFn((questionId: string) => {
    syncData(questionId);
  }, 1000);

  const loadInitialData = async () => {
    const response = await personData.getData({
      person_id: personId,
      kind: `examination__${formId}`,
    });

    if (response?.data) {
      if (dynamicQuestions) {
        addDynamicQuestions(response.data);
      }

      Object.entries(response.data).forEach(([key, value]) => {
        if (typeof value === "string" && (key in answers || dynamicQuestions)) {
          try {
            const parsedValue = JSON.parse(value);
            if (parsedValue && typeof parsedValue === "object") {
              answers[key] = parsedValue as Answer;
            }
          } catch (e) {
            console.error(`Error parsing value for key ${key}:`, e);
          }
        }
      });
    }
  };

  onMounted(loadInitialData);

  return {
    answers,
    syncData,
    debouncedSyncData,
  };
}
