import { storeToRefs } from "pinia";

import type { ClearDataRequest, GetDataRequest, SetDataRequest } from "@/api/bcare-types-v2";
import { usePersonDataStore } from "@/stores/person-data-store";

export default function usePersonData() {
  const personDataStore = usePersonDataStore();
  const { currentData, isLoading, error } = storeToRefs(personDataStore);

  const clearData = async (req: ClearDataRequest) => {
    return await personDataStore.clearData(req);
  };

  const getData = async (req: GetDataRequest) => {
    return await personDataStore.getData(req);
  };

  const setData = async (req: SetDataRequest) => {
    return await personDataStore.setData(req);
  };

  return {
    currentData,
    isLoading,
    error,
    clearData,
    getData,
    setData,
  };
}
