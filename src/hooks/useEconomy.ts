import dayjs from "dayjs";
import { computed, shallowRef } from "vue";

import type {
  AllocationAddRequest,
  ConvertToPaymentRequest,
  DeleteRequest,
  DepositAddRequest,
  DepositListRequest,
  DepositResponse,
  GetRequest,
  PaymentAddRequest,
  PaymentAllocationAddRequest,
  PaymentAllocationDeleteRequest,
  PaymentAllocationUpdateRequest,
  PaymentDeleteRequest,
  PaymentGetRequest,
  PaymentListRequest,
  PaymentResponse,
  PaymentUpdateRequest,
  UpdateDepositRequest,
} from "@/api/bcare-types-v2";
import {
  economy_depositAdd,
  economy_depositAllocationAdd,
  economy_depositAllocationDelete,
  economy_depositCancelConversion,
  economy_depositConvertToPayment,
  economy_depositDelete,
  economy_depositGet,
  economy_depositList,
  economy_depositUpdate,
  economy_payment_allocationAdd,
  economy_payment_allocationDelete,
  economy_payment_allocationUpdate,
  economy_paymentAdd,
  economy_paymentDelete,
  economy_paymentGet,
  economy_paymentList,
  economy_paymentUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UseEconomyOptions {
  initialState?: {
    deposits?: DepositResponse[];
    currentDeposit?: DepositResponse | null;
    payments?: PaymentResponse[];
    currentPayment?: PaymentResponse | null;
  };
}

export default function useEconomy(options: UseEconomyOptions = {}) {
  const { initialState = {} } = options;

  // State management using shallowRef
  const deposits = shallowRef<DepositResponse[]>(initialState.deposits || []);
  const currentDeposit = shallowRef<DepositResponse | null>(initialState.currentDeposit || null);
  const payments = shallowRef<PaymentResponse[]>(initialState.payments || []);
  const currentPayment = shallowRef<PaymentResponse | null>(initialState.currentPayment || null);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Computed properties
  const depositCount = computed(() => deposits.value.length);
  const paymentCount = computed(() => payments.value.length);
  const getDepositById = computed(
    () => (id: number) => deposits.value.find((deposit) => deposit.id === id),
  );
  const getPaymentById = computed(
    () => (id: number) => payments.value.find((payment) => payment.id === id),
  );

  // Deposit related actions
  const addDeposit = (req: DepositAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositAdd(req);
        if (response.data) {
          deposits.value.push(response.data);
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to add deposit");
      }
    });

  const addDepositAllocation = (req: AllocationAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositAllocationAdd(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to add deposit allocation");
      }
    });

  const deleteDepositAllocation = (req: DeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositAllocationDelete(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete deposit allocation");
      }
    });

  const cancelDepositConversion = (req: DeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositCancelConversion(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to cancel deposit conversion");
      }
    });

  const convertDepositToPayment = (req: ConvertToPaymentRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositConvertToPayment(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to convert deposit to payment");
      }
    });

  const deleteDeposit = (req: DeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositDelete(req);
        if (response.data) {
          deposits.value = deposits.value.filter((deposit) => deposit.id !== req.id);
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete deposit");
      }
    });

  const getDeposit = (req: GetRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositGet(req);
        if (response.data) {
          currentDeposit.value = response.data;
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to get deposit");
      }
    });

  const listDeposits = (req: DepositListRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositList(req);
        deposits.value = response.data?.deposits ?? [];
        return response.data;
      } catch (err) {
        throw new Error("Failed to list deposits");
      }
    });

  const updateDeposit = (req: UpdateDepositRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_depositUpdate(req);
        if (response.data) {
          const index = deposits.value.findIndex((deposit) => deposit.id === req.id);
          if (index !== -1) {
            deposits.value[index] = response.data;
          }
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to update deposit");
      }
    });

  // Payment related actions
  const addPayment = (req: PaymentAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_paymentAdd(req);
        if (response.code !== 0) {
          throw { code: response.code, message: response.message };
        }
        if (response.data) {
          payments.value.push(response.data);
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to add payment");
      }
    });

  const deletePayment = (req: PaymentDeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_paymentDelete(req);
        if (response.data) {
          payments.value = payments.value.filter((payment) => payment.id !== req.id);
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete payment");
      }
    });

  const getPayment = (req: PaymentGetRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_paymentGet(req);
        if (response.data) {
          currentPayment.value = response.data;
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to get payment");
      }
    });

  const listPayments = (req: PaymentListRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_paymentList(req);
        payments.value = response.data?.payments ?? [];
        return response.data;
      } catch (err) {
        throw new Error("Failed to list payments");
      }
    });

  const updatePayment = (req: PaymentUpdateRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_paymentUpdate(req);
        if (response.data) {
          const index = payments.value.findIndex((payment) => payment.id === req.id);
          if (index !== -1) {
            payments.value[index] = response.data;
          }
        }
        return response.data;
      } catch (err) {
        throw new Error("Failed to update payment");
      }
    });

  // Payment allocation actions
  const addPaymentAllocation = (req: PaymentAllocationAddRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_payment_allocationAdd(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to add payment allocation");
      }
    });

  const deletePaymentAllocation = (req: PaymentAllocationDeleteRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_payment_allocationDelete(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to delete payment allocation");
      }
    });

  const updatePaymentAllocation = (req: PaymentAllocationUpdateRequest) =>
    performAsyncAction(async () => {
      try {
        const response = await economy_payment_allocationUpdate(req);
        return response.data;
      } catch (err) {
        throw new Error("Failed to update payment allocation");
      }
    });

  const updatePaymentDate = (payment: PaymentResponse, newDate: Date) =>
    performAsyncAction(async () => {
      try {
        const newDateTime = dayjs(newDate)
          .set("hour", dayjs(payment.payment_date).hour())
          .set("minute", dayjs(payment.payment_date).minute())
          .set("second", dayjs(payment.payment_date).second());

        return await updatePayment({
          ...payment,
          payment_date: newDateTime.toISOString(),
          modified: ["payment_date"],
        });
      } catch (err) {
        throw new Error("Failed to update payment date");
      }
    });

  return {
    // State
    deposits,
    currentDeposit,
    payments,
    currentPayment,
    isLoading,
    error,

    // Computed
    depositCount,
    paymentCount,
    getDepositById,
    getPaymentById,

    // Deposit actions
    addDeposit,
    addDepositAllocation,
    deleteDepositAllocation,
    cancelDepositConversion,
    convertDepositToPayment,
    deleteDeposit,
    getDeposit,
    listDeposits,
    updateDeposit,

    // Payment actions
    addPayment,
    deletePayment,
    getPayment,
    listPayments,
    updatePayment,

    // Payment allocation actions
    addPaymentAllocation,
    deletePaymentAllocation,
    updatePaymentAllocation,

    // New action
    updatePaymentDate,
  };
}
