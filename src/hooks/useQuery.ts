import { Ref, ref } from "vue";

import { useAsyncAction } from "@/composables/useAsyncAction";

export interface QueryResponse {
  code: number;
  data: string;
  message?: string;
}

interface QueryConfig<T, P> {
  queryFn: (payload: P) => Promise<any>;
  initialQuery?: Partial<P>;
}

export function useQuery<T, P extends Record<string, any>>(config: QueryConfig<T, P>) {
  const { queryFn, initialQuery = {} } = config;
  const items: Ref<T[]> = ref([]);
  const total: Ref<number> = ref(0);
  const dynamicQuery: Ref<Partial<P>> = ref(initialQuery);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  async function fetchQuery(req: Partial<P>, getCount: boolean = true) {
    const payload = { ...dynamicQuery.value, ...req } as P;
    dynamicQuery.value = payload;

    return performAsyncAction(async () => {
      try {
        let queryPromises = [queryFn(payload)];

        if (getCount) {
          queryPromises.push(
            queryFn({
              ...payload,
              limit: 1,
              offset: 0,
              aggregations: [
                {
                  field: "id",
                  function: "COUNT",
                  alias: "count",
                },
              ],
            } as P),
          );
        }

        const responses = await Promise.all(queryPromises);
        const queryResponse = responses[0];
        const countResponse = responses[1];

        if (queryResponse.code === 0 && typeof queryResponse.data === "string") {
          const parsedData = JSON.parse(queryResponse.data);
          items.value = parsedData.rows || [];
        } else {
          items.value = [];
        }

        if (getCount && countResponse && countResponse.code === 0) {
          const parsedData = JSON.parse(countResponse.data);
          total.value = parsedData?.count || 0;
        }

        return queryResponse;
      } catch (e) {
        items.value = [];
        total.value = 0;
        throw e;
      }
    });
  }

  return {
    items,
    total,
    dynamicQuery,
    fetchQuery,
    isLoading,
    error,
  };
}
