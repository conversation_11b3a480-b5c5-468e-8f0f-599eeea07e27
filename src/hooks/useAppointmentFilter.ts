import { reactive, computed, ref, watch } from "vue";
import dayjs from "dayjs";
import { AppointmentDynamicQuery, AppointmentResponse, Filter } from "@/api/bcare-types-v2";
import { appointmentQuery } from "@/api/bcare-v2";
import { CommonStatus, FilterOperator } from "@/api/bcare-enum";
import { useQuery } from "./useQuery-v3";
import { useDebounceFn } from "@vueuse/core";

export interface AppointmentFilterState {
  start_time?: Date;
  end_time?: Date;
  doctor_id?: number | string | null;
  person_id?: number | null;
  person?: string;
  note?: string;
  extra_notes?: string;
  arrived?: "yes" | "no" | "both";
  has_doctor?: "yes" | "no" | "both";
}

// Default settings for the underlying AppointmentDynamicQuery
const defaultQueryConfig: AppointmentDynamicQuery = {
  table: "appointment",
  selects: [],
  filters: [
    {
      field: "status",
      operator: FilterOperator.NEQ,
      value: CommonStatus.DELETED.toString(),
    },
  ],
  group_by: [],
  aggregations: [],
  sort: [
    { field: "start_time", order: "ASC" },
    { field: "person_id", order: "ASC" },
  ],
  limit: 10, // Default page size
  offset: 0,
  joins: [],
  doctor: "",
  person: "",
  task: "",
  note: "",
  arrived: "both",
  has_doctor: "yes",
  source_id: undefined,
  export: false,
};

export function useAppointmentFilters(initialFilters?: Partial<AppointmentFilterState>) {
  const {
    items,
    total,
    dynamicQuery: underlyingDynamicQueryFromUseQuery,
    fetchQuery: underlyingFetchQuery,
    isLoading,
    error,
  } = useQuery<AppointmentDynamicQuery, AppointmentResponse>({
    queryFn: appointmentQuery,
    initialQuery: { ...defaultQueryConfig },
  });

  // Reactive state for filters, initialized with defaults and any initial overrides
  const filterState = reactive<AppointmentFilterState>({
    start_time: initialFilters?.start_time ?? undefined,
    end_time: initialFilters?.end_time ?? undefined,
    doctor_id: initialFilters?.doctor_id ?? null,
    person_id: initialFilters?.person_id ?? null,
    person: initialFilters?.person ?? "",
    note: initialFilters?.note ?? "",
    extra_notes: initialFilters?.extra_notes ?? "",
    arrived: initialFilters?.arrived ?? defaultQueryConfig.arrived,
    has_doctor: initialFilters?.has_doctor ?? defaultQueryConfig.has_doctor,
  });

  // Store initial filter state for reset functionality
  const initialFilterStateSnapshot = { ...filterState };

  // Base static filters that are always applied
  const baseStaticFilters: Filter[] = [
    {
      field: "status",
      operator: FilterOperator.NEQ,
      value: CommonStatus.DELETED.toString(),
    },
  ];

  // Search functionality
  const searchQuery = ref(filterState.person || "");

  // Check if there are active filters and count them
  const hasActiveFilters = computed(() => {
    return activeFiltersCount.value > 0;
  });

  // Count active filters
  const activeFiltersCount = computed(() => {
    let count = 0;

    if (filterState.note && filterState.note.length > 0) count++;
    if (filterState.extra_notes && filterState.extra_notes.length > 0) count++;
    if (filterState.arrived !== "both") count++;
    if (filterState.person && filterState.person.length > 0) count++;

    // Add other filters as needed
    if (
      filterState.start_time &&
      !dayjs(filterState.start_time).isSame(dayjs().startOf("day"), "day")
    )
      count++;

    if (filterState.end_time && !dayjs(filterState.end_time).isSame(dayjs().endOf("day"), "day"))
      count++;

    return count;
  });

  // Debounced search function
  const debouncedSearch = useDebounceFn(() => {
    filterState.person = searchQuery.value;
    return fetchFilteredAppointments({ page: 1 });
  }, 300);

  // Clear search
  const clearSearch = () => {
    searchQuery.value = "";
    filterState.person = "";
    return fetchFilteredAppointments({ page: 1 });
  };

  // Update searchQuery when filterState.person changes
  watch(
    () => filterState.person,
    (newValue) => {
      if (newValue !== searchQuery.value) {
        searchQuery.value = newValue || "";
      }
    },
    { immediate: true },
  );

  const buildDynamicFilters = (): Filter[] => {
    const constructedFilters: Filter[] = [];

    if (filterState.start_time) {
      constructedFilters.push({
        field: "start_time",
        operator: FilterOperator.GTE,
        value: dayjs(filterState.start_time).startOf("day").format("YYYY-MM-DD[T]00:00:00+07:00"),
      });
    }
    if (filterState.end_time) {
      let endDate = dayjs(filterState.end_time);
      if (filterState.start_time && endDate.isBefore(dayjs(filterState.start_time))) {
        endDate = dayjs(filterState.start_time);
      }
      constructedFilters.push({
        field: "start_time",
        operator: FilterOperator.LTE,
        value: endDate.endOf("day").format("YYYY-MM-DD[T]23:59:59+07:00"),
      });
    }

    if (filterState.doctor_id) {
      constructedFilters.push({
        field: "doctor_id",
        operator: FilterOperator.EQ,
        value: String(filterState.doctor_id),
      });
    }

    if (!filterState.person && filterState.person_id) {
      constructedFilters.push({
        field: "person_id",
        operator: FilterOperator.EQ,
        value: String(filterState.person_id),
      });
    }

    if (filterState.extra_notes) {
      constructedFilters.push({
        field: "extra_notes",
        operator: FilterOperator.LIKE,
        value: filterState.extra_notes,
      });
    }
    return constructedFilters;
  };

  const fetchFilteredAppointments = async (
    options: {
      page?: number;
      pageSize?: number;
      sort?: { field: string; order: "ASC" | "DESC" }[];
    } = {},
    getCount: boolean = true,
  ) => {
    const {
      page = 1,
      pageSize = underlyingDynamicQueryFromUseQuery.limit || defaultQueryConfig.limit || 10,
    } = options;
    const offset = (page - 1) * pageSize;

    const dynamicFilters = buildDynamicFilters();
    const allFilters = [...baseStaticFilters, ...dynamicFilters];

    const queryToFetch: AppointmentDynamicQuery = {
      table: defaultQueryConfig.table!,
      selects: defaultQueryConfig.selects,
      arrived: filterState.arrived,
      has_doctor: filterState.has_doctor,
      note: filterState.note,
      person: filterState.person,
      filters: allFilters,
      limit: pageSize,
      offset,
      sort: options.sort || underlyingDynamicQueryFromUseQuery.sort || defaultQueryConfig.sort,
      group_by: defaultQueryConfig.group_by || [],
      aggregations: defaultQueryConfig.aggregations || [],
      joins: defaultQueryConfig.joins,
      doctor: defaultQueryConfig.doctor,
      task: defaultQueryConfig.task,
      source_id: defaultQueryConfig.source_id,
      export: defaultQueryConfig.export,
    };

    Object.assign(underlyingDynamicQueryFromUseQuery, queryToFetch);

    return underlyingFetchQuery(queryToFetch, getCount);
  };

  const resetFiltersAndRefetch = (newFilterOverrides?: Partial<AppointmentFilterState>) => {
    const snapshotCopy = { ...initialFilterStateSnapshot };
    if (
      snapshotCopy.person_id &&
      newFilterOverrides &&
      newFilterOverrides.person_id === undefined
    ) {
      newFilterOverrides.person_id = snapshotCopy.person_id;
    }

    Object.keys(filterState).forEach((key) => delete (filterState as any)[key]);
    Object.assign(filterState, snapshotCopy);

    if (newFilterOverrides) {
      Object.assign(filterState, newFilterOverrides);
    }
    return fetchFilteredAppointments({ page: 1 });
  };

  const appointments = computed(() => items.value as AppointmentResponse[]);

  return {
    appointments,
    total,
    isLoading,
    error,
    filterState, // Expose for direct v-model or manipulation in components
    fetchFilteredAppointments,
    resetFiltersAndRefetch,
    underlyingDynamicQuery: underlyingDynamicQueryFromUseQuery, // Expose if needed for advanced pagination/sort controls
    searchQuery,
    hasActiveFilters,
    debouncedSearch,
    clearSearch,
    activeFiltersCount,
  };
}
