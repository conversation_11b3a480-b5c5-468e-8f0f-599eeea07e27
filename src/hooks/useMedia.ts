// composables/useMedia.ts
import { storeToRefs } from "pinia";
import { computed } from "vue";

import {
  FileUsageAddRequest,
  FileUsageDeleteRequest,
  FileUsageListRequest,
} from "@/api/bcare-types-v2";
import { useMediaStore } from "@/stores/media-store";

export const useMedia = () => {
  const store = useMediaStore();

  // Destructure store properties using storeToRefs to maintain reactivity
  const { isLoading, error, media, getGroupedMedia, getDateOptions } = storeToRefs(store);

  // Actions
  const fetchMedia = async (req: FileUsageListRequest) => {
    return await store.fetchMedia(req);
  };

  const addFile = async (file: File) => {
    return await store.addFile(file);
  };

  const addFileUsage = async (req: {
    entity_type: string;
    usage_type: string;
    track_id: number;
    file_id: number;
    usage_meta: { [key: string]: any };
    entity_id: number;
  }) => {
    return await store.addFileUsage(req);
  };

  const deleteFile = async (req: FileUsageDeleteRequest) => {
    return await store.deleteFile(req);
  };

  const deleteFiles = async (ids: number[]) => {
    return await store.deleteFiles(ids);
  };

  // Additional computed properties or methods if needed
  const hasMedia = computed(() => media.value.length > 0);

  const getMediaByDate = (date: string) => {
    return getGroupedMedia.value[date] || [];
  };

  return {
    // State
    isLoading,
    error,
    media,

    // Getters
    getGroupedMedia,
    getDateOptions,
    hasMedia,

    // Actions
    fetchMedia,
    addFile,
    addFileUsage,
    deleteFile,
    deleteFiles,

    // Helper methods
    getMediaByDate,
  };
};
