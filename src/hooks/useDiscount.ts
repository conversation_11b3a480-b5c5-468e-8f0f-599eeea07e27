import { computed,shallowRef } from "vue";

import type {
  CalculateDiscountRequest,
  CalculateDiscountResponse,
  Discount,
  DiscountAddRequest,
  DiscountDeleteRequest,
  DiscountGetRequest,
  DiscountListRequest,
  DiscountListResponse,
  DiscountUpdateRequest,
  DiscountUsageAddRequest,
  DiscountUsageDeleteRequest,
  DiscountUsageListRequest,
  DiscountUsageListResponse,
  DiscountUsageResponse,
  EligibleDiscountRequest,
  EligibleDiscountResponse,
} from "@/api/bcare-types-v2";
import {
  discount_usageAdd,
  discount_usageDelete,
  discount_usageList,
  discountAdd,
  discountCalculateDiscount,
  discountDelete,
  discountEligibleDiscount,
  discountGet,
  discountList,
  discountUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UseDiscountOptions {
  initialState?: {
    discounts?: Discount[];
    currentDiscount?: Discount | null;
  };
}

export default function useDiscount(options: UseDiscountOptions = {}) {
  const { initialState = {} } = options;

  // Local state management
  const discounts = shallowRef<Discount[]>(initialState.discounts || []);
  const currentDiscount = shallowRef<Discount | null>(initialState.currentDiscount || null);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const discountCount = computed(() => discounts.value.length);
  const getDiscountById = computed(() => (id: number) => discounts.value.find(discount => discount.id === id));

  const addDiscount = (req: DiscountAddRequest) =>
    performAsyncAction(async () => {
      const response = await discountAdd(req);
      if (response.data) {
        discounts.value.push(response.data);
      }
      return response.data;
    });

  const calculateDiscount = (req: CalculateDiscountRequest) =>
    performAsyncAction(async () => {
      const response = await discountCalculateDiscount(req);
      return response.data;
    });

  const deleteDiscount = (req: DiscountDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await discountDelete(req);
      if (response.data) {
        discounts.value = discounts.value.filter(discount => discount.id !== req.id);
      }
      return response.data;
    });

  const getEligibleDiscount = (req: EligibleDiscountRequest) =>
    performAsyncAction(async () => {
      const response = await discountEligibleDiscount(req);
      return response.data;
    });

  const getDiscount = (req: DiscountGetRequest) =>
    performAsyncAction(async () => {
      const response = await discountGet(req);
      currentDiscount.value = response.data ?? null;
      return response.data;
    });

  const listDiscounts = (req: DiscountListRequest) =>
    performAsyncAction(async () => {
      const response = await discountList(req);
      discounts.value = response.data?.discounts ?? [];
      return response.data;
    });

  const updateDiscount = (req: DiscountUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await discountUpdate(req);
      if (response.data) {
        const index = discounts.value.findIndex(discount => discount.id === req.id);
        if (index !== -1) {
          discounts.value[index] = response.data;
        }
      }
      return response.data;
    });

  const addDiscountUsage = (req: DiscountUsageAddRequest) =>
    performAsyncAction(async () => {
      const response = await discount_usageAdd(req);
      return response.data;
    });

  const deleteDiscountUsage = (req: DiscountUsageDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await discount_usageDelete(req);
      return response.data;
    });

  const listDiscountUsages = (req: DiscountUsageListRequest) =>
    performAsyncAction(async () => {
      const response = await discount_usageList(req);
      return response.data;
    });

  return {
    discounts,
    currentDiscount,
    isLoading,
    error,
    discountCount,
    getDiscountById,
    addDiscount,
    calculateDiscount,
    deleteDiscount,
    getEligibleDiscount,
    getDiscount,
    listDiscounts,
    updateDiscount,
    addDiscountUsage,
    deleteDiscountUsage,
    listDiscountUsages,
  };
}
