// @/hooks/useConstant.ts
import { storeToRefs } from "pinia";
import { computed,onMounted, onUnmounted } from "vue";

import { ConstantResponse } from "@/api/bcare-types-v2";
import { useConstantStore } from "@/stores/constant-store";

export default function useConstant(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const constantStore = useConstantStore();
  const { constants, isLoading, error } = storeToRefs(constantStore);

  const fetchConstants = async () => {
    try {
      await constantStore.fetchConstants();
    } catch (err) {
      console.error("Error fetching constants:", err);
      // You might want to handle this error, e.g., show a notification to the user
    }
  };

  const getConstants = computed((): ConstantResponse | null => {
    return constantStore.getConstants;
  });

  const loadConstants = async () => {
    if (constants.value === null || !constantStore.isCacheValid) {
      const cacheInitialized = constantStore.initializeFromCache();
      if (!cacheInitialized) {
        await fetchConstants();
      }
    }
  };

  const forceRefresh = async () => {
    await fetchConstants();
  };

  // Mounted hook
  onMounted(() => {
    if (options.autoLoad) {
      loadConstants();
    }
  });

  // Unmounted hook
  onUnmounted(() => {
    // Add cleanup logic if needed
  });

  return {
    constants,
    isLoading,
    error,
    fetchConstants,
    getConstants,
    loadConstants,
    forceRefresh,
  };
}
