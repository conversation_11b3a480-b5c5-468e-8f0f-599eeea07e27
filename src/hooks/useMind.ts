import { computed,ref } from "vue";

import type {
  CompleteRequest,
  CompleteResponse,
  ContentCommandRequest,
  ContentCommandResponse,
  ContentModificationRequest,
  ContentModificationResponse,
  GetCronRequest,
  GetCronResponse,
} from "@/api/bcare-types-v2";
import {
  mindComplete,
  mindContentCommand,
  mindContentContinue,
  mindContentImprove,
  mindContentLonger,
  mindContentShorter,
  mindGetCron,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UseMindOptions {
  initialState?: {
    lastResponse?: CompleteResponse | null;
  };
}
interface UseMindOptions {
  initialState?: {
    lastResponse?: CompleteResponse | null;
  };
}

export default function useMind(options: UseMindOptions = {}) {
  const { initialState = {} } = options;

  // Local state management
  const lastResponse = ref<CompleteResponse | null>(initialState.lastResponse || null);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const hasResponse = computed(() => lastResponse.value !== null);

  const complete = (req: CompleteRequest) =>
    performAsyncAction(async () => {
      const response = await mindComplete(req);
      lastResponse.value = response.data;
      return response.data;
    });

  const contentCommand = (req: ContentCommandRequest) =>
    performAsyncAction(async () => {
      const response = await mindContentCommand(req);
      return response.data;
    });

  const contentContinue = (req: ContentModificationRequest) =>
    performAsyncAction(async () => {
      const response = await mindContentContinue(req);
      return response.data;
    });

  const contentImprove = (req: ContentModificationRequest) =>
    performAsyncAction(async () => {
      const response = await mindContentImprove(req);
      return response.data;
    });

  const contentLonger = (req: ContentModificationRequest) =>
    performAsyncAction(async () => {
      const response = await mindContentLonger(req);
      return response.data;
    });

  const contentShorter = (req: ContentModificationRequest) =>
    performAsyncAction(async () => {
      const response = await mindContentShorter(req);
      return response.data;
    });

  const getCron = (req: GetCronRequest) =>
    performAsyncAction(async () => {
      const response = await mindGetCron(req);
      return response.data;
    });

  return {
    lastResponse,
    isLoading,
    error,
    hasResponse,
    complete,
    contentCommand,
    contentContinue,
    contentImprove,
    contentLonger,
    contentShorter,
    getCron,
  };
}
