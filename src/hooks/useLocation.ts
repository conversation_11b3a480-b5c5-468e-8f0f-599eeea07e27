// src/composables/useLocation.ts
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted } from "vue";

import { LocalDistrict, LocalWard } from "@/api/bcare-types-v2";
import { useLocationStore } from "@/stores/location-store-v2";

// Thêm interface
interface AddressInfo {
  province_id?: number;
  district_id?: number;
  ward_id?: number;
  address_number?: string;
}

export default function useLocation(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const locationStore = useLocationStore();
  const { locationData, isLoading, error } = storeToRefs(locationStore);

  const fetchLocation = async () => {
    await locationStore.fetchLocation();
  };

  const getProvinces = computed(() => locationStore.getProvinces);

  const getDistrictAll = computed(() => locationStore.getDistrictAll);

  const getWardAll = computed(() => locationStore.getWardAll);

  const getDistricts = computed(() => {
    return (provinceId: number) => locationStore.getDistricts(provinceId);
  });

  const getWards = computed(() => {
    return (districtId: number) => locationStore.getWards(districtId);
  });

  // Hàm mới để lấy tỉnh/thành phố theo ID
  const getProvinceById = computed(() => (provinceId: number) => {
    return locationStore.getProvinces.find((province) => province.id === provinceId);
  });

  // Hàm mới để lấy quận/huyện theo ID
  const getDistrictById = computed(() => (districtId: number) => {
    return locationStore.getDistrictAll.find(
      (district: LocalDistrict) => district.id === districtId,
    );
  });

  // Hàm mới để lấy phường/xã theo ID
  const getWardById = computed(() => (wardId: number) => {
    return locationStore.getWardAll.find((ward: LocalWard) => ward.id === wardId);
  });

  const loadLocation = async () => {
    if (!locationData || locationData.value.provinces.length === 0) {
      locationStore.initializeFromCache();
      if (locationData.value.provinces.length === 0) {
        await fetchLocation();
      }
    }
  };

  // Mounted hook
  onMounted(() => {
    if (options.autoLoad) {
      loadLocation();
    }
  });

  // Unmounted hook
  onUnmounted(() => {
    // Có thể thêm logic cleanup nếu cần
  });

  // Thêm function getFullAddress vào trong useLocation
  const getFullAddress = computed(() => {
    return (addressInfo?: AddressInfo): string => {
      if (!addressInfo) return "-";

      const addressParts: string[] = [];

      // Add address number
      if (addressInfo.address_number?.trim()) {
        addressParts.push(addressInfo.address_number.trim());
      }

      // Add ward name
      if (addressInfo.ward_id) {
        const ward = getWardById.value(addressInfo.ward_id)?.name;
        if (ward) addressParts.push(ward);
      }

      // Add district name
      if (addressInfo.district_id) {
        const district = getDistrictById.value(addressInfo.district_id)?.name;
        if (district) addressParts.push(district);
      }

      // Add province name
      if (addressInfo.province_id) {
        const province = getProvinceById.value(addressInfo.province_id)?.name;
        if (province) addressParts.push(province);
      }

      return addressParts.length > 0 ? addressParts.join(", ") : "-";
    };
  });

  return {
    locationData,
    isLoading,
    error,
    fetchLocation,
    getProvinces,
    getDistrictAll,
    getWardAll,
    getDistricts,
    getWards,
    loadLocation,
    getProvinceById,
    getDistrictById,
    getWardById,
    getFullAddress,
  };
}
