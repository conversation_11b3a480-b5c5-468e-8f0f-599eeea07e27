import { computed, ref } from "vue";

import {
  Aggregation,
  DynamicQuery,
  PaymentReportRecord,
  PaymentReportSummary,
} from "@/api/bcare-types-v2";
import { economy_paymentQuery, economy_paymentQueryDetail } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v3";

interface RevenueResponse extends PaymentReportRecord {
  id: number;
  created_at: string;
  full_name: string;
  phone: string;
  product_names: string;
  group_names: string;
  total_amount: number;
  cash: number;
  bank: number;
  mpos: number;
  credit_card: number;
  momo: number;
}

const revenueAggregations = [
  { field: "id", function: "COUNT", alias: "total_records", over: {} },
  { field: "total_amount", function: "SUM", alias: "total_revenue", over: {} },
  { field: "expense", function: "SUM", alias: "total_expense", over: {} },
  { field: "income", function: "SUM", alias: "total_income", over: {} },
  { field: "cash", function: "SUM", alias: "total_cash", over: {} },
  { field: "bank", function: "SUM", alias: "total_bank", over: {} },
  { field: "mpos", function: "SUM", alias: "total_mpos", over: {} },
  { field: "credit_card", function: "SUM", alias: "total_credit_card", over: {} },
  { field: "momo", function: "SUM", alias: "total_momo", over: {} },
  { field: "product_amount", function: "SUM", alias: "total_product_amount", over: {} },
  {
    field: "general_service_amount",
    function: "SUM",
    alias: "total_general_service_amount",
    over: {},
  },
  { field: "orthodontic_amount", function: "SUM", alias: "total_orthodontic_amount", over: {} },
  { field: "implant_amount", function: "SUM", alias: "total_implant_amount", over: {} },
  { field: "gxtn_amount", function: "SUM", alias: "total_gxtn_amount", over: {} },
  { field: "veneer_amount", function: "SUM", alias: "total_veneer_amount", over: {} },
  { field: "other_amount", function: "SUM", alias: "total_other_amount", over: {} },
];

export const initialRevenueDynamicQuery: DynamicQuery = {
  table: "payment_report_view",
  selects: [],
  sort: [
    {
      field: "created_at",
      order: "ASC",
    },
  ],
  limit: 10,
  offset: 0,
};

export function useRevenueQuery() {
  // Query chính cho danh sách
  const { items, total, dynamicQuery, fetchQuery, isLoading, error } = useQuery<
    DynamicQuery,
    RevenueResponse
  >({
    queryFn: economy_paymentQuery,
    initialQuery: initialRevenueDynamicQuery,
  });

  // Query riêng cho aggregations
  const { fetchQuery: fetchAggregationQuery } = useQuery<DynamicQuery, PaymentReportSummary>({
    queryFn: economy_paymentQuery,
    initialQuery: initialRevenueDynamicQuery,
  });

  // Query riêng cho export với function phù hợp
  const { fetchQuery: fetchExportQuery } = useQuery<DynamicQuery, RevenueResponse>({
    queryFn: (payload) => economy_paymentQuery(payload),
    initialQuery: initialRevenueDynamicQuery,
  });

  const revenues = computed(() => items.value as RevenueResponse[]);
  const aggregations = ref<PaymentReportSummary | null>(null);

  const fetchRevenues = (
    req: Partial<DynamicQuery>,
    getCount: boolean = true,
    isExport: boolean = false,
    isDetailed: boolean = false,
  ) => {
    if (isExport) {
      const exportQuery = {
        ...initialRevenueDynamicQuery,
        ...req,
        export: true,
        limit: undefined,
        offset: undefined,
        table: isDetailed ? "payment_report_detail_view" : "payment_report_view",
      };
      return isDetailed
        ? economy_paymentQueryDetail(exportQuery)
        : economy_paymentQuery(exportQuery);
    }
    return fetchQuery(req, getCount);
  };

  const fetchAggregations = async (req: Partial<DynamicQuery> = {}) => {
    const response = await fetchAggregationQuery(
      {
        ...req,
        limit: 1,
        offset: 0,
        aggregations: revenueAggregations as Aggregation[],
      },
      false,
    );

    if (response?.code === 0 && response.data) {
      aggregations.value = response.data.result as PaymentReportSummary;
    }

    return response;
  };

  return {
    revenues,
    total,
    dynamicQuery,
    fetchRevenues,
    isLoading,
    error,
    aggregations,
    fetchAggregations,
  };
}
