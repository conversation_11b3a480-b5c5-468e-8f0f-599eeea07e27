import { computed, shallowRef } from "vue";

import type {
  ActiveTrackRequest,
  Track,
  TrackAddRequest,
  TrackCheckoutRequest,
  TrackDeleteRequest,
  TrackListRequest,
  TrackResponse,
  TrackUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  trackActiveTrack,
  trackAdd,
  trackCheckout,
  trackDelete,
  trackList,
  trackUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UseTrackOptions {
  initialState?: {
    tracks?: TrackResponse[];
    currentTrack?: TrackResponse | null;
  };
}

export default function useTrack(options: UseTrackOptions = {}) {
  const { initialState = {} } = options;

  // State management using shallowRef
  const tracks = shallowRef<TrackResponse[]>(initialState.tracks || []);
  const currentTrack = shallowRef<TrackResponse | null>(initialState.currentTrack || null);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Computed properties
  const trackCount = computed(() => tracks.value.length);
  const getTrackById = computed(
    () => (id: number) => tracks.value.find((track) => track.id === id),
  );

  // Track related actions
  const addTrack = (req: TrackAddRequest) =>
    performAsyncAction(async () => {
      const response = await trackAdd(req);
      if (response.data) {
        tracks.value.push(response.data);
      }
      return response.data;
    });

  const checkoutTrack = (req: TrackCheckoutRequest) =>
    performAsyncAction(async () => {
      const response = await trackCheckout(req);
      return response.data;
    });

  const deleteTrack = (req: TrackDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await trackDelete(req);
      if (response.data) {
        tracks.value = tracks.value.filter((track) => track.id !== req.id);
      }
      return response.data;
    });

  const listTracks = (req: TrackListRequest) =>
    performAsyncAction(async () => {
      const response = await trackList(req);
      tracks.value = response.data?.tracks ?? [];
      return response.data;
    });

  const updateTrack = (req: TrackUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await trackUpdate(req);
      if (response.data) {
        const index = tracks.value.findIndex((track) => track.id === req.id);
        if (index !== -1) {
          tracks.value[index] = response.data;
        }
      }
      return response.data;
    });

  // Thêm action để lấy active track
  const getActiveTrack = (req: ActiveTrackRequest) =>
    performAsyncAction<Track | null>(async () => {
      const response = await trackActiveTrack(req);
      return response.data;
    });

  return {
    // State
    tracks,
    currentTrack,
    isLoading,
    error,

    // Computed
    trackCount,
    getTrackById,

    // Track actions
    addTrack,
    checkoutTrack,
    deleteTrack,
    listTracks,
    updateTrack,
    getActiveTrack,
  };
}
