import dayjs from "dayjs";
import { computed, ComputedRef, reactive, Ref, ref, watch } from "vue";

import { Parameter, RawQueryRequest, RawQueryResponse } from "@/api/bcare-types-v2";
import { queryRawQuery } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

// <PERSON><PERSON>nh nghĩa các kiểu dữ liệu
type ParamType =
  | "string"
  | "int"
  | "float"
  | "bool"
  | "date"
  | "time"
  | "datetime"
  | "timestamp"
  | "json"
  | "array"
  | "uuid"
  | "bytea"
  | "null";
type arrayElementType = "string" | "int" | "float" | "bool";

export interface ParamConfig {
  type: ParamType;
  placeholder?: string | number; // $1, $2... or position number
  template?: boolean; // For {{template_vars}}
  default?: any;
  isLimit?: boolean; // Mark this param as the LIMIT
  isOffset?: boolean; // Mark this param as the OFFSET
  arrayElementType?: arrayElementType; // Specify element type for 'array'
  // New: Identify the param used for ORDER BY clause template
  isOrderClause?: boolean;
}

export interface SortOrder {
  field: string;
  direction: "ASC" | "DESC";
}

export interface UseRawQueryV2Options {
  pageSize?: number;
  timeout?: number;
  onSuccess?: (data: RawQueryResponse) => void;
  onError?: (error: any) => void;
  countQuery?: string; // Optional count query string
  defaultSort?: SortOrder[]; // Default sorting
  maxSortOrders?: number; // Max number of sort fields
  initialParams?: Record<string, any>; // Initial parameter values
}

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  pageCount: ComputedRef<number>; // Make it computed
  goToPage: (page: number) => Promise<RawQueryResponse | undefined>;
  setPageSize: (size: number) => Promise<RawQueryResponse | undefined>;
}

interface SortingState {
  orders: SortOrder[];
  getOrderClause: () => string;
  setSort: (field: string, direction?: "ASC" | "DESC") => void;
  addOrUpdateSort: (field: string) => void;
  removeSort: (field: string) => boolean;
  toggleDirection: (field: string) => boolean;
  setOrders: (newOrders: SortOrder[]) => void;
  isSorted: (field: string) => boolean;
  getDirection: (field: string) => "ASC" | "DESC" | null;
  getSortPosition: (field: string) => number;
}

interface UseRawQueryV2Return {
  // Data & State
  data: Ref<any[]>;
  loading: Ref<boolean>;
  error: Ref<any>;
  rowCount: Ref<number>;
  executionTime: Ref<number>;

  // Parameters
  params: Record<string, any>;
  setParams: (newParams: Record<string, any>) => void;
  resetParams: (executeAfterReset?: boolean) => Promise<RawQueryResponse | undefined>;

  // Execution
  execute: () => Promise<RawQueryResponse | undefined>;
  refresh: () => Promise<RawQueryResponse | undefined>;

  // Pagination
  pagination: PaginationState;

  // Sorting
  sorting: SortingState;

  // Count specific state and execution
  countLoading: Ref<boolean>;
  executeCount: () => Promise<number>;
}

/**
 * Composable để làm việc với Raw Query API (v2 - tích hợp Sắp xếp)
 *
 * @param sqlQuery - SQL query chính với placeholders ($n) và template vars ({{var}})
 * @param paramConfig - Cấu hình cho mỗi tham số (bao gồm cả orderClause)
 * @param options - Các tùy chọn bổ sung (pagination, sorting, callbacks...)
 * @returns - Các state và hàm để thao tác với query
 */
export function useRawQueryV2(
  sqlQuery: string,
  paramConfig: Record<string, ParamConfig>,
  options: UseRawQueryV2Options = {},
): UseRawQueryV2Return {
  // --- States ---
  const data = ref<any[]>([]) as Ref<any[]>;
  const loading = ref<boolean>(false);
  const countLoading = ref<boolean>(false);
  const error = ref<any>(null);
  const rowCount = ref<number>(0); // Holds total count if countQuery is used, otherwise row count of last execution
  const executionTime = ref<number>(0);

  const { isLoading: asyncLoading, error: asyncError, performAsyncAction } = useAsyncAction();
  watch(asyncLoading, (newVal) => {
    // Sync loading state if needed elsewhere, though internal loading ref might be sufficient
  });

  // --- Parameters ---
  const params = reactive<Record<string, any>>({});

  // --- Sorting ---
  const maxSortOrders = options.maxSortOrders || 1; // Default to single column sort
  const defaultSortOrders = options.defaultSort || [];
  const sortOrders = reactive<SortOrder[]>(
    [...defaultSortOrders], // Clone default orders
  );

  const orderClauseParamKey = Object.entries(paramConfig).find(
    ([_, config]) => config.isOrderClause,
  )?.[0];

  if (!orderClauseParamKey) {
    console.warn(
      "useRawQueryV2: No parameter marked with isOrderClause=true. Sorting will not be applied automatically to SQL template.",
    );
  }

  const sorting: SortingState = reactive({
    orders: sortOrders,
    getOrderClause: () => {
      if (sortOrders.length === 0) return "1"; // Default stable sort if nothing specified
      return sortOrders.map((order) => `${order.field} ${order.direction}`).join(", ");
    },
    setSort: (field: string, direction: "ASC" | "DESC" = "DESC") => {
      sortOrders.splice(0, sortOrders.length, { field, direction }); // Replace all with new single sort
      if (orderClauseParamKey) {
        params[orderClauseParamKey] = sorting.getOrderClause();
      }
    },
    addOrUpdateSort: (field: string) => {
      const existingIndex = sortOrders.findIndex((order) => order.field === field);

      if (existingIndex >= 0) {
        sortOrders[existingIndex].direction =
          sortOrders[existingIndex].direction === "ASC" ? "DESC" : "ASC";
        if (existingIndex > 0) {
          // Move to front if not already
          const order = sortOrders.splice(existingIndex, 1)[0];
          sortOrders.unshift(order);
        }
      } else {
        sortOrders.unshift({ field, direction: "DESC" }); // Add to the beginning
        if (sortOrders.length > maxSortOrders) {
          sortOrders.pop(); // Remove the last one if exceeds max
        }
      }
      if (orderClauseParamKey) {
        params[orderClauseParamKey] = sorting.getOrderClause();
      }
    },
    removeSort: (field: string) => {
      const index = sortOrders.findIndex((order) => order.field === field);
      if (index >= 0) {
        sortOrders.splice(index, 1);
        // Restore default sort if list becomes empty and defaults exist
        if (sortOrders.length === 0 && defaultSortOrders.length > 0) {
          sortOrders.push(...defaultSortOrders);
        }
        if (orderClauseParamKey) {
          params[orderClauseParamKey] = sorting.getOrderClause();
        }
        return true;
      }
      return false;
    },
    toggleDirection: (field: string) => {
      const index = sortOrders.findIndex((order) => order.field === field);
      if (index >= 0) {
        sortOrders[index].direction = sortOrders[index].direction === "ASC" ? "DESC" : "ASC";
        if (orderClauseParamKey) {
          params[orderClauseParamKey] = sorting.getOrderClause();
        }
        return true;
      }
      return false;
    },
    setOrders: (newOrders: SortOrder[]) => {
      const limitedOrders = newOrders.slice(0, maxSortOrders);
      sortOrders.splice(0, sortOrders.length, ...limitedOrders);
      if (orderClauseParamKey) {
        params[orderClauseParamKey] = sorting.getOrderClause();
      }
    },
    isSorted: (field: string) => {
      return sortOrders.some((order) => order.field === field);
    },
    getDirection: (field: string) => {
      const order = sortOrders.find((order) => order.field === field);
      return order ? order.direction : null;
    },
    getSortPosition: (field: string) => {
      return sortOrders.findIndex((order) => order.field === field) + 1;
    },
  });

  // --- Pagination ---
  // Define the base reactive object first
  const paginationBase = reactive({
    page: 1,
    pageSize: options.pageSize || 50,
    total: rowCount.value, // Initialize with the current value of rowCount
  });

  // Use watch to keep pagination.total synchronized with rowCount
  watch(rowCount, (newCount) => {
    paginationBase.total = newCount;
  });

  // Define the computed property separately
  const pageCountComputed = computed(() =>
    Math.ceil(paginationBase.total / paginationBase.pageSize),
  );

  // Now, define the final pagination object conforming to PaginationState
  // This object itself doesn't need to be reactive(), as its parts are.
  const pagination: PaginationState = {
    // Use getters to access reactive properties from paginationBase
    get page() {
      return paginationBase.page;
    },
    get pageSize() {
      return paginationBase.pageSize;
    },
    get total() {
      return paginationBase.total;
    },
    // Assign the computed ref directly
    pageCount: pageCountComputed,
    // Assign the methods (they will operate on paginationBase)
    goToPage,
    setPageSize,
  };

  // Initialize Limit and Offset params based on pagination defaults
  const limitParamEntry = Object.entries(paramConfig).find(([_, config]) => config.isLimit);
  const offsetParamEntry = Object.entries(paramConfig).find(([_, config]) => config.isOffset);

  // Read initial values from paginationBase
  if (limitParamEntry) {
    params[limitParamEntry[0]] = paginationBase.pageSize;
  }
  if (offsetParamEntry) {
    params[offsetParamEntry[0]] = (paginationBase.page - 1) * paginationBase.pageSize;
  }

  // --- SQL Processing ---
  const processedSql: ComputedRef<string> = computed(() => {
    let sql = sqlQuery;
    // Replace template variables (excluding the order clause param if managed internally)
    Object.entries(paramConfig).forEach(([key, config]) => {
      if (config.template && params[key] !== undefined && key !== orderClauseParamKey) {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g");
        sql = sql.replace(regex, String(params[key]));
      }
    });
    // Inject the dynamically generated order clause if the param exists
    if (orderClauseParamKey && params[orderClauseParamKey] !== undefined) {
      const regex = new RegExp(`\\{\\{\\s*${orderClauseParamKey}\\s*\\}\\}`, "g");
      sql = sql.replace(regex, String(params[orderClauseParamKey]));
    }
    return sql;
  });

  // --- API Parameters Computation ---
  const apiParameters: ComputedRef<Parameter[]> = computed(() => {
    const parameters: Parameter[] = [];
    const parameterMap: Record<number, Parameter> = {};

    Object.entries(paramConfig).forEach(([key, config]) => {
      // Skip template params and order clause param (handled in processedSql)
      if (config.template || config.isOrderClause) return;

      // Use default value if param is undefined or null and default exists
      const value =
        (params[key] === undefined || params[key] === null) && config.default !== undefined
          ? config.default
          : params[key];

      if (value !== undefined) {
        const position =
          typeof config.placeholder === "string"
            ? parseInt(config.placeholder.replace("$", ""), 10)
            : (config.placeholder as number);

        if (position > 0) {
          const paramType = value === null ? "null" : config.type;
          const formattedValue = value === null ? "" : formatValue(value, config.type);

          parameterMap[position] = {
            type: paramType,
            value: formattedValue,
          };

          if (paramType === "array" && config.arrayElementType) {
            parameterMap[position].arrayElementType = config.arrayElementType;
          }
        }
      }
    });

    const positions = Object.keys(parameterMap)
      .map(Number)
      .sort((a, b) => a - b);
    if (positions.length > 0) {
      const maxPosition = positions[positions.length - 1];
      for (let i = 1; i <= maxPosition; i++) {
        parameters.push(parameterMap[i] || { type: "null", value: "" }); // Fill gaps with null
      }
    }

    return parameters;
  });

  // Format value based on type
  function formatValue(value: any, type: ParamType): string {
    if (value === null || value === undefined) {
      return ""; // Should be handled by "null" type upstream, but safeguard
    }

    switch (type) {
      case "string":
      case "uuid":
      case "bytea":
      case "json": // Assuming backend expects stringified JSON
        return String(value);
      case "int":
      case "float":
      case "bool":
        return String(value); // Let backend handle conversion
      case "date":
        return dayjs(value).isValid() ? dayjs(value).format("YYYY-MM-DD") : String(value);
      case "time":
        return dayjs(value).isValid() ? dayjs(value).format("HH:mm:ss") : String(value);
      case "datetime":
      case "timestamp":
        // Format for PostgreSQL timestamp without timezone
        return dayjs(value).isValid() ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : String(value);
      case "array":
        // Ensure it's always a JSON array string
        return Array.isArray(value) ? JSON.stringify(value) : JSON.stringify([value]);
      default:
        return String(value);
    }
  }

  // --- Count Query Execution ---
  async function executeCount(): Promise<number> {
    if (!options.countQuery) {
      console.warn("executeCount called but no countQuery provided in options.");
      return 0;
    }

    countLoading.value = true;

    try {
      // Prepare parameters for count query: exclude limit, offset, and order clause params
      const countParamConfig = { ...paramConfig };
      const paramsToExclude = Object.entries(countParamConfig)
        .filter(([_, config]) => config.isLimit || config.isOffset || config.isOrderClause)
        .map(([key]) => key);

      const countParams = { ...params };
      paramsToExclude.forEach((key) => delete countParams[key]);

      // Recalculate API parameters for the count query context
      const countApiParameters: Parameter[] = [];
      const countParameterMap: Record<number, Parameter> = {};

      Object.entries(countParamConfig).forEach(([key, config]) => {
        if (config.template || config.isOrderClause || config.isLimit || config.isOffset) return; // Skip these for count

        const value =
          (countParams[key] === undefined || countParams[key] === null) &&
          config.default !== undefined
            ? config.default
            : countParams[key];

        if (value !== undefined) {
          const position =
            typeof config.placeholder === "string"
              ? parseInt(config.placeholder.replace("$", ""), 10)
              : (config.placeholder as number);

          if (position > 0) {
            const paramType = value === null ? "null" : config.type;
            const formattedValue = value === null ? "" : formatValue(value, config.type);

            countParameterMap[position] = {
              type: paramType,
              value: formattedValue,
            };
            if (paramType === "array" && config.arrayElementType) {
              countParameterMap[position].arrayElementType = config.arrayElementType;
            }
          }
        }
      });

      const countPositions = Object.keys(countParameterMap)
        .map(Number)
        .sort((a, b) => a - b);
      if (countPositions.length > 0) {
        const maxPos = countPositions[countPositions.length - 1];
        for (let i = 1; i <= maxPos; i++) {
          countApiParameters.push(countParameterMap[i] || { type: "null", value: "" });
        }
      }

      // Process count SQL for templates (excluding order clause)
      let countSqlProcessed = options.countQuery;
      Object.entries(countParamConfig).forEach(([key, config]) => {
        if (config.template && countParams[key] !== undefined && !config.isOrderClause) {
          const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g");
          countSqlProcessed = countSqlProcessed.replace(regex, String(countParams[key]));
        }
      });

      const request: RawQueryRequest = {
        sql: countSqlProcessed,
        parameters: countApiParameters,
        timeout: options.timeout || 30,
      };

      const response = await queryRawQuery(request);

      if (response.data?.results && response.data.results.length > 0) {
        // Assuming the count query returns a column named 'total_count'
        const count = parseInt(
          response.data.results[0].total_count || response.data.results[0].count,
          10,
        );
        if (!isNaN(count)) {
          rowCount.value = count; // Update total count state
          return count;
        }
      }
      rowCount.value = 0; // Reset if count fails
      return 0;
    } catch (err) {
      console.error("Error executing count query:", err);
      error.value = err; // Set main error state as well
      rowCount.value = 0;
      return 0;
    } finally {
      countLoading.value = false;
    }
  }

  // --- Main Query Execution ---
  async function execute(): Promise<RawQueryResponse | undefined> {
    loading.value = true; // Use internal loading state
    error.value = null; // Reset error before execution

    // Trigger count query execution in parallel if configured
    const countPromise = options.countQuery ? executeCount() : Promise.resolve(undefined);

    return await performAsyncAction(async () => {
      try {
        // Ensure Limit and Offset params use values from paginationBase
        if (limitParamEntry) params[limitParamEntry[0]] = paginationBase.pageSize;
        if (offsetParamEntry)
          params[offsetParamEntry[0]] = (paginationBase.page - 1) * paginationBase.pageSize;
        // Ensure order clause param is up-to-date
        if (orderClauseParamKey) params[orderClauseParamKey] = sorting.getOrderClause();

        const request: RawQueryRequest = {
          sql: processedSql.value,
          parameters: apiParameters.value,
          timeout: options.timeout || 30,
        };

        const response = await queryRawQuery(request);

        // Wait for count query if it was triggered
        await countPromise;

        data.value = response.data?.results || [];
        // If countQuery is NOT used, update rowCount based on results length
        if (!options.countQuery) {
          rowCount.value = response.data?.rowCount ?? data.value.length;
        }
        executionTime.value = response.data?.executionTime || 0;

        if (options.onSuccess && response.data) {
          options.onSuccess(response.data);
        }

        return response.data || undefined; // Return actual data part
      } catch (err) {
        error.value = err; // Capture error in the main state
        if (options.onError) {
          options.onError(err);
        }
        throw err; // Re-throw for performAsyncAction to catch
      } finally {
        loading.value = false; // Reset internal loading state
      }
    });
  }

  // Refresh (alias for execute)
  function refresh(): Promise<RawQueryResponse | undefined> {
    // Ensure pagination is reset to page 1 before refreshing? Optional, depends on desired behavior.
    // pagination.page = 1;
    return execute();
  }

  // --- Parameter Management ---
  function resetParams(executeAfterReset = false): Promise<RawQueryResponse | undefined> {
    Object.keys(params).forEach((key) => {
      // Don't delete, set to default or undefined
      const config = paramConfig[key];
      if (config) {
        params[key] = config.default; // Set to default if defined
      } else {
        delete params[key]; // Or remove if no config (shouldn't happen often)
      }
    });

    // Reset paginationBase
    paginationBase.page = 1;
    // Recalculate limit/offset params based on reset paginationBase
    if (limitParamEntry) params[limitParamEntry[0]] = paginationBase.pageSize; // Use pageSize from base
    if (offsetParamEntry) params[offsetParamEntry[0]] = 0; // Offset is 0 for page 1
    sorting.setOrders([...defaultSortOrders]); // Reset sort orders
    if (orderClauseParamKey) params[orderClauseParamKey] = sorting.getOrderClause(); // Update clause param

    // Apply initial params if provided
    if (options.initialParams) {
      setParams(options.initialParams);
    }

    if (executeAfterReset) {
      return execute();
    } else {
      return Promise.resolve(undefined);
    }
  }

  function setParams(newParams: Record<string, any>): void {
    Object.entries(newParams).forEach(([key, value]) => {
      if (key in paramConfig) {
        // Only set params that are defined in config
        params[key] = value;
      } else {
        console.warn(`useRawQueryV2: Attempted to set unknown parameter "${key}"`);
      }
    });
  }

  // --- Pagination Methods ---
  // Ensure these methods modify paginationBase
  async function goToPage(page: number): Promise<RawQueryResponse | undefined> {
    // Check against the computed value is fine
    if (page < 1 || page > pagination.pageCount.value) {
      console.warn(
        `goToPage: Invalid page number ${page}. Max pages: ${pagination.pageCount.value}`,
      );
      return Promise.resolve(undefined);
    }
    // Update the source reactive object
    paginationBase.page = page;
    // Execute will read the updated page from paginationBase
    return execute();
  }

  async function setPageSize(size: number): Promise<RawQueryResponse | undefined> {
    if (size < 1) size = 1;
    // Update the source reactive object
    paginationBase.pageSize = size;
    paginationBase.page = 1; // Reset to first page
    // Execute will read the updated size and page from paginationBase
    return execute();
  }

  // --- Initial Setup ---
  function initialize() {
    // Set default values for all defined parameters
    Object.entries(paramConfig).forEach(([key, config]) => {
      if (config.default !== undefined) {
        params[key] = config.default;
      }
    });
    // Apply initial parameters from options, overriding defaults
    if (options.initialParams) {
      setParams(options.initialParams);
    }
    // Set initial order clause param
    if (orderClauseParamKey) {
      params[orderClauseParamKey] = sorting.getOrderClause();
    }
    // Set initial limit/offset based on pagination state
    if (limitParamEntry) params[limitParamEntry[0]] = pagination.pageSize;
    if (offsetParamEntry) params[offsetParamEntry[0]] = (pagination.page - 1) * pagination.pageSize;
  }

  initialize(); // Run initialization

  // --- Return Value ---
  return {
    // Data & State
    data,
    loading, // Use internal loading state
    error: asyncError, // Expose error state from useAsyncAction
    rowCount,
    executionTime,

    // Parameters
    params, // Expose for potential direct manipulation or inspection (use with caution)
    setParams,
    resetParams,

    // Execution
    execute,
    refresh,

    // Pagination
    pagination,

    // Sorting
    sorting,

    // Count specific
    countLoading,
    executeCount,
  };
}
