import { storeToRefs } from "pinia";
import { computed } from "vue";

import type { Setting, SettingAddRequest, SettingUpdateRequest } from "@/api/bcare-types-v2";
import {useConfigurationsStore} from "@/stores/configuration-store";

export const useConfigurations = () => {
  const store = useConfigurationsStore();
  const { state } = storeToRefs(store);

  const isLoading = computed(() => state.value.loading);
  const error = computed(() => state.value.error);
  const configurations = computed(() => state.value.configurations);
  const categoryMap = computed(() => state.value.categoryMap);

  const fetchSettings = async (category = "", name = "") => {
    await store.init({ category, name });
  };

  const getSetting = (category: string, name: string): Setting | undefined => {
    return store.getSettingByKey(category, name);
  };

  const addNewSetting = async (payload: SettingAddRequest): Promise<boolean> => {
    return await store.addSetting(payload);
  };

  const updateExistingSetting = async (payload: SettingUpdateRequest): Promise<boolean> => {
    return await store.updateSetting(payload);
  };

  const removeSetting = async (id: number): Promise<boolean> => {
    return await store.deleteSetting({ id });
  };

  const syncSettingValue = async (
    category: string,
    name: string,
    value: Record<string, any>,
    description?: string,
  ): Promise<boolean> => {
    return await store.syncSetting(category, name, value, description);
  };

  const getSettingsByCategory = (category: string): Setting[] => {
    return categoryMap.value[category] || [];
  };

  return {
    // State
    isLoading,
    error,
    configurations,
    categoryMap,

    // Methods
    fetchSettings,
    getSetting,
    addNewSetting,
    updateExistingSetting,
    removeSetting,
    syncSettingValue,
    getSettingsByCategory,
  };
};
