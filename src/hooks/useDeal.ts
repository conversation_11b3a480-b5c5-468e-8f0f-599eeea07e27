import { storeToRefs } from "pinia";
import { computed, ref, shallowRef } from "vue";

import type {
  DealAddRequest,
  DealDeleteRequest,
  DealGetRequest,
  DealListRequest,
  DealResponse,
  DealUpdateRequest,
  DealUserAddRequest,
  DealUserDeleteRequest,
  DealUserRating,
  DealUserRatingAddRequest,
  DealUserRatingUpdateRequest,
  DealUserUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  deal_userAdd,
  deal_userAddRating,
  deal_userDelete,
  deal_userDeleteRating,
  deal_userUpdate,
  deal_userUpdateRating,
  dealAdd,
  dealDelete,
  dealGet,
  dealList,
  dealUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useDealStore } from "@/stores/deal-store";

export interface RatingUpdatePayload {
  deal_user_id: number;
  category: string;
  rating: number;
  existing_rating_id?: number; // ID of the DealUserRating if it exists for update
}

interface UseDealOptions {
  useStore?: boolean;
  autoLoad?: boolean;
  personId?: number;
  initialState?: {
    deals?: DealResponse[];
    currentDeal?: DealResponse | null;
  };
}

export default function useDeal(options: UseDealOptions = {}) {
  const {
    useStore = true,
    autoLoad = false,
    personId: initialPersonId,
    initialState = {},
  } = options;

  if (useStore) {
    const store = useDealStore();
    const { deals, currentDeal, isLoading, error } = storeToRefs(store);

    const localPersonId = ref(initialPersonId);

    const fetchDealsByPerson = async (personId: number) => {
      return await store.fetchDealList({
        filter: { person_id: personId },
        page: 1,
        page_size: 100,
        include_relation: true,
      });
    };

    const loadDeals = async () => {
      if (localPersonId.value) {
        await fetchDealsByPerson(localPersonId.value);
      }
    };

    if (autoLoad) {
      loadDeals();
    }

    return {
      deals,
      currentDeal,
      isLoading,
      error,
      personId: localPersonId,
      fetchDeals: store.fetchDealList,
      fetchDealsByPerson,
      fetchDealsByStage: store.fetchDealsByStage,
      fetchDealDetails: store.fetchDeal,
      createDeal: store.addDeal,
      updateDeal: store.updateDeal,
      removeDeal: store.deleteDeal,
      addUserToDeal: store.addDealUser,
      updateDealUser: store.updateDealUser,
      removeUserFromDeal: store.deleteDealUser,
      getLatestDealForPerson: store.getLatestDeal,
      getDealById: (id: number) => store.getDealById(id),
      dealCount: computed(() => store.getDealCount),
      loadDeals,
      addDealUserRating: store.addDealUserRating,
      updateDealUserRating: store.updateDealUserRating,
    };
  }

  // Local state management
  const deals = shallowRef<DealResponse[]>(initialState.deals || []);
  const currentDeal = shallowRef<DealResponse | null>(initialState.currentDeal || null);
  const localPersonId = ref(initialPersonId);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const dealCount = computed(() => deals.value.length);
  const getDealById = computed(() => (id: number) => deals.value.find((deal) => deal.id === id));

  const fetchDeals = (req: DealListRequest) =>
    performAsyncAction(async () => {
      const response = await dealList(req);
      deals.value = response.data?.deals ?? [];
      return response.data;
    });

  const fetchDealsByPerson = async (personId: number) => {
    return await fetchDeals({
      filter: { person_id: personId },
      page: 1,
      page_size: 100,
      include_relation: true,
    });
  };

  const fetchDealsByStage = (stageId: number) =>
    performAsyncAction(async () => {
      const response = await dealList({ filter: { stage_id: stageId } });
      deals.value = response.data?.deals ?? [];
      return response.data;
    });

  const fetchDealDetails = (req: DealGetRequest) =>
    performAsyncAction(async () => {
      const response = await dealGet(req);
      currentDeal.value = response.data ?? null;
      return response.data;
    });

  const createDeal = (req: DealAddRequest) =>
    performAsyncAction(async () => {
      const response = await dealAdd(req);
      if (response.data) {
        deals.value.push(response.data);
      }
      return response.data;
    });

  const updateDeal = (req: DealUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await dealUpdate(req);
      if (response.data) {
        const index = deals.value.findIndex((deal) => deal.id === req.id);
        if (index !== -1) {
          deals.value[index] = response.data;
        }
      }
      return response.data;
    });

  const removeDeal = (req: DealDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await dealDelete(req);
      if (response.data) {
        deals.value = deals.value.filter((deal) => deal.id !== req.id);
      }
      return response.data;
    });

  const addUserToDeal = (req: DealUserAddRequest) =>
    performAsyncAction(async () => {
      const response = await deal_userAdd(req);
      // Cập nhật state nếu cần
      return response.data;
    });

  const updateDealUser = (req: DealUserUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await deal_userUpdate(req);
      // Cập nhật state nếu cần
      return response.data;
    });

  const removeUserFromDeal = (req: DealUserDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await deal_userDelete(req);
      // Cập nhật state nếu cần
      return response.data;
    });

  const getLatestDealForPerson = async (personId: number) => {
    const response = await fetchDealsByPerson(personId);
    return response?.deals?.[0] ?? null;
  };

  const loadDeals = async () => {
    if (localPersonId.value) {
      await fetchDealsByPerson(localPersonId.value);
    }
  };

  if (autoLoad) {
    loadDeals();
  }

  // Helper to update local state (similar to store helper, adapt if needed)
  const updateDealUserRatingInLocalState = (dealUserId: number, updatedRating: DealUserRating) => {
    const dealIndex = deals.value.findIndex((d) =>
      d.deal_assignment?.some((da) => da.id === dealUserId),
    );
    if (dealIndex === -1) return;

    const dealUserIndex = deals.value[dealIndex].deal_assignment?.findIndex(
      (da) => da.id === dealUserId,
    );
    if (dealUserIndex === -1 || !deals.value[dealIndex].deal_assignment) return;

    const userRatings = deals.value[dealIndex].deal_assignment[dealUserIndex].ratings || [];
    const ratingIndex = userRatings.findIndex((r) => r.id === updatedRating.id);

    if (ratingIndex !== -1) {
      userRatings[ratingIndex] = updatedRating;
    } else {
      userRatings.push(updatedRating);
    }
    deals.value[dealIndex].deal_assignment[dealUserIndex].ratings = userRatings;
    // Trigger reactivity
    deals.value[dealIndex].deal_assignment = [...deals.value[dealIndex].deal_assignment];
    deals.value = [...deals.value];
  };

  // Add Deal User Rating (Local)
  const addDealUserRating = (req: DealUserRatingAddRequest) =>
    performAsyncAction(async () => {
      const response = await deal_userAddRating(req);
      if (response.data && req.deal_user_id) {
        updateDealUserRatingInLocalState(req.deal_user_id, response.data);
      }
      return response.data;
    });

  // Update Deal User Rating (Local)
  const updateDealUserRating = (req: DealUserRatingUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await deal_userUpdateRating(req);
      if (response.data) {
        updateDealUserRatingInLocalState(response.data.deal_user_id, response.data);
      }
      return response.data;
    });

  return {
    deals,
    currentDeal,
    isLoading,
    error,
    personId: localPersonId,
    fetchDeals,
    fetchDealsByPerson,
    fetchDealsByStage,
    fetchDealDetails,
    createDeal,
    updateDeal,
    removeDeal,
    addUserToDeal,
    updateDealUser,
    removeUserFromDeal,
    getLatestDealForPerson,
    getDealById,
    dealCount,
    loadDeals,
    addDealUserRating,
    updateDealUserRating,
  };
}
