import { storeToRefs } from "pinia";
import { computed, ref } from "vue";

import type {
  PerformanceStatResponse,
  ScheduleStatResponse,
  TaskStatResponse,
} from "@/api/bcare-types-v2";
import { user_statPerformance, user_statSchedule, user_statTaskStat } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useUserDashboardStore } from "@/stores/user-dashboard-store";

interface UseUserDashboardOptions {
  useStore?: boolean;
  autoLoad?: boolean;
  userId?: number;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
}

export default function useUserDashboard(options: UseUserDashboardOptions = {}) {
  const {
    useStore = true,
    autoLoad = false,
    userId: initialUserId,
    dateRange: initialDateRange,
  } = options;

  if (useStore) {
    const store = useUserDashboardStore();
    const { taskStats, scheduleStats, performanceStats, isLoading, error } = storeToRefs(store);

    const userId = ref(initialUserId);
    const dateRange = ref(initialDateRange);

    // Computed
    const hasData = computed(() => {
      return !!(taskStats.value || scheduleStats.value || performanceStats.value);
    });

    // Methods
    const loadAllStats = async () => {
      if (!userId.value) return;

      await Promise.all([
        store.fetchTaskStats(userId.value, dateRange.value?.startDate, dateRange.value?.endDate),
        store.fetchScheduleStats(
          userId.value,
          dateRange.value?.startDate,
          dateRange.value?.endDate,
        ),
        store.fetchPerformanceStats(
          userId.value,
          dateRange.value?.startDate,
          dateRange.value?.endDate,
        ),
      ]);
    };

    const updateDateRange = async (newDateRange: { startDate?: string; endDate?: string }) => {
      dateRange.value = newDateRange;
      if (userId.value && autoLoad) {
        await loadAllStats();
      }
    };

    const updateUserId = async (newUserId: number) => {
      userId.value = newUserId;
      if (autoLoad) {
        await loadAllStats();
      }
    };

    // Auto load data if configured
    if (autoLoad && userId.value) {
      loadAllStats();
    }

    return {
      // State
      taskStats,
      scheduleStats,
      performanceStats,
      isLoading,
      error,
      userId,
      dateRange,

      // Computed
      hasData,

      // Methods
      loadAllStats,
      updateDateRange,
      updateUserId,

      // Individual fetch methods
      fetchTaskStats: store.fetchTaskStats,
      fetchScheduleStats: store.fetchScheduleStats,
      fetchPerformanceStats: store.fetchPerformanceStats,
    };
  }

  // Local state management
  const { isLoading, error, performAsyncAction } = useAsyncAction();
  const taskStats = ref<TaskStatResponse | null>(null);
  const scheduleStats = ref<ScheduleStatResponse | null>(null);
  const performanceStats = ref<PerformanceStatResponse | null>(null);
  const userId = ref(initialUserId);
  const dateRange = ref(initialDateRange);

  // Computed
  const hasData = computed(() => {
    return !!(taskStats.value || scheduleStats.value || performanceStats.value);
  });

  // Local fetch methods
  const fetchTaskStats = (userId: number, startDate?: string, endDate?: string) => {
    return performAsyncAction(async () => {
      const response = await user_statTaskStat({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        taskStats.value = response.data;
      }
      return response.data;
    });
  };

  const fetchScheduleStats = (userId: number, startDate?: string, endDate?: string) => {
    return performAsyncAction(async () => {
      const response = await user_statSchedule({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        scheduleStats.value = response.data;
      }
      return response.data;
    });
  };

  const fetchPerformanceStats = (userId: number, startDate?: string, endDate?: string) => {
    return performAsyncAction(async () => {
      const response = await user_statPerformance({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        performanceStats.value = response.data;
      }
      return response.data;
    });
  };

  // Methods
  const loadAllStats = async () => {
    if (!userId.value) return;

    await Promise.all([
      fetchTaskStats(userId.value, dateRange.value?.startDate, dateRange.value?.endDate),
      fetchScheduleStats(userId.value, dateRange.value?.startDate, dateRange.value?.endDate),
      fetchPerformanceStats(userId.value, dateRange.value?.startDate, dateRange.value?.endDate),
    ]);
  };

  const updateDateRange = async (newDateRange: { startDate?: string; endDate?: string }) => {
    dateRange.value = newDateRange;
    if (userId.value && autoLoad) {
      await loadAllStats();
    }
  };

  const updateUserId = async (newUserId: number) => {
    userId.value = newUserId;
    if (autoLoad) {
      await loadAllStats();
    }
  };

  // Auto load data if configured
  if (autoLoad && userId.value) {
    loadAllStats();
  }

  return {
    // State
    taskStats,
    scheduleStats,
    performanceStats,
    isLoading,
    error,
    userId,
    dateRange,

    // Computed
    hasData,

    // Methods
    loadAllStats,
    updateDateRange,
    updateUserId,

    // Individual fetch methods
    fetchTaskStats,
    fetchScheduleStats,
    fetchPerformanceStats,
  };
}
