import { computed } from "vue";

import { FormSubmissionDynamicQuery, FormSubmissionResponse } from "@/api/bcare-types-v2";
import { form_submissionQueryFormSubmissions } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v2";

export const initialFormSubmissionDynamicQuery: FormSubmissionDynamicQuery = {
  table: "form_submissions",
  selects: [],
  filters: [
    {
      field: "status",
      operator: "NEQ",
      value: "-1",
    },
  ],
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  joins: [],
  person: "",
};

export function useFormSubmissionQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error, getAggregationResult } =
    useQuery<FormSubmissionDynamicQuery>({
      queryFn: form_submissionQueryFormSubmissions,
      initialQuery: initialFormSubmissionDynamicQuery,
    });

  // Query for export
  const { fetchQuery: fetchExportQuery } = useQuery<FormSubmissionDynamicQuery>({
    queryFn: form_submissionQueryFormSubmissions,
    initialQuery: initialFormSubmissionDynamicQuery,
  });

  const submissions = computed(() => items.value as FormSubmissionResponse[]);

  const fetchSubmissions = (
    req: Partial<FormSubmissionDynamicQuery>,
    getCount: boolean = true,
    isExport: boolean = false,
  ) => {
    if (isExport) {
      const exportQuery = {
        ...initialFormSubmissionDynamicQuery,
        ...req,
        export: true,
        limit: undefined,
        offset: undefined,
      };
      return fetchExportQuery(exportQuery, false);
    }
    return fetchQuery(req, getCount);
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, initialFormSubmissionDynamicQuery);
  };

  return {
    // data
    submissions,
    total,
    dynamicQuery,
    // fetch
    fetchSubmissions,
    // state
    isLoading,
    error,
    resetQuery,
    // additional functionality
    getAggregationResult,
  };
}
