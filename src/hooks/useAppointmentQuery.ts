import { computed } from "vue";

import { CommonStatus, FilterOperator } from "@/api/bcare-enum";
import { AppointmentDynamicQuery, AppointmentResponse } from "@/api/bcare-types-v2";
import { appointmentQuery } from "@/api/bcare-v2";
import { useAuthStore } from "@/stores/auth-store";

import { useQuery } from "./useQuery-v3";

export const initialAppointmentDynamicQuery: AppointmentDynamicQuery = {
  table: "appointment",
  selects: [],
  arrived: "both",
  filters: [
    {
      field: "status",
      operator: "NEQ",
      value: CommonStatus.DELETED.toString(),
    },
  ],
  sort: [
    {
      field: "start_time",
      order: "ASC",
    },
    {
      field: "person_id",
      order: "ASC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  doctor: "",
  person: "",
  task: "",
  note: "",
  has_doctor: "yes",
};

export function useAppointmentQuery(options?: { hasDoctor?: "yes" | "no" }) {
  const authStore = useAuthStore();

  const defaultQuery = {
    ...initialAppointmentDynamicQuery,
    has_doctor: options?.hasDoctor ?? "yes",
  };

  // Query chính cho danh sách
  const { items, total, dynamicQuery, fetchQuery, isLoading, error } = useQuery<
    AppointmentDynamicQuery,
    AppointmentResponse
  >({
    queryFn: appointmentQuery,
    initialQuery: defaultQuery,
  });

  // Query riêng cho aggregations nếu cần
  const { fetchQuery: fetchAggregationQuery } = useQuery<AppointmentDynamicQuery, any>({
    queryFn: appointmentQuery,
    initialQuery: defaultQuery,
  });

  // Query riêng cho export
  const { fetchQuery: fetchExportQuery } = useQuery<AppointmentDynamicQuery, AppointmentResponse>({
    queryFn: appointmentQuery,
    initialQuery: defaultQuery,
  });

  const appointments = computed(() => items.value as AppointmentResponse[]);

  const fetchAppointments = (
    req: Partial<AppointmentDynamicQuery>,
    getCount: boolean = true,
    isExport: boolean = false,
  ) => {
    const mergedFilters = [...(defaultQuery.filters || []), ...(req.filters || [])];
    const arrived = req.arrived || defaultQuery.arrived;

    if (isExport) {
      const exportQuery = {
        ...req,
        filters: mergedFilters,
        arrived,
        export: true,
        limit: undefined,
        offset: undefined,
      };
      return fetchExportQuery(exportQuery, false);
    }

    return fetchQuery(
      {
        ...req,
        filters: mergedFilters,
        arrived,
      },
      getCount,
    );
  };

  const fetchDoctorAppointments = (req: Partial<AppointmentDynamicQuery> = {}) => {
    const doctorFilters = [
      {
        field: "doctor_id",
        operator: FilterOperator.EQ,
        value: authStore.currentUser?.id?.toString() ?? "",
      },
    ];

    const mergedFilters = [
      ...(defaultQuery.filters || []),
      ...doctorFilters,
      ...(req.filters || []),
    ];

    const arrived = req.arrived || defaultQuery.arrived;

    return fetchQuery(
      {
        ...req,
        filters: mergedFilters,
        arrived,
        has_doctor: "yes",
      },
      true, // always get count
    );
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, { ...defaultQuery });
  };

  return {
    // data
    appointments,
    total,
    dynamicQuery,
    // fetch
    fetchAppointments,
    fetchDoctorAppointments,
    // state
    isLoading,
    error,
    resetQuery,
  };
}
