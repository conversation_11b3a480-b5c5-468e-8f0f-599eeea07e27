import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted, ref } from "vue";

import type { OperationResponse, ProductOperation } from "@/api/bcare-types-v2";
import { useOperationStore } from "@/stores/operation-store";
import { normalizeVietnamese } from "@/utils/string";

export default function useOperation(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const operationStore = useOperationStore();
  const { operationData, isLoading, error } = storeToRefs(operationStore);

  const operationFilter = ref('');
  const selectedProductIds = ref<number[]>([]); // Changed to an array
  const selectedGroup = ref<string | null>(null);

  const fetchAllOperations = async () => {
    await operationStore.fetchAllOperations();
  };

  const loadOperations = async () => {
    if (Object.keys(operationData.value).length === 0) {
      operationStore.initializeFromCache();
      if (Object.keys(operationData.value).length === 0) {
        await fetchAllOperations();
      }
    }
  };

  const getOperationById = (id: number): OperationResponse | undefined => {
    return operationStore.getOperationById(id);
  };

  const getOperationNameById = (id: number): string => {
    return operationStore.getOperationNameById(id);
  };

  const getProductOperations = (operationId: number): ProductOperation[] => {
    return operationStore.getProductOperationsForOperation(operationId);
  };

  const getAllOperations = computed((): OperationResponse[] => {
    return Object.values(operationData.value);
  });

  // Updated to filter by multiple product IDs
  const operationsByProducts = computed((): OperationResponse[] => {
    return getAllOperations.value.filter(operation =>
      operation.product_operation?.some(po => selectedProductIds.value.includes(po.product_id))
    );
  });

  const filteredOperations = computed(() => {
    let operations = getAllOperations.value;

    // // Apply filtering by products
    // if (selectedProductIds.value.length > 0) {
    //   operations = operationsByProducts.value;
    // }

    // Apply filtering by group
    if (selectedGroup.value !== null) {
      operations = operations.filter(operation => operation.group?.includes(selectedGroup.value!));
    }

    // Apply filtering by keyword
    if (operationFilter.value) {
      const normalizedQuery = normalizeVietnamese(operationFilter.value.toLowerCase());
      operations = operations.filter(operation =>
        normalizeVietnamese(operation.name.toLowerCase()).includes(normalizedQuery)
      );
    }

    // Sort alphabetically based on operation name
    return operations.sort((a, b) => {
      const nameA = normalizeVietnamese(a.name.toLowerCase());
      const nameB = normalizeVietnamese(b.name.toLowerCase());
      return nameA.localeCompare(nameB, 'vi');
    });
  });

  // Updated to toggle selection of multiple products
  const selectProduct = (productId: number | null) => {
    if (productId === null) return; // Do nothing if null

    const index = selectedProductIds.value.indexOf(productId);
    if (index === -1) {
      selectedProductIds.value.push(productId); // Add product ID if not already selected
    } else {
      selectedProductIds.value.splice(index, 1); // Remove product ID if already selected
    }
  };

  const selectGroup = (group: string | null) => {
    selectedGroup.value = selectedGroup.value === group ? null : group;
  };

  const updateOperationFilter = (filter: string) => {
    operationFilter.value = filter;
  };

  // Hooks
  onMounted(() => {
    if (options.autoLoad) {
      loadOperations();
    }
  });

  onUnmounted(() => {
    // Cleanup logic if needed
  });

  return {
    operationData,
    isLoading,
    error,

    // Methods to load and initialize data
    fetchAllOperations,
    loadOperations,

    // Methods to access data
    getOperationById,
    getOperationNameById,
    getProductOperations,

    // Computed properties and filtering methods
    getAllOperations,
    operationsByProducts,
    filteredOperations,

    // Methods to update filters
    selectProduct,
    selectGroup,
    updateOperationFilter,

    // Reactive values for filters
    operationFilter,
    selectedProductIds, // Updated to reflect the change to an array
    selectedGroup
  };
}
