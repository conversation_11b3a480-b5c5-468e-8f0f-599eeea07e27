import { ref } from "vue";

export function useCallFuncSync() {
  const loading = ref(false);
  const error = ref(null);

  const callFunc = async (callback: () => void, delay?: number) => {
    loading.value = true;
    error.value = null;
    if (delay)
      setTimeout(() => {
        try {
          callback();
        } catch (err) {
          console.log(err);
        } finally {
          loading.value = false;
        }
      }, delay);
    else
      try {
        callback();
      } catch (err) {
        console.log(err);
      } finally {
        loading.value = false;
      }
  };

  return { loading, callFunc };
}
