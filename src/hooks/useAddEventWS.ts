import { onBeforeUnmount, onMounted } from "vue";

import { WsMessage } from "@/realtime/websocket";
import { useWsStore } from "@/stores/ws-store";

function useAddEventWS({ callback, event }: { event: string; callback: (msg: WsMessage) => void }) {
  const wsStore = useWsStore();

  onMounted(() => {
    if (wsStore.ws?.isOpen) wsStore.conn?.on(event, callback);
    else
      wsStore.ws?.addEvent("ready", () => {
        wsStore.conn?.on(event, callback);
      });
  });

  onBeforeUnmount(() => {
    wsStore.conn?.off(event, callback);
  });
}

export default useAddEventWS;
