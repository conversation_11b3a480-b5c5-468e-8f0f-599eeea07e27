import { computed } from "vue";

import {DynamicQuery, DynamicQueryResponse, PersonDynamicQuery, PersonResponse} from "@/api/bcare-types-v2";
import {attachmentQueryOperation, personQuery} from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v2";

export const initialPersonDynamicQuery: PersonDynamicQuery = {
  table: "person_query_view",
  selects: [],
  filters: [
    {
      field: "status",
      operator: "NEQ",
      value: "-1",
    },
  ],
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  creator: "",
  sale: "",
  doctor_id: "",
  search: "",
};

export function usePersonQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error, getAggregationResult } =
    useQuery<PersonDynamicQuery>({
      queryFn: personQuery,
      initialQuery: initialPersonDynamicQuery,
    });

  // Query for export
  const { fetchQuery: fetchExportQuery } = useQuery<PersonDynamicQuery>({
    queryFn: personQuery,
    initialQuery: initialPersonDynamicQuery,
  });

  const persons = computed(() => items.value as PersonResponse[]);

  const fetchPersons = (req: Partial<PersonDynamicQuery>, getCount: boolean = true,  isExport: boolean = false,) => {
    if (isExport) {
      const exportQuery = {
        ...initialPersonDynamicQuery,
        ...req,
        export: true,
        limit: undefined,
        offset: undefined,
      };
      return fetchExportQuery(exportQuery, false);
    }
    return fetchQuery(req, getCount);
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, initialPersonDynamicQuery);
  };

  return {
    // data
    persons,
    total,
    dynamicQuery,
    // fetch
    fetchPersons,
    // state
    isLoading,
    error,
    resetQuery,
    // additional functionality
    getAggregationResult,
  };
}
