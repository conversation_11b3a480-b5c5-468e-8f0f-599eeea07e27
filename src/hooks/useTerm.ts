import { storeToRefs } from "pinia";
import { onMounted, onUnmounted } from "vue";

import type { Term } from "@/api/bcare-types-v2";
import { BundleType, useTermStore } from "@/stores/term-store-v2";

export default function useTerm(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const termStore = useTermStore();
  const { termData, isLoading, error } = storeToRefs(termStore);

  const fetchAllTerms = async () => {
    await termStore.fetchAllTerms();
  };

  const refreshBundle = async (bundle: BundleType) => {
    await termStore.refreshBundle(bundle);
  };

  const getTermById = (bundle: BundleType, id: number): Term | undefined => {
    return termStore.getTermById(bundle, id);
  };

  const getTermNameById = (bundle: BundleType, id: number): string => {
    return termStore.getTermNameById(bundle, id);
  };

  const getBundleTerms = (bundle: BundleType): Term[] => {
    return Object.values(termData.value[bundle] || {});
  };

  const loadTerms = async () => {
    if (Object.keys(termData.value).length === 0) {
      termStore.initializeFromCache();
      if (Object.keys(termData.value).length === 0) {
        await fetchAllTerms();
      }
    }
  };

  // Mounted hook
  onMounted(() => {
    if (options.autoLoad) {
      loadTerms();
    }
  });

  // Unmounted hook
  onUnmounted(() => {
    // Có thể thêm logic cleanup nếu cần
  });

  return {
    termData,
    isLoading,
    error,
    fetchAllTerms,
    refreshBundle,
    getTermById,
    getTermNameById,
    getBundleTerms,
    loadTerms,
  };
}
