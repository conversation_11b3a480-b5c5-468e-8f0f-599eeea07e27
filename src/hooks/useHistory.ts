import { storeToRefs } from "pinia";

import type { HistoryListRequest } from "@/api/bcare-types-v2";
import { useHistoryStore } from "@/stores/history-store"; // Assuming you named the store file history-store.ts

export default function useHistory() {
  const historyStore = useHistoryStore();
  const { isLoading, error, histories, total, totalPage } = storeToRefs(historyStore);

  // List histories with filtering and pagination
  const listHistories = async (req: HistoryListRequest) => {
    return await historyStore.listHistories(req);
  };

  return {
    isLoading,
    error,
    histories,
    total,
    totalPage,
    listHistories,
  };
}
