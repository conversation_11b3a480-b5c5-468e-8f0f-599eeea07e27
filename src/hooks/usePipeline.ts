import { storeToRefs } from "pinia";
import { onMounted, onUnmounted } from "vue";

import { usePipelineStore } from "@/stores/pipeline-store";

export default function usePipeline(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const pipelineStore = usePipelineStore();
  const { pipelines, totalPipelines, totalPages, isLoading, error } = storeToRefs(pipelineStore);

  const fetchPipelines = async () => {
    await pipelineStore.fetchPipelines();
  };

  const getPipelineById = (id: number) => {
    return pipelineStore.getPipelineById(id);
  };

  const getStagesByPipelineID = (pipelineID: number) => {
    return pipelineStore.getStagesByPipelineID(pipelineID);
  };

  const getTopLevelStagesByPipelineID = (pipelineID: number) => {
    return pipelineStore.getTopLevelStagesByPipelineID(pipelineID);
  };

  const loadPipelines = async () => {
    if (pipelines.value.length === 0) {
      pipelineStore.initializeFromCache();
      if (pipelines.value.length === 0) {
        await fetchPipelines();
      }
    }
  };

  // Mounted hook
  onMounted(() => {
    if (options.autoLoad) {
      loadPipelines();
    }
  });

  // Unmounted hook
  onUnmounted(() => {
    // Có thể thêm logic cleanup nếu cần
  });

  return {
    pipelines,
    totalPipelines,
    totalPages,
    isLoading,
    error,
    fetchPipelines,
    getPipelineById,
    getStagesByPipelineID,
    getTopLevelStagesByPipelineID,
    loadPipelines,
  };
}
