// composables/useUpload.ts
import { ref } from "vue";

import { FileUsageAddRequest } from "@/api/bcare-types-v2"; // <PERSON><PERSON><PERSON> sử FileUsageAddRequest đã được định nghĩa ở nơi khác
import { FileResponse, FileUsageResponse } from "@/api/bcare-types-v2"; // Import interfaces
import { useMedia } from "@/hooks/useMedia";
import { useNotiStore } from "@/stores/notification";

interface UploadOptions {
  maxFileSize?: number; // in MB
  allowedMimeTypes?: string[];
  entity?: {
    id: number;
    type: string;
    usageType: string;
    usageMeta?: string;
  };
}

export const useUpload = (options: UploadOptions = {}) => {
  const {
    maxFileSize = 2,
    allowedMimeTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
    entity,
  } = options;

  const isUploading = ref(false);
  const progress = ref(0);
  const notiStore = useNotiStore();
  const { addFile, addFileUsage } = useMedia();

  const validateFile = (file: File): boolean => {
    const maxSize = maxFileSize * 1024 * 1024; // Convert to bytes
    if (file.size > maxSize) {
      notiStore.error({
        title: "Lỗi kích thước file",
        message: `File không được vượt quá ${maxFileSize}MB`,
      });
      return false;
    }

    if (!allowedMimeTypes.includes(file.type)) {
      notiStore.error({
        title: "Định dạng không hợp lệ",
        message: `Chỉ chấp nhận các định dạng: ${allowedMimeTypes.join(", ")}`,
      });
      return false;
    }

    return true;
  };

  const uploadFile = async (file: File): Promise<FileResponse> => {
    try {
      if (!validateFile(file)) {
        throw new Error("File validation failed");
      }

      isUploading.value = true;
      progress.value = 0;

      const response = await addFile(file);

      if (response.code !== 0 || !response.data) {
        throw new Error(response.message || "Upload failed");
      }

      const fileData: FileResponse = response.data;

      if (entity) {
        const usageRequest: FileUsageAddRequest = {
          entity_id: entity.id,
          entity_type: entity.type,
          file_id: fileData.id,
          usage_type: entity.usageType,
          usage_meta: entity.usageMeta || "",
        };

        await addFileUsage(usageRequest);
      }

      progress.value = 100;
      return fileData; // Return FileResponse directly
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

      notiStore.error({
        title: "Lỗi tải file",
        message: errorMessage,
      });
      throw error;
    } finally {
      isUploading.value = false;
    }
  };

  const uploadMultipleFiles = async (files: File[]): Promise<FileResponse[]> => {
    const uploadPromises = files.map((file) => uploadFile(file));
    return Promise.all(uploadPromises);
  };

  const handlePaste = async (event: ClipboardEvent): Promise<FileResponse[]> => {
    const items = Array.from(event.clipboardData?.items || []);
    const imageFiles = items
      .filter((item) => item.type.startsWith("image/"))
      .map((item) => item.getAsFile())
      .filter((file): file is File => file !== null);

    if (imageFiles.length === 0) return [];

    return uploadMultipleFiles(imageFiles);
  };

  const handleDrop = async (event: DragEvent): Promise<FileResponse[]> => {
    event.preventDefault();

    const files = Array.from(event.dataTransfer?.files || []).filter((file) =>
      allowedMimeTypes.includes(file.type),
    );

    if (files.length === 0) return [];

    return uploadMultipleFiles(files);
  };

  return {
    isUploading,
    progress,
    uploadFile,
    uploadMultipleFiles,
    handlePaste,
    handleDrop,
    validateFile,
  };
};
