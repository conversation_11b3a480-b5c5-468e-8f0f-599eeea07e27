import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";

import type {
  TagAddRequest,
  TagDeleteRequest,
  TagGetRequest,
  TagListRequest,
  TagListResponse,
  TagResponse,
  TagUpdateRequest,
  TagPersonAddRequest,
  TagPersonUpdateRequest,
  TagPersonDeleteRequest,
  TagDealAddRequest,
  TagDealUpdateRequest,
  TagDealDeleteRequest,
  TagPerson,
  TagDeal,
} from "@/api/bcare-types-v2";
import { GroupedTagOption, useTagStore } from "@/stores/tag-store";
import { fuzzySearch } from "@/utils/string";
import { useAsyncAction } from "@/composables/useAsyncAction";
import {
  tagAdd,
  tagDelete,
  tagGet,
  tagList,
  tagUpdate,
  tag_personAdd,
  tag_personUpdate,
  tag_personDelete,
  tag_dealAdd,
  tag_dealUpdate,
  tag_dealDelete,
} from "@/api/bcare-v2";

interface UseTagsOptions {
  autoLoad?: boolean;
  useStore?: boolean;
  initialState?: {
    tags?: TagResponse[];
    currentTag?: TagResponse | null;
  };
}

export default function useTag(options: UseTagsOptions = { autoLoad: true, useStore: false }) {
  const { autoLoad = true, useStore = true, initialState = {} } = options;

  if (useStore) {
    const tagStore = useTagStore();
    const {
      isLoading,
      error,
      tags,
      paginatedTags,
      tagPersonRelations,
      tagDealRelations,
      groupedTags,
    } = storeToRefs(tagStore);

    // Local state for filtered tags, initialized as empty
    const filteredTags = ref<TagResponse[]>([]);
    const totalRecords = ref<number>(0);

    // Computed property for tag count
    const tagCount = computed(() => tagStore.getTagCount);

    // Handle API response and update state
    const handleListResponse = (response: TagListResponse | null) => {
      if (response?.tags) {
        paginatedTags.value = response.tags;
        totalRecords.value = response.total;
      }
      return response;
    };

    // List tags with pagination and filtering
    const listTagsWithTotal = async (req: TagListRequest) => {
      const response = await tagStore.listTags(req);
      return handleListResponse(response);
    };

    // Load all tags
    const loadTags = async () => {
      if (tagStore.hasCachedData) {
        tagStore.initializeFromCache();
      } else {
        await tagStore.fetchAllTags();
      }
      // Keep filteredTags empty after loading
      filteredTags.value = [];
    };

    // Autoload tags if autoLoad is true
    if (autoLoad) {
      onMounted(() => {
        loadTags();
      });
    }

    // Add a new tag
    const addTag = async (req: TagAddRequest) => {
      return await tagStore.addTag(req);
    };

    // Delete a tag
    const deleteTag = async (req: TagDeleteRequest) => {
      // Only make the API call, let the store handle the state update
      return await tagStore.deleteTag(req);
    };

    // Get a specific tag
    const getTag = async (req: TagGetRequest) => {
      return await tagStore.getTag(req);
    };

    // List tags with filtering
    const listTags = async (req: TagListRequest) => {
      const response = await tagStore.listTags(req);
      if (response?.tags) {
        filteredTags.value = response.tags;
      }
      return response;
    };

    // Update a tag
    const updateTag = async (req: TagUpdateRequest) => {
      // Only make the API call, let the store handle the state update
      return await tagStore.updateTag(req);
    };

    // Get a tag by ID
    const getTagById = (id: number) => {
      return tagStore.getTagById(id);
    };

    // Get a tag by name
    const getTagByName = (name: string) => {
      return tagStore.getTagByName(name);
    };

    // Get multiple tags by their IDs
    const getTagsByIds = (ids: number[]) => {
      return ids.map((id) => tagStore.getTagById(id)).filter(Boolean) as TagResponse[];
    };

    // Get tags by category
    const getTagsByCategory = (category: string): TagResponse[] => {
      if (!category) return [];
      return tagStore.tags.filter((tag) => tag.category === category);
    };

    // Search tags
    const searchTags = (query: string, category?: string): TagResponse[] => {
      if (!query.trim() && !category) {
        filteredTags.value = [];
        return [];
      }

      const results = tagStore.tags.filter((tag) => {
        if (category) {
          if (tag.category !== category) {
            return false;
          }
        }

        if (!query.trim()) {
          return true;
        }

        const nameMatch = fuzzySearch(tag.name, query);
        const categoryMatch = tag.category ? fuzzySearch(tag.category, query) : false;
        const descriptionMatch = tag.description ? fuzzySearch(tag.description, query) : false;

        return nameMatch || categoryMatch || descriptionMatch;
      });

      filteredTags.value = results;
      return results;
    };

    // Paginate tags
    const paginateTags = (page: number, pageSize: number) => {
      const startIndex = (page - 1) * pageSize;
      return filteredTags.value.slice(startIndex, startIndex + pageSize);
    };

    // Get list of unique categories
    const getCategories = computed(() => {
      const categories = new Set<string>();
      tags.value.forEach((tag) => {
        if (tag.category) categories.add(tag.category);
      });
      return Array.from(categories);
    });

    // Tag-Person relationship methods
    const addTagToPerson = async (req: TagPersonAddRequest) => {
      return await tagStore.addTagToPerson(req);
    };

    const updateTagPerson = async (req: TagPersonUpdateRequest) => {
      return await tagStore.updateTagPerson(req);
    };

    const deleteTagFromPerson = async (req: TagPersonDeleteRequest) => {
      // Store handles the logic, just pass the request
      return await tagStore.deleteTagFromPerson(req);
    };

    // Get tags for a specific person
    const getTagsForPerson = (personId: number): TagResponse[] => {
      const personTagIds = tagPersonRelations.value
        .filter((relation) => relation.person_id === personId)
        .map((relation) => relation.tag_id);

      return getTagsByIds(personTagIds);
    };

    // Tag-Deal relationship methods
    const addTagToDeal = async (req: TagDealAddRequest) => {
      return await tagStore.addTagToDeal(req);
    };

    const updateTagDeal = async (req: TagDealUpdateRequest) => {
      return await tagStore.updateTagDeal(req);
    };

    const deleteTagFromDeal = async (req: TagDealDeleteRequest) => {
      // Store handles the logic, just pass the request
      return await tagStore.deleteTagFromDeal(req);
    };

    // Get tags for a specific deal
    const getTagsForDeal = (dealId: number): TagResponse[] => {
      const dealTagIds = tagDealRelations.value
        .filter((relation) => relation.deal_id === dealId)
        .map((relation) => relation.tag_id);

      return getTagsByIds(dealTagIds);
    };

    return {
      isLoading,
      error,
      tags,
      paginatedTags,
      filteredTags,
      tagCount,
      totalRecords,
      loadTags,
      addTag,
      deleteTag,
      getTag,
      listTags,
      listTagsWithTotal,
      updateTag,
      getTagById,
      getTagByName,
      getTagsByIds,
      getTagsByCategory,
      searchTags,
      paginateTags,
      getCategories,
      tagPersonRelations,
      addTagToPerson,
      updateTagPerson,
      deleteTagFromPerson,
      getTagsForPerson,
      tagDealRelations,
      addTagToDeal,
      updateTagDeal,
      deleteTagFromDeal,
      getTagsForDeal,
      groupedTags, // Return groupedTags
    };
  }

  // Local state management when useStore = false
  const tags = ref<TagResponse[]>(initialState.tags || []);
  const currentTag = ref<TagResponse | null>(initialState.currentTag || null);
  const filteredTags = ref<TagResponse[]>([]);
  const paginatedTags = ref<TagResponse[]>([]);
  const totalRecords = ref<number>(0);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // Computed property for tag count
  const tagCount = computed(() => tags.value.length);

  // Handle API response and update state
  const handleListResponse = (response: TagListResponse | null) => {
    if (response?.tags) {
      paginatedTags.value = response.tags;
      totalRecords.value = response.total;
    }
    return response;
  };

  // Load all tags
  const loadTags = async () => {
    return performAsyncAction(async () => {
      const response = await tagList({ page: 1, page_size: 1000 });
      if (response.data?.tags) {
        tags.value = response.data.tags;
      }
      return response.data;
    });
  };

  // Autoload tags if autoLoad is true
  if (autoLoad) {
    onMounted(() => {
      loadTags();
    });
  }

  // Add a new tag
  const addTag = (req: TagAddRequest) =>
    performAsyncAction(async () => {
      const response = await tagAdd(req);
      if (response.data) {
        tags.value.push(response.data);
      }
      return response.data;
    });

  // Delete a tag
  const deleteTag = (req: TagDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await tagDelete(req);
      if (response.data) {
        tags.value = tags.value.filter((tag) => tag.id !== req.id);
        filteredTags.value = filteredTags.value.filter((tag) => tag.id !== req.id);
        paginatedTags.value = paginatedTags.value.filter((tag) => tag.id !== req.id);
        if (currentTag.value?.id === req.id) {
          currentTag.value = null;
        }
      }
      return response.data;
    });

  // Get a specific tag
  const getTag = (req: TagGetRequest) =>
    performAsyncAction(async () => {
      const response = await tagGet(req);
      currentTag.value = response.data ?? null;
      return response.data;
    });

  // List tags with filtering
  const listTags = (req: TagListRequest) =>
    performAsyncAction(async () => {
      const response = await tagList(req);
      if (response.data?.tags) {
        filteredTags.value = response.data.tags;
      }
      return response.data;
    });

  // List tags with pagination and total
  const listTagsWithTotal = async (req: TagListRequest) => {
    return performAsyncAction(async () => {
      const response = await tagList(req);
      return handleListResponse(response.data);
    });
  };

  // Update a tag
  const updateTag = (req: TagUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await tagUpdate(req);
      if (response.data) {
        const index = tags.value.findIndex((tag) => tag.id === req.id);
        if (index !== -1) {
          tags.value[index] = response.data;
        }

        const filteredIndex = filteredTags.value.findIndex((tag) => tag.id === req.id);
        if (filteredIndex !== -1) {
          filteredTags.value[filteredIndex] = response.data;
        }

        const paginatedIndex = paginatedTags.value.findIndex((tag) => tag.id === req.id);
        if (paginatedIndex !== -1) {
          paginatedTags.value[paginatedIndex] = response.data;
        }

        if (currentTag.value?.id === req.id) {
          currentTag.value = response.data;
        }
      }
      return response.data;
    });

  // Get a tag by ID
  const getTagById = computed(() => (id: number) => tags.value.find((tag) => tag.id === id));

  // Get a tag by name
  const getTagByName = computed(
    () => (name: string) => tags.value.find((tag) => tag.name.toLowerCase() === name.toLowerCase()),
  );

  // Get multiple tags by their IDs
  const getTagsByIds = (ids: number[]) => {
    return ids
      .map((id) => tags.value.find((tag) => tag.id === id))
      .filter(Boolean) as TagResponse[];
  };

  // Get tags by category
  const getTagsByCategory = (category: string): TagResponse[] => {
    if (!category) return [];
    return tags.value.filter((tag) => tag.category === category);
  };

  // Search tags
  const searchTags = (query: string, category?: string): TagResponse[] => {
    if (!query.trim() && !category) {
      filteredTags.value = [];
      return [];
    }

    const results = tags.value.filter((tag) => {
      if (category) {
        if (tag.category !== category) {
          return false;
        }
      }

      if (!query.trim()) {
        return true;
      }

      const nameMatch = fuzzySearch(tag.name, query);
      const categoryMatch = tag.category ? fuzzySearch(tag.category, query) : false;
      const descriptionMatch = tag.description ? fuzzySearch(tag.description, query) : false;

      return nameMatch || categoryMatch || descriptionMatch;
    });

    filteredTags.value = results;
    return results;
  };

  // Paginate tags
  const paginateTags = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredTags.value.slice(startIndex, startIndex + pageSize);
  };

  // Get list of unique categories
  const getCategories = computed(() => {
    const categories = new Set<string>();
    tags.value.forEach((tag) => {
      if (tag.category) categories.add(tag.category);
    });
    return Array.from(categories);
  });

  // Additional local state for tag relationships
  const tagPersonRelations = ref<TagPerson[]>([]);
  const tagDealRelations = ref<TagDeal[]>([]);

  // Tag-Person relationship methods
  const addTagToPerson = (req: TagPersonAddRequest) =>
    performAsyncAction(async () => {
      const response = await tag_personAdd(req);
      if (response.data) {
        tagPersonRelations.value.push(response.data);
      }
      return response.data;
    });

  const updateTagPerson = (req: TagPersonUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await tag_personUpdate(req);
      if (response.data) {
        const index = tagPersonRelations.value.findIndex((relation) => relation.id === req.id);
        if (index !== -1) {
          tagPersonRelations.value[index] = response.data;
        }
      }
      return response.data;
    });

  const deleteTagFromPerson = (req: TagPersonDeleteRequest) =>
    performAsyncAction(async () => {
      // Pass the correct request object to the API call
      const response = await tag_personDelete(req);
      if (response.data) {
        // Filter local relations based on both tag_id and person_id
        tagPersonRelations.value = tagPersonRelations.value.filter(
          (relation) => !(relation.tag_id === req.tag_id && relation.person_id === req.person_id),
        );
      }
      return response.data;
    });

  // Get tags for a specific person
  const getTagsForPerson = (personId: number): TagResponse[] => {
    const personTagIds = tagPersonRelations.value
      .filter((relation) => relation.person_id === personId)
      .map((relation) => relation.tag_id);

    return getTagsByIds(personTagIds);
  };

  // Tag-Deal relationship methods
  const addTagToDeal = (req: TagDealAddRequest) =>
    performAsyncAction(async () => {
      const response = await tag_dealAdd(req);
      if (response.data) {
        tagDealRelations.value.push(response.data);
      }
      return response.data;
    });

  const updateTagDeal = (req: TagDealUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await tag_dealUpdate(req);
      if (response.data) {
        const index = tagDealRelations.value.findIndex((relation) => relation.id === req.id);
        if (index !== -1) {
          tagDealRelations.value[index] = response.data;
        }
      }
      return response.data;
    });

  const deleteTagFromDeal = (req: TagDealDeleteRequest) =>
    performAsyncAction(async () => {
      // Pass the correct request object to the API call
      const response = await tag_dealDelete(req);
      if (response.data) {
        // Filter local relations based on both tag_id and deal_id
        tagDealRelations.value = tagDealRelations.value.filter(
          (relation) => !(relation.tag_id === req.tag_id && relation.deal_id === req.deal_id),
        );
      }
      return response.data;
    });

  // Get tags for a specific deal
  const getTagsForDeal = (dealId: number): TagResponse[] => {
    const dealTagIds = tagDealRelations.value
      .filter((relation) => relation.deal_id === dealId)
      .map((relation) => relation.tag_id);

    return getTagsByIds(dealTagIds);
  };

  // Getter to group tags by category
  const groupedTags = computed((): GroupedTagOption[] => {
    const grouped: GroupedTagOption[] = [];
    const uncategorizedTags: TagResponse[] = [];

    tags.value.forEach((tag) => {
      if (tag.category) {
        let group = grouped.find((g) => g.category === tag.category);
        if (!group) {
          group = { category: tag.category, items: [] };
          grouped.push(group);
        }
        group.items.push(tag);
      } else {
        uncategorizedTags.push(tag);
      }
    });

    // Sort groups by category name
    grouped.sort((a, b) => a.category.localeCompare(b.category));

    // Sort items within each group by tag name
    grouped.forEach((group) => {
      group.items.sort((a, b) => a.name.localeCompare(b.name));
    });

    // Add sorted uncategorized tags as the last group if any exist
    if (uncategorizedTags.length > 0) {
      uncategorizedTags.sort((a, b) => a.name.localeCompare(b.name));
      grouped.push({ category: "Uncategorized", items: uncategorizedTags });
    }

    return grouped;
  });

  return {
    isLoading,
    error,
    tags,
    paginatedTags,
    groupedTags,
    filteredTags,
    tagCount,
    totalRecords,
    loadTags,
    addTag,
    deleteTag,
    getTag,
    listTags,
    listTagsWithTotal,
    updateTag,
    getTagById,
    getTagByName,
    getTagsByIds,
    getTagsByCategory,
    searchTags,
    paginateTags,
    getCategories,
    tagPersonRelations,
    addTagToPerson,
    updateTagPerson,
    deleteTagFromPerson,
    getTagsForPerson,
    tagDealRelations,
    addTagToDeal,
    updateTagDeal,
    deleteTagFromDeal,
    getTagsForDeal,
  };
}
