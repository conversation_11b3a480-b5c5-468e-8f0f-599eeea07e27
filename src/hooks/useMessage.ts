import { reactive, toRefs } from "vue";

import { HistoryRequest, MessageHistory, SendMessageRequest } from "@/api/bcare-types-v2";
import { useNotiStore } from "@/stores/notification";
import { personHistoryMessage, personSendMessage } from "@/api/bcare-v2";

export default function useMessage() {
  const notiStore = useNotiStore();
  const state = reactive<{
    messages?: MessageHistory[];
  }>({
    messages: [] as MessageHistory[],
  });
  const fetchMessageList = async (request: HistoryRequest) => {
    const response = await personHistoryMessage(request);
    if (response.code === 0) {
      if (response.data) state.messages = response.data.message_histories;
      return response.code;
    }
    return response.code;
  };

  const onAddMessage = async (request: SendMessageRequest) => {
    try {
      const response = await personSendMessage(request);
      if (response.code === 0) {
        notiStore.success({
          title: "<PERSON><PERSON><PERSON> tin nhắn thành công",
          message: "",
        });
        return response.code;
      }
      return response.code;
    } catch (error) {
      throw error;
    }
  };

  return {
    ...toRefs(state),
    fetchMessageList,
    onAddMessage,
  };
}
