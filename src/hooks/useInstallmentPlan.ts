import { computed, shallowRef } from "vue";

import type {
  Installment,
  InstallmentAddRequest,
  InstallmentDeleteRequest,
  InstallmentGetPartiallyPaidRequest,
  InstallmentGetRequest,
  InstallmentListRequest,
  InstallmentListResponse,
  InstallmentPlan,
  InstallmentPlanAddRequest,
  InstallmentPlanDeleteRequest,
  InstallmentPlanGetRequest,
  InstallmentPlanListRequest,
  InstallmentPlanListResponse,
  InstallmentPlanRefundableListRequest,
  InstallmentPlanResponse,
  InstallmentPlanUpdateRequest,
  InstallmentResponse,
  InstallmentUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  installment_planAdd,
  installment_planDelete,
  installment_planGet,
  installment_planList,
  installment_planListRefundable,
  installment_planUpdate,
  installmentAdd,
  installmentDelete,
  installmentGet,
  installmentGetPartiallyPaid,
  installmentGetRefund,
  installmentList,
  installmentUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface UsePaymentPlanOptions {
  initialState?: {
    installments?: Installment[];
    currentInstallment?: Installment | null;
    installmentPlans?: InstallmentPlan[];
    currentInstallmentPlan?: InstallmentPlan | null;
    partiallyPaidInstallments?: Installment[];
    refundableInstallmentPlans?: InstallmentPlanResponse[];
    refundableInstallments?: InstallmentResponse[];
  };
}

export default function useInstallmentPlan(options: UsePaymentPlanOptions = {}) {
  const { initialState = {} } = options;

  // Local state management
  const installments = shallowRef<Installment[]>(initialState.installments || []);
  const currentInstallment = shallowRef<Installment | null>(
    initialState.currentInstallment || null,
  );
  const installmentPlans = shallowRef<InstallmentPlan[]>(initialState.installmentPlans || []);
  const currentInstallmentPlan = shallowRef<InstallmentPlan | null>(
    initialState.currentInstallmentPlan || null,
  );
  const partiallyPaidInstallments = shallowRef<Installment[]>(
    initialState.partiallyPaidInstallments || [],
  );
  const refundableInstallmentPlans = shallowRef<InstallmentPlanResponse[]>(
    initialState.refundableInstallmentPlans || [],
  );
  const refundableInstallments = shallowRef<InstallmentResponse[]>(
    initialState.refundableInstallments || [],
  );

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const installmentCount = computed(() => installments.value.length);
  const installmentPlanCount = computed(() => installmentPlans.value.length);
  const partiallyPaidInstallmentCount = computed(() => partiallyPaidInstallments.value.length);

  const getInstallmentById = computed(
    () => (id: number) => installments.value.find((installment) => installment.id === id),
  );
  const getInstallmentPlanById = computed(
    () => (id: number) => installmentPlans.value.find((plan) => plan.id === id),
  );

  // Installment functions
  const addInstallment = (req: InstallmentAddRequest) =>
    performAsyncAction(async () => {
      const response = await installmentAdd(req);
      if (response.data) {
        installments.value.push(response.data);
      }
      return response.data;
    });

  const deleteInstallment = (req: InstallmentDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await installmentDelete(req);
      if (response.data) {
        installments.value = installments.value.filter((installment) => installment.id !== req.id);
      }
      return response.data;
    });

  const getInstallment = (req: InstallmentGetRequest) =>
    performAsyncAction(async () => {
      const response = await installmentGet(req);
      currentInstallment.value = response.data ?? null;
      return response.data;
    });

  const listInstallments = (req: InstallmentListRequest) =>
    performAsyncAction(async () => {
      const response = await installmentList(req);
      installments.value = response.data?.installments ?? [];
      return response.data;
    });

  const updateInstallment = (req: InstallmentUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await installmentUpdate(req);
      if (response.data) {
        const index = installments.value.findIndex((installment) => installment.id === req.id);
        if (index !== -1) {
          installments.value[index] = response.data;
        }
      }
      return response.data;
    });

  // Installment Plan functions
  const addInstallmentPlan = (req: InstallmentPlanAddRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planAdd(req);
      if (response.data) {
        installmentPlans.value.push(response.data);
      }
      return response.data;
    });

  const deleteInstallmentPlan = (req: InstallmentPlanDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planDelete(req);
      if (response.data) {
        installmentPlans.value = installmentPlans.value.filter((plan) => plan.id !== req.id);
      }
      return response.data;
    });

  const getInstallmentPlan = (req: InstallmentPlanGetRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planGet(req);
      currentInstallmentPlan.value = response.data ?? null;
      return response.data;
    });

  const listInstallmentPlans = (req: InstallmentPlanListRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planList(req);
      installmentPlans.value = response.data?.installment_plans ?? [];
      return response.data;
    });

  const updateInstallmentPlan = (req: InstallmentPlanUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planUpdate(req);
      if (response.data) {
        const index = installmentPlans.value.findIndex((plan) => plan.id === req.id);
        if (index !== -1) {
          installmentPlans.value[index] = response.data;
        }
      }
      return response.data;
    });

  const getPartiallyPaidInstallments = (req: InstallmentGetPartiallyPaidRequest) =>
    performAsyncAction(async () => {
      const response = await installmentGetPartiallyPaid(req);
      if (response.data) {
        partiallyPaidInstallments.value = response.data.installments ?? [];
      }
      return response.data;
    });

  const listRefundableInstallments = (req: InstallmentGetPartiallyPaidRequest) =>
    performAsyncAction(async () => {
      const response = await installmentGetRefund(req);
      if (response.data) {
        refundableInstallments.value = response.data.installments;
      }
      return response.data;
    });

  const listRefundableInstallmentPlans = (req: InstallmentPlanRefundableListRequest) =>
    performAsyncAction(async () => {
      const response = await installment_planListRefundable(req);
      if (response.data) {
        refundableInstallmentPlans.value = response.data.installment_plans;
      }
      return response.data;
    });

  return {
    // States
    installments,
    currentInstallment,
    installmentPlans,
    currentInstallmentPlan,
    refundableInstallments,
    refundableInstallmentPlans,
    partiallyPaidInstallments,
    isLoading,
    error,

    // Computed
    installmentCount,
    installmentPlanCount,
    partiallyPaidInstallmentCount,
    getInstallmentById,
    getInstallmentPlanById,

    // Methods
    addInstallment,
    deleteInstallment,
    getInstallment,
    listInstallments,
    updateInstallment,
    addInstallmentPlan,
    deleteInstallmentPlan,
    getInstallmentPlan,
    listInstallmentPlans,
    updateInstallmentPlan,
    getPartiallyPaidInstallments,
    listRefundableInstallments,
    listRefundableInstallmentPlans,
  };
}
