//useAttachment.ts

import { storeToRefs } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  AttachmenForcetUpdateRequest,
  AttachmentAddRequest,
  AttachmentDeleteRequest,
  AttachmentDynamicQuery,
  AttachmentGetRequest,
  AttachmentListRequest,
  AttachmentResponse,
  AttachmentUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  attachmentAdd,
  attachmentDelete,
  attachmentForceUpdateCreatedAt,
  attachmentGet,
  attachmentList,
  attachmentQuery,
  attachmentUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useQuery } from "@/hooks/useQuery-v3";
import { useAttachmentStore } from "@/stores/attachment-store";

interface UseAttachmentOptions {
  useStore?: boolean;
  initialState?: {
    attachments?: AttachmentResponse[];
    currentAttachment?: AttachmentResponse | null;
  };
}

export default function useAttachment(options: UseAttachmentOptions = {}) {
  const { useStore = false, initialState = {} } = options;

  if (useStore) {
    const store = useAttachmentStore();
    const { attachments, currentAttachment, isLoading, error } = storeToRefs(store);

    return {
      attachments,
      currentAttachment,
      isLoading,
      error,
      addAttachment: store.addAttachment,
      deleteAttachment: store.deleteAttachment,
      getAttachment: store.getAttachment,
      listAttachments: store.listAttachments,
      updateAttachment: store.updateAttachment,
      getAttachmentCount: computed(() => store.getAttachmentCount),
      getAttachmentById: (id: number) => store.getAttachmentById(id),
      queryAttachments: store.queryAttachments,
      attachmentQueryResult: store.attachmentQueryResult,
      getAttachmentsKindProduct: async (personId: number) => {
        const req: AttachmentListRequest = {
          filter: {
            person_id: personId,
            kind: "product",
          },
        };
        return await store.listAttachments(req);
      },
      forceUpdateCreatedAt: store.forceUpdateCreatedAt,
    };
  }

  // Local state management
  const attachments = shallowRef<AttachmentResponse[]>(initialState.attachments || []);
  const currentAttachment = shallowRef<AttachmentResponse | null>(
    initialState.currentAttachment || null,
  );

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const attachmentQueryResult = useQuery<AttachmentDynamicQuery, AttachmentResponse>({
    queryFn: attachmentQuery,
    initialQuery: {},
  });

  const getAttachmentCount = computed(() => attachments.value.length);
  const getAttachmentById = computed(
    () => (id: number) => attachments.value.find((attachment) => attachment.id === id),
  );

  const addAttachment = (req: AttachmentAddRequest) =>
    performAsyncAction(async () => {
      const response = await attachmentAdd(req);
      if (response.data) {
        attachments.value.push(response.data);
      }
      return response.data;
    });

  const deleteAttachment = (req: AttachmentDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await attachmentDelete(req);
      if (response.data) {
        attachments.value = attachments.value.filter((attachment) => attachment.id !== req.id);
      }
      return response.data;
    });

  const getAttachment = (req: AttachmentGetRequest) =>
    performAsyncAction(async () => {
      const response = await attachmentGet(req);
      currentAttachment.value = response.data ?? null;
      return response.data;
    });

  const listAttachments = (req: AttachmentListRequest) =>
    performAsyncAction(async () => {
      // Check if req.filter.status is not set, then set it to 2
      const response = await attachmentList(req);
      attachments.value = response.data?.attachments ?? [];
      return response.data;
    });

  const updateAttachment = (req: AttachmentUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await attachmentUpdate(req);
      if (response.data) {
        const index = attachments.value.findIndex((attachment) => attachment.id === req.id);
        if (index !== -1) {
          attachments.value[index] = response.data;
        }
      }
      return response.data;
    });

  const forceUpdateCreatedAt = (req: AttachmenForcetUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await attachmentForceUpdateCreatedAt(req);
      if (response.data) {
        const index = attachments.value.findIndex((attachment) => attachment.id === req.id);
        if (index !== -1) {
          attachments.value[index] = response.data;
        }
      }
      return response.data;
    });

  async function queryAttachments(
    req: Partial<AttachmentDynamicQuery> = {},
    getCount: boolean = true,
  ) {
    try {
      await attachmentQueryResult.fetchQuery(req, getCount);
      attachments.value = [...attachmentQueryResult.items.value];
    } catch (error) {
      attachments.value = [];
    }
  }

  const getAttachmentsKindProduct = (personId: number) => {
    const req: AttachmentListRequest = {
      filter: {
        person_id: personId,
        kind: "product",
      },
    };
    return listAttachments(req);
  };

  return {
    attachments,
    currentAttachment,
    isLoading,
    error,
    addAttachment,
    deleteAttachment,
    getAttachment,
    listAttachments,
    updateAttachment,
    getAttachmentCount,
    getAttachmentById,
    getAttachmentsKindProduct,
    queryAttachments,
    attachmentQueryResult,
    forceUpdateCreatedAt,
  };
}
