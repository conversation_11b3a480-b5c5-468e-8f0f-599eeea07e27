import { computed, reactive, Ref, ref } from "vue";

import { DynamicQuery, DynamicQueryResponse, GenericResponse } from "@/api/bcare-types-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface QueryConfig<T extends DynamicQuery, R> {
  queryFn: (payload: T) => Promise<GenericResponse<DynamicQueryResponse>>;
  initialQuery?: Partial<T>;
}

export function useQuery<T extends DynamicQuery, R>(config: QueryConfig<T, R>) {
  const { queryFn, initialQuery = {} } = config;
  const items = ref<R[]>([]) as Ref<R[]>;
  const total = ref(0);
  const dynamicQuery = reactive<Partial<T>>({ ...initialQuery });

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const countAggregation = computed(() => ({
    field: "id",
    function: "COUNT",
    alias: "count",
    over: {}
  }));

  async function fetchQuery(req: Partial<T> = {}, getCount: boolean = true) {
    const payload = { ...dynamicQuery, ...req } as T;
    Object.assign(dynamicQuery, payload);

    return performAsyncAction(async () => {
      try {
        const [queryResponse, countResponse] = await Promise.all([
          queryFn(payload),
          getCount
            ? queryFn({
              ...payload,
              limit: 1,
              offset: 0,
              aggregations: [countAggregation.value],
            } as T)
            : Promise.resolve(null as any),
        ]);

        if (queryResponse.code === 0 && queryResponse.data) {
          items.value = queryResponse.data.result.rows as R[];
        } else {
          items.value = [];
        }

        if (getCount && countResponse && countResponse.code === 0 && countResponse.data) {
          total.value = countResponse.data.result.count || 0;
        }

        return queryResponse;
      } catch (e) {
        items.value = [];
        total.value = 0;
        throw e;
      }
    });
  }

  function getAggregationResult(alias: string): any {
    const response = items.value[0] as any;
    return response && response[alias];
  }

  return {
    items,
    total,
    dynamicQuery,
    fetchQuery,
    getAggregationResult,
    isLoading,
    error,
  };
}
