// src/composables/useBill.ts
import { storeToRefs } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  Bill,
  BillAddFromDealRequest,
  BillAddRequest,
  BillDeleteRequest,
  BillDynamicQuery,
  BillGetRequest,
  BillItem,
  BillItemAddFromAttachment,
  BillItemAddRequest,
  BillItemDeleteRequest,
  BillItemDynamicQuery,
  BillItemGetPaidRequest,
  BillItemGetPartiallyPaidRequest,
  BillItemGetRequest,
  BillItemListRequest,
  BillItemListResponse,
  BillItemResponse,
  BillItemUpdateRequest,
  BillListRequest,
  BillListResponse,
  BillResponse,
  BillUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  bill_itemAdd,
  bill_itemAddFromAttachment,
  bill_itemDelete,
  bill_itemGet,
  bill_itemGetPaid,
  bill_itemGetPartiallyPaid,
  bill_itemList,
  bill_itemQuery,
  bill_itemUpdate,
  bill_itemUpdateFromAttachment,
  billAdd,
  billAdd<PERSON>rom<PERSON>eal,
  billD<PERSON>te,
  billGet,
  billList,
  billQuery,
  billUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useQuery } from "@/hooks/useQuery-v3";
import { useBillStore } from "@/stores/bill-store-v2";

interface UseBillOptions {
  useStore?: boolean;
  initialState?: {
    bills?: BillResponse[];
    currentBill?: BillResponse | null;
    billItems?: BillItemResponse[];
    currentBillItem?: BillItemResponse | null;
    partiallyPaidItems?: BillItemResponse[];
  };
}

export default function useBill(options: UseBillOptions = {}) {
  const { useStore = false, initialState = {} } = options;

  if (useStore) {
    const store = useBillStore();
    const { bills, currentBill, billItems, currentBillItem, partiallyPaidItems, isLoading, error } =
      storeToRefs(store);

    return {
      bills,
      currentBill,
      billItems,
      currentBillItem,
      partiallyPaidItems,
      isLoading,
      error,
      addBill: store.addBill,
      addBillFromDeal: store.addBillFromDeal,
      deleteBill: store.deleteBill,
      getBill: store.getBill,
      listBills: store.listBills,
      updateBill: store.updateBill,
      getBillCount: computed(() => store.getBillCount),
      getBillById: (id: number) => store.getBillById(id),
      queryBills: store.queryBills,
      billQueryResult: store.billQueryResult,
      addBillItem: store.addBillItem,
      addBillItemFromAttachment: store.addBillItemFromAttachment,
      deleteBillItem: store.deleteBillItem,
      getBillItem: store.getBillItem,
      listBillItems: store.listBillItems,
      updateBillItem: store.updateBillItem,
      getBillItemCount: computed(() => store.getBillItemCount),
      getBillItemById: (id: number) => store.getBillItemById(id),
      queryBillItems: store.queryBillItems,
      billItemQueryResult: store.billItemQueryResult,
      getPartiallyPaidItems: store.getPartiallyPaidItems,
      getBillsByPersonId: async (personId: number) => {
        const req: BillListRequest = {
          filter: {
            person_id: personId,
          },
        };
        return await store.listBills(req);
      },
      getBillItemsByBillId: async (billId: number) => {
        const req: BillItemListRequest = {
          filter: {
            bill_id: billId,
          },
        };
        return await store.listBillItems(req);
      },
      getPaidItems: store.getPaidItems,
      updateBillItemFromAttachment: store.updateBillItemFromAttachment,
    };
  }

  const bills = shallowRef<BillResponse[]>(initialState.bills || []);
  const currentBill = shallowRef<BillResponse | null>(initialState.currentBill || null);
  const billItems = shallowRef<BillItemResponse[]>(initialState.billItems || []);
  const currentBillItem = shallowRef<BillItemResponse | null>(initialState.currentBillItem || null);
  const partiallyPaidItems = shallowRef<BillItemResponse[]>(initialState.partiallyPaidItems || []);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const billQueryResult = useQuery<BillDynamicQuery, BillResponse>({
    queryFn: billQuery,
    initialQuery: {},
  });

  const billItemQueryResult = useQuery<BillItemDynamicQuery, BillItemResponse>({
    queryFn: bill_itemQuery,
    initialQuery: {},
  });

  const getBillCount = computed(() => bills.value.length);
  const getBillById = computed(() => (id: number) => bills.value.find((bill) => bill.id === id));
  const getBillItemCount = computed(() => billItems.value.length);
  const getBillItemById = computed(
    () => (id: number) => billItems.value.find((item) => item.id === id),
  );

  const addBill = (req: BillAddRequest) =>
    performAsyncAction(async () => {
      const response = await billAdd(req);
      if (response.data) {
        bills.value.push(response.data);
      }
      return response.data;
    });

  const addBillFromDeal = (req: BillAddFromDealRequest) =>
    performAsyncAction(async () => {
      const response = await billAddFromDeal(req);
      if (response.data) {
        bills.value.push(response.data);
      }
      return response.data;
    });

  const deleteBill = (req: BillDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await billDelete(req);
      if (response.data) {
        bills.value = bills.value.filter((bill) => bill.id !== req.id);
      }
      return response.data;
    });

  const getBill = (req: BillGetRequest) =>
    performAsyncAction(async () => {
      const response = await billGet(req);
      currentBill.value = response.data ?? null;
      return response.data;
    });

  const listBills = (req: BillListRequest) =>
    performAsyncAction(async () => {
      const response = await billList(req);
      bills.value = response.data?.bills ?? [];
      return response.data;
    });

  const updateBill = (req: BillUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await billUpdate(req);
      if (response.data) {
        const index = bills.value.findIndex((bill) => bill.id === req.id);
        if (index !== -1) {
          bills.value[index] = response.data;
        }
      }
      return response.data;
    });

  const queryBills = async (req: Partial<BillDynamicQuery> = {}, getCount: boolean = true) => {
    try {
      await billQueryResult.fetchQuery(req, getCount);
      bills.value = [...billQueryResult.items.value];
    } catch (error) {
      bills.value = [];
    }
  };

  const getBillsByPersonId = (personId: number) => {
    const req: BillListRequest = {
      filter: {
        person_id: personId,
      },
    };
    return listBills(req);
  };

  const addBillItem = (req: BillItemAddRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemAdd(req);
      if (response.data) {
        billItems.value.push(response.data);
      }
      return response.data;
    });

  const addBillItemFromAttachment = (req: BillItemAddFromAttachment) =>
    performAsyncAction(async () => {
      const response = await bill_itemAddFromAttachment(req);
      if (response.data) {
        billItems.value.push(response.data);
      }
      return response.data;
    });

  const deleteBillItem = (req: BillItemDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemDelete(req);
      if (response.data) {
        billItems.value = billItems.value.filter((item) => item.id !== req.id);
      }
      return response.data;
    });

  const getBillItem = (req: BillItemGetRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemGet(req);
      currentBillItem.value = response.data ?? null;
      return response.data;
    });

  const listBillItems = (req: BillItemListRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemList(req);
      billItems.value = response.data?.bill_items ?? [];
      return response.data;
    });

  const updateBillItem = (req: BillItemUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemUpdate(req);
      if (response.data) {
        const index = billItems.value.findIndex((item) => item.id === req.id);
        if (index !== -1) {
          billItems.value[index] = response.data;
        }
      }
      return response.data;
    });

  const queryBillItems = async (
    req: Partial<BillItemDynamicQuery> = {},
    getCount: boolean = true,
  ) => {
    try {
      await billItemQueryResult.fetchQuery(req, getCount);
      billItems.value = [...billItemQueryResult.items.value];
    } catch (error) {
      billItems.value = [];
    }
  };

  const getBillItemsByBillId = (billId: number) => {
    const req: BillItemListRequest = {
      filter: {
        bill_id: billId,
      },
    };
    return listBillItems(req);
  };

  const getPartiallyPaidItems = (req: BillItemGetPartiallyPaidRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemGetPartiallyPaid(req);
      if (response.data) {
        partiallyPaidItems.value = response.data.bill_items ?? [];
      }
      return response.data;
    });

  const getPaidItems = (req: BillItemGetPaidRequest) =>
    performAsyncAction(async () => {
      const response = await bill_itemGetPaid(req);
      if (response.data) {
        billItems.value = response.data.bill_items ?? [];
      }
      return response.data;
    });

  const updateBillItemFromAttachment = (req: BillItemAddFromAttachment) =>
    performAsyncAction(async () => {
      const response = await bill_itemUpdateFromAttachment(req);
      if (response.data) {
        const index = billItems.value.findIndex((item) => item.attachment_id === req.attachment_id);
        if (index !== -1) {
          billItems.value[index] = response.data;
        }
      }
      return response.data;
    });

  return {
    bills,
    currentBill,
    billItems,
    currentBillItem,
    partiallyPaidItems,
    isLoading,
    error,
    getBillCount,
    getBillById,
    getBillItemCount,
    getBillItemById,
    addBill,
    addBillFromDeal,
    deleteBill,
    getBill,
    listBills,
    updateBill,
    addBillItem,
    addBillItemFromAttachment,
    deleteBillItem,
    getBillItem,
    listBillItems,
    updateBillItem,
    queryBills,
    billQueryResult,
    queryBillItems,
    billItemQueryResult,
    getBillsByPersonId,
    getBillItemsByBillId,
    getPartiallyPaidItems,
    getPaidItems,
    updateBillItemFromAttachment,
  };
}
