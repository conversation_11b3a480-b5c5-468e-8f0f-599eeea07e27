// src/hooks/useAttachmentDataMeta.ts

import { useDebounceFn } from "@vueuse/core";
import { reactive } from "vue";

import useAttachmentData from "@/hooks/useAttachmentData";

type MetaValue = Record<string, any>;

export default function useAttachmentMeta(metaKeys: Record<string, any>) {
  const attachmentData = useAttachmentData();
  const attachments = reactive<Record<number, Record<string, MetaValue>>>({});

  const createMetaValuesForAttachment = () =>
    Object.keys(metaKeys).reduce(
      (acc, key) => {
        acc[key] = {};
        return acc;
      },
      {} as Record<string, MetaValue>,
    );

  const syncMetaData = (attachmentId: number, metaKey: string) => {
    if (!(attachmentId in attachments)) {
      console.error(`Attachment ID ${attachmentId} is not initialized`);
      return;
    }
    const metaValue = attachments[attachmentId][metaKey];
    const value = JSON.stringify(metaValue);
    attachmentData.setAttachmentData({
      attachment_id: attachmentId,
      kind: "meta",
      key: metaKey,
      value: value,
    });
  };

  const debouncedSyncMetaData = useDebounceFn((attachmentId: number, metaKey: string) => {
    syncMetaData(attachmentId, metaKey);
  }, 1000);

  const loadInitialMetaData = async (attachmentId: number) => {
    const response = await attachmentData.getAttachmentData({
      attachment_id: attachmentId,
      kind: "meta",
    });

    if (response?.data) {
      Object.entries(response.data).forEach(([key, value]) => {
        if (typeof value === "string" && key in attachments[attachmentId]) {
          try {
            const parsedValue = JSON.parse(value);
            if (parsedValue && typeof parsedValue === "object") {
              attachments[attachmentId][key] = parsedValue as MetaValue;
            }
          } catch (e) {
            console.error(`Error parsing value for meta key ${key}:`, e);
          }
        }
      });
    }
  };

  const initializeAttachment = (attachmentId: number) => {
    if (!(attachmentId in attachments)) {
      attachments[attachmentId] = createMetaValuesForAttachment();
    }
  };

  const getMetaValues = (attachmentId: number) => {
    if (!(attachmentId in attachments)) {
      initializeAttachment(attachmentId);
    }
    return attachments[attachmentId];
  };

  //Todo: tách hàm này ra thành util @vhlam
  const parseNextOperations = (data: string): { id: number | string; name: string }[] => {
    try {
      const parsed = JSON.parse(data);
      return Object.entries(parsed).map(([id, name]) => ({
        id,
        name: name as string,
      }));
    } catch (e) {
      console.error("Error parsing next_operation:", e);
      return [];
    }
  };

  return {
    parseNextOperations,
    getMetaValues,
    syncMetaData,
    debouncedSyncMetaData,
    initializeAttachment,
    loadInitialMetaData,
  };
}
