// src/composables/useMetadataParser.ts

import { Ref, ref } from "vue";

import { AttachmentDataResponse, AttachmentResponse } from "@/api/bcare-types-v2";
import { MedicationData } from "@/api/extend-types";

type TeethDataType = Record<string, boolean>;
type MetaDataType = Record<string, any>;
type OperationType = any; // Định nghĩa type cụ thể cho operations nếu có

export default function useMetadataParser() {
  const teethDataByAttachment = ref<Record<number, TeethDataType>>({});
  const nextTeethDataByAttachment = ref<Record<number, TeethDataType>>({});
  const metaDataByAttachment = ref<Record<number, MetaDataType>>({});
  const participantsByAttachment = ref<Record<number, number[]>>({});
  const operationsByAttachment = ref<Record<number, OperationType>>({});
  const medicationsByAttachment = ref<Record<number, Record<string, MedicationData>>>({});

  const processAttachments = (attachments: AttachmentResponse[]) => {
    attachments.forEach((attachment) => {
      if (attachment && typeof attachment.id === "number") {
        processOneAttachment(attachment);
      }
    });
  };

  const processOneAttachment = (attachment: AttachmentResponse) => {
    if (!attachment || typeof attachment.id !== "number") {
      console.warn("Invalid attachment data");
      return null;
    }

    const metaData = getMetaData(attachment);
    if (metaData) {
      metaDataByAttachment.value[attachment.id] = metaData;
      const { teethData, nextTeethData } = extractTeethData(metaData);
      teethDataByAttachment.value[attachment.id] = teethData;
      nextTeethDataByAttachment.value[attachment.id] = nextTeethData;
    }
    participantsByAttachment.value[attachment.id] = getParticipants(attachment.data || []);
    operationsByAttachment.value[attachment.id] = getOperations(attachment.data || []);
    medicationsByAttachment.value[attachment.id] = getMedications(attachment.data || []);

    return {
      metaData: metaData,
      teethData: teethDataByAttachment.value[attachment.id] || {},
      nextTeethData: nextTeethDataByAttachment.value[attachment.id] || {},
      participants: participantsByAttachment.value[attachment.id] || [],
      operations: operationsByAttachment.value[attachment.id] || null,
      medications: medicationsByAttachment.value[attachment.id] || null,
    };
  };

  const getMetaData = (item: AttachmentResponse): Record<string, any> | null => {
    const metaData = item.data?.find((data) => data.kind === "meta");
    if (metaData?.data) {
      Object.entries(metaData.data).forEach(([key, value]) => {
        if (typeof value === "string") {
          try {
            metaData.data[key] = JSON.parse(value);
          } catch {
            console.log(`Unable to parse ${key}, keeping original value`);
          }
        }
      });
    }
    return metaData?.data ?? null;
  };

  const extractTeethData = (metaData: Record<string, any>) => {
    const teethData: TeethDataType = {};
    const nextTeethData: TeethDataType = {};

    const parseTeethData = (data: string | object): TeethDataType => {
      if (typeof data === "string") {
        try {
          return JSON.parse(data);
        } catch (error) {
          console.error("Error parsing teeth data:", error);
          return {};
        }
      } else if (typeof data === "object" && data !== null) {
        return data as TeethDataType;
      }
      return {};
    };

    if (metaData.teeth) {
      Object.assign(teethData, parseTeethData(metaData.teeth));
    }

    if (metaData.next_teeth) {
      Object.assign(nextTeethData, parseTeethData(metaData.next_teeth));
    }

    return { teethData, nextTeethData };
  };

  const getParticipants = (data: AttachmentDataResponse[]): number[] => {
    const participantOrder = ["bac_si", "phu_ta", "dieu_phoi", "x_quang"];
    const participantMap = new Map<string, number>();

    data.forEach((item) => {
      if (participantOrder.includes(item.kind) && item.participant_id !== undefined) {
        participantMap.set(item.kind, item.participant_id);
      }
    });

    return participantOrder.reduce<number[]>((result, kind) => {
      const participantId = participantMap.get(kind);
      return participantId !== undefined ? [...result, participantId] : result;
    }, []);
  };

  const getOperations = (data: AttachmentDataResponse[]): OperationType => {
    const operationData = data.find((item) => item.kind === "operations");
    return operationData?.data || null;
  };

  const getMedications = (data: AttachmentDataResponse[]): Record<string, MedicationData> => {
    const medicationData = data.find((item) => item.kind === "medication");
    return medicationData?.data || {};
  };

  const getSafeParticipants = (attachmentId: number): number[] => {
    return participantsByAttachment.value[attachmentId] || [];
  };

  const getSafeMedications = (attachmentId: number): Record<string, MedicationData> => {
    return medicationsByAttachment.value[attachmentId] || {};
  };

  const getDoctorById = (data: AttachmentDataResponse[]): number | null => {
    const doctorData = data.find(
      (item) =>
        item.kind === "bac_si" &&
        typeof item.participant_id === "number" &&
        item.participant_id > 0,
    );
    return doctorData?.participant_id ?? null;
  };

  const getTotalPrice = (item: AttachmentResponse): number => {
    if (!item) return 0;
    const price = item.price || 0;
    const quantity = item.quantity || 1;
    const discount = item.discount || 0;
    return price * quantity - discount;
  };

  return {
    teethDataByAttachment,
    nextTeethDataByAttachment,
    metaDataByAttachment,
    participantsByAttachment,
    operationsByAttachment,
    medicationsByAttachment,
    processAttachments,
    processOneAttachment,
    getMetaData,
    extractTeethData,
    getParticipants,
    getOperations,
    getMedications,
    getSafeParticipants,
    getSafeMedications,
    getDoctorById,
    getTotalPrice,
  };
}
