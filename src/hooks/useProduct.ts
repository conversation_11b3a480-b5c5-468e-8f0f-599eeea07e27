import { storeToRefs } from "pinia";
import { computed, onMounted, shallowRef } from "vue";

import type {
  Product,
  ProductAddRequest,
  ProductDeleteRequest,
  ProductGetRequest,
  ProductListRequest,
  ProductUpdateRequest,
} from "@/api/bcare-types-v2";
import { useProductStore } from "@/stores/product-store-v2";

interface UseProductOptions {
  autoLoad?: boolean;
}

export default function useProduct(options: UseProductOptions = { autoLoad: true }) {
  const productStore = useProductStore();
  const { isLoading, error, products } = storeToRefs(productStore);

  // Local state for filtered products, initialized as empty
  const filteredProducts = shallowRef<Product[]>([]);

  // Computed property for product count
  const productCount = computed(() => productStore.getProductCount);

  // Load all products
  const loadProducts = async () => {
    if (productStore.hasCachedData) {
      productStore.initializeFromCache();
    } else {
      await productStore.fetchAllProducts();
    }
    // Keep filteredProducts empty after loading
    filteredProducts.value = [];
  };

  // Auto-load products if autoLoad is true
  if (options.autoLoad) {
    onMounted(() => {
      loadProducts();
    });
  }

  // Add a new product
  const addProduct = async (req: ProductAddRequest) => {
    return await productStore.addProduct(req);
  };

  // Delete a product
  const deleteProduct = async (req: ProductDeleteRequest) => {
    await productStore.deleteProduct(req);
  };

  // Get a specific product
  const getProduct = async (req: ProductGetRequest) => {
    return await productStore.getProduct(req);
  };

  // List products with filtering
  const listProducts = async (req: ProductListRequest) => {
    const response = await productStore.listProducts(req);
    if (response?.products) {
      filteredProducts.value = response.products;
    }
    return response;
  };

  // Update a product
  const updateProduct = async (req: ProductUpdateRequest) => {
    return await productStore.updateProduct(req);
  };

  // Get a product by ID
  const getProductById = (id: number) => {
    return productStore.getProductById(id);
  };

  // Get multiple products by their IDs
  const getProductsByIds = (ids: number[]) => {
    return ids.map((id) => productStore.getProductById(id)).filter(Boolean) as Product[];
  };

  // Search products
  const searchProducts = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    filteredProducts.value = productStore.products.filter(
      (product) =>
        product.name.toLowerCase().includes(lowercaseQuery) ||
        product.code.toLowerCase().includes(lowercaseQuery),
    );
  };

  // Sort products
  const sortProducts = (key: keyof Product, order: "asc" | "desc" = "asc") => {
    filteredProducts.value = [...filteredProducts.value].sort((a, b) => {
      if (a[key] < b[key]) return order === "asc" ? -1 : 1;
      if (a[key] > b[key]) return order === "asc" ? 1 : -1;
      return 0;
    });
  };

  // Paginate products
  const paginateProducts = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredProducts.value.slice(startIndex, startIndex + pageSize);
  };

  const getProductNameById = (id: number): string => {
    const product = productStore.getProductById(id);
    return product ? product.name : "Unknown";
  };

  return {
    isLoading,
    error,
    products, // Exposed products list from the store
    filteredProducts,
    productCount,
    loadProducts,
    addProduct,
    deleteProduct,
    getProduct,
    listProducts,
    updateProduct,
    getProductById,
    getProductsByIds,
    searchProducts,
    sortProducts,
    paginateProducts,
    getProductNameById,
  };
}
