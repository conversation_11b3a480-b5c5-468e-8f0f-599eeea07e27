import { storeToRefs } from "pinia";
import type { WritableComputedRef } from "vue";
import { computed } from "vue";

import type {
  PersonAddRequest,
  PersonAssignmentAddRequest,
  PersonAssignmentDeleteRequest,
  PersonAssignmentUpdateRequest,
  PersonDeleteRequest,
  PersonGetRequest,
  PersonListRequest,
  PersonUpdateRequest,
} from "@/api/bcare-types-v2";
import { usePersonStore } from "@/stores/person-store-v2";

export default function usePerson() {
  const personStore = usePersonStore();
  const { persons, currentPerson, isLoading, error, totalRecords } = storeToRefs(personStore);

  const addPerson = async (req: PersonAddRequest) => {
    return await personStore.addPerson(req);
  };

  const deletePerson = async (req: PersonDeleteRequest) => {
    return await personStore.deletePerson(req);
  };

  const getPerson = async (req: PersonGetRequest, isUpdateCurrentPerson = true) => {
    req.include_relation = req.include_relation ?? true;
    return await personStore.getPerson(req, isUpdateCurrentPerson);
  };

  const listPersons = async (req: PersonListRequest) => {
    return await personStore.listPersons(req);
  };

  const updatePerson = async (req: PersonUpdateRequest) => {
    return await personStore.updatePerson(req);
  };

  const addPersonAssignment = async (req: PersonAssignmentAddRequest) => {
    return await personStore.addPersonAssignment(req);
  };

  const updatePersonAssignment = async (req: PersonAssignmentUpdateRequest) => {
    return await personStore.updatePersonAssignment(req);
  };

  const deletePersonAssignment = async (req: PersonAssignmentDeleteRequest) => {
    return await personStore.deletePersonAssignment(req);
  };

  const convertFormSubmissionsToPerson = async (formSubmissionIds: number[]) => {
    return await personStore.convertFormSubmissionsToPerson(formSubmissionIds);
  };

  const deleteFormSubmissions = async (formSubmissionIds: number[]) => {
    return await personStore.deleteFormSubmissions(formSubmissionIds);
  };

  const checkPersonIn = async (req: PersonGetRequest) => {
    return await personStore.checkPersonIn(req);
  };

  const getPersonCount = computed(() => personStore.getPersonCount);
  const getPersonById = (id: number) => personStore.getPersonById(id);

  return {
    persons,
    currentPerson,
    isLoading,
    error,
    totalRecords,
    addPerson,
    deletePerson,
    getPerson,
    listPersons,
    updatePerson,
    addPersonAssignment,
    updatePersonAssignment,
    deletePersonAssignment,
    getPersonCount,
    getPersonById,
    convertFormSubmissionsToPerson,
    deleteFormSubmissions,
    checkPersonIn,
  };
}
