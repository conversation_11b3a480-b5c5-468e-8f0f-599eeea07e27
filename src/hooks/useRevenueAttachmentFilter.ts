import { reactive, computed, ref, watch } from "vue";
import dayjs from "dayjs";
import type {
  AttachmentDynamicQuery,
  Filter,
  SortCriteria,
  AttachmentOperationReportRecord,
} from "@/api/bcare-types-v2";
import { attachmentQueryOperation } from "@/api/bcare-v2";
import { FilterOperator } from "@/api/bcare-enum";
import { useQuery } from "./useQuery-v3";
import { useDebounceFn } from "@vueuse/core";
import { useAuthStore } from "@/stores/auth-store";

interface AttachmentOperationsResponse extends AttachmentOperationReportRecord {
  id: number;
}

export interface RevenueAttachmentFilterState {
  startDate?: Date | string | null;
  endDate?: Date | string | null;
  personName?: string | null;
  doctorName?: string | null;
  serviceName?: string | null;
}

const defaultQuerySettings: Omit<AttachmentDynamicQuery, "table" | "filters" | "offset" | "limit"> =
  {
    group_by: [],
    aggregations: [],
    sort: [{ field: "treatment_date", order: "DESC" }],
    joins: [],
    export: false,
  };

const REVENUE_ATTACHMENT_TABLE_NAME = "attachment_operation_report_view";

export function useRevenueAttachmentFilter(initialFilters?: Partial<RevenueAttachmentFilterState>) {
  const initialQueryConfig: AttachmentDynamicQuery = {
    table: REVENUE_ATTACHMENT_TABLE_NAME,
    ...defaultQuerySettings,
    limit: 10,
    offset: 0,
    filters: [],
  };

  const {
    items,
    total,
    dynamicQuery: underlyingDynamicQueryFromUseQuery,
    fetchQuery: underlyingFetchQuery,
    isLoading,
    error,
  } = useQuery<AttachmentDynamicQuery, AttachmentOperationsResponse>({
    queryFn: attachmentQueryOperation,
    initialQuery: initialQueryConfig,
  });

  const filterState = reactive<RevenueAttachmentFilterState>({
    startDate: initialFilters?.startDate ?? null,
    endDate: initialFilters?.endDate ?? null,
    personName: initialFilters?.personName ?? "",
    doctorName: initialFilters?.doctorName ?? "",
    serviceName: initialFilters?.serviceName ?? "",
  });

  const initialFilterStateSnapshot = { ...filterState };

  // Get current user from auth store
  const authStore = useAuthStore();
  const currentUserId = computed(() => authStore.currentUser?.id);

  // Add fixed filter for current user
  const baseStaticFilters = computed<Filter[]>(() => {
    const filters: Filter[] = [];

    // Add user_id filter if we have a current user
    if (currentUserId.value) {
      filters.push({
        field: "doctor_id",
        operator: FilterOperator.EQ,
        value: currentUserId.value.toString(),
      });
    }

    return filters;
  });

  const searchQuery = ref(filterState.personName || "");

  const hasActiveFilters = computed(() => activeFiltersCount.value > 0);

  const activeFiltersCount = computed(() => {
    let count = 0;

    // Only count date filters if they're not set to today
    const today = dayjs().startOf('day');
    const todayEnd = dayjs().endOf('day');

    if (filterState.startDate && !dayjs(filterState.startDate).isSame(today, 'day')) {
      count++;
    }

    if (filterState.endDate && !dayjs(filterState.endDate).isSame(todayEnd, 'day')) {
      count++;
    }

    // Count other filters
    if (filterState.personName && filterState.personName.trim() !== "") count++;
    if (filterState.serviceName && filterState.serviceName.trim() !== "") count++;

    return count;
  });

  const debouncedSearch = useDebounceFn(() => {
    filterState.personName = searchQuery.value.trim();
    fetchFilteredData({ page: 1 });
  }, 500);

  const clearSearch = () => {
    searchQuery.value = "";
    filterState.personName = "";
    fetchFilteredData({ page: 1 });
  };

  watch(
    () => filterState.personName,
    (newValue) => {
      if (newValue !== searchQuery.value) {
        searchQuery.value = newValue || "";
      }
    },
    { immediate: true },
  );

  watch(
    () => filterState.personName,
    (newValue) => {
      if (newValue !== searchQuery.value) {
        searchQuery.value = newValue || "";
      }
    },
    { immediate: true },
  );

  const buildDynamicFilters = (): Filter[] => {
    const constructedFilters: Filter[] = [];

    if (filterState.startDate) {
      constructedFilters.push({
        field: "treatment_date",
        operator: FilterOperator.GTE,
        value: dayjs(filterState.startDate).startOf("day").toISOString(),
      });
    }
    if (filterState.endDate) {
      let endDateValue = dayjs(filterState.endDate);
      if (filterState.startDate && endDateValue.isBefore(dayjs(filterState.startDate))) {
        endDateValue = dayjs(filterState.startDate); // Đảm bảo endDate không trước startDate
      }
      constructedFilters.push({
        field: "treatment_date",
        operator: FilterOperator.LTE,
        value: endDateValue.endOf("day").toISOString(),
      });
    }

    if (filterState.personName && filterState.personName.trim() !== "") {
      constructedFilters.push({
        field: "full_name",
        operator: FilterOperator.LIKE,
        value: `%${filterState.personName.trim()}%`,
      });
    }

    if (filterState.serviceName && filterState.serviceName.trim() !== "") {
      constructedFilters.push({
        field: "product_title",
        operator: FilterOperator.LIKE,
        value: `%${filterState.serviceName.trim()}%`,
      });
    }

    return constructedFilters;
  };

  const fetchFilteredData = async (
    options: {
      page?: number;
      pageSize?: number;
      sort?: SortCriteria[];
    } = {},
    getCount: boolean = true,
  ) => {
    const currentPage = options.page || 1;
    const currentPageSize =
      options.pageSize || underlyingDynamicQueryFromUseQuery.limit || initialQueryConfig.limit!;
    const currentOffset = (currentPage - 1) * currentPageSize;

    const dynamicFilters = buildDynamicFilters();
    const allFilters = [...baseStaticFilters.value, ...dynamicFilters];

    const currentUnderlyingQuery = underlyingDynamicQueryFromUseQuery as AttachmentDynamicQuery;

    const queryToFetch: AttachmentDynamicQuery = {
      table: currentUnderlyingQuery.table!,
      selects: currentUnderlyingQuery.selects || defaultQuerySettings.selects,
      filters: allFilters,
      limit: currentPageSize,
      offset: currentOffset,
      sort: options.sort || currentUnderlyingQuery.sort || defaultQuerySettings.sort,
      group_by: currentUnderlyingQuery.group_by || defaultQuerySettings.group_by,
      aggregations: currentUnderlyingQuery.aggregations || defaultQuerySettings.aggregations,
      joins: currentUnderlyingQuery.joins || defaultQuerySettings.joins,
      export: currentUnderlyingQuery.export || defaultQuerySettings.export,
    };

    underlyingDynamicQueryFromUseQuery.limit = queryToFetch.limit;
    underlyingDynamicQueryFromUseQuery.offset = queryToFetch.offset;
    underlyingDynamicQueryFromUseQuery.sort = queryToFetch.sort;

    return underlyingFetchQuery(queryToFetch, getCount);
  };

  const resetFiltersAndRefetch = (newFilterOverrides?: Partial<RevenueAttachmentFilterState>, skipFetch = false) => {
    // Reset all filter state properties
    Object.keys(filterState).forEach((key) => delete (filterState as any)[key]);

    // Always set date to today when resetting filters
    const todayStart = dayjs().startOf("day").toDate();
    const todayEnd = dayjs().endOf("day").toDate();

    // Apply initial state with today's date
    Object.assign(filterState, {
      ...initialFilterStateSnapshot,
      startDate: todayStart,
      endDate: todayEnd,
    });

    // Apply any overrides if provided
    if (newFilterOverrides) {
      Object.assign(filterState, newFilterOverrides);
    }

    // Sync search query with filter state
    if (filterState.personName === "" && searchQuery.value !== "") {
      searchQuery.value = "";
    }

    // Only fetch if not skipped
    if (!skipFetch) {
      return fetchFilteredData({ page: 1 });
    }

    return Promise.resolve();
  };

  const revenueData = computed(() => items.value as AttachmentOperationsResponse[]);

  return {
    revenueData,
    total,
    isLoading,
    error,
    filterState,
    fetchFilteredData,
    resetFiltersAndRefetch,
    underlyingDynamicQuery: underlyingDynamicQueryFromUseQuery,
    searchQuery,
    hasActiveFilters,
    debouncedSearch,
    clearSearch,
    activeFiltersCount,
  };
}
