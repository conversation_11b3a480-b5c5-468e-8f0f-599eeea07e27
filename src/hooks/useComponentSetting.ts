import { computed, onMounted, ref, toRaw } from "vue";

import { useConfigurationsStore } from "@/stores/configuration-store";

export function useComponentSetting(initialSettingName?: string) {
  const store = useConfigurationsStore();
  const category = "component";

  // State management
  const syncStatus = ref(false);
  const errorMessage = ref<string | null>(null);
  const isInitialized = ref(false);
  const currentSetting = ref<{ [key: string]: any } | null>(null);
  const settingName = ref<string | undefined>(initialSettingName);

  // Thêm method refresh
  const refreshCurrentSetting = () => {
    if (settingName.value) {
      currentSetting.value = getSetting(settingName.value);
    }
  };

  // Initialize store and load initial setting if settingName is provided
  onMounted(async () => {
    if (!isInitialized.value) {
      await store.init({ category: "", name: "" });
      isInitialized.value = true;
      refreshCurrentSetting();
    }
  });

  const syncSetting = async (name: string, value: any) => {
    try {
      syncStatus.value = false;
      errorMessage.value = null;

      const requestValue = JSON.stringify(value);
      const success = await store.syncSetting(category, name, requestValue);
      syncStatus.value = success;

      if (!success) {
        throw new Error("Failed to sync setting");
      }

      // Sau khi sync thành công, refresh lại giá trị
      if (settingName.value && name === settingName.value) {
        refreshCurrentSetting();
      }
    } catch (error) {
      errorMessage.value = (error as Error).message || "Unknown error";
      console.error("Error syncing setting:", errorMessage.value);
    }
  };

  const getSetting = (name: string): { [key: string]: any } | null => {
    const setting = store.getSettingByKey(category, name);
    return setting ? setting.value : null;
  };

  const updateSettingName = (newSettingName: string) => {
    settingName.value = newSettingName;
    refreshCurrentSetting();
  };

  const currentSettingValue = computed(() => {
    return toRaw(currentSetting.value);
  });

  return {
    syncSetting,
    getSetting,
    syncStatus,
    errorMessage,
    currentSetting,
    currentSettingValue,
    settingName,
    updateSettingName,
    refreshCurrentSetting,
  };
}
