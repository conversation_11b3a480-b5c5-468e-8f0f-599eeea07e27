import { storeToRefs } from "pinia";
import { computed, Ref, ref, shallowRef } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import {
  BulkDeleteRequest,
  TaskAddRequest,
  TaskAssignment,
  TaskAssignmentAddRequest,
  TaskAssignmentDeleteRequest,
  TaskAssignmentDynamicQuery,
  TaskAssignmentUpdateRequest,
  TaskDeleteRequest,
  TaskDepartmentAddRequest,
  TaskDepartmentDeleteRequest,
  TaskDepartmentDynamicQuery,
  TaskDepartmentUpdateRequest,
  TaskDynamicQuery,
  TaskListRequest,
  TaskNoteAddRequest,
  TaskNoteDeleteRequest,
  TaskNoteUpdateRequest,
  TaskResponse,
  TaskUpdateRequest,
  UserResponse,
  UserShort,
} from "@/api/bcare-types-v2";
import {
  task_assignmentAdd,
  task_assignmentDelete,
  task_assignmentQuery,
  task_assignmentUpdate,
  task_departmentAdd,
  task_departmentDelete,
  task_departmentQuery,
  task_departmentUpdate,
  taskAdd,
  taskAddNote,
  taskBulkDelete,
  taskBulkUpdate,
  taskDelete,
  taskDeleteNote,
  taskList,
  taskQuery,
  taskUpdate,
  taskUpdateNote,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useTaskStore } from "@/stores/task-store";

interface UseTaskOptions {
  useStore?: boolean;
  autoLoad?: boolean;
  initialState?: {
    tasks?: TaskResponse[];
    currentTask?: TaskResponse | null;
  };
}

export const taskDynamicQuery: TaskDynamicQuery = {
  table: "task_serial_view",
  sort: [
    { field: "task_serial_view.start_date", order: "DESC" },
    { field: "task_serial_view.serial", order: "DESC" },
  ],
  limit: 10,
  offset: 0,
};

export default function useTask(options: UseTaskOptions = {}) {
  const { useStore = false, autoLoad = false, initialState = {} } = options;

  if (useStore) {
    const store = useTaskStore();
    const { tasks, task: currentTask, isLoading, error, total } = storeToRefs(store);
    const dynamicQuery = ref<TaskDynamicQuery>(taskDynamicQuery);

    const loadTasks = async () => {
      await store.fetchQueryTasks(dynamicQuery.value);
    };

    if (autoLoad) {
      loadTasks();
    }

    return {
      tasks,
      currentTask,
      isLoading,
      error,
      total,
      dynamicQuery,
      fetchQueryTasks: store.fetchQueryTasks,
      deleteTasks: store.deleteTasks,
      addTask: store.addTask,
      updateTask: store.updateTask,
      addTaskAssignment: store.addTaskAssignment,
      updateTaskAssignment: store.updateTaskAssignment,
      queryTaskAssignments: store.queryTaskAssignments,
      deleteTaskAssignment: store.deleteTaskAssignment,
      addTaskDepartmentAssignment: store.addTaskDepartmentAssignment,
      updateTaskDepartmentAssignment: store.updateTaskDepartmentAssignment,
      queryTaskDepartmentAssignments: store.queryTaskDepartmentAssignments,
      deleteTaskDepartmentAssignment: store.deleteTaskDepartmentAssignment,
      taskCount: computed(() => store.tasks.length),
      getTaskById: computed(() => (id: number) => store.tasks.find((task) => task.id === id)),
      loadTasks,
    };
  }

  // Local state management
  const tasks = shallowRef<TaskResponse[]>(initialState.tasks || []);
  const currentTask = shallowRef<TaskResponse | null>(initialState.currentTask || null);
  const total = ref(0);
  const dynamicQuery = ref<TaskDynamicQuery>(taskDynamicQuery);
  const totalPage = ref(0);

  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const fetchQueryTasks = (req: TaskDynamicQuery, isReset: boolean = false) =>
    performAsyncAction(async () => {
      const payload = isReset ? { ...taskDynamicQuery, ...req } : { ...dynamicQuery.value, ...req };
      dynamicQuery.value = payload;

      const [response, countResponse] = await Promise.all([
        taskQuery(payload),
        taskQuery({
          ...payload,
          sort: [],
          limit: 1,
          aggregations: [{ field: "id", function: "COUNT", alias: "count" }],
        }),
      ]);

      if (countResponse.data?.result?.count) {
        total.value = countResponse.data.result.count;
      }

      if (response.data?.result?.rows) {
        tasks.value = response.data.result.rows;
      } else {
        tasks.value = [];
      }

      return response.data;
    });

  const deleteTasks = (request: BulkDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await taskBulkDelete(request);
      if (response.code === 0) {
        tasks.value = tasks.value.filter((task) => !request.id_list.includes(task.id));
      }
      return response.code === 0;
    });

  const addTask = (request: TaskAddRequest) =>
    performAsyncAction(async () => {
      const response = await taskAdd(request);
      if (response.data) {
        tasks.value.push(response.data as TaskResponse);
      }
      return response.data;
    });

  const updateTask = (request: TaskUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await taskUpdate(request);
      if (response.data) {
        const index = tasks.value.findIndex((task) => task.id === request.id);
        if (index !== -1) {
          tasks.value[index] = { ...tasks.value[index], ...response.data };
        }
      }
      return response.data;
    });

  const addTaskAssignment = (assignment: TaskAssignmentAddRequest) =>
    performAsyncAction(async () => {
      const response = await task_assignmentAdd(assignment);
      return response.data;
    });

  const updateTaskAssignment = (assignment: TaskAssignmentUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await task_assignmentUpdate(assignment);
      return response.data;
    });

  const queryTaskAssignments = (query: TaskAssignmentDynamicQuery) =>
    performAsyncAction(async () => {
      const response = await task_assignmentQuery(query);
      return response.data;
    });

  const deleteTaskAssignment = (request: TaskAssignmentDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await task_assignmentDelete(request);
      return response.data;
    });

  const addTaskDepartmentAssignment = (assignment: TaskDepartmentAddRequest) =>
    performAsyncAction(async () => {
      const response = await task_departmentAdd(assignment);
      return response.data;
    });

  const updateTaskDepartmentAssignment = (assignment: TaskDepartmentUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await task_departmentUpdate(assignment);
      return response.data;
    });

  const queryTaskDepartmentAssignments = (query: TaskDepartmentDynamicQuery) =>
    performAsyncAction(async () => {
      const response = await task_departmentQuery(query);
      return response.data;
    });

  const deleteTaskDepartmentAssignment = (request: TaskDepartmentDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await task_departmentDelete(request);
      return response.data;
    });

  const loadTasks = async () => {
    await fetchQueryTasks(dynamicQuery.value);
  };

  if (autoLoad) {
    loadTasks();
  }

  const getUsersByRole = (assignments: TaskAssignment[], role: string) => {
    return assignments
      .filter((a) => a.role === role)
      .map((a) => a.user_id)
      .filter((user): user is number => user !== undefined);
  };

  const getComputedUsers = (
    primary: Ref<UserShort[] | UserResponse[]>,
    contributor: Ref<UserShort[] | UserResponse[]>,
    reviewer: Ref<UserShort[] | UserResponse[]>,
  ) => {
    return computed(() => [
      ...primary.value.map((user) => ({ user_id: user.id, role: "primary" })),
      ...contributor.value.map((user) => ({ user_id: user.id, role: "contributor" })),
      ...reviewer.value.map((user) => ({ user_id: user.id, role: "reviewer" })),
    ]);
  };

  const addNoteTask = async (request: TaskNoteAddRequest) => {
    const response = await taskAddNote(request);
    if (response.code === 0) {
      return response.data;
    }
    return response.data;
  };

  const updateNoteTask = async (request: TaskNoteUpdateRequest) => {
    const response = await taskUpdateNote(request);
    if (response.code === 0) {
      return true;
    }
    return false;
  };

  const deleteNoteTask = async (request: TaskNoteDeleteRequest) => {
    const response = await taskDeleteNote(request);
    if (response.code === 0) {
      return true;
    }
    return false;
  };

  function fetchTaskList(req: TaskListRequest) {
    return performAsyncAction(async () => {
      const response = await taskList(req);
      if (response.code === 0) {
        tasks.value = response.data?.tasks ?? [];
        total.value = response.data?.total ?? 1;
        totalPage.value = response.data?.total_page || 0;
        return response.data?.tasks;
      }
      tasks.value = [];
      return [];
    });
  }

  function deleteTask(req: TaskDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await taskDelete(req);
      return response.data;
    });
  }

  function checkDoneTask(id: number) {
    const request: TaskUpdateRequest = {
      id,
      state: TaskStateEnum.COMPLETED,
      modified: ["state"],
    };

    return performAsyncAction(async () => {
      const response = await taskUpdate(request);
      return response.data;
    });
  }

  return {
    tasks,
    currentTask,
    isLoading,
    error,
    total,
    dynamicQuery,
    fetchTaskList,
    deleteTasks,
    addTask,
    updateTask,
    addTaskAssignment,
    updateTaskAssignment,
    queryTaskAssignments,
    deleteTaskAssignment,
    addTaskDepartmentAssignment,
    updateTaskDepartmentAssignment,
    queryTaskDepartmentAssignments,
    deleteTaskDepartmentAssignment,
    getUsersByRole,
    getComputedUsers,
    addNoteTask,
    updateNoteTask,
    deleteNoteTask,
    deleteTask,
    checkDoneTask,
  };
}
