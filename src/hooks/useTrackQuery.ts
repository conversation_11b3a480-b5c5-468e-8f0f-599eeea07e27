import { computed } from "vue";

import { CommonStatus, FilterOperator } from "@/api/bcare-enum";
import { NewTrackReportResponse, TrackDynamicQuery, Filter } from "@/api/bcare-types-v2";
import { trackQuery } from "@/api/bcare-v2";
import { useAuthStore } from "@/stores/auth-store";

import { useQuery } from "./useQuery-v3";

export const initialTrackDynamicQuery: TrackDynamicQuery = {
  table: "new_tracks_report_view",
  selects: [],
  filters: [
    {
      field: "status",
      operator: "NEQ",
      value: CommonStatus.DELETED.toString(),
    },
  ],
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  aggregations: [],
  limit: 10,
  offset: 0,
};

// Helper function to process filters specific to Tracks
const processTrackFilters = (filters?: Filter[]): Filter[] => {
  if (!filters) return [];
  return (
    filters
      .map((filter: Filter) => {
        // Handle specific filters (like stage or user assignment if needed)
        if (filter.field === "deal_stage_id" && typeof filter.value === "object") {
          // Example: If filter.value is an array/object from a multi-select, extract necessary value
          // This depends on how PipelineStageSelect multiple mode returns value
          // For now, assuming it returns an array of IDs or a specific structure
          // Let's assume it returns an array of simple values or objects needing processing
          // Placeholder: return filter; // Adjust as per actual filter value structure
          return filter; // Keep as is for now, adjust if complex object
        }
        if (filter.field === "deal_assignment_user_ids" && typeof filter.value === "object") {
          // Example: Process user multi-assign filter if needed
          // Placeholder: return filter; // Adjust as per actual filter value structure
          return filter; // Keep as is for now, adjust if complex object
        }
        // Return other filters as is
        return filter;
      })
      // Ensure state != 'void' filter exists if not handled globally
      // .concat({ field: "state", operator: FilterOperator.NEQ, value: "" })
      .filter(Boolean)
  ); // filter(Boolean) to remove any null/undefined results from map
};

export function useTrackQuery() {
  const authStore = useAuthStore();

  // Query chính cho danh sách
  const { items, total, dynamicQuery, fetchQuery, isLoading, error } = useQuery<
    TrackDynamicQuery,
    NewTrackReportResponse
  >({
    queryFn: trackQuery,
    initialQuery: initialTrackDynamicQuery,
  });

  const tracks = computed(() => items.value as NewTrackReportResponse[]);

  const fetchTracks = (
    req: Partial<TrackDynamicQuery>,
    getCount: boolean = true,
    isExport: boolean = false,
  ) => {
    // Process the incoming filters first
    const processedReqFilters = processTrackFilters(req.filters);

    // Merge processed request filters with initial filters
    const finalFilters = [...(initialTrackDynamicQuery.filters || []), ...processedReqFilters];

    // Remove potential duplicates based on field and operator (optional, depends on needs)
    // const uniqueFilters = Array.from(new Map(finalFilters.map(f => [`${f.field}-${f.operator}`, f])).values());

    if (isExport) {
      const exportQuery: TrackDynamicQuery = {
        ...(initialTrackDynamicQuery as TrackDynamicQuery), // Base query
        ...req, // Include other req params like sort, selects
        filters: finalFilters, // Use final merged & processed filters
        export: true,
        limit: undefined, // No limit for export
        offset: undefined, // No offset for export
        table: "new_tracks_report_view", // Ensure table is set
      };
      // Call trackQuery directly for export
      return trackQuery(exportQuery);
    }

    // For regular fetch, use the processed & merged filters
    return fetchQuery(
      {
        ...req, // Include other req params like limit, offset, sort
        filters: finalFilters, // Use final merged & processed filters
      },
      getCount,
    );
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, { ...initialTrackDynamicQuery });
  };

  return {
    // data
    tracks,
    total,
    dynamicQuery,
    // fetch
    fetchTracks,
    // state
    isLoading,
    error,
    resetQuery,
  };
}
