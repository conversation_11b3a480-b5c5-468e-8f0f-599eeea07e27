import { computed, ref, shallowRef } from "vue";

import {
  AppointmentAddRequest,
  AppointmentDeleteRequest,
  AppointmentGetRequest,
  AppointmentListRequest,
  AppointmentResponse,
  AppointmentUpdateRequest,
  Person,
  PersonGetRequest,
  ScheduleRequest,
  ScheduleResponse,
} from "@/api/bcare-types-v2";
import {
  appointmentAdd,
  appointmentDelete,
  appointmentGetLasted,
  appointmentList,
  appointmentUpdate,
  personGetExpectedTasks,
  schedulegetWorkSchedule,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useNotiStore } from "@/stores/notification";
import { getLastTwoWords } from "@/utils/helper";

// Types
export interface DoctorSchedule {
  doctor_id: number;
  doctor_name: string;
  working_slots: WorkingSlot[];
}

export interface WorkingSlot {
  start_time: string;
  end_time: string;
}

export interface CalendarResource {
  id: string;
  title: string;
  working_time: WorkingSlot[];
}

export interface DoctorScheduleWithAppointments {
  doctor_id: number;
  doctor_name: string;
  department_id?: number;
  working_slots: Array<{
    start_time: string;
    end_time: string;
    appointments: Array<{
      id: number;
      start_time: string;
      end_time: string;
      status: number;
      notes?: string;
      extra_notes?: string;
      person?: Person;
    }>;
  }>;
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  displayTime: string;
  key: string;
}

export const AFTERNOON_SLOTS = [
  { key: "13:30-14:30", startHour: 13, startMinute: 30, endHour: 14, endMinute: 30 },
  { key: "14:30-15:30", startHour: 14, startMinute: 30, endHour: 15, endMinute: 30 },
  { key: "15:30-16:30", startHour: 15, startMinute: 30, endHour: 16, endMinute: 30 },
  { key: "16:30-17:30", startHour: 16, startMinute: 30, endHour: 17, endMinute: 30 },
  { key: "17:30-18:30", startHour: 17, startMinute: 30, endHour: 18, endMinute: 30 },
  { key: "18:30-19:00", startHour: 18, startMinute: 30, endHour: 19, endMinute: 0 },
];

export const defaultGetListAppointmentRequest: AppointmentListRequest = {
  page_size: 0,
  page: 0,
  filter: {
    person_id: 0,
    doctor_id: 0,
    status: 0,
    type: 0,
  },
  from_date: "",
  to_date: "",
  order_by: "start_time desc",
};

export const formDataDefault: AppointmentAddRequest = {
  start_time: "",
  end_time: "",
  person_id: 0,
  doctor_id: 0,
  status: 2,
  type: 1,
  notes: "",
  extra_notes: "",
};

export function useAppointment() {
  const notiStore = useNotiStore();
  // State management
  const appointmentsList = shallowRef<AppointmentResponse[]>([]);
  const doctorSchedules = shallowRef<Map<number, DoctorSchedule>>(new Map());
  const calendarResources = shallowRef<CalendarResource[]>([]);
  const totalItems = ref(0);
  const totalPages = ref(0);
  const appointmentCancelStatus = ref<number>(1);

  const { isLoading, error, performAsyncAction } = useAsyncAction({
    onError: (err) => {
      console.error("Appointment operation failed:", err);
      return undefined;
    },
  });

  // Add this new function
  const mapSchedulesWithAppointments = (
    schedules: ScheduleResponse[],
    appointments: AppointmentResponse[],
  ): DoctorScheduleWithAppointments[] => {
    // 1. Group schedules by doctor
    const doctorSchedules = schedules.reduce(
      (acc, schedule) => {
        const doctorId = schedule.user_id;
        if (!acc[doctorId]) {
          acc[doctorId] = {
            doctor_id: doctorId,
            department_id: schedule.user?.department_id,
            doctor_name: `BS. ${getLastTwoWords(schedule.user?.name || "")}`,
            working_slots: [],
          };
        }

        acc[doctorId].working_slots.push({
          start_time: schedule.start_time,
          end_time: schedule.end_time,
          appointments: [],
        });

        return acc;
      },
      {} as Record<number, DoctorScheduleWithAppointments>,
    );

    // 2. Map appointments into corresponding working slots
    appointments.forEach((appointment) => {
      const doctorSchedule = doctorSchedules[appointment.doctor_id];
      if (!doctorSchedule) return;
      if (appointment.status === appointmentCancelStatus.value) return;

      // Find the working slot that contains this appointment
      const slot = doctorSchedule.working_slots.find((slot) => {
        const slotStart = new Date(slot.start_time).getTime();
        const slotEnd = new Date(slot.end_time).getTime();
        const appointmentStart = new Date(appointment.start_time).getTime();
        const appointmentEnd = new Date(appointment.end_time).getTime();

        return appointmentStart >= slotStart && appointmentEnd <= slotEnd;
      });

      if (slot) {
        slot.appointments.push({
          id: appointment.id,
          start_time: appointment.start_time,
          end_time: appointment.end_time,
          status: appointment.status,
          notes: appointment.notes,
          extra_notes: appointment.extra_notes,
          person: appointment.person,
        });
      }
    });

    return Object.values(doctorSchedules);
  };

  // Enhanced API Methods
  const getWorkScheduleWithAppointments = async (request: ScheduleRequest) => {
    return performAsyncAction(async () => {
      const scheduleResponse = await schedulegetWorkSchedule(request);
      if (scheduleResponse.code !== 0 || !scheduleResponse.data?.schedules?.length) {
        return {
          schedules: [],
          schedulesWithAppointments: [],
        };
      }

      const appointmentResponse = await appointmentList({
        ...defaultGetListAppointmentRequest,
        from_date: request.from,
        to_date: request.to,
      });

      const schedulesWithAppointments = mapSchedulesWithAppointments(
        scheduleResponse.data.schedules,
        appointmentResponse?.data?.appointments || [],
      );

      return {
        schedules: scheduleResponse.data.schedules,
        schedulesWithAppointments,
      };
    });
  };

  // Computed
  const hasAppointments = computed(() => appointmentsList.value.length > 0);
  const isEmpty = computed(() => !isLoading.value && appointmentsList.value.length === 0);

  const fetchAppointmentList = (request: AppointmentListRequest) =>
    performAsyncAction(async () => {
      const response = await appointmentList(request);
      if (response.code === 0 && response.data) {
        appointmentsList.value = response.data.appointments;
        totalItems.value = response.data.total;
        totalPages.value = response.data.total_page;

        return {
          appointments: response.data.appointments,
          total: response.data.total,
          totalPage: response.data.total_page,
        };
      }
      return undefined;
    });

  const createAppointment = (request: AppointmentAddRequest) =>
    performAsyncAction(async () => {
      const response = await appointmentAdd(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Tạo mới lịch hẹn thành công",
          message: "",
        });
        return response.data;
      }
      return undefined;
    });

  const updateAppointment = (request: AppointmentUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await appointmentUpdate(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Chỉnh sửa lịch hẹn thành công",
          message: "",
        });
        return response.data;
      }
      return undefined;
    });

  const deleteAppointment = (request: AppointmentDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await appointmentDelete(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Xóa lịch hẹn thành công",
          message: "",
        });
        return true;
      }
      return false;
    });

  const getWorkSchedule = (request: ScheduleRequest) =>
    performAsyncAction(async () => {
      const response = await schedulegetWorkSchedule(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const fetchExpectTask = async (request: PersonGetRequest) => {
    return performAsyncAction(async () => {
      const response = await personGetExpectedTasks(request);
      if (response.code === 0) {
        return response.data?.expected_task?.split(";") ?? [];
      }
      return [];
    });
  };

  const getLatestAppointment = (request: AppointmentGetRequest) =>
    performAsyncAction(async () => {
      const response = await appointmentGetLasted(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  // Helper functions
  const clearAppointments = () => {
    appointmentsList.value = [];
    totalItems.value = 0;
    totalPages.value = 0;
  };

  const roundToMinute = (date: Date): Date => {
    const newDate = new Date(date);
    newDate.setSeconds(0);
    newDate.setMilliseconds(0);
    return newDate;
  };

  // Hàm tạo time slots
  const generateTimeSlots = (selectedDate: Date): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const date = new Date(selectedDate).toISOString().split("T")[0];

    // Morning slots: 8h-12h
    for (let hour = 8; hour < 12; hour++) {
      const startHour = hour.toString().padStart(2, "0");
      const endHour = (hour + 1).toString().padStart(2, "0");

      slots.push({
        startTime: `${date}T${startHour}:00:00+07:00`,
        endTime: `${date}T${endHour}:00:00+07:00`,
        displayTime: `${startHour}:00 - ${endHour}:00`,
        key: `${startHour}:00-${endHour}:00`,
      });
    }

    // Afternoon slots with new time ranges
    AFTERNOON_SLOTS.forEach((slot) => {
      const startHour = slot.startHour.toString().padStart(2, "0");
      const startMinute = slot.startMinute.toString().padStart(2, "0");
      const endHour = slot.endHour.toString().padStart(2, "0");
      const endMinute = slot.endMinute.toString().padStart(2, "0");

      slots.push({
        startTime: `${date}T${startHour}:${startMinute}:00+07:00`,
        endTime: `${date}T${endHour}:${endMinute}:00+07:00`,
        displayTime: `${startHour}:${startMinute} - ${endHour}:${endMinute}`,
        key: slot.key,
      });
    });

    return slots;
  };

  // Hàm map appointment vào slot
  const mapAppointmentToSlot = (appointment: AppointmentResponse): string => {
    const startTime = new Date(appointment.start_time);
    const startHour = startTime.getHours();
    const startMinute = startTime.getMinutes();

    // Morning slots
    if (startHour < 12) {
      return `${startHour.toString().padStart(2, "0")}:00-${(startHour + 1).toString().padStart(2, "0")}:00`;
    }

    // Special handling for common old time slots
    if (startHour === 17 && startMinute === 0) {
      return "16:30-17:30";
    } else if (startHour === 18 && startMinute === 0) {
      return "17:30-18:30";
    }

    // Find best matching afternoon slot
    const appointmentTimeInMinutes = startHour * 60 + startMinute;

    let bestSlot = null;
    let minTimeDifference = Infinity;

    for (const slot of AFTERNOON_SLOTS) {
      const slotStartInMinutes = slot.startHour * 60 + slot.startMinute;
      const slotEndInMinutes = slot.endHour * 60 + slot.endMinute;

      // Check if appointment starts exactly at slot start time
      if (appointmentTimeInMinutes === slotStartInMinutes) {
        return slot.key;
      }

      // Check if appointment is within slot time range
      if (
        appointmentTimeInMinutes >= slotStartInMinutes &&
        appointmentTimeInMinutes < slotEndInMinutes
      ) {
        return slot.key;
      }

      // If not exact match, find the closest slot
      const diffToSlotStart = Math.abs(appointmentTimeInMinutes - slotStartInMinutes);
      if (diffToSlotStart < minTimeDifference) {
        minTimeDifference = diffToSlotStart;
        bestSlot = slot;
      }
    }

    return bestSlot?.key || AFTERNOON_SLOTS[0].key;
  };

  // Hàm group appointments theo slot
  const groupAppointmentsBySlot = (
    appointments: AppointmentResponse[],
  ): Map<string, AppointmentResponse[]> => {
    const appointmentMap = new Map<string, AppointmentResponse[]>();

    appointments.forEach((appointment) => {
      const slotKey = mapAppointmentToSlot(appointment);

      if (!appointmentMap.has(slotKey)) {
        appointmentMap.set(slotKey, []);
      }

      appointmentMap.get(slotKey)?.push(appointment);
    });

    return appointmentMap;
  };

  return {
    // State
    appointmentsList,
    doctorSchedules,
    calendarResources,
    totalItems,
    totalPages,
    isLoading,
    error,

    // Computed
    hasAppointments,
    isEmpty,

    // Enhanced methods
    getWorkScheduleWithAppointments,

    // Original CRUD operations
    fetchAppointmentList,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    getWorkSchedule,
    fetchExpectTask,
    getLatestAppointment,
    // Helpers
    clearAppointments,
    roundToMinute,

    // New utility functions
    generateTimeSlots,
    mapAppointmentToSlot,
    groupAppointmentsBySlot,
    AFTERNOON_SLOTS,
  };
}
