import { createGlobalState } from "@vueuse/core";
import { computed, ref } from "vue";

import type { BillItemResponse, InstallmentResponse } from "@/api/bcare-types-v2";
import { AllocationItem, PaymentFormData } from "@/hooks/usePaymentFormState";

export const useRefundFormState = createGlobalState(() => {
  const formData = ref<PaymentFormData>({
    person_id: 0,
    payment_date: new Date().toISOString(),
    total_amount: 0,
    cash: 0,
    credit_card: 0,
    bank: 0,
    mpos: 0,
    momo: 0,
    state: "draft",
    bill_id: 0,
    modified: [],
  });

  const selectedAllocations = ref<AllocationItem[]>([]);
  const availableBillItems = ref<BillItemResponse[]>([]);
  const availableInstallments = ref<InstallmentResponse[]>([]);

  const totalAmount = computed(() => {
    return (
      (formData.value.cash || 0) +
      (formData.value.credit_card || 0) +
      (formData.value.bank || 0) +
      (formData.value.mpos || 0) +
      (formData.value.momo || 0)
    );
  });

  const totalPaidAmount = computed(() => {
    return selectedAllocations.value.reduce((sum, allocation) => {
      switch (allocation.type) {
        case "bill_item":
          return sum + (allocation.bill_item?.paid_amount || 0);
        case "installment_plan":
          return sum + (allocation.installment_plan?.paid_amount || 0);
        case "installment":
          return sum + (allocation.installment?.paid_amount || 0);
        default:
          return sum;
      }
    }, 0);
  });

  const isOverRefunded = computed(() => {
    return selectedAllocations.value.some((allocation) => {
      const refundAmount = Math.abs(allocation.amount);
      switch (allocation.type) {
        case "bill_item":
          return refundAmount > (allocation.bill_item?.paid_amount || 0);
        case "installment_plan":
          return refundAmount > (allocation.installment_plan?.paid_amount || 0);
        case "installment":
          return refundAmount > (allocation.installment?.paid_amount || 0);
        default:
          return false;
      }
    });
  });

  const totalPayableAmount = computed(() => {
    return selectedAllocations.value.reduce((sum, allocation) => {
      switch (allocation.type) {
        case "bill_item":
          return sum + (allocation.bill_item?.paid_amount || 0);
        case "installment_plan":
          return sum + (allocation.installment_plan?.paid_amount || 0);
        case "installment":
          return sum + (allocation.installment?.paid_amount || 0);
        default:
          return sum;
      }
    }, 0);
  });

  const allocatePayments = () => {
    let remainingToAllocate = totalAmount.value;
    selectedAllocations.value = selectedAllocations.value.map((allocation) => {
      let previouslyPaid = 0;

      switch (allocation.type) {
        case "bill_item":
          previouslyPaid = allocation.bill_item?.paid_amount || 0;
          break;
        case "installment_plan":
          previouslyPaid = allocation.installment_plan?.paid_amount || 0;
          break;
        case "installment":
          previouslyPaid = allocation.installment?.paid_amount || 0;
          break;
      }

      if (remainingToAllocate >= previouslyPaid) {
        remainingToAllocate -= previouslyPaid;
        return {
          ...allocation,
          amount: previouslyPaid,
          remaining: 0,
          note: allocation.note || "",
        };
      } else {
        const partialAmount = remainingToAllocate;
        remainingToAllocate = 0;
        return {
          ...allocation,
          amount: partialAmount,
          remaining: previouslyPaid - partialAmount,
          note: allocation.note || "",
        };
      }
    });
  };

  const resetState = () => {
    formData.value = {
      bill_id: 0,
      person_id: 0,
      payment_date: new Date().toISOString(),
      total_amount: 0,
      cash: 0,
      credit_card: 0,
      bank: 0,
      mpos: 0,
      momo: 0,
      state: "draft",
      modified: [],
    };
    selectedAllocations.value = [];
    availableBillItems.value = [];
    availableInstallments.value = [];
  };

  return {
    formData,
    selectedAllocations,
    availableBillItems,
    availableInstallments,
    totalAmount,
    totalPaidAmount,
    isOverRefunded,
    resetState,
    totalPayableAmount,
    allocatePayments,
  };
});
