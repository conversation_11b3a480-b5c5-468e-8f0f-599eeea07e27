import type { EventInput } from "@fullcalendar/core";
import dayjs from "dayjs";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted } from "vue";

import type { ScheduleResponse } from "@/api/bcare-types-v2";
import { useScheduleStore } from "@/stores/schedule-store";

import useUser from "./useUser";

interface UseScheduleOptions {
  autoLoad?: boolean;
}

export default function useSchedule(options: UseScheduleOptions = { autoLoad: true }) {
  const scheduleStore = useScheduleStore();
  const { schedules, isLoading, error } = storeToRefs(scheduleStore);
  const { getUserById } = useUser();

  // Computed for PrimeVue Dropdown options
  const statusOptions = computed(() => [
    { label: "Chờ duyệt", value: 0 },
    { label: "Đã duyệt", value: 1 },
    { label: "Từ chối", value: 2 },
  ]);

  const getScheduleById = (id: number): ScheduleResponse | undefined => {
    return schedules.value.find((schedule) => schedule.id === id);
  };

  const getUserNameById = (id: number): string => {
    return getUserById(id)?.name || "Chưa xác định";
  };

  const loadSchedules = async () => {
    if (schedules.value.length === 0) {
      const today = dayjs();
      await scheduleStore.fetchWorkSchedules({
        from: today.startOf("month").toISOString(),
        to: today.endOf("month").toISOString(),
      });
    }
  };

  // Transform schedule to calendar event
  const transformScheduleToEvent = (schedule: ScheduleResponse): EventInput => {
    return {
      id: schedule.id.toString(),
      title: getUserNameById(schedule.user_id),
      start: schedule.start_time,
      end: schedule.end_time,
      extendedProps: {
        userId: schedule.user_id,
        status: schedule.status,
        stage: schedule.stage,
      },
    };
  };

  // Transform schedules to calendar events
  const calendarEvents = computed(() => schedules.value.map(transformScheduleToEvent));

  // Get initial calendar view based on screen size
  const getInitialView = computed(() => {
    return "timeGridWeek";
  });

  onMounted(() => {
    if (options.autoLoad) {
      loadSchedules();
    }
  });

  onUnmounted(() => {
    // Cleanup if needed
  });

  return {
    // State
    schedules,
    isLoading,
    error,

    // Computed
    statusOptions,

    // Methods
    fetchWorkSchedules: scheduleStore.fetchWorkSchedules,
    fetchSchedules: scheduleStore.fetchSchedules,
    addSchedule: scheduleStore.addSchedule,
    updateSchedule: scheduleStore.updateSchedule,
    deleteSchedule: scheduleStore.deleteSchedule,
    getScheduleById,
    getUserNameById,
    loadSchedules,
    calendarEvents,
    getInitialView,
    transformScheduleToEvent,
  };
}
