import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";

import { type UserDataKind } from "@/api/bcare-enum";
import type {
  UserAddRequest,
  UserDataResponse,
  UserDeleteRequest,
  UserGetRequest,
  UserListRequest,
  UserListResponse,
  UserResponse,
  UserUpdateRequest,
} from "@/api/bcare-types-v2";
import { useUpload } from "@/hooks/useUpload";
import { useUserStore } from "@/stores/user-store-v2";
import { fuzzySearch } from "@/utils/string";

interface UseUsersOptions {
  autoLoad?: boolean;
}

export default function useUser(options: UseUsersOptions = { autoLoad: true }) {
  const userStore = useUserStore();
  const { isLoading, error, users, currentUser, paginatedUsers } = storeToRefs(userStore);

  // Local state for filtered users, initialized as empty
  const filteredUsers = ref<UserResponse[]>([]);
  const totalRecords = ref<number>(0);

  // Computed property for user count
  const userCount = computed(() => userStore.getUserCount);

  // Handle API response and update state
  const handleListResponse = (response: UserListResponse | null) => {
    if (response?.users) {
      paginatedUsers.value = response.users;
      totalRecords.value = response.total;
    }
    return response;
  };

  // New function to list users with pagination and filtering
  const listUsersWithTotal = async (req: UserListRequest) => {
    const response = await userStore.listUsers(req);
    return handleListResponse(response);
  };

  // Load all users
  const loadUsers = async () => {
    if (userStore.hasCachedData) {
      userStore.initializeFromCache();
    } else {
      await userStore.fetchAllUsers();
    }
    // Keep filteredUsers empty after loading
    filteredUsers.value = [];
  };

  // Autoload users if autoLoad is true
  if (options.autoLoad) {
    onMounted(() => {
      loadUsers();
    });
  }

  // Add a new user
  const addUser = async (req: UserAddRequest) => {
    return await userStore.addUser(req);
  };

  // Delete a user
  const deleteUser = async (req: UserDeleteRequest) => {
    await userStore.deleteUser(req);
  };

  // Get a specific user
  const getUser = async (req: UserGetRequest) => {
    return await userStore.getUser(req);
  };

  // List users with filtering
  const listUsers = async (req: UserListRequest) => {
    const response = await userStore.listUsers(req);
    if (response?.users) {
      filteredUsers.value = response.users;
    }
    return response;
  };

  // Update a user
  const updateUser = async (req: UserUpdateRequest) => {
    return await userStore.updateUser(req);
  };

  // Get a user by ID
  const getUserById = (id: number) => {
    return userStore.getUserById(id);
  };

  // Get multiple users by their IDs
  const getUsersByIds = (ids: number[]) => {
    return ids.map((id) => userStore.getUserById(id)).filter(Boolean) as UserResponse[];
  };

  // Get users by department IDs
  const getUsersByDepartmentIds = (departmentIds: number[]): UserResponse[] => {
    if (!departmentIds.length) return [];

    return userStore.users.filter((user) => departmentIds.includes(user.department_id));
  };

  // Search users
  const searchUsers = (query: string, departmentIds?: number[]): UserResponse[] => {
    if (!query.trim() && !departmentIds?.length) {
      filteredUsers.value = [];
      return [];
    }

    const results = userStore.users.filter((user) => {
      if (departmentIds?.length) {
        if (!departmentIds.includes(user.department_id)) {
          return false;
        }
      }

      if (!query.trim()) {
        return true;
      }

      const nameParts = user.name.split(" ");

      const searchableName =
        nameParts.length === 2
          ? nameParts[1] // Get only first name for 2-word names
          : nameParts.length > 2
            ? nameParts.slice(-2).join(" ") // Get last 2 parts for longer names
            : user.name; // For single word names

      const fullNameMatch = fuzzySearch(user.name, query);
      const partialNameMatch = fuzzySearch(searchableName, query);
      const usernameMatch = fuzzySearch(user.username, query);

      return fullNameMatch || partialNameMatch || usernameMatch;
    });

    filteredUsers.value = results;
    return results;
  };

  // Sort users
  const sortUsers = (key: keyof UserResponse, order: "asc" | "desc" = "asc") => {
    filteredUsers.value = [...filteredUsers.value].sort((a, b) => {
      if (a[key] < b[key]) return order === "asc" ? -1 : 1;
      if (a[key] > b[key]) return order === "asc" ? 1 : -1;
      return 0;
    });
  };

  // Paginate users
  const paginateUsers = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    return filteredUsers.value.slice(startIndex, startIndex + pageSize);
  };

  const getCurrentUser = () => {
    return currentUser.value;
  };

  // Add upload functionality
  const { uploadFile } = useUpload({
    maxFileSize: 2, // 2MB
    allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  });

  const uploadAvatar = async (file: File) => {
    try {
      if (!currentUser.value?.id) {
        throw new Error("No current user found");
      }

      const fileResponse = await uploadFile(file);

      await updateUser({
        id: currentUser.value.id,
        profile_image: fileResponse.path,
      });

      return fileResponse;
    } catch (error) {
      console.error("Avatar upload error:", error);
      throw new Error(error instanceof Error ? error.message : "Failed to upload avatar");
    }
  };

  // Add this helper function to get user data by kind
  const getUserDataByKind = (user: UserResponse, kind: UserDataKind) => {
    if (!user?.data) return null;
    return user.data.find((d: UserDataResponse) => d.kind === kind);
  };

  return {
    isLoading,
    error,
    users,
    paginatedUsers,
    filteredUsers,
    userCount,
    totalRecords,
    loadUsers,
    addUser,
    deleteUser,
    getUser,
    listUsers,
    listUsersWithTotal,
    updateUser,
    getUserById,
    getUsersByIds,
    getUsersByDepartmentIds,
    searchUsers,
    sortUsers,
    paginateUsers,
    getCurrentUser,
    uploadAvatar,
    getUserDataByKind,
  };
}
