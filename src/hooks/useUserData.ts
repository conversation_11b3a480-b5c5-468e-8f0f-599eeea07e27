import { storeToRefs } from "pinia";
import { computed, onMounted } from "vue";

import { type UserDataKind } from "@/api/bcare-enum";
import { UserDataResponse } from "@/api/bcare-types-v2";
import { useUserDataStore } from "@/stores/user-data-store";

interface UseUserDataOptions {
  autoLoad?: boolean;
  userId?: number;
}

export default function useUserData(options: UseUserDataOptions = { autoLoad: true }) {
  const userDataStore = useUserDataStore();
  const { isLoading, error, userData } = storeToRefs(userDataStore);

  // Load user data
  const loadUserData = async (userId: number) => {
    return await userDataStore.getUserData(userId);
  };

  // Autoload if enabled and userId is provided
  if (options.autoLoad && options.userId) {
    onMounted(() => {
      options.userId && loadUserData(options.userId);
    });
  }

  // Get user data with specific kind/key
  const getUserData = async (userId: number, kind?: string, key?: string) => {
    return await userDataStore.getUserData(userId, kind, key);
  };

  // Set user data
  const setUserData = async (
    userId: number,
    kind: string,
    key: string,
    value: string,
    data?: { [key: string]: any },
  ) => {
    return await userDataStore.setUserData(userId, kind, key, value, data);
  };

  // Clear user data
  const clearUserData = async (userId: number, kind?: string, key?: string) => {
    return await userDataStore.clearUserData(userId, kind, key);
  };

  // Helper to get specific user data value
  const getUserDataValue = computed(() => {
    return (userId: number, kind: string, key: string) => {
      return userData.value[userId]?.data?.[key];
    };
  });

  // Helper to check if user has specific data
  const hasUserData = computed(() => {
    return (userId: number, kind: string, key: string) => {
      return !!userData.value[userId]?.data?.[key];
    };
  });

  // Helper function to get user data by kind
  const getUserDataByKind = computed(() => {
    return (userId: number, kind: UserDataKind) => {
      if (!userData.value[userId]?.data) return null;
      return userData.value[userId].data.find((d: UserDataResponse) => d.kind === kind);
    };
  });

  return {
    isLoading,
    error,
    userData,
    loadUserData,
    getUserData,
    setUserData,
    clearUserData,
    getUserDataValue,
    hasUserData,
    getUserDataByKind,
  };
}
