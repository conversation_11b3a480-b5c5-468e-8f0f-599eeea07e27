// useRawQuerySorting.ts
import { reactive } from 'vue';

export interface SortOrder {
  field: string;
  direction: 'ASC' | 'DESC';
}

export interface UseSortingOptions {
  defaultOrders?: SortOrder[];
  maxOrders?: number;
  onOrdersChange?: (orders: SortOrder[]) => void;
}

export function useSorting(options: UseSortingOptions = {}) {
  const maxOrders = options.maxOrders || 3;

  // Khởi tạo orders với giá trị mặc định
  const orders = reactive<SortOrder[]>(
    options.defaultOrders?.length
      ? [...options.defaultOrders]
      : []
  );

  // Tạo order clause string
  function getOrderClause() {
    return orders
      .map(order => `${order.field} ${order.direction}`)
      .join(', ');
  }

  // Thêm hoặc cập nhật một trường sắp xếp
  function addOrUpdateSort(field: string) {
    const existingIndex = orders.findIndex(order => order.field === field);

    if (existingIndex >= 0) {
      // Nếu đã tồn tại, đảo ngược direction
      orders[existingIndex].direction = orders[existingIndex].direction === 'ASC' ? 'DESC' : 'ASC';

      // Nếu đây không phải là field đầu tiên, đưa nó lên đầu
      if (existingIndex > 0) {
        const order = orders.splice(existingIndex, 1)[0];
        orders.unshift(order);
      }
    } else {
      // Nếu chưa tồn tại, thêm vào đầu mảng với direction mặc định là DESC
      orders.unshift({ field, direction: 'DESC' });

      // Giới hạn số lượng trường sắp xếp
      if (orders.length > maxOrders) {
        orders.pop();
      }
    }

    // Gọi callback nếu có
    if (options.onOrdersChange) {
      options.onOrdersChange([...orders]);
    }
  }

  // Xóa một trường sắp xếp
  function removeSort(field: string) {
    const index = orders.findIndex(order => order.field === field);
    if (index >= 0) {
      orders.splice(index, 1);

      // Nếu không còn trường sắp xếp nào và có defaultOrders, thêm lại mặc định
      if (orders.length === 0 && options.defaultOrders?.length) {
        orders.push(...options.defaultOrders);
      }

      // Gọi callback nếu có
      if (options.onOrdersChange) {
        options.onOrdersChange([...orders]);
      }

      return true;
    }
    return false;
  }

  // Thay đổi hướng sắp xếp của một trường
  function toggleDirection(field: string) {
    const index = orders.findIndex(order => order.field === field);
    if (index >= 0) {
      orders[index].direction = orders[index].direction === 'ASC' ? 'DESC' : 'ASC';

      // Gọi callback nếu có
      if (options.onOrdersChange) {
        options.onOrdersChange([...orders]);
      }

      return true;
    }
    return false;
  }

  // Thiết lập thứ tự sắp xếp hoàn toàn mới
  function setOrders(newOrders: SortOrder[]) {
    // Giới hạn số lượng orders
    const limitedOrders = newOrders.slice(0, maxOrders);

    // Xóa tất cả orders hiện tại và thêm mới
    orders.splice(0, orders.length);
    limitedOrders.forEach(order => orders.push(order));

    // Gọi callback nếu có
    if (options.onOrdersChange) {
      options.onOrdersChange([...orders]);
    }
  }

  // Kiểm tra xem một trường có đang được sắp xếp không
  function isSorted(field: string) {
    return orders.some(order => order.field === field);
  }

  // Lấy hướng sắp xếp của một trường
  function getDirection(field: string) {
    const order = orders.find(order => order.field === field);
    return order ? order.direction : null;
  }

  // Lấy vị trí của một trường trong thứ tự sắp xếp
  function getSortPosition(field: string) {
    return orders.findIndex(order => order.field === field) + 1;
  }

  return {
    orders,
    getOrderClause,
    addOrUpdateSort,
    removeSort,
    toggleDirection,
    setOrders,
    isSorted,
    getDirection,
    getSortPosition
  };
}
