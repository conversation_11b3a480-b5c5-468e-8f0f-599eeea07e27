import type { Ref } from "vue";
import { computed, ref } from "vue";

import {
  Call,
  CallAddRequest,
  CallDeleteRequest,
  CallEndRequest,
  CallListRequest,
  CallUpdateFeedbackRequest,
  CallUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  callAccept,
  callAdd,
  callDelete,
  callEnd,
  callList,
  callUpdateFeedback,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useNotiStore } from "@/stores/notification";

export function useCall() {
  const notiStore = useNotiStore();
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State management
  const callsList: Ref<Call[]> = ref([]);
  const totalItems = ref(0);
  const totalPages = ref(0);

  // Computed
  const hasCalls = computed(() => callsList.value.length > 0);
  const isEmpty = computed(() => !isLoading.value && callsList.value.length === 0);

  const fetchCallList = (request: CallListRequest) =>
    performAsyncAction(async () => {
      const response = await callList(request);
      if (response.code === 0 && response.data) {
        callsList.value = response.data.calls;
        totalItems.value = response.data.total;
        totalPages.value = response.data.total_page;

        return {
          calls: response.data.calls,
          total: response.data.total,
          totalPage: response.data.total_page,
          totalInboundMiss: response.data.total_inbound_miss,
          totalInbound: response.data.total_inbound,
          totalOutbound: response.data.total_outbound,
          totalOutboundMiss: response.data.total_outbound_miss,
        };
      }
      return undefined;
    });

  const createCall = (request: CallAddRequest) =>
    performAsyncAction(async () => {
      const response = await callAdd(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const deleteCall = (request: CallDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await callDelete(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Xóa cuộc gọi thành công",
          message: "",
        });
        return true;
      }
      return false;
    });

  const updateCallFeedback = (request: CallUpdateFeedbackRequest) =>
    performAsyncAction(async () => {
      const response = await callUpdateFeedback(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Cập nhật đánh giá cuộc gọi thành công",
          message: "",
        });
        return response.data;
      }
      return undefined;
    });

  const acceptCall = (request: CallUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await callAccept(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const endCall = (request: CallEndRequest) =>
    performAsyncAction(async () => {
      const response = await callEnd(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const cleanupMediaTracks = async () => {
    try {
      // Stop all existing media tracks
      const allTracks = [] as MediaStreamTrack[];
      // Get all audio tracks from existing devices
      const audioDevices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = audioDevices.filter((device) => device.kind === "audioinput");

      for (const device of audioInputs) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: { deviceId: device.deviceId },
          });
          allTracks.push(...stream.getTracks());
        } catch (e) {
          console.warn(`Could not get stream for device: ${device.label}`, e);
        }
      }
      // Stop all tracks
      allTracks.forEach((track) => {
        track.stop();
        track.enabled = false;
      });
      // Double check cleanup
      setTimeout(async () => {
        const streams = await navigator.mediaDevices.getUserMedia({ audio: true });
        streams.getTracks().forEach((track) => {
          track.stop();
          track.enabled = false;
        });
      }, 100);
    } catch (error) {
      console.error("Error cleaning up media tracks:", error);
    }
  };

  const downloadRecording = async (url: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = url.split("/").pop() || "audio.mp3";
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  // Helper functions
  const clearCalls = () => {
    callsList.value = [];
    totalItems.value = 0;
    totalPages.value = 0;
  };

  return {
    // State
    callsList,
    totalItems,
    totalPages,
    isLoading,
    error,

    // Computed
    hasCalls,
    isEmpty,

    // CRUD operations
    fetchCallList,
    createCall,
    deleteCall,
    updateCallFeedback,
    acceptCall,
    endCall,
    cleanupMediaTracks,
    // Helpers
    clearCalls,
    downloadRecording,
  };
}
