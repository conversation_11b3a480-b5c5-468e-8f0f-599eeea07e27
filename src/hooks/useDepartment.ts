import { useStorage } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted } from "vue";

import type { DepartmentResponse } from "@/api/bcare-types-v2";
import { useDepartmentStore } from "@/stores/department-store";

interface UseDepartmentOptions {
  autoLoad?: boolean;
}

export default function useDepartment(options: UseDepartmentOptions = { autoLoad: true }) {
  const departmentStore = useDepartmentStore();
  const { departments, isLoading, error } = storeToRefs(departmentStore);

  const cachedDepartments = useStorage<DepartmentResponse[]>("departmentCache", []);

  // Computed for PrimeVue Dropdown options
  const departmentOptions = computed(() =>
    departments.value.map((dept) => ({
      label: dept.name,
      value: dept.id,
    })),
  );

  // Computed for position options (customize based on your needs)
  const positionOptions = computed(() => [
    { label: "Trưởng phòng", value: "manager" },
    { label: "<PERSON>ó phòng", value: "deputy" },
    { label: "Nhân viên", value: "staff" },
  ]);

  const fetchDepartments = async () => {
    await departmentStore.fetchDepartments({});
  };

  const getDepartmentById = (id: number): DepartmentResponse | undefined => {
    return departments.value.find((dept) => dept.id === id);
  };

  const getDepartmentNameById = (id: number): string => {
    return getDepartmentById(id)?.name || "Chưa xác định";
  };

  const loadDepartments = async () => {
    if (departments.value.length === 0) {
      if (cachedDepartments.value.length > 0) {
        departments.value = cachedDepartments.value;
      } else {
        await fetchDepartments();
        cachedDepartments.value = departments.value;
      }
    }
  };

  onMounted(() => {
    if (options.autoLoad) {
      loadDepartments();
    }
  });

  onUnmounted(() => {
    // Cleanup if needed
  });

  return {
    // State
    departments,
    isLoading,
    error,

    // Computed
    departmentOptions,
    positionOptions,

    // Methods
    fetchDepartments,
    getDepartmentById,
    getDepartmentNameById,
    loadDepartments,
  };
}
