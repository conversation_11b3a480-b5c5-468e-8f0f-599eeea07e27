// src/hooks/useNote.ts
import type { Ref } from "vue";
import { computed, ref } from "vue";

import {
  NoteAddRequest,
  NoteDeleteRequest,
  NoteListRequest,
  NoteR<PERSON>ponese,
  NoteUpdateRequest,
} from "@/api/bcare-types-v2";
import { noteAdd, noteDelete, noteList, noteUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export function useNote() {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State management
  const notes: Ref<NoteResponese[]> = ref([]);
  const totalItems = ref(0);
  const totalPages = ref(0);

  // Computed
  const hasNotes = computed(() => notes.value.length > 0);
  const isEmpty = computed(() => !isLoading.value && notes.value.length === 0);

  const fetchNoteList = (request: NoteListRequest) =>
    performAsyncAction(async () => {
      const response = await noteList(request);
      if (response.code === 0 && response.data) {
        notes.value = response.data.notes;
        totalItems.value = response.data.total;
        totalPages.value = response.data.total_page;

        return {
          notes: response.data.notes,
          total: response.data.total,
          totalPage: response.data.total_page,
        };
      }
      return undefined;
    });

  const createNote = (request: NoteAddRequest) =>
    performAsyncAction(async () => {
      const response = await noteAdd(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const updateNote = (request: NoteUpdateRequest) =>
    performAsyncAction(async () => {
      const response = await noteUpdate(request);
      if (response.code === 0) {
        return response.data;
      }
      return undefined;
    });

  const deleteNote = (request: NoteDeleteRequest) =>
    performAsyncAction(async () => {
      const response = await noteDelete(request);
      if (response.code === 0) {
        return true;
      }
      return false;
    });

  // Helper functions
  const clearNotes = () => {
    notes.value = [];
    totalItems.value = 0;
    totalPages.value = 0;
  };

  return {
    // State
    notes,
    totalItems,
    totalPages,
    isLoading,
    error,

    // Computed
    hasNotes,
    isEmpty,

    // CRUD operations
    fetchNoteList,
    createNote,
    updateNote,
    deleteNote,

    // Helpers
    clearNotes,
  };
}
