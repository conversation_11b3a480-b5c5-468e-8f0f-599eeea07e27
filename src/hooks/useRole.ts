import { useStorage } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted } from "vue";

import type { Role } from "@/api/bcare-types-v2";
import { useRoleStore } from "@/stores/role-store";

export default function useRole(options: { autoLoad?: boolean } = { autoLoad: true }) {
  const roleStore = useRoleStore();
  const { roles, isLoading, error } = storeToRefs(roleStore);

  const cachedRoles = useStorage<Role[]>("roleCache", []);

  // Computed
  const roleOptions = computed(() =>
    roles.value.map((role) => ({
      label: role.description || role.name,
      value: role.name,
    })),
  );

  const fetchRoles = async () => {
    await roleStore.fetchRoles();
  };

  const addRole = async (role: Role) => {
    return await roleStore.addRole(role);
  };

  const getRoleByName = (name: string): Role | undefined => {
    return roleStore.getRoleByName(name);
  };

  const getRoleDescription = (name: string): string => {
    return getRoleByName(name)?.description || "Chưa xác định";
  };

  const loadRoles = async () => {
    if (roles.value.length === 0) {
      if (cachedRoles.value.length > 0) {
        roles.value = cachedRoles.value;
      } else {
        await fetchRoles();
        cachedRoles.value = roles.value;
      }
    }
  };

  // Mounted hook
  onMounted(() => {
    if (options.autoLoad) {
      loadRoles();
    }
  });

  // Unmounted hook
  onUnmounted(() => {
    // Cleanup logic if needed
  });

  return {
    roles,
    roleOptions,
    isLoading,
    error,
    fetchRoles,
    addRole,
    getRoleByName,
    getRoleDescription,
    loadRoles,
  };
}
