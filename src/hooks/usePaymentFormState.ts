import { createGlobalState } from "@vueuse/core";
import { computed, ref } from "vue";

import type {
  BillItemResponse,
  InstallmentPlanResponse,
  InstallmentResponse,
} from "@/api/bcare-types-v2";

export interface PaymentFormData {
  person_id: number;
  payment_date: string;
  total_amount: number;
  cash: number;
  credit_card: number;
  bank: number;
  mpos: number;
  momo: number;
  state: "pending" | "completed" | "failed" | "draft";
  bill_id: number;
  modified: Array<"cash" | "credit_card" | "bank" | "mpos" | "momo">;
}

export interface AllocationItem {
  type: "bill_item" | "installment" | "installment_plan";
  bill_item?: BillItemResponse;
  installment?: InstallmentResponse;
  installment_plan?: InstallmentPlanResponse;
  amount: number;
  remaining?: number;
  isEditing?: boolean;
  note?: string;
}

export const usePaymentFormState = createGlobalState(() => {
  const formData = ref<PaymentFormData>({
    person_id: 0,
    payment_date: new Date().toISOString(),
    total_amount: 0,
    cash: 0,
    credit_card: 0,
    bank: 0,
    mpos: 0,
    momo: 0,
    state: "draft",
    bill_id: 0,
    modified: [],
  });

  const selectedAllocations = ref<AllocationItem[]>([]);
  const availableBillItems = ref<BillItemResponse[]>([]);
  const availableInstallments = ref<InstallmentResponse[]>([]);
  const selectedRefundAllocations = ref<AllocationItem[]>([]);

  const totalAmount = computed(() => {
    return (
      (formData.value.cash || 0) +
      (formData.value.credit_card || 0) +
      (formData.value.bank || 0) +
      (formData.value.mpos || 0) +
      (formData.value.momo || 0)
    );
  });

  const totalPayableAmount = computed(() => {
    return selectedAllocations.value.reduce((sum, allocation) => {
      if (allocation.type === "bill_item" && allocation.bill_item) {
        const previouslyPaid = allocation.bill_item.paid_amount || 0;
        const remainingToPay = allocation.bill_item.amount - previouslyPaid;
        return sum + remainingToPay;
      } else if (allocation.type === "installment" && allocation.installment) {
        const previouslyPaid = allocation.installment.paid_amount || 0;
        const remainingToPay = allocation.installment.amount - previouslyPaid;
        return sum + remainingToPay;
      }
      return sum;
    }, 0);
  });

  const shortageAmount = computed(() => {
    return Math.max(0, totalPayableAmount.value - totalAmount.value);
  });

  const isOverPaid = computed(() => {
    return totalAmount.value > totalPayableAmount.value;
  });

  const totalPaidAmount = computed(() => {
    return selectedAllocations.value.reduce((sum, allocation) => {
      switch (allocation.type) {
        case "bill_item":
          return sum + (allocation.bill_item?.paid_amount || 0);
        case "installment_plan":
          return sum + (allocation.installment_plan?.paid_amount || 0);
        case "installment":
          return sum + (allocation.installment?.paid_amount || 0);
        default:
          return sum;
      }
    }, 0);
  });

  const isOverRefunded = computed(() => {
    return selectedAllocations.value.some((allocation) => {
      const refundAmount = Math.abs(allocation.amount);
      switch (allocation.type) {
        case "bill_item":
          return refundAmount > (allocation.bill_item?.paid_amount || 0);
        case "installment_plan":
          return refundAmount > (allocation.installment_plan?.paid_amount || 0);
        case "installment":
          return refundAmount > (allocation.installment?.paid_amount || 0);
        default:
          return false;
      }
    });
  });

  const allocatePayments = () => {
    let remainingToAllocate = totalAmount.value;
    selectedAllocations.value = selectedAllocations.value.map((allocation) => {
      let previouslyPaid = 0;
      let totalAmount = 0;

      if (allocation.type === "bill_item" && allocation.bill_item) {
        previouslyPaid = allocation.bill_item.paid_amount || 0;
        totalAmount = allocation.bill_item.amount;
      } else if (allocation.type === "installment" && allocation.installment) {
        previouslyPaid = allocation.installment.paid_amount || 0;
        totalAmount = allocation.installment.amount;
      }

      const remainingToPay = totalAmount - previouslyPaid;
      if (remainingToAllocate >= remainingToPay) {
        remainingToAllocate -= remainingToPay;
        return {
          ...allocation,
          amount: remainingToPay,
          remaining: 0,
        };
      } else {
        const partialAmount = remainingToAllocate;
        remainingToAllocate = 0;
        return {
          ...allocation,
          amount: partialAmount,
          remaining: remainingToPay - partialAmount,
        };
      }
    });
  };

  const resetRefundState = () => {
    selectedRefundAllocations.value = [];
  };

  const resetBillState = () => {
    selectedAllocations.value = [];
    availableBillItems.value = [];
    availableInstallments.value = [];
  };

  const resetState = () => {
    formData.value = {
      person_id: 0,
      payment_date: new Date().toISOString(),
      total_amount: 0,
      cash: 0,
      credit_card: 0,
      bank: 0,
      mpos: 0,
      momo: 0,
      state: "draft",
      bill_id: 0,
      modified: [],
    };
    resetBillState();
    resetRefundState();
  };

  return {
    formData,
    selectedAllocations,
    availableBillItems,
    availableInstallments,
    totalAmount,
    totalPayableAmount,
    shortageAmount,
    isOverPaid,
    allocatePayments,
    resetState,
    totalPaidAmount,
    isOverRefunded,
    selectedRefundAllocations,
    resetRefundState,
    resetBillState,
  };
});
