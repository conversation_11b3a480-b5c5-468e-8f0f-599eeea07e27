import JsSIP from "jssip";
import { RTCSession } from "jssip/lib/RTCSession";
import { CallOptions } from "jssip/lib/UA";
import { computed, ref } from "vue";

import { useAsyncAction } from "@/composables/useAsyncAction";
import { addLeading<PERSON>ero } from "@/utils/helper";

// Types
export type RTCSessionApp = RTCSession & {
  roomId?: number;
  localMuted?: boolean;
  audioTag?: HTMLAudioElement;
  _automaticHold?: boolean;
  call_id?: string;
};

export interface PersonInfoCallType {
  name: string;
  address: string;
  phone: string;
  personId: number;
}

interface TimeType {
  callId: string;
  minutes: number;
  seconds: number;
  formatted: string;
}

interface RoomType {
  roomId: number;
  incomingInProgress: boolean;
}

export const CONSTRAINTS = {
  CALL_DIRECTION_OUTGOING: "outgoing",
  CALL_DIRECTION_INCOMING: "incoming",
};

type CallEventType = "new_call" | "confirmed" | "ended" | "failed" | "progress";
type CallbackFunction = (session: RTCSessionApp) => void;

export function useCallHandler() {
  // Core states
  const uaInit = ref(false);
  const isMuted = ref(false);
  const infoStartCall = ref<PersonInfoCallType>();
  const activeCalls = ref<Record<string, RTCSessionApp>>({});
  const activeRooms = ref<Record<string, RoomType>>({});
  const time = ref<Record<string, TimeType>>({});
  const currentActiveRoomId = ref<number>();

  // Event listeners
  const eventListeners = ref<Record<CallEventType, CallbackFunction[]>>({
    new_call: [],
    confirmed: [],
    ended: [],
    failed: [],
    progress: [],
  });

  let UA: JsSIP.UA;
  const { performAsyncAction } = useAsyncAction();

  // Computed
  const getValueActiveCalls = computed(() => Object.values(activeCalls.value));

  // Timer management
  const timers: Record<string, NodeJS.Timer> = {};

  const startCallTimer = (callId: string) => {
    time.value[callId] = { callId, minutes: 0, seconds: 0, formatted: "00:00" };

    timers[callId] = setInterval(() => {
      const currentTime = time.value[callId];
      if (!currentTime) return;

      currentTime.seconds++;
      if (currentTime.seconds === 60) {
        currentTime.minutes++;
        currentTime.seconds = 0;
      }

      currentTime.formatted = `${addLeadingZero(currentTime.minutes)}:${addLeadingZero(
        currentTime.seconds,
      )}`;
    }, 1000);
  };

  const stopCallTimer = (callId: string) => {
    if (timers[callId]) {
      clearInterval(timers[callId]);
      delete timers[callId];
    }
    if (time.value[callId]) {
      delete time.value[callId];
    }
  };

  // Core methods
  const init = async ({
    configuration,
    socketInterfaces,
  }: {
    configuration: {
      uri: string;
      password: string;
      session_timers?: boolean;
    };
    socketInterfaces: string[];
  }) => {
    return performAsyncAction(async () => {
      const sockets = socketInterfaces.map((sock) => new JsSIP.WebSocketInterface(sock));

      UA = new JsSIP.UA({
        ...configuration,
        sockets,
      });

      UA.on("newRTCSession", handleNewSession);
      UA.start();

      uaInit.value = true;
      console.log("init success");
      console.log(uaInit.value);
    });
  };

  const handleNewSession = ({ session, request }: { session: RTCSessionApp; request: any }) => {
    session.call_id =
      session.direction === CONSTRAINTS.CALL_DIRECTION_INCOMING && request.hasHeader("X-IVR-EXT")
        ? request.getHeader("X-IVR-EXT")
        : request.call_id;

    setupSessionListeners(session);
    triggerListener("new_call", session);
    addCall(session);
  };

  const setupSessionListeners = (session: RTCSessionApp) => {
    session.on("ended", () => {
      triggerListener("ended", session);
      removeCall(session.id);
      stopCallTimer(session.id);
      stopMediaTracks(session);
    });

    session.on("failed", () => {
      triggerListener("failed", session);
      removeCall(session.id);
      stopCallTimer(session.id);
      stopMediaTracks(session);
    });

    session.on("confirmed", () => {
      triggerListener("confirmed", session);
      startCallTimer(session.id);
    });

    session.on("progress", () => {
      triggerListener("progress", session);
    });
  };

  // Call control methods
  const doCall = ({ target, options = {} }: { target: string; options?: CallOptions }) => {
    console.log("doCall", uaInit.value);
    if (!uaInit.value) throw new Error("Run init action first");
    if (!target) throw new Error("Target must be passed");

    const call = UA.call(target, {
      mediaConstraints: { audio: true, video: false },
      sessionTimersExpires: 120,
      ...options,
    });

    setupAudioStream(call);
    return call;
  };

  const callAnswer = ({ callId }: { callId: string }) => {
    const session = activeCalls.value[callId];
    if (!session) return;

    session.answer({
      mediaConstraints: { audio: true, video: false },
    });
  };

  const callTerminate = (callId: string) => {
    const session = activeCalls.value[callId];
    if (!session) return;

    session.terminate();
  };

  // Helper methods
  const setupAudioStream = (call: RTCSessionApp) => {
    call.connection.addEventListener("addstream", async (event: any) => {
      const audio = document.createElement("audio") as HTMLAudioElement & {
        setSinkId: (deviceId: string) => Promise<void>;
      };
      audio.id = call.id;
      audio.className = "audioTag";
      audio.srcObject = event.stream;
      audio.play();
    });
  };

  const stopMediaTracks = (session: RTCSessionApp) => {
    if (session.connection?.getLocalStreams()) {
      session.connection.getLocalStreams().forEach((stream) => {
        stream.getTracks().forEach((track) => track.stop());
      });
    }
  };

  // Event management
  const subscribe = ({ type, callback }: { type: CallEventType; callback: CallbackFunction }) => {
    eventListeners.value[type].push(callback);
  };

  const triggerListener = (type: CallEventType, session: RTCSessionApp) => {
    eventListeners.value[type].forEach((callback) => callback(session));
  };

  // Call management
  const addCall = (session: RTCSessionApp) => {
    activeCalls.value[session.id] = session;
  };

  const removeCall = (sessionId: string) => {
    delete activeCalls.value[sessionId];
  };

  return {
    // State
    infoStartCall,
    uaInit,
    isMuted,
    activeCalls: getValueActiveCalls,
    time,
    activeRooms,

    // Methods
    init,
    doCall,
    callAnswer,
    callTerminate,
    subscribe,

    // Helper methods
    startCall: (data: PersonInfoCallType) => (infoStartCall.value = data),
    clearInfoStartCall: () => (infoStartCall.value = undefined),
  };
}
