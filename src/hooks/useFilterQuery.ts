import { debounce } from "lodash";
import { computed, readonly, ref } from "vue";

import { Filter, UserResponse } from "@/api/bcare-types-v2";
import { FilterOperator } from "@/api/bcare-enum";
import dayjs from "dayjs";

export interface FilterConfig {
  field: string;
  operator?: FilterOperator;
  valueTransform?: (value: any) => any;
  isDateRange?: boolean;
  isPayload?: boolean;
  isSelectUsers?: boolean;
}

const formatDate = (date: Date): string => {
  const datePart = dayjs(date).format("YYYY-MM-DD");
  return `${datePart}T00:00:00.000+07:00`;
};

const getNextDay = (date: Date): Date => {
  const nextDay = new Date(date);
  nextDay.setDate(nextDay.getDate() + 1);
  nextDay.setHours(0, 0, 0, 0);
  return nextDay;
};

const getDateFilterQuery = (fromDate: Date, toDate: Date | null, field: string): Filter[] => [
  {
    logic: "AND",
    field,
    operator: FilterOperator.GTE,
    value: `time(${formatDate(fromDate)})`,
  },
  {
    logic: "AND",
    field,
    operator: FilterOperator.LT,
    value: `time(${formatDate(getNextDay(toDate || fromDate))})`,
  },
];

const getSelectUsersFilterQuery = (users: UserResponse[], field?: string): Filter[] => {
  if (!users.length) return [];
  if (field === "task_serial_view") {
    return [
      {
        field: "task_serial_view.primary_users",
        operator: FilterOperator.JSON_CONTAINS_ANY,
        value: `[${users.map((user) => user.id).join(",")}]`,
      },
    ];
  }

  const prefixField = field ? `${field}.` : "";
  return [
    {
      field: `${prefixField}role`,
      operator: FilterOperator.EQ,
      value: "primary",
    },
    {
      logic: "OR",
      conditions: users.map((user) => ({
        field: `${prefixField}user_id`,
        operator: FilterOperator.EQ,
        value: user.id.toString(),
      })),
    },
  ];
};

export function useFilterQuery(
  applyFiltersCallback: (filters: any, shouldResetPagination?: boolean) => void,
  filterConfigs: Record<string, FilterConfig>,
) {
  const filters = ref<Record<string, any>>({});
  const currentFilterPayload = ref<Record<string, any>>({});

  Object.keys(filterConfigs).forEach((key) => {
    filters.value[key] = { value: null };
  });

  const applyFilters = debounce(() => {
    const appliedFilters: Record<string, any> = {
      filters: [],
    };

    Object.entries(filters.value).forEach(([key, { value }]) => {
      const config = filterConfigs[key];
      if ((value == null || value === "") && !config.isPayload) return;

      let filterValue: Filter | any;

      if (config.isDateRange) {
        filterValue = getDateFilterQuery(value[0], value[1], config.field);
      } else if (config.isSelectUsers) {
        filterValue = getSelectUsersFilterQuery(value, config.field);
      } else {
        filterValue = {
          field: config.field,
          operator: config.operator || FilterOperator.EQ,
          value: config.valueTransform ? config.valueTransform(value) : value,
          logic: "AND",
        };
      }

      if (config.isPayload) {
        appliedFilters[config.field] = filterValue?.value ?? "";
      } else {
        appliedFilters.filters.push(...(Array.isArray(filterValue) ? filterValue : [filterValue]));
      }
    });

    currentFilterPayload.value = appliedFilters;
    applyFiltersCallback(appliedFilters, true);
  }, 500);

  const computedFilters = computed({
    get: () => filters.value,
    set: (newFilters) => {
      filters.value = newFilters;
      applyFilters();
    },
  });

  return {
    filters: computedFilters,
    currentFilterPayload: readonly(currentFilterPayload),
  };
}

export { getDateFilterQuery };
