import { computed } from "vue";

import { TaskDynamicQuery, TaskResponse } from "@/api/bcare-types-v2";
import { taskQuery } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v2";

export const initialTaskDynamicQuery: TaskDynamicQuery = {
  table: "task",
  selects: [],
  filters: [],
  creator: "",
  primary: "",
  contributor: "",
  person: "",
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
};

export function useTaskQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error, getAggregationResult } =
    useQuery<TaskDynamicQuery>({
      queryFn: taskQuery,
      initialQuery: initialTaskDynamicQuery,
    });

  const tasks = computed(() => items.value as TaskResponse[]);

  const fetchTasks = (req: Partial<TaskDynamicQuery>, getCount: boolean = true) => {
    return fetchQuery(req, getCount);
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, initialTaskDynamicQuery);
  };

  const refetchTasks = () => {
    fetchTasks(dynamicQuery);
  };

  return {
    // data
    tasks,
    total,
    dynamicQuery,
    // fetch
    fetchTasks,
    refetchTasks,
    // state
    isLoading,
    error,
    resetQuery,
    // additional functionality
    getAggregationResult,
  };
}
