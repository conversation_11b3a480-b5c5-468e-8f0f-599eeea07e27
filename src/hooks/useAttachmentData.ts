// src/hooks/useAttachmentData.ts

import { storeToRefs } from "pinia";

import type {
  ClearAttachmentDataRequest,
  GetAttachmentDataRequest,
  SetAttachmentDataRequest,
} from "@/api/bcare-types-v2";
import { MedicationData } from "@/api/extend-types";
import { useAttachmentDataStore } from "@/stores/attachment-data-store";

export default function useAttachmentData() {
  const attachmentDataStore = useAttachmentDataStore();
  const { currentData, isLoading, error } = storeToRefs(attachmentDataStore);

  const clearAttachmentData = async (req: ClearAttachmentDataRequest) => {
    return await attachmentDataStore.clearAttachmentData(req);
  };

  const getAttachmentData = async (req: GetAttachmentDataRequest) => {
    return await attachmentDataStore.getAttachmentData(req);
  };

  const setAttachmentData = async (req: SetAttachmentDataRequest) => {
    return await attachmentDataStore.setAttachmentData(req);
  };

  const setParticipantByRole = async (
    attachmentId: number,
    roleName: string,
    participantId: number,
  ) => {
    let kind: string;
    switch (roleName.toLowerCase()) {
      case "bac_si":
        kind = "bac_si";
        break;
      case "phu_ta":
        kind = "phu_ta";
        break;
      case "dieu_phoi":
        kind = "dieu_phoi";
        break;
      case "x_quang":
        kind = "x_quang";
        break;
      default:
        throw new Error("Invalid role name");
    }

    // Nếu participantId = 0, clear data
    if (participantId === 0) {
      return await clearAttachmentData({
        attachment_id: attachmentId,
        kind: kind,
      });
    }

    // Nếu có participant, set data như bình thường
    return await setAttachmentData({
      attachment_id: attachmentId,
      kind: kind,
      participant_id: participantId,
    });
  };

  const setMedications = async (attachmentId: number, data: MedicationData[]) => {
    // Convert array to object with medication names as keys
    const medicationObject = data.reduce(
      (acc, med) => {
        acc[med.ten] = med;
        return acc;
      },
      {} as Record<string, MedicationData>,
    );

    // Nếu không có medication nào, clear data
    if (Object.keys(medicationObject).length === 0) {
      return await clearAttachmentData({
        attachment_id: attachmentId,
        kind: "medication",
      });
    }

    // Nếu có medication, set data như bình thường
    return await setAttachmentData({
      attachment_id: attachmentId,
      kind: "medication",
      data: medicationObject,
    });
  };

  return {
    currentData,
    isLoading,
    error,
    clearAttachmentData,
    getAttachmentData,
    setAttachmentData,
    setParticipantByRole,
    setMedications,
  };
}
