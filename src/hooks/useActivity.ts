import { computed } from "vue";

import { ActivityDynamicQuery, DynamicQueryResponse } from "@/api/bcare-types-v2";
import { activityQuery } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v2";

export const initialActivityDynamicQuery: ActivityDynamicQuery = {
  table: "activity_view",
  selects: [],
  filters: [],
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
};

export function useActivityQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error, getAggregationResult } =
    useQuery<ActivityDynamicQuery>({
      queryFn: activityQuery,
      initialQuery: initialActivityDynamicQuery,
      countField: "entity_id",
    });

  const activities = computed(() => items.value as DynamicQueryResponse[]);

  const fetchActivities = (req: Partial<ActivityDynamicQuery>, getCount: boolean = true) => {
    return fetchQuery(req, getCount);
  };

  const resetQuery = () => {
    Object.assign(dynamicQuery, initialActivityDynamicQuery);
  };

  const refetchActivities = () => {
    fetchActivities(dynamicQuery);
  };

  return {
    // data
    activities,
    total,
    dynamicQuery,
    // fetch
    fetchActivities,
    refetchActivities,
    // state
    isLoading,
    error,
    resetQuery,
    // additional functionality
    getAggregationResult,
  };
}
