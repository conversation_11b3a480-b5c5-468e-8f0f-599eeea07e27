import { computed, ref } from "vue";

import { AttachmentOperationReportRecord, DynamicQuery } from "@/api/bcare-types-v2";
import { attachmentQueryOperation } from "@/api/bcare-v2";

import { useQuery } from "./useQuery-v3";

interface AttachmentOperationsResponse extends AttachmentOperationReportRecord {
  id: number;
}

const initialAttachmentOperationsDynamicQuery: DynamicQuery = {
  table: "attachment_operation_report_view",
  selects: [],
  sort: [
    {
      field: "treatment_date",
      order: "ASC",
    },
  ],
  limit: 10,
  offset: 0,
};

export function useRevenueAttachmentQuery() {
  const { items, total, dynamicQuery, fetchQuery, isLoading, error } = useQuery<
    DynamicQuery,
    AttachmentOperationsResponse
  >({
    queryFn: attachmentQueryOperation,
    initialQuery: initialAttachmentOperationsDynamicQuery,
  });

  // Query for export
  const { fetchQuery: fetchExportQuery } = useQuery<DynamicQuery, AttachmentOperationsResponse>({
    queryFn: attachmentQueryOperation,
    initialQuery: initialAttachmentOperationsDynamicQuery,
  });

  const operations = computed(() => items.value as AttachmentOperationsResponse[]);

  const fetchOperations = (
    req: Partial<DynamicQuery>,
    getCount: boolean = true,
    isExport: boolean = false,
  ) => {
    if (isExport) {
      const exportQuery = {
        ...initialAttachmentOperationsDynamicQuery,
        ...req,
        export: true,
        limit: undefined,
        offset: undefined,
      };
      return fetchExportQuery(exportQuery, false);
    }
    return fetchQuery(req, getCount);
  };

  return {
    operations,
    total,
    dynamicQuery,
    fetchOperations,
    isLoading,
    error,
  };
}
