import { defineAsyncComponent } from "vue";
import { Icon } from "@/base-components/Lucide/Lucide.vue";

export interface TabItem {
  label: string;
  icon: Icon;
  key: string;
  subject: string;
  component: ReturnType<typeof defineAsyncComponent>;
}

export interface MobileTabItem {
  label: string;
  icon: string;
  key: string;
  subject: string;
  component: ReturnType<typeof defineAsyncComponent>;
}

export const tabList: TabItem[] = [
  {
    label: "Hóa đơn",
    key: "bill",
    subject: "customer-tab-bill",
    icon: "DollarSign",
    component: defineAsyncComponent(() => import("@/pages/customer/components/BillsTab/index.vue")),
  },
  {
    label: "Hoàn phí",
    key: "refund",
    subject: "customer-tab-refund",
    icon: "Redo2",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/RefundTab/index.vue"),
    ),
  },
  {
    label: "Khám",
    key: "examine",
    subject: "customer-tab-examine",
    icon: "FileAxis3d",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/MedicalExam/index.vue"),
    ),
  },
  {
    label: "Hình ảnh",
    key: "media",
    subject: "customer-tab-media",
    icon: "Image",
    component: defineAsyncComponent(() => import("@/pages/customer/components/MediaTab/index.vue")),
  },
  {
    label: "Kế hoạch",
    key: "plan",
    subject: "customer-tab-plan",
    icon: "CalendarClock",
    component: defineAsyncComponent(() => import("@/pages/customer/components/PlanTab/index.vue")),
  },
  {
    label: "Deals",
    key: "deals",
    subject: "customer-tab-deals",
    icon: "ShoppingCart",
    component: defineAsyncComponent(() => import("@/pages/customer/components/DealsTab/index.vue")),
  },
  {
    label: "Điều trị",
    key: "treatment",
    subject: "customer-tab-treatment",
    icon: "FilePlus",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/TreatmentTab/TreatmentTab.vue"),
    ),
  },
  {
    label: "Lịch hẹn",
    key: "appointment",
    subject: "customer-tab-appointment",
    icon: "CalendarClock",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/AppointmentTab/AppointmentTab.vue"),
    ),
  },
  {
    label: "Hoạt động",
    key: "activity",
    subject: "customer-tab-activity",
    icon: "MessageSquare",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/ActivityTab/index.vue"),
    ),
  },
];

export const mobilePersonTabs: MobileTabItem[] = [
  {
    label: "Hình ảnh",
    key: "media",
    subject: "customer-tab-media",
    icon: "pi pi-image",
    component: defineAsyncComponent(() => import("@/pages/customer/components/MediaTab/index.vue")),
  },
  {
    label: "Lịch hẹn",
    key: "appointment",
    subject: "customer-tab-appointment",
    icon: "pi pi-calendar",
    component: defineAsyncComponent(
      () => import("@/pages/persons/components/mobile/tabs/AppointmentTabMobile.vue"),
    ),
  },
  {
    label: "Điều trị",
    key: "treatment",
    subject: "customer-tab-treatment",
    icon: "pi pi-file-plus",
    component: defineAsyncComponent(
      () => import("@/pages/persons/components/mobile/tabs/TreatmentTabMobile.vue"),
    ),
  },
  {
    label: "Hóa đơn",
    key: "bill",
    subject: "customer-tab-bill",
    icon: "pi pi-dollar",
    component: defineAsyncComponent(() => import("@/pages/customer/components/BillsTab/index.vue")),
  },
  {
    label: "Hoàn phí",
    key: "refund",
    subject: "customer-tab-refund",
    icon: "pi pi-undo",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/RefundTab/index.vue"),
    ),
  },
  {
    label: "Khám",
    key: "examine",
    subject: "customer-tab-examine",
    icon: "pi pi-file-edit",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/MedicalExam/index.vue"),
    ),
  },

  {
    label: "Kế hoạch",
    key: "plan",
    subject: "customer-tab-plan",
    icon: "pi pi-calendar-plus",
    component: defineAsyncComponent(() => import("@/pages/customer/components/PlanTab/index.vue")),
  },
  {
    label: "Deals",
    key: "deals",
    subject: "customer-tab-deals",
    icon: "pi pi-shopping-cart",
    component: defineAsyncComponent(() => import("@/pages/customer/components/DealsTab/index.vue")),
  },

  {
    label: "Hoạt động",
    key: "activity",
    subject: "customer-tab-activity",
    icon: "pi pi-comments",
    component: defineAsyncComponent(
      () => import("@/pages/customer/components/ActivityTab/index.vue"),
    ),
  },
];
