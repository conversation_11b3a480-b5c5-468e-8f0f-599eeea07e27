export const drawerPT = {
  root: ({ props }: { props: { position: string } }) => ({
    class: [
      "border-none shadow-xl",
      {
        "h-[80vh]": props.position === "bottom",
      },
    ],
  }),
  header: {
    class: "p-4 border-b border-surface-200 dark:border-surface-700",
  },
  title: {
    class: "text-lg font-semibold text-surface-800 dark:text-surface-0/80",
  },
  closeButton: {
    class:
      "w-4 h-4 rounded-full text-surface-500 dark:text-surface-400 hover:text-surface-700 dark:hover:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-800/50 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 transition-colors duration-150 ease-in-out",
  },
  closeIcon: {
    class: "w-4 h-4",
  },
  content: {
    class: "p-4 overflow-y-auto",
  },
  footer: {
    class:
      "p-4 border-t border-surface-200 dark:border-surface-700 bg-surface-50 dark:bg-surface-800 flex items-center justify-end gap-2",
  },
};

export const dialogPT = {
  header: {
    class: "p-4 border-b border-surface-200 dark:border-surface-700 rounded-t-lg",
  },
  title: {
    class: "text-lg font-semibold text-surface-800 dark:text-surface-0/80",
  },
  headerIcons: {
    class: "flex items-center",
  },
  closeButton: {
    class: [
      "w-8 h-8 rounded-full text-surface-500 dark:text-surface-400",
      "hover:text-surface-700 dark:hover:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-800/50",
      "flex items-center justify-center",
      "focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400",
      "transition-colors duration-150 ease-in-out",
      "ml-2",
    ],
  },
  closeIcon: {
    class: "w-4 h-4",
  },
  content: ({ state, props }: { state: any; props: any }) => ({
    class: [
      "p-4",
      "overflow-y-auto",
      {
        "pt-0": props.showHeader === false,
      },
    ],
  }),
  footer: {
    class:
      "p-4 border-t border-surface-200 dark:border-surface-700 bg-surface-50 dark:bg-surface-800 rounded-b-lg flex items-center justify-end gap-2",
  },
  mask: ({ props }: { props: { modal: boolean } }) => ({
    class: [
      {
        "bg-black/40": props.modal,
        "backdrop-blur-sm": props.modal,
      },
    ],
  }),
};
