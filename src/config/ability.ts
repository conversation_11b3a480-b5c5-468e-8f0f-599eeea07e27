import { Ability, AbilityBuilder } from "@casl/ability";

export type AppAbility = Ability<[string, string]>;

const rolePermissions = {
  admin: [{ action: "manage", subject: "all" }],

  marketing: [
    // Route permissions
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    // Tab permissions
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-phone" },
  ],

  sale_admin: [
    // Route permissions
    { action: "read", subject: "top-menu-task" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    // Tab permissions
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-phone" },
    { action: "read", subject: "customer-tab-deals" },
  ],

  sale_online_telesales: [
    // Route permissions
    { action: "read", subject: "top-menu-task" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    { action: "read", subject: "top-menu-call-center" },
    { action: "read", subject: "top-menu-new-tracks" },
    // Tab permissions
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-phone" },
    { action: "read", subject: "customer-tab-deals" },
  ],

  cskh: [
    // Route permissions
    { action: "read", subject: "top-menu-task" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    { action: "read", subject: "top-menu-call-center" },
    // Tab permissions
    { action: "read", subject: "customer-tab-bill" },
    { action: "read", subject: "customer-tab-refund" },
    { action: "read", subject: "customer-tab-treatment" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-tab-deals" },
    { action: "read", subject: "customer-phone" },
  ],

  le_tan: [
    // Route permissions
    { action: "read", subject: "top-menu-dashboard" },
    { action: "read", subject: "top-menu-dashboard-updental" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    { action: "read", subject: "top-menu-statistics" },
    { action: "read", subject: "top-menu-revenue" },
    { action: "read", subject: "top-menu-revenue-attachment" },
    { action: "read", subject: "top-menu-new-tracks" },

    // Tab permissions
    { action: "read", subject: "customer-tab-bill" },
    { action: "read", subject: "customer-tab-refund" },
    { action: "read", subject: "customer-tab-examine" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-plan" },
    { action: "read", subject: "customer-tab-deals" },
    { action: "read", subject: "customer-tab-treatment" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-phone" },
  ],

  sale_offline: [
    // Route permissions
    { action: "read", subject: "top-menu-dashboard" },
    { action: "read", subject: "top-menu-dashboard-updental" },
    { action: "read", subject: "top-menu-task" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    { action: "read", subject: "top-menu-appointments-offline" },
    { action: "read", subject: "top-menu-new-tracks" },
    // Tab permissions
    { action: "read", subject: "customer-tab-bill" },
    { action: "read", subject: "customer-tab-refund" },
    { action: "read", subject: "customer-tab-examine" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-plan" },
    { action: "read", subject: "customer-tab-deals" },
    { action: "read", subject: "customer-tab-treatment" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-phone" },
  ],

  phu_ta: [
    // Route permissions
    { action: "read", subject: "top-menu-dashboard" },
    { action: "read", subject: "top-menu-dashboard-updental" },
    // Tab permissions
    { action: "read", subject: "customer-tab-examine" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-plan" },
    { action: "read", subject: "customer-tab-treatment" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-phone" },
  ],

  x_quang: [
    // Route permissions
    { action: "read", subject: "top-menu-dashboard" },
    { action: "read", subject: "top-menu-dashboard-updental" },
    { action: "read", subject: "top-menu-appointments-doctor" },
    // Tab permissions
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-phone" },
  ],

  bac_si: [
    // Route permissions
    { action: "read", subject: "top-menu-appointment-of-doctor" },
    { action: "read", subject: "top-menu-revenue-attachment-doctor" },
    // Tab permissions
    { action: "read", subject: "customer-tab-examine" },
    { action: "read", subject: "customer-tab-media" },
    { action: "read", subject: "customer-tab-treatment" },
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-activity" },
    { action: "read", subject: "customer-tab-plan" },
  ],

  ke_toan: [
    // Route permissions
    { action: "read", subject: "top-menu-statistics" },
    { action: "read", subject: "top-menu-revenue" },
    { action: "read", subject: "top-menu-customer-list" },
    { action: "read", subject: "top-menu-revenue-attachment" },

    // Tab permissions
    { action: "read", subject: "customer-tab-bill" },
    { action: "read", subject: "customer-tab-refund" },
    { action: "read", subject: "customer-tab-deals" },
    { action: "read", subject: "customer-phone" },
  ],

  bao_tri: [
    // Route permissions
    { action: "read", subject: "top-menu-appointments-doctor" },
    // Tab permissions
    { action: "read", subject: "customer-tab-appointment" },
    { action: "read", subject: "customer-tab-media" },
  ],
};

// Define type for extra permissions structure
type Permission = { action: string; subject: string };
type RolePermissionMap = Record<string, Permission[]>;
type PositionPermissionMap = Record<string, RolePermissionMap>;

// Add position-specific permissions
const positionExtraPermissions: PositionPermissionMap = {
  deputy: {
    phu_ta: [
      { action: "read", subject: "top-menu-appointments-doctor" },
      { action: "read", subject: "top-menu-appointments-offline" },
    ],
  },
};

// Cache role permissions
const rolePermissionsCache = new Map<string, AppAbility>();

export function defineAbility(roles: string[], departmentPosition?: string) {
  // Create cache key
  const cacheKey = `${roles.sort().join(",")}_${departmentPosition || ""}`;

  // Check cache
  if (rolePermissionsCache.has(cacheKey)) {
    return rolePermissionsCache.get(cacheKey)!;
  }

  const { can, build } = new AbilityBuilder<AppAbility>(Ability);

  roles.forEach((role) => {
    const permissions = rolePermissions[role as keyof typeof rolePermissions] || [];
    permissions.forEach(({ action, subject }) => {
      can(action, subject);
    });

    // Apply position-specific permissions for this role
    if (
      departmentPosition &&
      departmentPosition in positionExtraPermissions &&
      role in positionExtraPermissions[departmentPosition]
    ) {
      const extraPermissions = positionExtraPermissions[departmentPosition][role];
      extraPermissions.forEach(({ action, subject }) => {
        can(action, subject);
      });
    }
  });

  const ability = build();
  rolePermissionsCache.set(cacheKey, ability);
  return ability;
}
