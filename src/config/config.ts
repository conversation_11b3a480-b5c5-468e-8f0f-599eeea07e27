interface ClinicConfig {
  name: string;
  address: string;
  phone: string;
  email: string;
  offline_pipeline_id: number;
  after_payment_stage_id: number;
}

interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  appTitle: string;
  appVersion: string;
  appBrandName: string;
  appLogo: string;
  clinic: ClinicConfig;
}

const config: AppConfig = {
  apiUrl: import.meta.env.VITE_API_URL,
  wsUrl: import.meta.env.VITE_WS_URL,
  appTitle: import.meta.env.VITE_APP_TITLE,
  appVersion: import.meta.env.VITE_APP_VERSION,

  appBrandName: import.meta.env.VITE_APP_BRAND_NAME,
  appLogo: import.meta.env.VITE_APP_LOGO,

  // Clinic Information
  clinic: {
    name: import.meta.env.VITE_CLINIC_NAME,
    address: import.meta.env.VITE_CLINIC_ADDRESS,
    phone: import.meta.env.VITE_CLINIC_PHONE,
    email: import.meta.env.VITE_CLINIC_EMAIL,
    offline_pipeline_id: Number(import.meta.env.VITE_CLINIC_OFFLINE_PIPELINE_ID),
    after_payment_stage_id: Number(import.meta.env.VITE_CLINIC_AFTER_PAYMENT_STAGE_ID),
  },
};

export default config;
