<template>
  <DefineTrackDraggableList v-slot="{ stage }">
    <transition name="fade">
      <draggable
        v-if="tracksByStageId[stage.id]"
        v-model="tracksByStageId[stage.id]"
        :class="{
          'relative after:absolute after:inset-0 after:mb-2 after:rounded-md after:border after:border-dashed after:border-gray-300 after:content-[\'\']':
            showPlaceHolder(stage),
          'min-h-[74.5px]': !stage.children,
        }"
        :component-data="{
          tag: 'ul',
          type: 'transition-group',
          name: !drag ? 'flip-list' : null,
        }"
        :data-stageid="stage.id"
        :move="onMove"
        class="list-group no-scrollbar surface-overlay space-y-2 pb-2 transition-all duration-500 ease-in-out empty:pb-0"
        item-key="id"
        v-bind="dragOptions"
        @end="onEnd($event)"
        @start="onStart($event)"
      >
        <template #item="{ element, index }">
          <TrackCard
            v-if="
              (!stageControls[stage.id]?.energySaver || index < 11) &&
              (!filterTrack || isTrackMatchingSearch(element, filterTrack))
            "
            :key="element.id + element.deal_id"
            :class="{ ghost: isTrackBeingDraggedByAnother(element.id) }"
            :data-stageid="element.stage_id"
            :model-value="element"
            :toggle-mode="toggleMode"
            @deal-switched="switchDeal"
          >
            <template #actions>
              <Menu.Item
                v-for="stageItem in topLevelStages"
                :key="stageItem.id"
                class="flex flex-col items-start p-0 font-medium hover:bg-transparent"
              >
                <div
                  class="flex w-full items-start rounded-md p-2 hover:bg-slate-200"
                  @click="handleChangeTrackStage(element.id, stageItem.id, element.stage_id, 0)"
                >
                  <Lucide class="mr-1 h-4 w-4" icon="ChevronRight" />
                  <span class="truncate">{{ stageItem.name }}</span>
                </div>
                <div
                  v-for="childStage in stageItem.children"
                  :key="childStage.id"
                  class="flex w-full max-w-[100%] items-start rounded-md p-2 pl-4 font-normal hover:bg-slate-200"
                  @click="handleChangeTrackStage(element.id, childStage.id, element.stage_id, 0)"
                >
                  <Lucide class="mr-1 h-4 w-4" icon="ChevronsRight" />
                  <span class="truncate">{{ childStage.name }}</span>
                </div>
              </Menu.Item>
              <Menu.Item
                class="font-medium text-danger"
                @click="handleCheckOut(element.id, stage.id)"
              >
                <Lucide class="mr-1 h-4 w-4" icon="CornerDownLeft" />
                <span>Checkout</span>
              </Menu.Item>
            </template>
          </TrackCard>
        </template>
      </draggable>
    </transition>
  </DefineTrackDraggableList>

  <div
    ref="containerRef"
    class="relative w-full snap-x snap-mandatory overflow-x-auto scroll-smooth"
  >
    <div :style="containerStyle" class="flex">
      <div
        v-for="(stage, key) in topLevelStages"
        :key="key"
        :data-id="stage.id"
        :style="{ width: `${stageWidth}px` }"
        class="flex flex-1 snap-start flex-col px-1"
      >
        <Alert class="box flex h-12 items-center p-3 font-medium" variant="soft-secondary">
          <Lucide class="mr-2 h-4 w-4" icon="Settings" />
          {{ stage.name }}
          <div
            class="ml-auto flex items-center rounded-full border border-slate-500 px-2 py-1 text-xs font-medium text-slate-500 dark:border-slate-500 dark:text-slate-400"
          >
            {{ getTotalTracksCount(stage) }}
            <Lucide class="ml-1 h-3.5 w-3.5" icon="User" />
          </div>
          <template v-if="stage.children?.length">
            <Toggle
              v-model="stageControls[stage.id].viewMode"
              :icon-component="Lucide"
              :options="[
                { value: 'normal', icon: 'Minimize' },
                { value: 'maximize', icon: 'Maximize' },
              ]"
              class="ml-1 border-slate-400 p-1 hover:bg-slate-300 focus:ring-0"
              icon-class="h-3.5 w-3.5 font-bold"
            />
          </template>
          <button
            class="ml-1 rounded p-1 transition-colors hover:bg-slate-200"
            @click="toggleEnergySaver(stage.id)"
          >
            <Lucide
              :class="{
                'text-green-500': stageControls[stage.id].energySaver,
                'text-gray-400': !stageControls[stage.id].energySaver,
              }"
              class="h-4 w-4"
              icon="Leaf"
            />
          </button>
        </Alert>

        <div class="mt-3 flex-1">
          <ReuseTrackDraggableList :stage="stage" />
          <template v-if="stage.children?.length">
            <template
              v-for="(childStage, key) in stage.children"
              :key="stage.id + key"
              :data-id="childStage.id"
              :style="{ width: `${stageWidth}px` }"
              class="flex flex-1 snap-start flex-col px-1"
            >
              <Fieldset
                :legend="childStage.name"
                :pt="{
                  legend: {
                    class:
                      'text-sm font-light hover:bg-white bg-transparent mb-2 w-full rounded border-t border-slate-200/60',
                  },
                  toggleButton: { class: 'p-2 bg-transparent w-full justify-start' },
                  legendLabel: { class: 'font-normal' },
                  transition: {
                    enterActiveClass: 'transition-none',
                    leaveActiveClass: 'transition-none',
                  },
                }"
                :toggleable="true"
                class="border-0 bg-transparent p-0"
              >
                <ReuseTrackDraggableList :stage="childStage" />
              </Fieldset>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>

  <Teleport to="body">
    <div class="fixed left-0 top-0 z-50 h-full w-5" @mouseenter="scrollToStart"></div>
    <div class="fixed right-0 top-0 z-50 h-full w-5" @mouseenter="scrollToEnd"></div>
  </Teleport>
</template>

<script lang="ts" setup>
import { createReusableTemplate, useStorage } from "@vueuse/core";
import { computed, nextTick, onMounted, onUnmounted, ref, unref, watch } from "vue";
import draggable from "vuedraggable";

import { StageResponse, TrackAddRequest, TrackResponse } from "@/api/bcare-types-v2";
import Alert from "@/base-components/Alert";
import Toggle from "@/base-components/Button/Toggle.vue";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import TrackCard from "@/components/Deal/TrackCard.vue";
import { useStageControls } from "@/components/Pipeline/useStageControls";
import useTrack from "@/hooks/useTrack";
import usePerson from "@/hooks/usePerson";
import useTrackDragDrop from "@/pages/dashboard/useTrackDragDrop";
import { useNotiStore } from "@/stores/notification";
import { usePipelineStore } from "@/stores/pipeline-store";
import { fuzzySearch } from "@/utils/string";

const [DefineTrackDraggableList, ReuseTrackDraggableList] = createReusableTemplate();

const MIN_STAGE_WIDTH = 300;

const props = defineProps<{
  pipelineId: number;
  toggleMode: {
    note: boolean;
    user: boolean;
    comment: boolean;
  };
  newPerson: number;
  filterTrack: string;
}>();

const { checkPersonIn } = usePerson();
const notiStore = useNotiStore();
const pipelineStore = usePipelineStore();
const { stageControls, initializeStageControl, toggleEnergySaver } = useStageControls(
  props.pipelineId,
);

const containerRef = ref<HTMLElement | null>(null);
const containerWidth = ref(0);

const { tracks, listTracks: fetchTracksList, addTrack, checkoutTrack, updateTrack } = useTrack();

// Computed properties
const pipeline = computed(() => pipelineStore.getPipelineById(props.pipelineId));

const topLevelStages = computed<StageResponse[]>(() => {
  return (
    pipeline.value?.stages.filter(
      (stage) => stage.parent_stage_id == null || stage.parent_stage_id == 0,
    ) || []
  );
});

const stagesCount = computed(() => topLevelStages.value.length);

const stageWidth = computed(() => {
  const maxVisibleStages = Math.floor(containerWidth.value / MIN_STAGE_WIDTH);
  const calculatedWidth = containerWidth.value / Math.min(maxVisibleStages, stagesCount.value);
  return Math.max(calculatedWidth, MIN_STAGE_WIDTH);
});

const containerStyle = computed(() => {
  const totalWidth = stageWidth.value * stagesCount.value;
  if (totalWidth > containerWidth.value) {
    return { width: `${totalWidth}px` };
  }
  return {};
});

const WS_ROOM = computed(() => "pipeline_" + props.pipelineId);

// Track drag & drop logic
const {
  drag,
  draggingTracks,
  onStart,
  onEnd,
  onMove,
  tracksByStageId,
  broadcastTrackUpdate,
  broadcastTrackAdd,
  handleChangeTrackStage,
  initializeTracksByStageId,
  insertTrack,
  switchDeal,
  removeTrackFromStage,
} = useTrackDragDrop(tracks, WS_ROOM.value, {
  onRefetchTracks: async () => {
    await pipelineStore.fetchPipelines();
    await fetchTracks();
    await nextTick();
    if (pipeline.value?.stages) {
      initializeTracksByStageId(pipeline.value.stages);
    }
  },
});

const dragOptions = computed(() => ({
  animation: 200,
  group: "pipeline",
  ghostClass: "ghost",
}));

const isTrackBeingDraggedByAnother = computed(
  () => (trackId: number) => draggingTracks.value.has(trackId),
);

// Methods
const fetchTracks = async () => {
  await fetchTracksList({
    page_size: 500,
    order_by: "created_at asc",
    filter: {
      pipeline_id: props.pipelineId,
    },
  });
};

const updateContainerWidth = async () => {
  await nextTick();
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth;
  }
};

const scrollToEnd = () => {
  if (containerRef.value) {
    containerRef.value.scrollLeft = containerRef.value.scrollWidth - containerRef.value.clientWidth;
  }
};

const scrollToStart = () => {
  if (containerRef.value) {
    containerRef.value.scrollLeft = 0;
  }
};

const handleCheckInPerson = async (id: number) => {
  const checkPerson = await checkPersonIn({
    id: id,
    full_name: "",
    phone: "",
    email: "",
  });

  if (checkPerson) {
    notiStore.warning({ message: "Khách hàng đã được check-in" });
    return;
  }

  const topLevelStage = pipeline.value?.stages.find((stage) => !stage.parent_stage_id);
  const newTrack: TrackAddRequest = {
    stage_id: topLevelStage?.id || 0,
    person_id: id,
  };

  try {
    const createdTrack = await addTrack(newTrack);
    if (createdTrack) {
      insertTrack(createdTrack);
      broadcastTrackAdd(createdTrack);
      notiStore.success({ message: "Track đã được tạo thành công." });
    } else {
      notiStore.error({ message: "Không thể tạo Track mới." });
    }
  } catch (error) {
    notiStore.error({ message: "Lỗi khi tạo Track mới." });
    console.error("Error creating track:", error);
  }
};

const handleCheckOut = async (trackId: number, stageId: number) => {
  try {
    await checkoutTrack({ id: trackId });
    removeTrackFromStage(trackId, stageId);
    broadcastTrackUpdate(trackId, 0, stageId, 0);
    notiStore.success({ message: "Track đã được checkout thành công." });
  } catch (error) {
    notiStore.error({ message: "Lỗi khi checkout Track." });
    console.error("Error checking out track:", error);
  }
};

const showPlaceHolder = (stage: StageResponse) => {
  return (
    !tracksByStageId[stage.id] ||
    (tracksByStageId[stage.id] && tracksByStageId[stage.id].length === 0 && !stage.children)
  );
};

const getTotalTracksCount = (stage: any): number => {
  const currentStageTracks = tracksByStageId[stage.id]?.length || 0;

  if (!stage.children?.length) {
    return currentStageTracks;
  }

  const childrenTracks = stage.children.reduce((total: number, childStage: any) => {
    return total + (tracksByStageId[childStage.id]?.length || 0);
  }, 0);

  return currentStageTracks + childrenTracks;
};

const isTrackMatchingSearch = (track: TrackResponse, searchTerm: string): boolean => {
  if (!searchTerm) return true;

  const { person } = track;
  if (!person) return false;

  return (
    fuzzySearch(person.full_name || "", searchTerm) ||
    fuzzySearch(person.phone || "", searchTerm) ||
    fuzzySearch(person.person_field?.code || "", searchTerm)
  );
};

// Watchers
watch(
  () => topLevelStages.value,
  (stages) => {
    stages.forEach((stage) => initializeStageControl(stage.id));
  },
  { immediate: true },
);

watch(
  [() => tracks.value, () => pipeline.value],
  ([newTracks, newPipeline]) => {
    if (newTracks.length > 0 && newPipeline && newPipeline.stages.length > 0) {
      initializeTracksByStageId(unref(newPipeline.stages));
    }
  },
  { immediate: true },
);

watch(
  () => props.newPerson,
  (newPerson) => {
    if (newPerson) {
      handleCheckInPerson(newPerson);
    }
  },
);

// Lifecycle hooks
onMounted(async () => {
  await updateContainerWidth();
  window.addEventListener("resize", updateContainerWidth);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateContainerWidth);
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.ghost {
  opacity: 0.5;
}
</style>
