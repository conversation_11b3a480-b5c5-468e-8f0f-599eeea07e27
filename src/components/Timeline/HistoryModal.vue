<script setup lang="ts">
import { computed } from "vue";
import Dialog from "primevue/dialog";
import HistoryTimelineV2 from "@/components/Timeline/HistoryTimelineV2.vue";

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    entityType: "person" | "deal";
    entityId?: number | null;
    title?: string;
  }>(),
  {
    title: "L<PERSON>ch sử chỉnh sửa",
  },
);

const emit = defineEmits(["update:modelValue"]);

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
</script>

<template>
  <Dialog
    v-model:visible="isVisible"
    modal
    :header="props.title"
    :style="{ width: '70vw', maxHeight: '80vh' }"
    :pt="{
      root: 'border-round',
      mask: {
        style: 'backdrop-filter: blur(2px)',
      },
      content: 'p-0',
    }"
    @hide="isVisible = false"
  >
    <template v-if="isVisible && props.entityId">
      <HistoryTimelineV2
        :entity-type="props.entityType"
        :entity-id="props.entityId"
        class="max-h-[calc(80vh-5rem)] overflow-y-auto"
      />
    </template>
    <template v-else-if="isVisible && !props.entityId">
      <p class="p-4 text-center text-gray-500">Không có lịch sử</p>
    </template>
  </Dialog>
</template>
