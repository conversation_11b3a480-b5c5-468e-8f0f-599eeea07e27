<script setup lang="ts">
import Dialog from "primevue/dialog";
import { computed } from "vue";

import { HistoryEntry } from "@/api/bcare-types-v2";

import HistoryTimeline from "./HistoryTimeline.vue";

export type TimelineModalProps = {
  modelValue: boolean;
  history: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
};

const props = defineProps<TimelineModalProps>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
}>();

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
</script>

<template>
  <Dialog
    v-model:visible="visible"
    modal
    :draggable="false"
    dismissableMask
    :style="{ width: '90vw', maxWidth: '768px' }"
    :pt="{
      root: { class: 'rounded-xl' },
      header: { class: 'bg-gray-50 rounded-t-xl px-6 py-4' },
      headerTitle: { class: 'text-lg font-semibold text-gray-800' },
      closeButton: {
        class:
          'w-8 h-8 rounded-lg flex items-center justify-center transition-colors hover:bg-gray-200',
      },
      closeButtonIcon: { class: 'text-gray-500' },
      content: { class: 'p-0' },
    }"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <i class="pi pi-history text-xl text-primary-500"></i>
        <span>Lịch sử chỉnh sửa</span>
      </div>
    </template>

    <div class="max-h-[70vh] overflow-y-auto px-6 py-4">
      <template v-if="history.length || (creatorId && createdAt)">
        <HistoryTimeline :history="history" :creator-id="creatorId" :created-at="createdAt" />
      </template>
      <template v-else>
        <div class="flex flex-col items-center justify-center py-12 text-center">
          <i class="pi pi-history mb-4 text-4xl text-gray-300"></i>
          <p class="text-gray-500">Chưa có lịch sử chỉnh sửa</p>
        </div>
      </template>
    </div>
  </Dialog>
</template>
