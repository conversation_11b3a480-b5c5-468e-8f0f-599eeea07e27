<script setup lang="ts">
import DOMPurify from "dompurify";
import { computed, onMounted, watch } from "vue";

import type { HistoryListRequest, TagResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import useHistory from "@/hooks/useHistory";
import useLocation from "@/hooks/useLocation";
import usePipeline from "@/hooks/usePipeline";
import useUser from "@/hooks/useUser";
import useTag from "@/hooks/useTag";
import { formatCurrency } from "@/utils/helper";
import Empty from "@/base-components/Empty";
import useTerm from "@/hooks/useTerm";

const RICH_TEXT_FIELDS = new Set(["notes", "body", "note"]);
const USER_ID_FIELDS = new Set(["user_id", "assigned_by", "creator_id", "doctor_id"]);
const DATE_FIELDS = new Set([
  "start_time",
  "end_time",
  "changed_at",
  "created_at",
  "updated_at",
  "timestamp",
  "arrived_at",
  "start_date",
  "due_date",
  "end_date",
  "date_of_birth",
]);
const TIME_SENSITIVE_DATE_FIELDS = new Set([
  // For :showTime prop
  "start_time",
  "end_time",
  "changed_at",
  "created_at",
  "updated_at",
  "timestamp",
  "arrived_at",
]);
const FIELDS_WITHOUT_OLD_VALUE = new Set([
  "current_serial",
  "arrived_at",
  "extra_notes",
  "person_field",
  ...RICH_TEXT_FIELDS,
]);
const TECHNICAL_FIELDS = new Set([
  "id",
  "created_at",
  "updated_at",
  "deleted_at",
  "version",
  "person_id",
  "deal_id",
  "related_id",
  "entity_id",
]);

const props = defineProps<{
  entityType: "person" | "deal";
  entityId: number;
}>();

const { isLoading, error, histories, listHistories } = useHistory();
const { getUserById } = useUser();
const { getProvinceById, getDistrictById, getWardById } = useLocation();
const { getStagesByPipelineID } = usePipeline();
const { getTagById } = useTag({ useStore: true, autoLoad: true });
const { getTermNameById } = useTerm();
const pipelineId = 2;

const fetchHistory = async () => {
  const request: HistoryListRequest = {
    page: 1,
    page_size: 300,
    filter: {
      entity_type: props.entityType,
      entity_id: props.entityId,
    },
    order_by: "changed_at DESC",
  };
  await listHistories(request);
};

onMounted(fetchHistory);
watch([() => props.entityId, () => props.entityType], fetchHistory, { immediate: false });

const getFieldLabel = (field: string) => {
  const labels: Record<string, string> = {
    state: "Giai đoạn",
    status: "Trạng thái",
    notes: "Ghi chú",
    note: "Nội dung",
    body: "Nội dung",
    type: "Loại",
    full_name: "Họ tên",
    phone: "Số điện thoại",
    email: "Email",
    date_of_birth: "Ngày sinh",
    gender: "Giới tính",
    address_number: "Số nhà",
    province_id: "Tỉnh/Thành phố",
    district_id: "Quận/Huyện",
    ward_id: "Phường/Xã",
    job_id: "Nghề nghiệp",
    source_id: "Nguồn",
    person_field: "Thông tin thêm",
    stage_id: "Stage",
    name: "Tên Deal",
    total_amount: "Tổng tiền",
    user_id: "Người dùng",
    doctor_id: "Bác sĩ",
    person_id: "Khách hàng",
    deal_id: "Deal",
    creator_id: "Người tạo",
    code: "Mã hồ sơ",
    treatment_id: "Loại điều trị",
    treatment_status_id: "Trạng thái điều trị",
    special_note: "Ghi chú đặc biệt",
    medical_condition: "Bệnh lý",
    description: "Ghi chú",
    pancake_link: "Link pancake",
    has_zalo: "Sử dụng zalo",
    secondary_phone: "Sdt phụ",
    bank_account_name: "Chủ tài khoản",
    bank_account_number: "Số tài khoản",
    bank: "Ngân hàng",
    bank_branch: "Chi nhánh",
    role: "Vai trò",
  };
  return labels[field] || field;
};

const getRelatedEntityLabel = (related: string) => {
  const labels: Record<string, string> = {
    PersonAssignment: "Nhân viên liên quan",
    DealUser: "Nhân viên liên quan",
    TagDeal: "Thẻ",
    TagPerson: "Thẻ",
  };
  return labels[related] || related;
};

const getRoleLabel = (role: string) => {
  const labels: Record<string, string> = {
    doctor: "Bác sĩ",
    counselor: "Tư vấn viên",
    sale: "Sale",
    customer_care: "Chăm sóc khách hàng",
    treatment_doctor: "Bác sĩ điều trị",
    consultant_doctor: "Bác sĩ tư vấn",
    consultant: "Tư vấn viên",
    doctor_assistant: "Trợ lý bác sĩ",
  };
  return labels[role] || role;
};

const formatDisplayValue = (field: string, value: any): string => {
  if (value === null || typeof value === "undefined") {
    if (RICH_TEXT_FIELDS.has(field)) {
      return '<i class="pi pi-trash text-red-500 mr-1"></i> <span class="text-red-500">Đã xóa</span>';
    }
    return '<span class="text-gray-500">Chưa có</span>';
  }

  if (field === "province_id") {
    const province = getProvinceById.value(Number(value));
    return province?.name || String(value);
  }
  if (field === "district_id") {
    const district = getDistrictById.value(Number(value));
    return district?.name || String(value);
  }
  if (field === "ward_id") {
    const ward = getWardById.value(Number(value));
    return ward?.name || String(value);
  }
  if (field === "stage_id") {
    const stages = getStagesByPipelineID(pipelineId);
    const stage = stages?.find((stage) => stage.id === Number(value));
    return stage?.name || String(value);
  }
  if (field === "source_id") {
    return getTermNameById("nguon", Number(value));
  }
  if (field === "treatment_id") {
    return getTermNameById("loai_dieu_tri", Number(value));
  }
  if (field === "treatment_status_id") {
    return getTermNameById("trang_thai_dieu_tri", Number(value));
  }

  if (field === "total_amount") {
    return formatCurrency(Number(value));
  }

  if (DATE_FIELDS.has(field)) {
    try {
      new Date(value).toISOString();
      return value;
    } catch (e) {
      return `<span class="text-red-500">Invalid Date</span>`;
    }
  }

  if (typeof value === "boolean") return value ? "Có" : "Không";
  if (typeof value === "object") {
    return '<span class="flex min-w-0 flex-1 flex-wrap items-baseline gap-1">Đã cập nhật</span>';
  }
  if (RICH_TEXT_FIELDS.has(field)) {
    return DOMPurify.sanitize(String(value));
  }

  if (USER_ID_FIELDS.has(field)) {
    return value;
  }
  if (field === "tag_id") {
    const tag = (getTagById as (id: number) => TagResponse | undefined)(Number(value));
    return `<span class="flex min-w-0 flex-1 flex-wrap items-baseline gap-1">#${tag?.name}</span>`;
  }
  if (field === "role") {
    return getRoleLabel(value);
  }

  return String(value);
};

const getGeneralChanges = (operation: string, before: any, after: any) => {
  const allKeys = new Set([...Object.keys(before || {}), ...Object.keys(after || {})]);
  return Array.from(allKeys)
    .filter((key) => !TECHNICAL_FIELDS.has(key) && !key.startsWith("_"))
    .map((key) => ({
      key,
      oldValue: before ? before[key] : undefined,
      newValue: after ? after[key] : undefined,
    }))
    .filter((change) => {
      if (operation?.toUpperCase() === "UPDATE") {
        return JSON.stringify(change.oldValue) !== JSON.stringify(change.newValue);
      }
      if (operation?.toUpperCase() === "CREATE") {
        return typeof change.newValue !== "undefined" && change.newValue !== null;
      }
      if (operation?.toUpperCase() === "DELETE") {
        return typeof change.oldValue !== "undefined" && change.oldValue !== null;
      }
      return true;
    });
};

const timelineEvents = computed(() => {
  return histories.value.map((record) => {
    let changes;
    if (record.related) {
      changes = getGeneralChanges(record.operation, record.before, record.after);
    } else {
      changes = getChanges(record.before, record.after);
    }
    const timestamp = record.changed_at || record.created_at;
    return {
      timestamp: timestamp,
      record: record,
      user: getUserById(record.user_id ?? 0),
      icon: getOperationIconClass(record.operation),
      colorClass: getOperationColorClass(record.operation),
      changes: changes,
    };
  });
});

const getChanges = (
  before: any,
  after: any,
): Array<{ key: string; oldValue: any; newValue: any }> => {
  if (
    typeof before !== "object" ||
    typeof after !== "object" ||
    before === null ||
    after === null
  ) {
    return [];
  }

  const changedKeys = new Set<string>();
  const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

  allKeys.forEach((key) => {
    if (JSON.stringify(before[key]) !== JSON.stringify(after[key])) {
      if (!["updated_at", "version", "created_at"].includes(key)) {
        changedKeys.add(key);
      }
    }
  });

  return Array.from(changedKeys)
    .filter((key) => !key.startsWith("_"))
    .map((key) => ({
      key: key,
      oldValue: before[key],
      newValue: after[key],
    }));
};

const getOperationIconClass = (operation?: string) => {
  switch (operation?.toUpperCase()) {
    case "CREATE":
      return "pi pi-plus";
    case "UPDATE":
      return "pi pi-pencil";
    case "DELETE":
      return "pi Fpi-trash";
    default:
      return "pi pi-info-circle";
  }
};

const getOperationColorClass = (operation?: string) => {
  switch (operation?.toUpperCase()) {
    case "CREATE":
      return "bg-green-500";
    case "UPDATE":
      return "bg-blue-500";
    case "DELETE":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const isShowOldValue = (field: string, operation?: string) => {
  if (operation?.toUpperCase() === "CREATE") return false;
  // Use the constant FIELDS_WITHOUT_OLD_VALUE
  return !FIELDS_WITHOUT_OLD_VALUE.has(field);
};
</script>

<template>
  <div class="bg-transparent">
    <div v-if="isLoading" class="py-6 text-center text-gray-500">
      <i class="pi pi-spin pi-spinner mr-2" />
    </div>

    <div v-else-if="error" class="m-4 rounded bg-red-50 p-4 text-center text-red-600">
      <i class="pi pi-exclamation-triangle mr-2" />
    </div>

    <div v-else-if="timelineEvents.length > 0" class="relative">
      <div class="absolute bottom-0 left-4 top-0 w-0.5 bg-gray-200" />
      <div v-for="(event, index) in timelineEvents" :key="index" class="relative mb-4 pl-12">
        <!-- Icon marker -->
        <div class="absolute left-1 top-0">
          <span
            class="flex h-7 w-7 items-center justify-center rounded-full text-white shadow-md"
            :class="event.colorClass"
          >
            <i :class="[event.icon, '!text-xs']"></i>
          </span>
        </div>

        <!-- Content -->
        <div class="overflow-hidden rounded-md border border-gray-200/80 bg-white p-2 shadow-sm">
          <div class="mb-2 flex items-center gap-1">
            <UserAvatar :user="event.user" size="small" />
            <div class="flex flex-col">
              <span class="text-start text-sm font-semibold text-gray-800">{{
                event.user?.name ?? "Hệ thống"
              }}</span>
              <DateTime
                :time="event.record.changed_at"
                size="xs"
                :showIcon="true"
                :showTime="true"
                class="text-start text-gray-500"
              />
            </div>
          </div>
          <div class="mt-1 space-y-1.5 text-sm">
            <div v-if="event.record.related">
              <Tag
                class="text-xs"
                :value="getRelatedEntityLabel(event.record.related)"
                severity="info"
              />
              <div
                v-for="change in event.changes"
                :key="change.key"
                class="mt-1 flex items-center gap-2"
              >
                <span>{{ getFieldLabel(change.key) }}:</span>
                <template v-if="event.record.operation?.toUpperCase() === 'UPDATE'">
                  <template v-if="USER_ID_FIELDS.has(change.key)">
                    <UserAvatar
                      v-if="change.oldValue"
                      :user-id="change.oldValue"
                      size="small"
                      show-name
                      class="line-through decoration-red-500 decoration-1 opacity-60"
                    />
                    <i class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"></i>
                    <UserAvatar
                      v-if="change.newValue"
                      :user-id="change.newValue"
                      size="small"
                      show-name
                    />
                  </template>
                  <template v-else>
                    <span
                      v-if="change.oldValue"
                      class="text-red-500 line-through"
                      v-html="formatDisplayValue(change.key, change.oldValue)"
                    ></span>
                    <i class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"></i>
                    <span
                      v-if="change.newValue"
                      class="font-semibold text-green-700"
                      v-html="formatDisplayValue(change.key, change.newValue)"
                    ></span>
                  </template>
                </template>
                <template v-else-if="event.record.operation?.toUpperCase() === 'CREATE'">
                  <template v-if="USER_ID_FIELDS.has(change.key)">
                    <UserAvatar
                      v-if="change.newValue"
                      :user-id="change.newValue"
                      size="small"
                      show-name
                    />
                  </template>
                  <template v-else>
                    <span
                      class="font-semibold text-green-700"
                      v-html="formatDisplayValue(change.key, change.newValue)"
                    ></span>
                  </template>
                </template>
                <template v-else-if="event.record.operation?.toUpperCase() === 'DELETE'">
                  <template v-if="USER_ID_FIELDS.has(change.key)">
                    <UserAvatar
                      v-if="change.oldValue"
                      :user-id="change.oldValue"
                      size="small"
                      show-name
                      class="line-through decoration-red-500 decoration-1 opacity-60"
                    />
                  </template>
                  <template v-else>
                    <span
                      class="text-red-500 line-through"
                      v-html="formatDisplayValue(change.key, change.oldValue)"
                    ></span>
                  </template>
                </template>
              </div>
            </div>
            <template v-else>
              <div
                v-if="event.record.operation?.toUpperCase() === 'CREATE'"
                class="flex flex-wrap items-baseline gap-x-2"
              >
                <Tag class="text-xs" value="Đã tạo" severity="success" />
              </div>
              <div
                v-else-if="event.record.operation?.toUpperCase() === 'DELETE'"
                class="flex flex-wrap items-baseline gap-x-2"
              >
                <Tag class="text-xs" value="Đã xóa" severity="danger" />
              </div>
              <div
                v-else
                v-for="change in event.changes"
                :key="change.key"
                class="flex flex-wrap items-baseline gap-x-1"
              >
                <Tag class="text-xs" :value="getFieldLabel(change.key)" severity="primary" />
                <div class="flex min-w-0 flex-1 flex-wrap items-baseline gap-1">
                  <template v-if="USER_ID_FIELDS.has(change.key)">
                    <UserAvatar
                      v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                      :user-id="change.oldValue"
                      size="small"
                      show-name
                      class="line-through decoration-red-500 decoration-1 opacity-60"
                    />
                    <i
                      class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"
                      v-if="
                        isShowOldValue(change.key, event.record.operation) &&
                        change.oldValue &&
                        change.newValue
                      "
                    ></i>
                    <UserAvatar
                      v-if="change.newValue"
                      :user-id="change.newValue"
                      size="small"
                      show-name
                    />
                    <span
                      v-else-if="!change.newValue"
                      v-html="formatDisplayValue(change.key, change.newValue)"
                      class="font-medium text-green-600"
                    ></span>
                  </template>
                  <template v-else-if="change.key === 'role'">
                    <span
                      v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                      class="text-red-500 line-through"
                    >
                      {{ getRoleLabel(change.oldValue) }}
                    </span>
                    <i
                      class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"
                      v-if="
                        isShowOldValue(change.key, event.record.operation) &&
                        change.oldValue &&
                        change.newValue
                      "
                    ></i>
                    <span v-if="change.newValue" class="font-medium text-green-600">
                      {{ getRoleLabel(change.newValue) }}
                    </span>
                    <span v-else-if="!change.newValue" class="font-medium text-green-600">
                      <span class="text-gray-500">Chưa có</span>
                    </span>
                  </template>
                  <template v-else-if="DATE_FIELDS.has(change.key)">
                    <DateTime
                      v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                      :time="change.oldValue"
                      :showTime="TIME_SENSITIVE_DATE_FIELDS.has(change.key)"
                      size="xs"
                      :showIcon="false"
                      class="text-red-500 line-through"
                    />
                    <i
                      class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"
                      v-if="
                        isShowOldValue(change.key, event.record.operation) &&
                        change.oldValue &&
                        change.newValue
                      "
                    ></i>
                    <DateTime
                      v-if="change.newValue"
                      :time="change.newValue"
                      :showTime="TIME_SENSITIVE_DATE_FIELDS.has(change.key)"
                      size="xs"
                      :showIcon="false"
                      class="font-medium text-green-600"
                    />
                    <span v-else-if="!change.newValue" class="font-medium text-green-600">
                      <span class="text-gray-500">Chưa có</span>
                    </span>
                  </template>
                  <template v-else>
                    <span
                      v-if="isShowOldValue(change.key, event.record.operation) && change.oldValue"
                      class="break-all text-red-500 line-through"
                      v-html="formatDisplayValue(change.key, change.oldValue)"
                    />
                    <i
                      class="pi pi-arrow-right mx-0.5 text-xs text-blue-500"
                      v-if="
                        isShowOldValue(change.key, event.record.operation) &&
                        change.oldValue &&
                        change.newValue
                      "
                    ></i>
                    <span
                      class="break-all font-medium text-green-600"
                      v-html="formatDisplayValue(change.key, change.newValue)"
                    />
                  </template>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div v-else><Empty /></div>
  </div>
</template>

<style scoped></style>
