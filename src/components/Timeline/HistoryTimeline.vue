<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import DOMPurify from "dompurify";
import Tag from "primevue/tag";
import Timeline from "primevue/timeline";
import { computed } from "vue";

import { HistoryEntry } from "@/api/bcare-types-v2";
import PriorityChip from "@/components/Chip/PriorityChip.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import useConstant from "@/hooks/useConstant";
import useUser from "@/hooks/useUser";
import { parseExtraNotesV2 } from "@/pages/customer/components/AppointmentTab/utils";
import { StateSelectBtn } from "../Select";

const props = defineProps<{
  history: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
  usedAtComponent?: string;
}>();

const { getUserById } = useUser();
const { getConstants } = useConstant();
// Format helpers
const formatDate = (date: string) => {
  return useDateFormat(new Date(date), "HH:mm DD/MM/YYYY").value;
};

const formatValue = (field: string, value: string | number) => {
  if (!value && !["notes", "body", "note"].includes(field)) return "Chưa có";

  const dateFields = ["start_date", "due_date", "end_date", "start_time", "end_time", "arrived_at"];
  if (dateFields.includes(field)) {
    return useDateFormat(new Date(value), "DD/MM/YYYY HH:mm").value;
  }

  switch (field) {
    case "reminder_status":
      return value === 1 ? "Đã nhắc" : "Chưa nhắc";

    case "type":
      if (props.usedAtComponent === "task") {
        return value;
      }
      return getConstants.value?.appointment_type?.[value] || "Không xác định";

    case "extra_notes": {
      const newParsed = parseExtraNotesV2(value as string);

      const formatExtraNotes = (data: ReturnType<typeof parseExtraNotesV2>) => {
        const parts = [];

        if (data.expected_task.length) {
          parts.push(
            `<span class="font-medium">CV dự kiến:</span> ${data.expected_task.join(", ")}`,
          );
        }

        if (data.expected_task_other.length) {
          parts.push(
            `<span class="font-medium">CV dự kiến:</span> ${data.expected_task_other.join(", ")}`,
          );
        }

        if (data.reminder_fee) {
          parts.push(
            `<span class="font-medium">Nhắc phí:</span> ${Number(data.reminder_fee).toLocaleString("vi-VN")} đ`,
          );
        }

        return parts.length ? parts.join("<br/>") : "Không có";
      };

      return formatExtraNotes(newParsed);
    }

    case "current_serial": {
      return `Lần ${value}`;
    }

    case "body":
    case "note":
    case "notes": {
      if (!!value) {
        return DOMPurify.sanitize(value as string);
      }
      return '<i class="pi pi-trash text-red-500"></i> <span class="text-red-500">Đã xóa</span>';
    }

    default:
      return value;
  }
};

// Field mapping
const getFieldLabel = (field: string) => {
  const labels: Record<string, string> = {
    state: "Trạng thái",
    priority: "Độ ưu tiên",
    title: "Tiêu đề",
    notes: "Ghi chú",
    note: "Nội dung",
    body: "Nội dung",
    due_date: "Ngày hết hạn",
    start_date: "Ngày bắt đầu",
    type: getTypeLabel(),
    users: "Người tham gia",
    start_time: "Thời gian bắt đầu",
    end_time: "Thời gian kết thúc",
    completed_at: "Ngày hoàn thành",
    doctor_id: "Bác sĩ phụ trách",
    reminder_status: "Nhắc hẹn",
    extra_notes: "CV dự kiến",
    created_at: "Lịch hẹn",
    current_serial: "Lặp lại",
    person_id: "Khách hàng",
    arrived_at: "Thời gian đến",
  };
  return labels[field] || field;
};

const getEventColor = (field: string) => {
  const colors: Record<string, string> = {
    state: "#4ade80", // green-400
    priority: "#fb923c", // orange-400
    title: "#60a5fa", // blue-400
    notes: "#94a3b8", // slate-400
    note: "#94a3b8", // slate-400
    due_date: "#f472b6", // pink-400
    start_date: "#c084fc", // purple-400
    type: "#22d3ee", // cyan-400
    person_id: "#a78bfa", // violet-400
    users: "#818cf8", // indigo-400
    default: "#9ca3af", // gray-400
    start_time: "#f59e0b", // yellow-400
    reminder_status: "#f472b6", // pink-400
    extra_notes: "#22d3ee", // cyan-400
    created_at: "#22c55e", // green-600
    current_serial: "#60a5fa", // blue-400
  };
  return colors[field] || colors.default;
};

const getEventIcon = (field: string) => {
  const icons: Record<string, string> = {
    state: "pi pi-check-circle",
    priority: "pi pi-flag",
    title: "pi pi-list",
    notes: "pi pi-clipboard",
    note: "pi pi-clipboard",
    due_date: "pi pi-calendar-times",
    start_date: "pi pi-calendar-plus",
    start_time: "pi pi-clock",
    end_time: "pi pi-clock",
    type: "pi pi-tag",
    person_id: "pi pi-user",
    users: "pi pi-users",
    default: "pi pi-sync",
    current_serial: "pi pi-refresh",
  };
  return icons[field] || icons.default;
};

// Transform logs for Timeline component
const timelineEvents = computed(() => {
  let events = [...props.history]
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .map((log) => ({
      ...log,
      color: getEventColor(log.changes[0]?.field),
      icon: getEventIcon(log.changes[0]?.field),
      changes: log.changes.filter((change) => isVisibleField(change.field)),
    }));

  // Thêm event tạo mới nếu có creatorId và createdAt
  if (props.creatorId && props.createdAt) {
    events.push({
      timestamp: props.createdAt,
      user_id: props.creatorId,
      changes: [
        {
          field: "created_at",
          old_value: "",
          new_value: "",
        },
      ],
      color: "#22c55e", // green-600
      icon: "pi pi-plus-circle",
    });
  }

  // Sắp xếp lại để đảm bảo thứ tự thời gian
  return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
});

const getTypeLabel = () => {
  if (props.usedAtComponent === "task") {
    return "Loại công việc";
  }
  return "Loại hẹn";
};

const isShowOldValue = (field: string) => {
  const fieldsNotShowOldValue = [
    "current_serial",
    "note",
    "notes",
    "body",
    "arrived_at",
    "extra_notes",
  ];
  return !fieldsNotShowOldValue.includes(field);
};

const isVisibleField = (field: string) => {
  const hiddenFields = ["completed_at", "end_date"];
  return !hiddenFields.includes(field);
};
</script>

<template>
  <Timeline
    :value="timelineEvents"
    class="w-full"
    :pt="{
      root: { class: 'gap-1' },
    }"
  >
    <!-- Marker -->
    <template #marker="slotProps">
      <span
        class="flex h-6 w-6 items-center justify-center rounded-full"
        :style="{ backgroundColor: slotProps.item.color }"
      >
        <i :class="[slotProps.item.icon, 'text-xs text-white']"></i>
      </span>
    </template>

    <!-- Timestamp -->
    <template #opposite="slotProps">
      <small class="block w-[120px] whitespace-normal text-right text-gray-500">
        {{ formatDate(slotProps.item.timestamp) }}
      </small>
    </template>

    <!-- Content -->
    <template #content="slotProps">
      <div class="flex flex-col gap-2">
        <!-- User Info -->
        <div class="flex items-center gap-2">
          <UserAvatar :user="getUserById(slotProps.item.user_id)" class="h-6 w-6" />
          <span class="font-medium">{{ getUserById(slotProps.item.user_id)?.name }}</span>
        </div>

        <!-- Changes -->
        <div
          v-for="(change, idx) in slotProps.item.changes"
          :key="idx"
          class="flex flex-wrap items-center gap-1"
        >
          <!-- Field Label Tag - now vertically centered -->
          <Tag
            v-if="change.field !== 'created_at'"
            class="flex-shrink-0 text-xs font-normal"
            :value="getFieldLabel(change.field)"
            severity="primary"
          />

          <!-- Change Content -->
          <div class="flex flex-1 flex-wrap items-center gap-2 break-all">
            <template v-if="change.field === 'state'">
              <StateSelectBtn
                :state="change.old_value"
                size="small"
                :editable="false"
                :show-dot="false"
              />
              <i class="pi pi-arrow-right text-xs text-blue-500" />
              <StateSelectBtn
                :state="change.new_value"
                size="small"
                :editable="false"
                :show-dot="false"
              />
            </template>

            <template v-else-if="change.field === 'doctor_id'">
              <UserAvatar :user="getUserById(change.old_value)" class="h-8 w-8" />
              <i class="pi pi-arrow-right text-xs text-blue-500"></i>
              <UserAvatar :user="getUserById(change.new_value)" class="h-8 w-8" />
            </template>

            <template v-else-if="change.field === 'type'">
              <span
                class="py-0.2 ml-1 inline-block whitespace-nowrap rounded-md bg-blue-500 px-1.5 text-xs text-white"
              >
                {{ formatValue(change.field, change.old_value) }}
              </span>
              <i class="pi pi-arrow-right text-xs text-blue-500"></i>
              <span
                class="py-0.2 ml-1 inline-block whitespace-nowrap rounded-md bg-blue-500 px-1.5 text-xs text-white"
              >
                {{ formatValue(change.field, change.new_value) }}
              </span>
            </template>

            <template v-else-if="change.field === 'created_at'">
              <span
                class="py-0.2 ml-1 inline-block whitespace-nowrap rounded-md bg-blue-500 px-1.5 text-xs text-white"
              >
                Đã tạo
              </span>
            </template>

            <template v-else-if="change.field === 'note'">
              <span
                class="prose relative w-full max-w-none whitespace-pre-wrap break-normal break-words text-sm font-medium prose-p:m-0"
                v-lightbox
                v-html="formatValue(change.field, change.new_value)"
              />
            </template>

            <template v-else-if="change.field === 'priority'">
              <div class="flex items-center gap-2">
                <PriorityChip :priority="change.old_value" size="small" />
                <i class="pi pi-arrow-right text-xs text-blue-500" />
                <PriorityChip :priority="change.new_value" size="small" />
              </div>
            </template>

            <template
              v-else-if="
                change.field === 'body' || change.field === 'note' || change.field === 'notes'
              "
            >
              <div class="flex items-center gap-2">
                <div
                  v-if="isShowOldValue(change.field)"
                  class="prose max-w-none text-sm text-gray-500 [overflow-wrap:break-word]"
                  v-html="formatValue(change.field, change.old_value)"
                />
                <i
                  v-if="isShowOldValue(change.field)"
                  class="pi pi-arrow-right text-xs text-blue-500"
                />
                <div
                  class="prose flex max-w-none items-center gap-1 whitespace-pre-wrap break-normal break-words text-sm prose-p:m-0"
                  v-html="formatValue(change.field, change.new_value)"
                />
              </div>
            </template>

            <template v-else>
              <span v-if="isShowOldValue(change.field)" class="min-w-fit text-gray-600">
                <span
                  v-if="change.field === 'extra_notes'"
                  v-html="formatValue(change.field, change.old_value)"
                />
                <span v-else>{{ formatValue(change.field, change.old_value) }}</span>
              </span>
              <i
                v-if="isShowOldValue(change.field)"
                class="pi pi-arrow-right text-xs text-blue-500"
              />
              <span
                class="min-w-fit"
                :class="{ 'whitespace-pre-line': change.field === 'extra_notes' }"
              >
                <span
                  v-if="change.field === 'extra_notes'"
                  v-html="formatValue(change.field, change.new_value)"
                />
                <span v-else class="font-medium">{{
                  formatValue(change.field, change.new_value)
                }}</span>
              </span>
            </template>
          </div>
        </div>
      </div>
    </template>
  </Timeline>
</template>

<style scoped>
:deep(.p-timeline-event-opposite) {
  flex: 0 1 50px !important;
}

:deep(.prose) {
  overflow-wrap: break-word;
}
</style>
