<template>
  <Lucide
    :icon="!fullscreenStore.isFullScreen ? 'Maximize' : 'Minimize'"
    class="h-5 w-5 cursor-pointer outline-none transition-all hover:scale-110 dark:text-slate-500"
  />
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";

import Lucide from "@/base-components/Lucide";
import { useFullscreenStore } from "@/stores/full-screen-store";

const fullscreenStore = useFullscreenStore();

const handleBrowserFullscreenChange = () => {
  fullscreenStore.setBrowserFullScreen(!!document.fullscreenElement);
};

onMounted(() => {
  document.addEventListener("fullscreenchange", handleBrowserFullscreenChange);
});

onUnmounted(() => {
  document.removeEventListener("fullscreenchange", handleBrowserFullscreenChange);
});
</script>
