<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";

import Empty from "@/base-components/Empty";
import useOperation from "@/hooks/useOperation";

interface Operation {
  id: number | string;
  name: string;
}

interface Props {
  productIds: number[]; // Changed to an array for multiple product IDs
  modelValue: Operation[];
}

const props = defineProps<Props>();
const emit = defineEmits(["update:modelValue"]);

const {
  operationsByProducts,
  loadOperations,
  filteredOperations,
  updateOperationFilter,
  selectedProductIds,
} = useOperation({ autoLoad: true });

watch(
  () => props.productIds,
  (newProductIds) => {
    selectedProductIds.value = newProductIds;
  },
  { immediate: true },
);

const operationFilter = ref("");
const customOperations = ref<Operation[]>([]);

const displayedOperations = computed(() => {
  // Get operations for all selected product IDs
  //const operations = operationsByProducts.value; // Use the computed property directly

  if (!operationFilter.value) return operationsByProducts.value;

  updateOperationFilter(operationFilter.value);
  return filteredOperations.value;
});

const onFilterInput = () => {
  // Filter is handled by the computed property and updateOperationFilter
};

const toggleOperation = (operation: Operation) => {
  operationFilter.value = "";

  const index = props.modelValue.findIndex((op) => op.id === operation.id);
  if (index > -1) {
    const newValue = [...props.modelValue];
    newValue.splice(index, 1);
    emit("update:modelValue", newValue);
  } else {
    emit("update:modelValue", [...props.modelValue, operation]);
  }
};

const removeOperation = (operationId: number | string) => {
  const newValue = props.modelValue.filter((op) => op.id !== operationId);
  emit("update:modelValue", newValue);

  // Clear custom operation nếu bị remove
  customOperations.value = customOperations.value.filter((op) => op.id !== operationId);
};

const isOperationActive = (operationId: number | string) => {
  return props.modelValue.some((op) => op.id === operationId);
};

const clearFilter = () => {
  operationFilter.value = "";
};

const addCustomOperation = () => {
  if (
    operationFilter.value &&
    !customOperations.value.some((op) => op.name === operationFilter.value)
  ) {
    const newOperation = { id: operationFilter.value, name: operationFilter.value };
    customOperations.value.push(newOperation);
    emit("update:modelValue", [...props.modelValue, newOperation]);
    operationFilter.value = "";
  }
};

onMounted(async () => {
  await loadOperations();
});

const popoverRef = ref();

defineExpose({
  popoverRef,
});

// Thêm ref để track việc đã submit/remove
const isSubmitted = ref(false);

// Clear khi hide popover
const onHide = () => {
  clearFilter();
  if (isSubmitted.value) {
    customOperations.value = [];
    isSubmitted.value = false;
  }
};

// Thêm watch để detect khi modelValue empty (đã submit)
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal.length === 0) {
      isSubmitted.value = true;
    }
  },
  { deep: true },
);
</script>
<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @hide="onHide()">
    <div class="w-[25rem]">
      <!-- Main content area with fixed height -->
      <div class="h-[15rem] overflow-hidden">
        <div v-if="displayedOperations.length > 0" class="h-full p-2">
          <ul
            class="m-0 h-full snap-y scroll-py-1 list-none space-y-1 overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="operation in displayedOperations"
              :key="operation.id"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isOperationActive(operation.id),
              }"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="toggleOperation(operation)"
            >
              <div class="flex-1">
                <span class="hyphens-auto">
                  {{ operation.name }}
                </span>
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="isOperationActive(operation.id)"
                  :class="{ 'bg-primary': isOperationActive(operation.id) }"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                ></div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="h-full p-2">
          <Empty class="h-full" />
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <div v-if="modelValue.length" class="mb-2 flex max-w-lg flex-wrap gap-1">
          <Chip
            v-for="operation in modelValue"
            :key="operation.id"
            :label="operation.name"
            removable
            @remove="removeOperation(operation.id)"
          />
        </div>
        <div class="flex items-center gap-2">
          <IconField class="flex-grow">
            <InputText
              v-model="operationFilter"
              autofocus
              class="w-full text-sm"
              placeholder="Tìm kiếm"
              type="text"
              @input="onFilterInput"
              @keyup.enter="addCustomOperation"
            />
            <InputIcon
              :class="operationFilter ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="clearFilter"
            />
          </IconField>
          <Button
            class="text-sm"
            label="OK"
            @click="
              () => {
                addCustomOperation();
                popoverRef?.hide();
                clearFilter();
              }
            "
          />
        </div>
      </div>
    </div>
  </Popover>
</template>
