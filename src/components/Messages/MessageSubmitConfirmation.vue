<script setup lang="ts">
import { ref, watch } from "vue";

import { PersonResponse } from "@/api/bcare-types-v2";
import Button from "@/base-components/Button";
import { Dialog } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";

const selectedPhone = ref<number | null>(null);

const props = defineProps<{
  isOpen: boolean;
  person: PersonResponse;
}>();

const emits = defineEmits<{
  (event: "close"): void;
  (event: "confirm", index: number): void;
}>();

// Reset selected phone when modal opens
watch(
  () => props.isOpen,
  () => {
    selectedPhone.value = Number(props.person.phone);
  },
);
</script>

<template>
  <Dialog :open="props.isOpen" @close="emits('close')" destroyOnHide style="z-index: 9992">
    <Dialog.Panel class="relative">
      <!-- Close button -->
      <a
        class="absolute right-0 top-0 mr-3 mt-3"
        href="#"
        @click="
          (event: MouseEvent) => {
            event.preventDefault();
            emits('close');
          }
        "
      >
        <Lucide icon="X" class="h-7 w-7 text-slate-400" />
      </a>

      <!-- Title -->
      <Dialog.Title>
        <h2 class="mr-auto text-base font-medium">Chọn số điện thoại</h2>
      </Dialog.Title>

      <!-- Phone number selection -->
      <Dialog.Description class="space-y-2">
        <!-- Primary phone -->
        <div
          class="group flex cursor-pointer items-center gap-3 rounded p-2 transition duration-100 ease-in-out hover:bg-slate-100"
          :class="{
            'bg-soft text-primary ring-1 ring-highlight':
              selectedPhone === Number(props.person.phone),
          }"
          @click="selectedPhone = Number(props.person.phone)"
        >
          <div class="min-w-0 flex-1">
            <span class="font-medium">{{ props.person.phone }}</span>
          </div>
          <div class="text-gray-600 dark:text-gray-400">
            <div
              :class="{
                'bg-primary': selectedPhone === Number(props.person.phone),
                invisible: selectedPhone !== Number(props.person.phone),
              }"
              class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
            />
          </div>
        </div>

        <!-- Secondary phone (if exists) -->
        <div
          v-if="props.person.person_field.secondary_phone"
          class="group flex cursor-pointer items-center gap-3 rounded p-2 transition duration-100 ease-in-out hover:bg-slate-100"
          :class="{
            'bg-soft text-primary ring-1 ring-highlight':
              selectedPhone === Number(props.person.person_field.secondary_phone),
          }"
          @click="selectedPhone = Number(props.person.person_field.secondary_phone)"
        >
          <div class="min-w-0 flex-1">
            <span class="font-medium">{{ props.person.person_field.secondary_phone }}</span>
          </div>
          <div class="text-gray-600 dark:text-gray-400">
            <div
              :class="{
                'bg-primary': selectedPhone === Number(props.person.person_field.secondary_phone),
                invisible: selectedPhone !== Number(props.person.person_field.secondary_phone),
              }"
              class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
            />
          </div>
        </div>
      </Dialog.Description>

      <!-- Footer buttons -->
      <Dialog.Footer class="text-right">
        <Button type="button" variant="outline-secondary" class="mr-2 w-24" @click="emits('close')">
          Đóng
        </Button>
        <Button
          type="button"
          variant="primary"
          class="w-24"
          @click="emits('confirm', selectedPhone ?? 0)"
        >
          Gửi
        </Button>
      </Dialog.Footer>
    </Dialog.Panel>
  </Dialog>
</template>
