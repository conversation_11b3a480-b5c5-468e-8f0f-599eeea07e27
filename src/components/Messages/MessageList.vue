<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from "vue";

import { SmsTemplate } from "@/api/bcare-types";
import { FormCheck } from "@/base-components/Form";
import { deepClone } from "@/utils/helper";

const props = defineProps<{
  messages: string; // Nhận messageStore.messages.sms từ parent
}>();

const smsMessage = ref<SmsTemplate[]>([]);
const checkSms = ref<SmsTemplate[]>([]);
const editableSpan = ref<HTMLDivElement[]>();
let newSmsTemplate: SmsTemplate[] = [];

const emit = defineEmits<{
  (e: "update:findSms", findSms: SmsTemplate[]): void;
}>();

const cleanup = () => {
  checkSms.value = [];
  smsMessage.value = [];
  newSmsTemplate = [];
  editableSpan.value?.forEach((item) => {
    item.replaceWith(item.cloneNode(true)); // Remove all listeners
  });
  editableSpan.value = undefined;
  emit("update:findSms", []);
};

// Cleanup when component unmounts
onUnmounted(() => {
  cleanup();
});

// Cleanup when messages prop changes
watch(
  () => props.messages,
  (newVal) => {
    if (!newVal) {
      cleanup();
    } else {
      smsMessage.value = JSON.parse(newVal);
      if (Array.isArray(smsMessage.value)) {
        newSmsTemplate = deepClone(smsMessage.value);
      }
    }
  },
  { immediate: true },
);

const updateMessages = computed(() => {
  return smsMessage.value.map((template: SmsTemplate) => {
    const content = template.content.replace(/<([^>]*)>/g, (match, capture) => {
      return `<strong>${capture}</strong>`;
    });
    return {
      ...template,
      content,
    };
  });
});

watch([checkSms, () => newSmsTemplate], () => {
  const findSms = newSmsTemplate.filter((sms: SmsTemplate) => {
    return checkSms.value.some((checkSms: SmsTemplate) => checkSms.name === sms.name);
  });
  emit("update:findSms", findSms);
});

watch(editableSpan, () => {
  editableSpan.value?.forEach((item, idx) => {
    item.addEventListener("input", (event: Event) => {
      const target = event.target as HTMLElement;
      const key = target.getAttribute("data-key");
      if (key === "content" && newSmsTemplate[idx]) {
        newSmsTemplate[idx].content = target.innerHTML;
      }
    });
  });
});
</script>
<template>
  <div
    v-for="(message, key) in updateMessages"
    class="intro-y col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3"
  >
    <div class="box flex h-full flex-col overflow-hidden rounded-md">
      <div class="relative flex items-center justify-between bg-slate-200">
        <FormCheck.Label class="flex w-full items-center justify-between px-5 py-3">
          <div class="flex-1 truncate text-base font-medium">
            <span class="mr-2">{{ key + 1 }}</span
            >{{ message.name }}
          </div>
          <FormCheck class="ml-2">
            <FormCheck.Input v-model="checkSms" type="checkbox" :value="message" />
          </FormCheck>
        </FormCheck.Label>
      </div>

      <div ref="editableSpan" class="flex-1 p-5">
        <span
          class="text-sm text-slate-600 focus-visible:outline-none dark:text-slate-400"
          contenteditable="true"
          data-key="content"
          v-html="message.content"
        ></span>
      </div>
    </div>
  </div>
</template>
