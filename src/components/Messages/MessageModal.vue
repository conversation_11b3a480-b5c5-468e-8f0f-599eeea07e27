<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { paramsZnsTemplate, SmsTemplate, ZnsTemplate } from "@/api/bcare-types";
import { AppointmentResponse } from "@/api/bcare-types-v2";
import Button from "@/base-components/Button";
import { FormCheck } from "@/base-components/Form";
import { Dialog } from "@/base-components/Headless";
import { Tab } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import useToggle from "@/hooks/useToggle";
import usePerson from "@/hooks/usePerson";
import { useMessageStore } from "@/stores/message-store";
import { deepClone, removeHTMLTags } from "@/utils/helper";
import MessageSubmitConfirmation from "./MessageSubmitConfirmation.vue";
import MessageList from "./MessageList.vue";
import useMessage from "@/hooks/useMessage";

const { onAddMessage } = useMessage();
const props = defineProps<{
  isOpen: boolean;
  appointment?: AppointmentResponse;
  personId: number;
}>();
const messagePayload = {
  person_id: 0,
  deal_id: 0,
  appointment_id: 0,
};
const messageStore = useMessageStore();
const isShowModalConfirmMessage = useToggle(false);

const { getPerson, currentPerson } = usePerson();
const emits = defineEmits<{ (event: "onClose"): void }>();
const selectedTabIndex = ref(0);
const znsTemplate = ref<ZnsTemplate[]>([]);
let newZnsTemplate: ZnsTemplate[] = [];
const checkZns = ref<string[]>([]);
const fallbackSms = ref(true);
// const checkSms = ref<number[]>([]);
const isSend = ref(true);
const editableSpan = ref<HTMLDivElement[]>();
const findSms = ref<SmsTemplate[]>([]);

const changeTab = async (index: number) => {
  selectedTabIndex.value = index;
};

const handleUpdateFindSms = (newFindSms: SmsTemplate[]) => {
  findSms.value = newFindSms;
};
const updateMessages = computed(() => {
  return znsTemplate.value.map((template: ZnsTemplate) => {
    const div = document.createElement("div");
    div.innerHTML = template.content
      .replace(template.params.customer_ID, `<strong>${template.params.customer_ID}</strong>`)
      .replace(
        template.params.customer_name,
        `<span data-key="customer_name" contenteditable="true" class="inline-block focus-visible:outline-none border-none font-bold px-2">${template.params.customer_name}</span>`,
      )
      .replace(
        template.params.schedule_time,
        `<span data-key="schedule_time" contenteditable="true" class="inline-block focus-visible:outline-none border-none font-bold px-2">${template.params.schedule_time}</span>`,
      )
      .replace(
        template.params.price_need_to_paid,
        `<span data-key="price_need_to_paid" contenteditable="true" class="inline-block focus-visible:outline-none border-none font-bold px-2">${template.params.price_need_to_paid}</span>`,
      );

    return {
      ...template,
      content: div.innerHTML,
    };
  });
});

const handleSubmit = async (phone: number) => {
  isSend.value = false;
  if (checkZns.value.length > 0) {
    const filterZns = newZnsTemplate.filter((zns: ZnsTemplate) => {
      return checkZns.value.includes(zns.zns_id);
    });
    for (const zns of filterZns) {
      if (messageStore.messages) {
        const findSms = JSON.parse(messageStore.messages.sms).find(
          (sms: any) => sms.name === zns.name,
        );
        const res = await onAddMessage({
          person_id: Number(props.appointment?.person.id),
          phone: `0${phone}`,
          email: "",
          sms_content: zns.content,
          zns_template_id: zns.zns_id,
          zns_content: zns.content,
          zns_params: JSON.stringify(zns.params),
          email_content: "",
          fallback_sms: fallbackSms.value,
        });
        if (res === 0) isSend.value = true;
      }
    }
  } else {
    for (const sms of findSms.value) {
      if (messageStore.messages) {
        const res = await onAddMessage({
          person_id: props?.personId,
          phone: `0${phone}`,
          email: "",
          sms_content: removeHTMLTags(sms.content),
          zns_template_id: "",
          zns_content: "",
          zns_params: "",
          email_content: "",
          fallback_sms: fallbackSms.value,
        });
        if (res === 0) isSend.value = true;
      }
    }
  }
  isShowModalConfirmMessage.hide();
};
const loadPerson = async () => {
  if (props.personId) await getPerson({ id: props.personId, full_name: "", phone: "", email: "" });
  if (currentPerson.value?.person_field?.has_zalo !== "yes") {
    changeTab(1);
  } else changeTab(0);
};
const handleConfirm = (phone: number) => {
  handleSubmit(phone);
};
const loadMessageTemplate = async () => {
  try {
    if (!props.appointment) return;
    await messageStore.getMessageTemplate({
      ...messagePayload,
      person_id: props.appointment.person.id,
      appointment_id: props.appointment.id,
    });
    if (messageStore.messages) {
      const dataParse = JSON.parse(messageStore.messages.zns);
      if (Array.isArray(dataParse)) {
        znsTemplate.value = dataParse.map((item) => {
          const { content, zns_id, name, params } = item;
          const { customer_ID, customer_name, schedule_time, price_need_to_paid } = params;
          return {
            name: name || "",
            zns_id: zns_id || "",
            content: content || "",
            params: {
              customer_name: customer_name || "",
              customer_ID: customer_ID || "",
              schedule_time: schedule_time || "",
              price_need_to_paid: price_need_to_paid || "",
            },
          };
        });
        newZnsTemplate = deepClone(znsTemplate.value);
      }
    }
  } catch (error) {
    znsTemplate.value = [];
    newZnsTemplate = [];
  }
};
watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      fallbackSms.value = true;
      loadPerson();
      loadMessageTemplate();
    } else {
      // Cleanup when modal closes
      checkZns.value = [];
      znsTemplate.value = [];
      newZnsTemplate = [];
      editableSpan.value?.forEach((item) => {
        item.replaceWith(item.cloneNode(true)); // Remove all listeners
      });
      editableSpan.value = undefined;
    }
  },
);
watch(editableSpan, () => {
  editableSpan.value?.map((item, idx) => {
    item.addEventListener("input", () => {
      item.querySelectorAll("[contenteditable]").forEach((itemSpan) => {
        const key = ["customer_name", "customer_ID", "schedule_time", "price_need_to_paid"].find(
          (keyItem) => keyItem === itemSpan.getAttribute("data-key"),
        ) as keyof paramsZnsTemplate;
        newZnsTemplate[idx].content = item.textContent || "";
        if (key && newZnsTemplate[idx]) {
          newZnsTemplate[idx].params[key] = itemSpan.innerHTML;
        }
      });
    });
  });
});
</script>

<template>
  <Dialog size="2xl" :open="props.isOpen" destroyOnHide style="z-index: 9991">
    <Tab.Group :selected-index="selectedTabIndex" @change="changeTab">
      <Dialog.Panel class="overflow-hidden bg-slate-100">
        <Dialog.Title class="bg-white p-0">
          <div
            class="flex items-center border-b border-slate-200/60 px-5 py-5 dark:border-darkmode-400 sm:py-0"
          >
            <Tab.List variant="link-tabs">
              <Tab>
                <Tab.Button
                  v-if="props.appointment && currentPerson?.person_field?.has_zalo === 'yes'"
                  class="cursor-pointer py-5"
                >
                  ZNS
                </Tab.Button>
              </Tab>
              <Tab>
                <Tab.Button class="cursor-pointer py-5"> SMS </Tab.Button>
              </Tab>
              <Tab :full-width="false">
                <Tab.Button class="cursor-pointer py-5"> Email </Tab.Button>
              </Tab>
            </Tab.List>
          </div>

          <a
            class="absolute right-0 top-0 mr-3 mt-3"
            href="#"
            @click="
              (event: MouseEvent) => {
                event.preventDefault();
                emits('onClose');
              }
            "
          >
            <Lucide icon="X" class="h-8 w-8 text-slate-400" />
          </a>
        </Dialog.Title>
        <Dialog.Description class="min-h-[80vh] p-0">
          <Tab.Panels>
            <Tab.Panel class="grid max-h-[80vh] grid-cols-12 gap-5 overflow-y-auto p-5">
              <div
                v-for="(message, key) in updateMessages"
                v-if="currentPerson?.person_field?.has_zalo === 'yes'"
                :key="message.name"
                class="intro-y col-span-12 md:col-span-6 lg:col-span-4 xl:col-span-3"
              >
                <div class="box flex h-full flex-col overflow-hidden rounded-md">
                  <div class="relative flex items-center justify-between bg-slate-200">
                    <FormCheck.Label class="flex w-full items-center justify-between px-5 py-3">
                      <div class="flex-1 truncate text-base font-medium">
                        <span class="mr-2">{{ key + 1 }}</span
                        >{{ message.name }}
                      </div>
                      <FormCheck class="ml-2">
                        <FormCheck.Input
                          id="condition-new"
                          v-model="checkZns"
                          type="checkbox"
                          name="horizontal_radio_button"
                          :value="message.zns_id"
                        />
                      </FormCheck>
                    </FormCheck.Label>
                  </div>
                  <div class="flex-1 p-5">
                    <div
                      ref="editableSpan"
                      class="editableSpan text-sm text-slate-600 dark:text-slate-400"
                      data-content="content"
                      v-html="message.content"
                    ></div>
                  </div>
                </div>
              </div>
            </Tab.Panel>
            <Tab.Panel class="grid max-h-[80vh] grid-cols-12 gap-5 overflow-y-auto p-5">
              <MessageList
                v-if="messageStore.messages?.sms"
                :messages="messageStore.messages.sms"
                @update:find-sms="handleUpdateFindSms"
              />
            </Tab.Panel>
          </Tab.Panels>
        </Dialog.Description>
        <Dialog.Footer class="flex justify-between bg-white text-right">
          <FormCheck class="mr-3">
            <FormCheck.Input
              id="condition-new"
              v-model="fallbackSms"
              type="checkbox"
              :checked="fallbackSms"
              name="horizontal_radio_button"
            />
            <FormCheck.Label class="text-slate-700">Gửi SMS nếu không thành công</FormCheck.Label>
          </FormCheck>
          <Button
            ref="cancelButtonRef"
            variant="primary"
            type="button"
            class="ml-auto w-20"
            :disabled="!isSend || (checkZns.length === 0 && findSms.length === 0)"
            @click="isShowModalConfirmMessage.show"
          >
            <Lucide icon="Send" class="mr-1 h-4 w-4" />
            <span> Gửi </span>
          </Button>
        </Dialog.Footer>
      </Dialog.Panel>
    </Tab.Group>
  </Dialog>
  <MessageSubmitConfirmation
    v-if="currentPerson"
    :is-open="isShowModalConfirmMessage.isVisible.value"
    :person="currentPerson"
    @close="isShowModalConfirmMessage.hide"
    @confirm="
      (phone) => {
        handleConfirm(phone);
      }
    "
  />
</template>
