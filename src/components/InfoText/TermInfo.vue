<template>
  <div class="flex items-center">
    <Term :color-key="termName" size="sm" variant="soft">
      {{ termName }}
    </Term>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import Term from "@/base-components/Term";
import useTerm from "@/hooks/useTerm";
import type { BundleType } from "@/stores/term-store-v2";

const props = defineProps<{
  termType: BundleType | undefined;
  termId: number | undefined;
}>();

const { getTermNameById } = useTerm();

const termName = computed(() =>
  props.termType && props.termId ? getTermNameById(props.termType, props.termId) : "Unknown",
);
</script>
