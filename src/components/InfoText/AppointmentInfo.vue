<template>
  <div class="appointment-info text-xs">
    <!-- Display regular notes first -->
    <div v-if="appointment.notes" class="mb-1 text-gray-600">
      <DateTime
        :time="appointment.start_time"
        show-time
        format="HH:mm"
        size="xs"
        class="mr-1 font-medium text-primary-600"
      />
      <span>{{ appointment.notes }}</span>
    </div>

    <!-- Display parsed extra_notes as tags -->
    <div v-if="hasParsedTasks" class="parsed-tasks">
      <div v-if="combinedNotesTooltip">
        <template v-for="(task, index) in expectedTasks">
          <Tag
            v-if="task.trim() !== ''"
            :key="`expected-${index}`"
            :value="task"
            class="mb-0.5 mr-0.5 inline-block p-1 text-xs font-normal leading-none"
            severity="info"
          ></Tag>
        </template>
        <template v-for="(task, index) in expectedTasksOther">
          <Tag
            v-if="task.trim() !== ''"
            :key="`other-${index}`"
            :value="task"
            class="mb-0.5 mr-0.5 inline-block p-1 text-xs font-normal leading-none"
            severity="warning"
          ></Tag>
        </template>
      </div>
      <!-- Render tags without tippy if no regular notes -->
      <div v-else>
        <template v-for="(task, index) in expectedTasks">
          <Tag
            v-if="task.trim() !== ''"
            :key="`expected-${index}`"
            :value="task"
            class="mb-0.5 mr-0.5 inline-block p-1 text-xs font-normal leading-none"
            severity="info"
          ></Tag>
        </template>
        <template v-for="(task, index) in expectedTasksOther">
          <Tag
            v-if="task.trim() !== ''"
            :key="`other-${index}`"
            :value="task"
            class="mb-0.5 mr-0.5 inline-block p-1 text-xs font-normal leading-none"
            severity="warning"
          ></Tag>
        </template>
      </div>
    </div>
    <!-- Show placeholder if no notes at all -->
    <div v-if="!appointment.notes && !hasParsedTasks" class="italic text-gray-400">
      <DateTime
        :time="appointment.start_time"
        show-time
        format="HH:mm"
        size="xs"
        class="mr-1 font-medium text-gray-500"
      />
      <span>-</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Tag from "primevue/tag";
import { computed } from "vue";
import { AppointmentSort } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue"; // Import DateTime
import { parseExtraNotesV2 } from "@/pages/customer/components/AppointmentTab/utils"; // Assuming this path is correct

const props = defineProps<{
  appointment: AppointmentSort;
}>();

const parsedNotes = computed(() => {
  return parseExtraNotesV2(props.appointment.extra_notes);
});

const expectedTasks = computed(() => {
  return parsedNotes.value.expected_task.filter((task) => task.trim() !== "");
});

const expectedTasksOther = computed(() => {
  return parsedNotes.value.expected_task_other.filter((task) => task.trim() !== "");
});

const hasParsedTasks = computed(() => {
  return expectedTasks.value.length > 0 || expectedTasksOther.value.length > 0;
});

// Combine notes for Tippy only if regular notes exist
const combinedNotesTooltip = computed(() => {
  if (props.appointment.notes && hasParsedTasks.value) {
    return `Ghi chú: ${props.appointment.notes}`;
  }
  return null; // No tooltip if no regular notes
});
</script>
