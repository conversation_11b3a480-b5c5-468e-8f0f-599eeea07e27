<template>
  <div class="flex items-center" v-if="formattedDate" :class="[sizeClasses[size]]">
    <i :class="['pi', icon || 'pi-calendar', 'mr-1', iconColor]" />
    <span :class="textColor">{{ formattedDate }}</span>
  </div>
</template>

<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed } from "vue";

const props = withDefaults(
  defineProps<{
    date?: string | number | Date;
    icon?: string;
    format?: string;
    size?: "small" | "normal" | "large";
    iconColor?: string;
    textColor?: string;
  }>(),
  {
    icon: "pi-calendar",
    format: "DD/MM/YYYY",
    size: "normal",
    iconColor: "text-yellow-500",
    textColor: "text-gray-700",
  },
);

const sizeClasses = {
  small: "text-xs",
  normal: "text-sm",
  large: "text-base",
};

const formattedDate = computed(() => {
  if (!props.date) return "";
  return useDateFormat(props.date, props.format).value;
});
</script>
