<template>
  <div v-if="hasContent">
    <!-- Text mode -->
    <div v-if="props.mode === 'text'" class="min-w-[10rem]">
      <span
        v-for="task in allTasks"
        :key="task"
        :class="['mr-1 inline-block pr-1 font-normal text-slate-500', currentSizeClasses.text]"
      >
        {{ task }}
      </span>
    </div>

    <!-- Tag mode -->
    <div v-else-if="props.mode === 'tag'" class="min-w-[8rem]">
      <Tag
        v-for="task in allTasks"
        v-if="!isEditing"
        :key="task"
        :class="[
          'mb-1 mr-1 text-sm font-normal leading-5',
          currentSizeClasses.tag,
          {
            'cursor-pointer': props.editable,
            'cursor-text': !props.editable,
            'opacity-75': props.muted,
          },
        ]"
        :severity="props.muted ? 'secondary' : 'info'"
        :value="isEditing === task ? editedTask : task"
        fluid
        @click="startEditing(task)"
      />

      <div
        v-if="isEditing && props.editable"
        ref="editingContainer"
        class="flex min-w-[120px] items-center gap-1"
      >
        <Textarea
          v-model="editedTask"
          v-focus
          :class="[
            'inline-block flex-1 text-sm font-normal leading-5',
            currentSizeClasses.tag,
            'border-none bg-[#e0f2fe] text-[#2e84b3]',
          ]"
          auto-resize
          fluid
        />
        <Button
          v-tooltip="'Xác nhận'"
          class="confirm-btn flex-shrink-0 p-1"
          icon="pi pi-check"
          rounded
          size="small"
          text
          @click="saveEdit"
        />
      </div>
    </div>

    <!-- List mode -->
    <ul v-else-if="props.mode === 'list'" class="min-w-[12rem]">
      <li
        v-for="task in allTasks"
        :key="task"
        :class="['font-normal', currentSizeClasses.list, props.muted ? 'text-slate-500' : '']"
      >
        <Lucide
          :class="['mr-1 inline', currentSizeClasses.icon, props.muted ? 'text-slate-500' : '']"
          :icon="'Check'"
        />
        {{ task.charAt(0).toUpperCase() + task.slice(1) }}
      </li>
    </ul>
  </div>

  <div v-else>
    <div v-if="props.editable">
      <Button
        v-if="!isAddingNew"
        v-tooltip="'Thêm ghi chú'"
        aria-label="Filter"
        icon="pi pi-pen-to-square"
        rounded
        size="small"
        text
        @click="startAddingNew"
      />

      <div
        v-if="isAddingNew"
        ref="addingContainer"
        class="flex min-w-[120px] flex-wrap items-center gap-1"
      >
        <Textarea
          v-model="editedTask"
          v-focus
          :class="[
            'inline-block flex-1 text-sm font-normal leading-5',
            currentSizeClasses.tag,
            'border-none bg-[#e0f2fe] text-[#2e84b3]',
          ]"
          auto-resize
          fluid
        />
        <Button
          v-tooltip="'Xác nhận'"
          class="confirm-btn flex-shrink-0 p-1"
          icon="pi pi-check"
          rounded
          size="small"
          text
          @click="saveNewTask"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onClickOutside } from "@vueuse/core";
import Button from "primevue/button";
import Tag from "primevue/tag";
import { computed, ref } from "vue";

import Lucide from "@/base-components/Lucide";
import { parseExtraNotesV2 } from "@/pages/customer/components/AppointmentTab/utils";

const props = withDefaults(
  defineProps<{
    notes?: string;
    extra_notes?: string;
    mode?: "text" | "tag" | "list";
    size?: "small" | "normal" | "large";
    editable?: boolean;
    muted?: boolean;
  }>(),
  {
    mode: "tag",
    size: "normal",
    editable: false,
    muted: false,
  },
);

const sizeClasses = {
  small: {
    text: "text-xs",
    tag: "text-xs p-0.5",
    list: "text-xs",
    icon: "h-2 w-2",
  },
  normal: {
    text: "text-sm",
    tag: "text-sm p-1",
    list: "text-sm",
    icon: "h-3 w-3",
  },
  large: {
    text: "text-base",
    tag: "text-sm p-1.5",
    list: "text-base",
    icon: "h-4 w-4",
  },
};

const currentSizeClasses = computed(() => sizeClasses[props.size]);

const parseNotes = (notes: string): string[] => {
  return notes
    .split("\n")
    .map((line) => line.replace(/^notes:\s*/, "").trim())
    .filter(Boolean);
};

const parsedNotes = computed(() => (props.notes ? parseNotes(props.notes) : []));
const parsedExtraNotes = computed(() =>
  props.extra_notes
    ? parseExtraNotesV2(props.extra_notes)
    : { expected_task: [], expected_task_other: [] },
);

const allTasks = computed(() =>
  [
    ...parsedNotes.value,
    ...parsedExtraNotes.value.expected_task,
    ...parsedExtraNotes.value.expected_task_other,
  ].filter((task) => task.trim() !== ""),
);

const hasContent = computed(() => allTasks.value.length > 0);

const emit = defineEmits<{
  (e: "update:notes", value: string): void;
  (e: "update:extra_notes", value: string): void;
}>();

const isEditing = ref<string | null>(null);
const editedTask = ref("");
const vFocus = {
  mounted: (el: HTMLElement) => el.focus(),
};

const startEditing = (task: string) => {
  if (!props.editable) return;
  isEditing.value = task;
  editedTask.value = task;
};

const saveEdit = async () => {
  if (!isEditing.value) return;

  const oldTask = isEditing.value;
  const newTask = editedTask.value.trim();

  // Only emit if the value has changed
  if (oldTask !== newTask) {
    // Update notes or extra_notes based on where the task was found
    if (props.notes?.includes(oldTask)) {
      const newNotes = props.notes.replace(oldTask, newTask).trim();
      emit("update:notes", newNotes);
    } else if (props.extra_notes) {
      const newExtraNotes = props.extra_notes.replace(oldTask, newTask).trim();
      emit("update:extra_notes", newExtraNotes);
    }
  }

  isEditing.value = null;
  editedTask.value = "";
};

const cancelEdit = () => {
  isEditing.value = null;
  editedTask.value = "";
};

const isAddingNew = ref(false);

const startAddingNew = () => {
  isAddingNew.value = true;
  editedTask.value = "";
};

const saveNewTask = async () => {
  if (editedTask.value.trim()) {
    const newTask = editedTask.value.trim();
    // Add to notes by default
    const newNotes = props.notes ? `${props.notes}\n${newTask}` : newTask;
    await emit("update:notes", newNotes);
  }
  isAddingNew.value = false;
  editedTask.value = "";
};

const cancelAddingNew = () => {
  isAddingNew.value = false;
  editedTask.value = "";
};

const editingContainer = ref<HTMLElement | null>(null);
const addingContainer = ref<HTMLElement | null>(null);

onClickOutside(editingContainer, (event) => {
  // Prevent cancellation if clicking the confirm button
  const target = event.target as HTMLElement;
  if (target.closest(".confirm-btn")) return;
  cancelEdit();
});

onClickOutside(addingContainer, (event) => {
  // Prevent cancellation if clicking the confirm button
  const target = event.target as HTMLElement;
  if (target.closest(".confirm-btn")) return;
  cancelAddingNew();
});
</script>
