<template>
  <div class="flex items-center gap-1">
    <i :class="['pi pi-filter mr-1 text-info', iconClass]" v-if="showIcon"></i>

    <span
      :class="[
        'whitespace-normal break-words border-none p-0 opacity-100 shadow-none',
        'decoration-none cursor-default',
        labelClass,
      ]"
    >
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  text: string;
  iconClass?: string;
  labelClass?: string;
  showIcon?: boolean;
}

// Define props with defaults
const props = withDefaults(defineProps<Props>(), {
  stageId: undefined,
  iconClass: "",
  labelClass: "",
  showIcon: true,
});
</script>
