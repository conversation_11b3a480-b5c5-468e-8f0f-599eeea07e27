<template>
  <div class="flex items-center" v-if="formattedAddress">
    <i class="pi pi-map-marker mr-2 mt-1 text-green-500"></i>
    <span>{{ formattedAddress }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  addressNumber?: string;
  ward?: string;
  district?: string;
  province?: string;
}>();

const formattedAddress = computed(() => {
  const parts = [props.addressNumber, props.ward, props.district, props.province].filter(Boolean);

  return parts.join(", ");
});
</script>
