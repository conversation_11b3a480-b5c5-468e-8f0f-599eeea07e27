<template>
  <div class="flex items-center">
    <i :class="['pi', genderInfo.icon, 'mr-1', genderInfo.color]"></i>
    <span v-if="showText">{{ genderInfo.text }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  gender?: string;
  showText?: boolean;
}>();

const genderMap = {
  male: { icon: "pi-mars", color: "text-blue-500", text: "Nam" },
  female: { icon: "pi-venus", color: "text-pink-500", text: "Nữ" },
  default: { icon: "pi-question", color: "text-gray-500", text: "Khác" },
};

const genderInfo = computed(() => {
  const key = props.gender?.toLowerCase();
  return genderMap[key as keyof typeof genderMap] || genderMap.default;
});
</script>
