<template>
  <div class="relative">
    <div
      @click="toggle"
      :class="[
        'flex cursor-pointer items-center rounded-md p-2 transition-colors duration-200 hover:bg-gray-100',
        getBorderClass,
      ]"
    >
      <i
        :class="[
          getPriorityIconClass(selectedPriority as TaskPriorityEnum),
          getPriorityClass(selectedPriority as TaskPriorityEnum),
        ]"
      />
    </div>

    <Popover
      ref="popover"
      :pt="{
        content: 'p-0',
      }"
    >
      <div class="max-h-60 overflow-y-auto">
        <ul class="m-0 list-none p-0">
          <li
            v-for="priority in priorities"
            :key="priority.value"
            @click="handlePriorityChange(priority.value as TaskPriorityEnum)"
            class="flex cursor-pointer items-center rounded-md p-2 hover:bg-gray-100"
          >
            <i
              :class="[
                getPriorityIconClass(priority.value as TaskPriorityEnum),
                getPriorityClass(priority.value as TaskPriorityEnum),
              ]"
            />
            <span class="ml-2">{{ priority.label }}</span>
          </li>
        </ul>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import Popover from "primevue/popover";
import { computed, PropType, ref } from "vue";

import { mapTaskPriorityEnum } from "@/api/bcare-configs";
import { TaskPriorityEnum } from "@/api/bcare-enum";

interface Priority {
  value: TaskPriorityEnum | string | number;
  label: string;
}

interface PriorityStyle {
  iconClass: string;
  class: string;
}

const props = defineProps({
  modelValue: {
    type: [Number, Object, String] as PropType<number | TaskPriorityEnum | null | string>,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue"]);

const popover = ref();
const priorities = computed<Priority[]>(() =>
  Object.entries(mapTaskPriorityEnum)
    .filter(([key]) => key !== TaskPriorityEnum.ALL.toString())
    .map(([key, value]) => ({
      value: parseInt(key) as TaskPriorityEnum,
      label: value,
    }))
    .reverse(),
);

const priorityStyles: Record<TaskPriorityEnum, PriorityStyle> = {
  [TaskPriorityEnum.HIGH]: { iconClass: "pi pi-flag-fill", class: "text-red-500 font-bold" },
  [TaskPriorityEnum.MEDIUM]: { iconClass: "pi pi-flag-fill", class: "text-yellow-500 font-bold" },
  [TaskPriorityEnum.LOW]: { iconClass: "pi pi-flag-fill", class: "text-blue-500 font-bold" },
  [TaskPriorityEnum.ALL]: { iconClass: "pi pi-minus", class: "text-gray-500 font-bold" },
};

const getPriorityIconClass = (value: TaskPriorityEnum): string => {
  return priorityStyles[value]?.iconClass || "";
};

const getPriorityClass = (value: TaskPriorityEnum): string => {
  return priorityStyles[value]?.class || "";
};

const selectedPriority = computed({
  get: () => props.modelValue,
  set: (value: number | TaskPriorityEnum | null | string) => {
    emit("update:modelValue", Number(value));
  },
});

function toggle(event: Event) {
  popover.value.toggle(event);
}

function handlePriorityChange(newPriority: TaskPriorityEnum) {
  selectedPriority.value = newPriority;
  popover.value.hide();
}

const getBorderClass = computed(() => {
  const priority = selectedPriority.value as TaskPriorityEnum;
  const colorClass = priorityStyles[priority]?.class.split(" ")[0] || "border-gray-200";
  return `border border-dashed ${colorClass.replace("text-", "border-")}`;
});
</script>
