<template>
  <Select
    v-model="selectedPriority"
    :options="priorities"
    optionLabel="label"
    optionValue="value"
    placeholder="Chọn độ ưu tiên"
    class="flex h-full w-full items-center"
  >
    <template #value="slotProps">
      <div v-if="slotProps.value" class="flex items-center">
        <i :class="[getPriorityIconClass(slotProps.value), 'mr-2']"></i>
        <div>{{ getPriorityLabel(slotProps.value) }}</div>
      </div>
      <span v-else>
        {{ slotProps.placeholder }}
      </span>
    </template>
    <template #option="slotProps">
      <div class="flex items-center">
        <i :class="[getPriorityIconClass(slotProps.option.value), 'mr-2']"></i>
        <div>{{ slotProps.option.label }}</div>
      </div>
    </template>
  </Select>
</template>

<script setup lang="ts">
import Select from "primevue/select";
import { computed } from "vue";
import { PropType } from "vue";

import { mapTaskPriorityEnum } from "@/api/bcare-configs";
import { TaskPriorityEnum } from "@/api/bcare-enum";

const props = defineProps({
  modelValue: {
    type: [Number, Object, String] as PropType<number | TaskPriorityEnum | null | string>,
    default: null,
  },
});

const emit = defineEmits<{
  (e: "update:modelValue", value: number | TaskPriorityEnum | null): void;
}>();

interface Priority {
  value: TaskPriorityEnum;
  label: string;
}

const priorities = computed<Priority[]>(() =>
  Object.entries(mapTaskPriorityEnum)
    .filter(([key]) => key !== TaskPriorityEnum.ALL.toString())
    .map(([key, value]) => ({
      value: parseInt(key) as TaskPriorityEnum,
      label: value,
    }))
    .reverse(),
);

const priorityStyles: Record<TaskPriorityEnum, string> = {
  [TaskPriorityEnum.HIGH]: "pi pi-flag-fill text-red-500",
  [TaskPriorityEnum.MEDIUM]: "pi pi-flag-fill text-yellow-500",
  [TaskPriorityEnum.LOW]: "pi pi-flag-fill text-blue-500",
  [TaskPriorityEnum.ALL]: "pi pi-minus text-gray-500",
};

const getPriorityIconClass = (value: TaskPriorityEnum): string => {
  return priorityStyles[value] || "";
};

const getPriorityLabel = (value: TaskPriorityEnum): string => {
  return priorities.value.find((p) => p.value === value)?.label || "";
};

const selectedPriority = computed({
  get: () => Number(props.modelValue),
  set: (value: number | TaskPriorityEnum | null | string) => {
    emit("update:modelValue", Number(value));
  },
});
</script>
