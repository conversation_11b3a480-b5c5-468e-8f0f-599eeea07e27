<template>
  <template v-if="hasAppointments">
    <div v-if="props.mode === 'text'">
      <template v-if="track.appointments">
        <template v-for="appointment in parsedAppointments" :key="appointment.id">
          <span v-tooltip="appointment.notes">
            <div>
              <template v-for="(task, index) in appointment.expectedTasks">
                <span
                  v-if="task.trim() !== ''"
                  :key="`expected-${index}`"
                  class="mr-1 inline-block pr-1 text-sm font-normal text-slate-500"
                  >{{ task }}</span
                >
              </template>
              <template v-for="(task, index) in appointment.expectedTasksOther">
                <span
                  v-if="task.trim() !== ''"
                  :key="`other-${index}`"
                  class="mr-1 inline-block pr-1 text-sm font-normal text-slate-500"
                  >{{ task }}</span
                >
              </template>
            </div>
          </span>
        </template>
      </template>
    </div>

    <div v-else-if="props.mode === 'tag'">
      <template v-if="track.appointments">
        <template v-for="appointment in parsedAppointments" :key="appointment.id">
          <span v-tooltip="appointment.notes">
            <template v-for="(task, index) in appointment.expectedTasks">
              <Tag
                v-if="task.trim() !== ''"
                :key="`expected-${index}`"
                :value="task"
                class="mb-1 mr-1 inline-block p-1 text-xs font-normal leading-none"
                severity="info"
              ></Tag>
            </template>
            <template v-for="(task, index) in appointment.expectedTasksOther">
              <Tag
                v-if="task.trim() !== ''"
                :key="`other-${index}`"
                :value="task"
                class="mb-1 mr-1 inline-block p-1 text-xs font-normal leading-none"
                severity="info"
              ></Tag>
            </template>
          </span>
        </template>
      </template>
    </div>

    <template v-else-if="props.mode === 'list'">
      <template v-if="track.appointments">
        <template v-for="appointment in parsedAppointments" :key="appointment.id">
          <span v-tooltip="appointment.notes">
            <ul>
              <template v-for="(task, index) in appointment.expectedTasks">
                <li
                  v-if="task.trim() !== ''"
                  :key="`expected-${index}`"
                  class="text-sm font-normal text-slate-500"
                >
                  <Lucide class="mr-1 inline h-3 w-3" icon="Check"></Lucide>
                  {{ task.charAt(0).toUpperCase() + task.slice(1) }}
                </li>
              </template>
              <template v-for="(task, index) in appointment.expectedTasksOther">
                <li
                  v-if="task.trim() !== ''"
                  :key="`other-${index}`"
                  class="text-sm font-normal text-slate-500"
                >
                  <Lucide class="mr-1 inline h-3 w-3" icon="Check"></Lucide>
                  {{ task.charAt(0).toUpperCase() + task.slice(1) }}
                </li>
              </template>
            </ul>
          </span>
        </template>
      </template>
    </template>
  </template>
</template>

<script lang="ts" setup>
import Tag from "primevue/tag";
import { computed } from "vue";

import { TrackResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { parseExtraNotesV2 } from "@/pages/customer/components/AppointmentTab/utils";

const props = withDefaults(
  defineProps<{
    track: TrackResponse;
    mode?: "text" | "tag" | "list";
  }>(),
  {
    mode: "tag",
  },
);

const hasAppointments = computed(() => {
  if (!props.track.appointments || props.track.appointments.length === 0) {
    return false;
  }

  // Kiểm tra xem có ít nhất một appointment có expectedTasks hoặc expectedTasksOther không rỗng
  return parsedAppointments.value.some(
    (appointment) =>
      (appointment.expectedTasks && appointment.expectedTasks.length > 0) ||
      (appointment.expectedTasksOther && appointment.expectedTasksOther.length > 0),
  );
});

const parsedAppointments = computed(() => {
  return (
    props.track.appointments?.map((appointment) => {
      const parsedNotes = parseExtraNotesV2(appointment.extra_notes);
      return {
        ...appointment,
        expectedTasks: parsedNotes.expected_task.filter((task) => task.trim() !== ""),
        expectedTasksOther: parsedNotes.expected_task_other.filter((task) => task.trim() !== ""),
      };
    }) || []
  );
});
</script>
