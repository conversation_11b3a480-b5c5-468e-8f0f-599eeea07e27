// useGlobalDealPopover.ts
import { computed, reactive } from "vue";

import { DealResponse } from "@/api/bcare-types-v2";
import { dealList } from "@/api/bcare-v2";

interface PopoverState {
  isVisible: boolean;
  personId: number | null;
  deals: DealResponse[];
  loading: boolean;
  position: { left: number; top: number };
  onSelect?: (deal: DealResponse) => void;
  activeDealId: number;
  currentPersonId: number | null;
}

const state = reactive<PopoverState>({
  isVisible: false,
  personId: null,
  deals: [],
  loading: false,
  position: { left: 0, top: 0 },
  onSelect: undefined,
  activeDealId: 0,
  currentPersonId: null,
});

//TODO optimize them co che neu fetch lau thi set loading de hien skeleton
//Hien tai load qua nhanh hien skeleton se bi flickering content
export function usePersonDealsPopover() {
  const fetchDeals = async (personId: number) => {
    // Optimization: Chỉ fetch lại nếu là person mới hoặc chưa có deals
    if (state.personId === personId && state.deals.length > 0) {
      return;
    }

    state.personId = personId;
    state.loading = true;

    try {
      const response = await dealList({
        filter: { person_id: personId },
        page: 1,
        page_size: 10,
        include_relation: false,
      });
      state.deals = response.data?.deals || [];
    } catch (error) {
      console.error("Error fetching deals:", error);
      // Có thể thêm xử lý lỗi cụ thể ở đây
    } finally {
      state.loading = false;
    }
  };

  const show = async (
    event: Event,
    personId: number,
    currentDealId: number,
    callback?: (deal: DealResponse) => void,
  ) => {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();

    // Toggle popover nếu click vào cùng một deal
    if (state.isVisible && state.activeDealId === currentDealId) {
      hide();
      return;
    }

    hide();

    state.currentPersonId = personId;
    state.activeDealId = currentDealId;

    // Tính toán vị trí mới, bao gồm scroll
    const scrollLeft = window.scrollX || document.documentElement.scrollLeft;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;

    state.position = {
      left: rect.left + scrollLeft,
      top: rect.bottom + scrollTop,
    };

    state.onSelect = callback;

    // Fetch deals trước khi hiển thị popover
    await fetchDeals(personId);

    // Chỉ hiển thị popover nếu có deals để hiển thị
    if (state.deals.length > 0) {
    state.isVisible = true;
    }
  };

  const hide = () => {
    state.isVisible = false;
    state.currentPersonId = null;
  };

  const selectDeal = (deal: DealResponse) => {
    if (state.onSelect) {
      state.onSelect(deal);
    }
    hide();
  };

  return {
    isVisible: computed(() => state.isVisible),
    loading: computed(() => state.loading),
    deals: computed(() => state.deals),
    position: computed(() => state.position),
    activeDealId: computed(() => state.activeDealId),
    show,
    hide,
    selectDeal,
  };
}
