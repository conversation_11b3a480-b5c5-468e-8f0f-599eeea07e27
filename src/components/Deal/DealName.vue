<!-- ParentComponent.vue -->
<script lang="ts" setup>
import { StateType } from "@/api/extend-types";
import State from "@/base-components/State.vue";

interface Props {
  name: string;
  state?: string | StateType;
  nameClass?: string;
  shine?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  nameClass: "font-medium",
  shine: true,
});
</script>

<template>
  <div class="flex items-center space-x-2">
    <span :class="props.nameClass">{{ props.name }}</span>
    <State :state="props.state" size="sm" v-if="props.state" :shine="props.shine" />
  </div>
</template>
