<script setup lang="ts">
import { ref, watch, computed } from "vue";
import Popover from "primevue/popover";
import type { PopoverPassThroughOptions } from "primevue/popover";
import ProgressSpinner from "primevue/progressspinner";

import type { Deal, DealListRequest } from "@/api/bcare-types-v2";
import DealName from "@/components/Deal/DealName.vue";
import { formatShortDateTime } from "@/utils/time-helper";
import useDeal from "@/hooks/useDeal";
import State from "@/base-components/State.vue";
import { StateType } from "@/api/extend-types";

interface Props {
  deal: Deal | null;
  disabled?: boolean;
  personId?: number;
  state?: string | StateType;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  personId: undefined,
});

const emit = defineEmits<{
  (e: "update:deal", deal: Deal): void;
}>();

const popoverRef = ref();
const hasFetched = ref(false); // Track if fetch has been attempted

const {
  deals,
  isLoading,
  error,
  fetchDeals: fetchDealsFromComposable,
} = useDeal({ useStore: false });

const personId = computed(() => props.deal?.person_id || props.personId);

const triggerLabelClasses = "flex items-center gap-1 !font-light";
const popoverPanelClasses =
  "min-w-[15rem] !p-0 overflow-hidden rounded-md border border-slate-200 bg-white shadow-[0px_3px_10px_#00000017] dark:border-transparent dark:bg-darkmode-600";
const listItemClasses =
  "group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400";
const activeListItemClasses = "box-content border border-highlight bg-soft text-primary";

const pt: PopoverPassThroughOptions = {
  root: {},
  content: {
    class: popoverPanelClasses,
  },
};

const loadDeals = async () => {
  if (!personId.value || isLoading.value || hasFetched.value || props.disabled) {
    return;
  }
  hasFetched.value = true;
  try {
    const request: DealListRequest = {
      filter: { person_id: personId.value },
      page: 1,
      page_size: 100,
      include_relation: false,
    };
    await fetchDealsFromComposable(request);
    if (error.value) {
      console.error("Failed to fetch deals via useDeal:", error.value);
      hasFetched.value = false;
    }
  } catch (err) {
    console.error("Error calling fetchDeals:", err);
    hasFetched.value = false;
  }
};

const selectDeal = (selectedDeal: Deal) => {
  if (selectedDeal.id !== props.deal?.id) {
    emit("update:deal", selectedDeal);
  }
  popoverRef.value?.hide();
};

const handlePopoverShow = () => {
  if (!hasFetched.value && !isLoading.value) {
    loadDeals();
  }
};

watch(personId, (newPersonId, oldPersonId) => {
  if (newPersonId !== oldPersonId) {
    hasFetched.value = false;
    deals.value = [];
  }
});
</script>

<template>
  <div>
    <button
      type="button"
      @click="popoverRef?.toggle($event)"
      :disabled="disabled || !personId"
      :aria-haspopup="true"
      :aria-controls="`popover-content-${personId || 'no-person'}`"
      :class="[triggerLabelClasses, (disabled || !personId) && 'cursor-default']"
    >
      <i class="pi pi-shopping-cart text-lime-500" />
      <DealName
        v-if="deal"
        :name="deal.name || 'Deal chưa đặt tên'"
        :class="[
          'whitespace-normal break-words border-none p-0 shadow-none',
          !disabled && personId
            ? 'underline decoration-dotted underline-offset-2'
            : 'decoration-none opacity-100',
        ]"
        name-class="!font-normal"
      />
      <State v-if="state" :state="state" size="sm" />
      <span v-else class="italic text-gray-500">Chọn Deal</span>
    </button>

    <Popover
      ref="popoverRef"
      :pt="pt"
      @show="handlePopoverShow"
      :id="`popover-content-${personId || 'no-person'}`"
    >
      <div v-if="isLoading" class="flex justify-center p-4">
        <ProgressSpinner
          style="width: 30px; height: 30px"
          strokeWidth="4"
          fill="transparent"
          animationDuration=".5s"
          aria-label="Loading deals"
        />
      </div>
      <div v-else-if="deals && deals.length > 0" class="py-2">
        <ul
          class="m-0 max-h-[50vh] snap-y list-none space-y-1.5 overflow-hidden overflow-y-auto overscroll-contain px-2"
          role="listbox"
          :aria-activedescendant="deal ? `deal-option-${deal.id}` : undefined"
        >
          <li
            v-for="dealItem in deals"
            :key="dealItem.id"
            :id="`deal-option-${dealItem.id}`"
            role="option"
            :aria-selected="dealItem.id === deal?.id"
            :class="[listItemClasses, { [activeListItemClasses]: dealItem.id === deal?.id }]"
            @click="selectDeal(dealItem)"
          >
            <div class="flex-1">
              <DealName :name="dealItem.name || 'Deal chưa đặt tên'" :state="'won'" />
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatShortDateTime(dealItem.created_at) }}
              </div>
            </div>
            <div class="ml-3 text-gray-600 dark:text-gray-400">
              <div
                v-if="dealItem.id === deal?.id"
                class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                :class="{ 'bg-primary': dealItem.id === deal?.id }"
              ></div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else class="px-3 py-2 text-sm text-gray-500">
        {{
          hasFetched
            ? "Không tìm thấy deal nào."
            : error
              ? "Lỗi khi tải deals."
              : "Không thể tải deals."
        }}
      </div>
    </Popover>
  </div>
</template>
