<template>
  <div class="mx-auto w-full">
    <div class="relative">
      <div v-if="showLabel" class="mb-2 flex items-center justify-between">
        <div>
          <span
            class="inline-block rounded-full bg-teal-200 px-2 py-1 text-xs font-medium uppercase text-teal-600"
          >
            {{ label }}
          </span>
        </div>
        <div class="text-right">
          <span class="inline-block text-xs font-medium text-teal-600">
            {{ percentageFormatted }}%
          </span>
        </div>
      </div>
      <div class="mb-3 flex overflow-hidden rounded bg-gray-200 text-sm">
        <div
          v-for="(segment, index) in segments"
          :key="index"
          :style="{ width: `${segment.percentage}%`, backgroundColor: segment.color }"
          class="flex flex-col justify-center whitespace-nowrap text-center shadow-none"
        >
          <span
            v-if="shouldShowAmount(segment)"
            class="border border-transparent px-2 py-1.5 text-sm font-semibold"
            :style="{ color: segment.labelColor }"
          >
            {{ formatCurrency(segment.amount) }}
          </span>
        </div>
      </div>

      <div class="flex flex-wrap gap-2.5 text-sm">
        <div v-for="(segment, index) in segments" :key="index" class="flex items-center">
          <template v-if="segment.label === 'Tổng tiền thanh toán'">
            <i class="pi pi-dollar mr-1 text-slate-700" />
          </template>
          <span
            v-else
            :style="{ backgroundColor: segment.color }"
            class="mr-1 h-3 w-3 flex-shrink-0 rounded-full"
          />
          <span
            class="cursor-pointer truncate"
            v-if="segment.label === 'Khuyến mãi' && discountUsages?.length"
            @click="promotionRef?.toggle($event)"
            ref="promotionRef"
            v-tooltip="segment.label"
          >
            {{ formatCurrency(segment.amount) }}
          </span>
          <span v-else class="truncate" v-tooltip="segment.label">
            {{ formatCurrency(segment.amount) }}
          </span>
        </div>
      </div>

      <!-- Discount Popover -->
      <Popover ref="promotionRef" :autoHide="true">
        <div class="mb-2 text-sm font-medium">Chi tiết khuyến mãi</div>
        <div class="space-y-2">
          <div
            v-for="usage in discountUsages"
            :key="usage.id"
            class="flex items-center justify-between gap-2 rounded-md border border-sky-100 bg-sky-50 p-2"
          >
            <div class="text-sm">
              <div class="font-medium text-sky-700">{{ usage.discount.name }}</div>
              <div class="text-xs text-sky-500">
                {{ formatDiscountValue(usage) }}
              </div>
            </div>
            <div class="text-sm font-medium text-success">-{{ formatCurrency(usage.value) }}</div>
          </div>
        </div>
      </Popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import type { DiscountUsageResponse } from "@/api/bcare-types-v2";
import { formatCurrency } from "@/utils/helper";

interface Props {
  label?: string;
  showLabel?: boolean;
  totalAmount: number;
  amountPaid: number;
  depositAmount: number;
  promotionAmount: number;
  hideSmallAmounts?: boolean;
  smallAmountThreshold?: number;
  discountUsages?: DiscountUsageResponse[];
}

const props = withDefaults(defineProps<Props>(), {
  label: "Tiến độ thanh toán",
  showLabel: true,
  hideSmallAmounts: false,
  smallAmountThreshold: 1,
  discountUsages: () => [],
});

const promotionRef = ref();

const segments = computed(() => {
  const total = props.totalAmount;
  const paid = props.amountPaid;
  const deposit = props.depositAmount;
  const promotion = props.promotionAmount;
  const remaining = Math.max(0, total - paid - deposit);

  return [
    {
      label: "Tổng tiền thanh toán",
      color: "#374151",
      labelColor: "#fff",
      amount: total,
      percentage: 0,
    },

    {
      label: "Trả trước",
      color: "#fbbf24",
      labelColor: "#fff",
      amount: deposit,
      percentage: (deposit / total) * 100,
    },
    {
      label: "Đã trả",
      color: "#34d399",
      labelColor: "#fff",
      amount: paid,
      percentage: (paid / total) * 100,
    },
    {
      label: "Còn lại",
      color: "#ececec",
      labelColor: "#777",
      amount: remaining,
      percentage: (remaining / total) * 100,
    },
    {
      label: "Khuyến mãi",
      color: "#60a5fa",
      labelColor: "#fff",
      amount: promotion,
      percentage: (promotion / total) * 100,
    },
  ];
});

const shouldShowAmount = (segment: { percentage: number; label: string }) => {
  if (!props.hideSmallAmounts) {
    return true;
  }
  return (
    segment.percentage >= props.smallAmountThreshold ||
    ["Đã trả", "Còn lại"].includes(segment.label)
  );
};

const percentage = computed(() => {
  const paidPercentage =
    ((props.amountPaid + props.depositAmount + props.promotionAmount) / props.totalAmount) * 100;
  return Math.min(100, paidPercentage);
});

const percentageFormatted = computed(() => {
  return percentage.value.toFixed(2);
});

// Format discount value based on type
const formatDiscountValue = (discount: DiscountUsageResponse) => {
  const { type, value } = discount.discount;
  return type === "percent" ? `${(value * 100).toFixed(0)}%` : formatCurrency(value);
};
</script>
