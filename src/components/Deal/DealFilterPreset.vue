<template>
  <div>
    <div class="flex flex-wrap gap-2">
      <!-- Deal Presets -->
      <template v-for="preset in dealPresets" :key="preset.type">
        <!-- Use PrimeVue Button -->
        <Button
          :label="preset.name"
          :icon="getButtonProps(preset).icon"
          :severity="getButtonProps(preset).severity"
          :outlined="getButtonProps(preset).outlined"
          @click="() => handleSelectPreset(preset.type)"
          :class="{
            'border border-gray-300 text-sm': true,
            'border-0': selectedPreset === preset.type,
          }"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import { useDealDatatableStore } from "@/stores/deal-datatable-store";
import { useAuthStore } from "@/stores/auth-store";

import {
  DEAL_FILTER_PRESETS,
  type DealFilterPreset,
} from "@/pages/customer/components/DealsTab/constants";

interface Props {
  modelValue?: DealFilterPreset;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: DEAL_FILTER_PRESETS.ALL,
});

const emit = defineEmits<{
  "update:modelValue": [value: DealFilterPreset];
}>();

const dealStore = useDealDatatableStore();
const authStore = useAuthStore();

// Updated PresetDefinition interface
interface PresetDefinition {
  type: DealFilterPreset;
  name: string;
  icon: string;
  severity?: "success" | "info" | "danger" | "primary" | "secondary" | "warning" | "contrast";
}

// Updated dealPresets array with PrimeIcons and severities
const dealPresets: PresetDefinition[] = [
  { type: DEAL_FILTER_PRESETS.ALL, name: "Tất cả", icon: "pi pi-list", severity: "primary" },
  {
    type: DEAL_FILTER_PRESETS.MY_DEALS,
    name: "Deal đã tạo",
    icon: "pi pi-user",
    severity: "primary",
  },
  {
    type: DEAL_FILTER_PRESETS.WON_TODAY,
    name: "Deal won",
    icon: "pi pi-trophy",
    severity: "success",
  },
  {
    type: DEAL_FILTER_PRESETS.LOST_TODAY,
    name: "Deal lost",
    icon: "pi pi-shield",
    severity: "danger",
  },
  {
    type: DEAL_FILTER_PRESETS.ACTIVE_DEALS,
    name: "Deal active",
    icon: "pi pi-bolt",
    severity: "info",
  },
  // { type: DEAL_FILTER_PRESETS.RELATED_DEALS, name: "Deal liên quan", icon: 'pi pi-users' }, // Example PrimeIcon if needed
  // { type: DEAL_FILTER_PRESETS.CREATED_TODAY, name: "Tạo hôm nay", icon: 'pi pi-calendar-plus' }, // Example PrimeIcon if needed
];

const selectedPreset = computed<DealFilterPreset>({
  get: () => props.modelValue,
  set: (value: DealFilterPreset) => emit("update:modelValue", value),
});

const getButtonProps = (preset: PresetDefinition) => {
  const isSelected = selectedPreset.value === preset.type;
  if (isSelected) {
    return {
      severity: preset.severity || "primary",
      outlined: false,
      icon: preset.icon,
    };
  } else {
    return {
      severity: "secondary",
      outlined: true,
      icon: preset.icon,
    };
  }
};

const handleSelectPreset = (presetType: DealFilterPreset): void => {
  if (selectedPreset.value !== presetType) {
    selectedPreset.value = presetType;

    const currentUserId = authStore.user?.user?.id;
    if (!currentUserId && presetType === DEAL_FILTER_PRESETS.MY_DEALS) {
      return;
    }

    dealStore.applyFilterPreset(presetType, currentUserId || 0);
  }
};
</script>
