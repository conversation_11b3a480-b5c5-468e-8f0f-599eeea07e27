<template>
  <div
    :class="{ 'animate-pulse': isLoading }"
    :data-trackid="modelValue.id"
    class="list-group-item relative"
  >
    <div
      class="box cursor-pointer space-y-2 divide-y divide-dotted p-3 hover:ring-1 hover:ring-primary-500 hover:ring-opacity-50"
      @click="
        router.push({
          name: 'top-menu-customer-profile',
          params: { id: modelValue.person_id },
          query: { tab: 'deals' },
        })
      "
    >
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <PersonAvatar :issues="modelValue.person?.issues" :person="modelValue.person" />
        </div>
        <div class="ml-2 flex-grow">
          <div class="group font-medium leading-tight text-slate-600">
            <span v-tooltip.top="modelValue.person?.phone">{{ modelValue.person?.full_name }}</span>
            <span
              v-if="modelValue.person?.person_field.code"
              class="z-5 invisible relative ml-2 rounded border border-warning/50 bg-orange-50 px-0.5 text-xs font-semibold text-warning/80 group-hover:visible dark:bg-secondary"
            >
              {{ modelValue.person?.person_field.code }}
            </span>
          </div>
          <div
            class="mt-0.5 inline-flex items-center pr-1 text-sm text-slate-500 hover:bg-soft hover:text-primary"
          >
            <Lucide class="mr-1 h-3 w-3" icon="BookmarkPlus" />
            <span @click.stop="showDeals($event)">
              {{ modelValue.deal?.name || "#" + modelValue.deal?.id }}
            </span>
          </div>
        </div>
      </div>
      <div v-if="toggleMode.note" class="pt-2 [&:empty]:hidden">
        <TrackInfo :track="modelValue" />
      </div>
      <div v-if="toggleMode.user" class="flex justify-end pt-2 [&:empty]:hidden">
        <template v-if="doctors.length > 0">
          <UserAvatarGroup size="small" :max-display="3" :users="doctors" animation-style="lift" />
        </template>
      </div>
    </div>
    <div class="absolute right-3 top-3 flex items-center justify-center">
      <div class="flex items-center text-xs text-slate-500">
        <i class="pi pi-clock mr-1 text-xs text-gray-400" />
        <span>
          {{ formatHoursMinutes(modelValue.created_at) }}
        </span>
      </div>
      <div class="ml-2 flex">
        <Menu class="ml-auto sm:ml-0">
          <Menu.Button :as="Button" class="p-1 hover:bg-slate-200">
            <Lucide class="h-3 w-3 font-bold" icon="MoreHorizontal" />
          </Menu.Button>
          <Menu.Items class="w-52">
            <slot name="actions"></slot>
          </Menu.Items>
        </Menu>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

import { DealResponse, TrackResponse, UserResponse } from "@/api/bcare-types-v2";
import { pipelineSwitchDeal } from "@/api/bcare-v2";
import Button from "@/base-components/Button";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import { usePersonDealsPopover } from "@/components/Deal/person-deal-popover";
import TrackInfo from "@/components/Deal/TrackInfo.vue";
import PersonAvatar from "@/components/Person/PersonAvatar.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import useTrack from "@/hooks/useTrack";
import useUser from "@/hooks/useUser";
import router from "@/router";
import { formatHoursMinutes } from "@/utils/time-helper";

// Interfaces
interface ToggleMode {
  note: boolean;
  user: boolean;
  comment: boolean;
}

interface Props {
  modelValue: TrackResponse;
  toggleMode: ToggleMode;
}

// Props
const props = withDefaults(defineProps<Props>(), {
  toggleMode: () => ({ note: true, user: true, comment: true }),
});

// Emits
const emit = defineEmits<{
  (event: "update:modelValue", track: TrackResponse): void;
  (event: "deal-switched", trackId: number, oldDealId: number, newDeal: DealResponse): void;
  (event: "check-out"): void;
}>();

const { show } = usePersonDealsPopover();
const { isLoading } = useTrack({
  initialState: {
    currentTrack: null,
  },
});

const { getUserById } = useUser();

// Methods
const showDeals = (event: Event) => {
  show(
    event,
    props.modelValue.person_id,
    props.modelValue.deal_id,
    (selectedDeal: DealResponse) => {
      if (props.modelValue.deal_id === selectedDeal.id) return;

      try {
        const oldDealId = props.modelValue.deal_id;

        pipelineSwitchDeal({
          old_id: oldDealId,
          new_id: selectedDeal.id,
          track_id: props.modelValue.id,
        });

        const updatedTrack: TrackResponse = {
          ...props.modelValue,
          deal_id: selectedDeal.id,
          deal: selectedDeal,
        };

        emit("update:modelValue", updatedTrack);
        emit("deal-switched", props.modelValue.id, oldDealId, selectedDeal);
      } catch (error) {
        console.error("Error switching deal:", error);
        // Có thể thêm notification service để hiển thị lỗi
      }
    },
  );
};

const doctors = computed((): UserResponse[] => {
  if (!props.modelValue.appointments || props.modelValue.appointments.length === 0) {
    return [];
  }

  return props.modelValue.appointments
    .filter((appointment) => appointment.doctor_id && Number(appointment.doctor_id) > 0)
    .map((appointment) => {
      const doctorId = Number(appointment.doctor_id);
      return getUserById(doctorId);
    })
    .filter((user): user is UserResponse => user !== null && user !== undefined);
});

// Lifecycle hooks
// TODO Tao hieu ung de co ai dang update track thi the hien ra de user khac biet
</script>
