<!-- GlobalDealPopover.vue -->
<template>
  <Teleport to="body">
    <div
      v-if="isVisible"
      ref="popoverRef"
      class="global-deal-popover"
      :style="{
        'transform-origin': 'center top',
        position: 'absolute',
        left: `${position.left}px`,
        top: `${position.top}px`,
        zIndex: 9999,
      }"
    >
      <div
        class="min-w-[15rem] overflow-hidden rounded-md border border-slate-200 bg-white shadow-[0px_3px_10px_#00000017] dark:border-transparent dark:bg-darkmode-600"
      >
        <div v-if="loading" class="p-3">
          <!-- Skeleton loading -->
          <div v-for="i in 3" :key="i" class="flex items-center gap-3 p-2">
            <div class="flex-1">
              <div class="mb-2 h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
              <div class="h-3 w-1/2 animate-pulse rounded bg-gray-200"></div>
            </div>
          </div>
        </div>
        <div v-else class="py-2">
          <ul
            class="m-0 max-h-[50vh] snap-y list-none space-y-1.5 overflow-hidden overflow-y-auto overscroll-contain px-2"
          >
            <li
              v-for="deal in deals"
              :key="deal.id"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              :class="{
                'box-content border border-highlight bg-soft text-primary':
                  deal.id === activeDealId,
              }"
              @click="selectDeal(deal)"
            >
              <div class="flex-1">
                <DealName :name="deal.name || 'Deal chưa đặt tên'" :state="deal.state" />
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ formatShortDateTime(deal.created_at) }}
                </div>
              </div>
              <div
                v-if="isShowPrice"
                class="mr-2 text-right text-sm font-medium text-success dark:text-gray-300"
              >
                <Money :amount="dealValue(deal)" />
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="deal.id === activeDealId"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                  :class="{ 'bg-primary': deal.id === activeDealId }"
                ></div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";

import Money from "@/base-components/Money.vue";
import DealName from "@/components/Deal/DealName.vue";
import { usePersonDealsPopover } from "@/components/Deal/person-deal-popover";
import { dealValue } from "@/utils/dealUtils";
import { formatShortDateTime } from "@/utils/time-helper";

interface Props {
  activeDealId?: number;
  isShowPrice?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  // activeDealId is already undefined by default due to the optional '?'
  isShowPrice: false,
});

const { isVisible, loading, deals, position, hide, selectDeal, activeDealId } =
  usePersonDealsPopover(); // Pass props to the composable if needed
const popoverRef = ref<HTMLElement>();

const handleClickOutside = (event: MouseEvent) => {
  const popover = document.querySelector(".global-deal-popover");
  if (popover && !popover.contains(event.target as Node)) {
    hide();
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
