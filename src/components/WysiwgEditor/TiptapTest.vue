<template>
  <div class="p-4">
    <h2 class="mb-4 text-xl font-bold">Tiptap Template Manager</h2>

    <div class="grid grid-cols-1 gap-6">
      <!-- Template List Section -->
      <Card class="shadow-sm">
        <template #content>
          <TemplateList
            :templates="templates"
            :is-loading="isLoading"
            @edit="handleEditTemplate"
            @delete="handleDeleteTemplate"
          />
        </template>
      </Card>

      <!-- Template Editor Section -->
      <Card class="shadow-sm">
        <template #content>
          <TemplateForm
            :template="currentTemplate"
            :is-editing="isEditing"
            :is-saving="isSaving"
            @save="handleSaveTemplate"
            @cancel="resetForm"
          />
        </template>
      </Card>

      <!-- Template Test Section -->
      <Card class="shadow-sm">
        <template #content>
          <TemplatePreview ref="previewRef" />
        </template>
      </Card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <DeleteConfirmation
      v-model="showDeleteModal"
      :template="templateToDelete"
      @confirm="deleteTemplate"
      @cancel="cancelDelete"
    />

    <!-- Notifications are handled with alerts -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from "vue";
import Card from "primevue/card";
import { useConfigurations } from "@/hooks/useSetting";
import { TemplateItem, emptyTemplate } from "./types";

// Lazy load components for better performance
const TemplateList = defineAsyncComponent(() => import("./components/TemplateList.vue"));
const TemplateForm = defineAsyncComponent(() => import("./components/TemplateForm.vue"));
const TemplatePreview = defineAsyncComponent(() => import("./components/TemplatePreview.vue"));
const DeleteConfirmation = defineAsyncComponent(
  () => import("./components/DeleteConfirmation.vue"),
);

// Initialize state
const templates = ref<TemplateItem[]>([]);
const isLoading = ref(false);
const isSaving = ref(false);
const isEditing = ref(false);
const showDeleteModal = ref(false);
const templateToDelete = ref<TemplateItem | null>(null);
const previewRef = ref<InstanceType<typeof TemplatePreview> | null>(null);

// Current template being edited
const currentTemplate = ref<TemplateItem>({ ...emptyTemplate });

// Get configuration hooks
const { fetchSettings, getSetting, syncSettingValue } = useConfigurations();

// Load templates from server
const loadTemplates = async () => {
  isLoading.value = true;
  try {
    await fetchSettings("", "");
    const templatesSetting = getSetting("templates", "templates");

    if (templatesSetting?.value?.templates) {
      templates.value = templatesSetting.value.templates as TemplateItem[];
    } else {
      templates.value = [];
    }
  } catch (error) {
    console.error("Error loading templates:", error);
  } finally {
    isLoading.value = false;
  }
};

// Handle edit template
const handleEditTemplate = (template: TemplateItem) => {
  currentTemplate.value = { ...template };
  isEditing.value = true;
};

// Handle delete template
const handleDeleteTemplate = (template: TemplateItem) => {
  templateToDelete.value = template;
  showDeleteModal.value = true;
};

// Handle save template
const handleSaveTemplate = async (template: TemplateItem) => {
  isSaving.value = true;

  try {
    // Get current templates
    const templatesSetting = getSetting("templates", "templates");
    let allTemplates: TemplateItem[] = [];

    if (templatesSetting?.value?.templates) {
      allTemplates = [...templatesSetting.value.templates];
    }

    if (isEditing.value) {
      // Update existing template
      const index = allTemplates.findIndex((t) => t.id === template.id);
      if (index !== -1) {
        allTemplates[index] = { ...template };
      }
    } else {
      // Add new template
      // Check if ID already exists
      if (allTemplates.some((t) => t.id === template.id)) {
        alert("A template with this ID already exists. Please use a unique ID.");
        isSaving.value = false;
        return;
      }

      allTemplates.push({ ...template });
    }

    // Save to server
    const success = await syncSettingValue("templates", "templates", { templates: allTemplates });

    if (success) {
      // Reload templates
      await loadTemplates();
      resetForm();
      alert(isEditing.value ? "Template updated successfully" : "Template created successfully");
    } else {
      alert("Failed to save template. Please try again.");
    }
  } catch (error) {
    console.error("Error saving template:", error);
    alert("An error occurred while saving the template.");
  } finally {
    isSaving.value = false;
  }
};

// Delete template
const deleteTemplate = async () => {
  if (!templateToDelete.value) return;

  try {
    // Get current templates
    const templatesSetting = getSetting("templates", "templates");
    let allTemplates: TemplateItem[] = [];

    if (templatesSetting?.value?.templates) {
      allTemplates = [...templatesSetting.value.templates];
    }

    // Filter out the template to delete
    const filteredTemplates = allTemplates.filter((t) => t.id !== templateToDelete.value?.id);

    // Save to server
    const success = await syncSettingValue("templates", "templates", {
      templates: filteredTemplates,
    });

    if (success) {
      // Reload templates
      await loadTemplates();
      showDeleteModal.value = false;
      templateToDelete.value = null;
      alert("Template deleted successfully");
    } else {
      alert("Failed to delete template. Please try again.");
    }
  } catch (error) {
    console.error("Error deleting template:", error);
    alert("An error occurred while deleting the template.");
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  templateToDelete.value = null;
};

// Reset form
const resetForm = () => {
  currentTemplate.value = { ...emptyTemplate };
  isEditing.value = false;
};

// Load templates on mount
onMounted(async () => {
  await loadTemplates();
});
</script>
