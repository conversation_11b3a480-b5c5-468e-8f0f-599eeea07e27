<template>
  <div class="mention-list max-h-[300px] overflow-y-auto rounded-lg border-0 bg-white p-0 shadow-xl">
    <template v-if="items.length">
      <div
        v-for="(item, index) in items"
        :key="index"
        :class="[
          'flex w-full items-center gap-3 px-3 py-2 text-left border-b border-gray-100 transition-colors duration-150 hover:bg-slate-50 cursor-pointer',
          { 'bg-slate-100': index === selectedIndex }
        ]"
        @click="selectItem(index)"
      >
        <UserAvatar :user="item" :size="28" class="flex-shrink-0" />
        <div class="flex flex-col min-w-0 flex-1">
          <div class="flex items-center gap-1">
            <span class="font-medium text-gray-800 truncate text-sm">{{ item.name }}</span>
            <span v-if="item.roles && item.roles.length > 0" class="text-xs bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded-full">{{ item.roles[0] }}</span>
          </div>
          <span class="text-xs text-gray-500 truncate">{{ item.email }}</span>
        </div>
      </div>
    </template>
    <div v-else class="px-3 py-2 text-sm text-gray-500 flex items-center justify-center border-t">
      Không tìm thấy kết quả
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

import type { UserResponse } from '@/api/bcare-types-v2'
import UserAvatar from '@/components/User/UserAvatar.vue'

interface Props {
  items: UserResponse[]
  command: (params: { id: string, label: string }) => void
}

const props = defineProps<Props>()
const emit = defineEmits(['select'])

const selectedIndex = ref(0)

watch(
  () => props.items,
  () => {
    selectedIndex.value = 0
  }
)

const selectItem = (index: number) => {
  const item = props.items[index]
  if (item) {
    props.command({ id: item.id.toString(), label: item.name })
    emit('select', item)
  }
}

const onKeyDown = ({ event }: { event: KeyboardEvent }) => {
  if (event.key === 'ArrowUp') {
    selectedIndex.value = ((selectedIndex.value + props.items.length) - 1) % props.items.length
    return true
  }

  if (event.key === 'ArrowDown') {
    selectedIndex.value = (selectedIndex.value + 1) % props.items.length
    return true
  }

  if (event.key === 'Enter') {
    selectItem(selectedIndex.value)
    return true
  }

  return false
}

defineExpose({
  onKeyDown
})
</script>
