<template>
  <div
    class="template-list max-h-[500px] overflow-y-auto rounded-lg border-0 bg-white p-0 shadow-xl"
  >
    <template v-if="items.length">
      <div
        v-for="(item, index) in items"
        :key="index"
        :class="[
          'flex w-full cursor-pointer items-center border-b border-gray-100 px-3 py-2 text-left transition-colors duration-150 hover:bg-slate-100',
          { 'bg-slate-100': index === selectedIndex },
        ]"
        @click="selectItem(index)"
      >
        <div class="flex w-full flex-col">
          <span class="text-sm font-medium text-gray-800">
            <HighlightText :text="item.description || ''" :highlight="query" />
          </span>
          <div class="mt-1 flex flex-wrap gap-1">
            <span
              v-for="(keyword, keywordIndex) in item.keywords"
              :key="keywordIndex"
              class="inline-flex items-center rounded-full bg-gray-200 px-1.5 py-0.5 text-xs font-normal text-gray-700"
            >
              <HighlightText :text="keyword" :highlight="query" />
            </span>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="flex items-center justify-center border-t px-3 py-2 text-sm text-gray-500">
      No templates found
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import HighlightText from "@/base-components/HighlightText.vue";

// Define the TemplateItem interface
interface TemplateItem {
  id: string;
  keywords: string[];
  description?: string;
  content: string;
}

interface Props {
  items: TemplateItem[];
  command: (params: { templateItem: TemplateItem }) => void;
  query?: string;
}

const props = withDefaults(defineProps<Props>(), {
  query: "",
});
const emit = defineEmits(["select"]);

const selectedIndex = ref(0);

watch(
  () => props.items,
  () => {
    selectedIndex.value = 0;
  },
);

const selectItem = (index: number) => {
  const item = props.items[index];
  if (item) {
    props.command({ templateItem: item });
    emit("select", item);
  }
};

const onKeyDown = ({ event }: { event: KeyboardEvent }) => {
  if (event.key === "ArrowUp") {
    selectedIndex.value = (selectedIndex.value + props.items.length - 1) % props.items.length;
    return true;
  }

  if (event.key === "ArrowDown") {
    selectedIndex.value = (selectedIndex.value + 1) % props.items.length;
    return true;
  }

  if (event.key === "Enter") {
    selectItem(selectedIndex.value);
    return true;
  }

  return false;
};

defineExpose({
  onKeyDown,
});
</script>
