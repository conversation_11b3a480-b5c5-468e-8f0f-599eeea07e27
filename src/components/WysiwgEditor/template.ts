import { Extension } from '@tiptap/core'
import { PluginKey } from '@tiptap/pm/state'
import Suggestion from '@tiptap/suggestion'
import { VueRenderer } from '@tiptap/vue-3'
import tippy, { GetReferenceClientRect, Instance } from 'tippy.js'

import { useConfigurations } from '@/hooks/useSetting'
import TemplateList from './TemplateList.vue'

// Define the TemplateItem interface
interface TemplateItem {
  id: string;
  keywords: string[];
  description?: string;
  content: string;
}

// Initialize templates array
let templates: TemplateItem[] = []

// Fetch templates from server
const { fetchSettings, getSetting } = useConfigurations()

// Function to refresh templates
export const refreshTemplates = async () => {
  console.log('Refreshing templates from server')
  try {
    await fetchSettings('', '')
    const templatesSetting = getSetting('templates', 'templates')

    if (templatesSetting && templatesSetting.value && templatesSetting.value.templates) {
      templates = templatesSetting.value.templates as TemplateItem[]
      console.log('Templates refreshed from server:', templates)
    } else {
      console.log('No templates found on server during refresh')
      templates = []
    }
  } catch (error) {
    console.error('Error refreshing templates:', error)
  }
}

// Initial load of templates
refreshTemplates()

export const TemplatePluginKey = new PluginKey('template')

export const TemplateExtension = Extension.create({
  name: 'template',

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        char: '/',
        pluginKey: TemplatePluginKey,
        items: ({ query }) => {
          console.log('Template suggestion triggered with query:', query)

          if (!query) {
            console.log('Returning all templates:', templates)
            return templates
          }

          const search = query.toLowerCase()
          console.log('Searching for:', search)

          const filteredTemplates = templates.filter(item => {
            // Search in keywords
            if (item.keywords.some(keyword => keyword.toLowerCase().includes(search))) {
              return true
            }

            // Search in description
            if (item.description && item.description.toLowerCase().includes(search)) {
              return true
            }

            return false
          })

          console.log('Filtered templates:', filteredTemplates)
          return filteredTemplates
        },
        render: () => {
          let component: VueRenderer | null = null
          let popup: Instance | null = null

          const getClientRect = (props: any): DOMRect | null => {
            return typeof props.clientRect === 'function' ? props.clientRect() : props.clientRect
          }

          const createReferenceRect = (props: any): GetReferenceClientRect => {
            return () => getClientRect(props) || new DOMRect()
          }

          const createTippyInstance = (props: any) => {
            const rect = getClientRect(props)
            if (!rect || !component?.element) return null

            return tippy('body', {
              getReferenceClientRect: createReferenceRect(props),
              appendTo: () => document.body,
              content: component.element,
              showOnCreate: true,
              interactive: true,
              trigger: 'manual',
              placement: 'bottom-start',
              theme: 'light',
              arrow: false,
              maxWidth: 'none',
              offset: [0, 10],
              zIndex: 9999,
              animation: 'fade',
              duration: 150,
              popperOptions: {
                strategy: 'fixed',
                modifiers: [
                  {
                    name: 'flip',
                    options: {
                      fallbackPlacements: ['top-start', 'bottom-end', 'top-end'],
                    },
                  },
                ],
              },
            })[0]
          }

          return {
            onStart: props => {
              console.log('Template suggestion onStart called with props:', props)

              component = new VueRenderer(TemplateList, {
                props: {
                  ...props,
                  query: props.query || '',
                },
                editor: props.editor,
              })

              popup = createTippyInstance(props)

              console.log('Template suggestion popup created:', popup)
            },

            onUpdate(props) {
              if (component) {
                component.updateProps(props)
              }

              if (popup && !popup.state.isDestroyed) {
                popup.setProps({
                  getReferenceClientRect: createReferenceRect(props),
                })
              }
            },

            onKeyDown(props) {
              if (!component || !popup) return false

              if (props.event.key === 'Escape') {
                popup.hide()
                return true
              }

              return component.ref?.onKeyDown(props) ?? false
            },

            onExit() {
              if (popup && !popup.state.isDestroyed) {
                popup.destroy()
                popup = null
              }

              if (component) {
                component.destroy()
                component = null
              }
            },
          }
        },
        command: ({ editor, range, props }) => {
          const { templateItem } = props

          // Delete the template command text
          editor
            .chain()
            .focus()
            .deleteRange(range)
            .run()

          // Insert the template content
          editor
            .chain()
            .focus()
            .insertContent(templateItem.content)
            .run()
        },
      }),
    ]
  },
})

export function createTemplatePlugin() {
  return TemplateExtension
}
