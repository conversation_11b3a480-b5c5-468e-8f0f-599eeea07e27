<template>
  <div v-if="hasValidContent" class="rich-text-note">
    <!-- Container wrapper -->
    <div v-if="wrapperMode" class="group relative cursor-pointer" @click.stop="toggleExpand">
      <div
        :class="[
          'flex items-center overflow-hidden rounded-lg transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700',
          { 'border dark:border-gray-700': content },
          { 'inline-block': hasImages },
        ]"
      >
        <div :class="['relative', { 'w-full': !hasImages }]">
          <div
            ref="contentRef"
            :class="[
              'prose relative block p-1 text-sm prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:rounded-md',
              { 'font-medium': medium },
              { 'content-collapsed': !isExpanded },
              { '[&_img]:!w-auto [&_img]:!max-w-[100px]': hasImages },
              { 'w-full': !hasImages },
            ]"
          >
            <span v-html="processedContent" v-lightbox></span>
          </div>

          <!-- Gradient overlay when collapsed -->
          <div
            v-if="!isExpanded && hasOverflow"
            class="pointer-events-none absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white/90 to-transparent dark:from-gray-800/90"
          ></div>
        </div>
      </div>
    </div>

    <!-- Direct span for inline usage -->
    <span
      v-else
      v-html="processedContent"
      v-lightbox
      :class="[
        'prose relative text-sm prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:rounded-md',
        { 'font-medium': medium },
        { 'inline-block [&_img]:!w-auto [&_img]:!max-w-[300px]': hasImages },
        { 'w-full': !hasImages },
      ]"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { useResizeObserver } from "@vueuse/core";
import DOMPurify from "dompurify";
import { computed, nextTick, onMounted, ref, watch } from "vue";

interface Props {
  content: string | null;
  medium?: boolean;
  wrapperMode?: boolean;
  processEmptyParagraphs?: boolean;
  maxHeight?: number;
  stopPropagation?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  content: "",
  medium: false,
  wrapperMode: false,
  processEmptyParagraphs: true,
  maxHeight: 100, // Default max height in pixels
  stopPropagation: true,
});

const contentRef = ref<HTMLElement | null>(null);
const isExpanded = ref(false);
const hasOverflow = ref(false);

const processedContent = computed(() => {
  if (!props.content) return "";

  let processed = props.content;

  if (props.processEmptyParagraphs) {
    processed = processed
      .replace(/<p><\/p>/g, "<p><br></p>")
      .replace(/<p>\s*<\/p>/g, "<p><br></p>");
  }

  return DOMPurify.sanitize(processed);
});

const checkOverflow = () => {
  if (!contentRef.value) return;

  const element = contentRef.value;
  hasOverflow.value = element.scrollHeight > props.maxHeight;
};

// Watch for content changes and resize
useResizeObserver(contentRef, () => {
  checkOverflow();
});

watch(
  () => props.content,
  () => {
    nextTick(() => {
      checkOverflow();
    });
  },
);

const toggleExpand = (event: Event) => {
  // Prevent click event when clicking on images (for lightbox)
  if ((event.target as HTMLElement).tagName === "IMG") {
    return;
  }

  if (props.stopPropagation) {
    event.stopPropagation();
  }

  isExpanded.value = !isExpanded.value;
};

onMounted(() => {
  checkOverflow();
});

const hasValidContent = computed(() => {
  if (!props.content) return false;
  return true;
});
const hasImages = computed(() => {
  return props.content?.includes("<img");
});
</script>

<style scoped>
.content-collapsed {
  max-height: v-bind("`${props.maxHeight}px`");
  overflow: hidden;
}

/* Optional: Add smooth transition for expand/collapse */
.content-collapsed,
.prose {
  transition: max-height 0.3s ease-in-out;
}
</style>
