import Mention from '@tiptap/extension-mention'
import type { SuggestionProps } from '@tiptap/suggestion'
import { VueRenderer } from '@tiptap/vue-3'
import tippy, { GetReferenceClientRect, Instance } from 'tippy.js'

import type { UserResponse } from '@/api/bcare-types-v2'
import useUser from '@/hooks/useUser'

import MentionList from './MentionList.vue'

export function createMentionPlugin() {
  const { searchUsers, users } = useUser()

  return Mention.configure({
    HTMLAttributes: {
      class: 'bg-warning/10 font-medium rounded-md text-warning px-1.5 py-0.5 box-decoration-clone',
    },
    suggestion: {
      items: ({ query }: { query: string }): UserResponse[] => {
        return query ? searchUsers(query) : users.value
      },

      render: () => {
        let component: VueRenderer | null = null
        let popup: Instance | null = null

        const getClientRect = (props: SuggestionProps): DOMRect | null => {
          return typeof props.clientRect === 'function' ? props.clientRect() : null
        }

        const createReferenceRect = (props: SuggestionProps): GetReferenceClientRect => {
          return () => getClientRect(props) || new DOMRect()
        }

        const createTippyInstance = (props: SuggestionProps) => {
          const rect = getClientRect(props)
          if (!rect || !component?.element) return null

          return tippy('body', {
            getReferenceClientRect: createReferenceRect(props),
            appendTo: () => document.body,
            content: component.element,
            showOnCreate: true,
            interactive: true,
            trigger: 'manual',
            placement: 'bottom-start',
            theme: 'light',
            arrow: false,
            maxWidth: 'none',
            offset: [0, 10],
            zIndex: 9999,
            animation: 'fade',
            duration: 150,
            popperOptions: {
              strategy: 'fixed',
              modifiers: [
                {
                  name: 'flip',
                  options: {
                    fallbackPlacements: ['top-start', 'bottom-end', 'top-end'],
                  },
                },
              ],
            },
          })[0]
        }

        return {
          onStart: (props: SuggestionProps) => {
            component = new VueRenderer(MentionList, {
              props,
              editor: props.editor,
            })
            popup = createTippyInstance(props)
          },

          onUpdate(props: SuggestionProps) {
            component?.updateProps(props)
            popup?.setProps({
              getReferenceClientRect: createReferenceRect(props),
            })
          },

          onKeyDown(props: { event: KeyboardEvent }) {
            if (!component || !popup) return false

            if (props.event.key === 'Escape') {
              popup.hide()
              return true
            }

            return component.ref?.onKeyDown(props) ?? false
          },

          onExit() {
            if (popup && !popup.state.isDestroyed) {
              popup.destroy()
              popup = null
            }

            if (component) {
              component.destroy()
              component = null
            }
          },
        }
      },
    },
  })
}
