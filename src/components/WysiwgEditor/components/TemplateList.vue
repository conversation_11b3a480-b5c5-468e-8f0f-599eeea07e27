<template>
  <div class="template-list">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="flex items-center gap-2">
        <InputText
          v-model="searchQuery"
          placeholder="Search templates..."
          class="w-48"
          size="small"
        />
        <Badge :value="templates.length" severity="info" />
      </div>
    </div>

    <ProgressSpinner v-if="isLoading" class="w-8 h-8 mx-auto my-4" />

    <div v-else-if="templates.length === 0" class="text-center py-6 text-gray-500">
      {{ emptyMessage }}
    </div>

    <div v-else class="max-h-[300px] overflow-y-auto">
      <DataTable
        :value="filteredTemplates"
        stripedRows
        size="small"
        class="p-datatable-sm"
        :paginator="filteredTemplates.length > 10"
        :rows="10"
        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
        responsiveLayout="scroll"
      >
        <Column field="id" header="ID" style="width: 15%">
          <template #body="{ data }">
            <span class="text-xs text-gray-600">{{ data.id }}</span>
          </template>
        </Column>

        <Column field="description" header="Description" style="width: 25%">
          <template #body="{ data }">
            <div class="font-medium">{{ data.description || 'No description' }}</div>
          </template>
        </Column>

        <Column field="keywords" header="Keywords" style="width: 40%">
          <template #body="{ data }">
            <div class="flex flex-wrap gap-1">
              <Chip
                v-for="(keyword, index) in data.keywords"
                :key="index"
                :label="'/' + keyword"
                class="text-xs bg-gray-200 text-gray-700"
              />
            </div>
          </template>
        </Column>

        <Column header="Actions" style="width: 20%">
          <template #body="{ data }">
            <div class="flex gap-2 justify-center">
              <Button
                @click="$emit('edit', data)"
                icon="pi pi-pencil"
                text
                severity="info"
                size="small"
                aria-label="Edit"
              />
              <Button
                @click="$emit('delete', data)"
                icon="pi pi-trash"
                text
                severity="danger"
                size="small"
                aria-label="Delete"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import Badge from 'primevue/badge';
import Button from 'primevue/button';
import Chip from 'primevue/chip';
import Column from 'primevue/column';
import DataTable from 'primevue/datatable';
import InputText from 'primevue/inputtext';
import ProgressSpinner from 'primevue/progressspinner';
import { TemplateItem } from '../types';

interface Props {
  templates: TemplateItem[];
  isLoading?: boolean;
  title?: string;
  emptyMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  title: 'Template List',
  emptyMessage: 'No templates found. Create your first template below.'
});

defineEmits<{
  (e: 'edit', template: TemplateItem): void;
  (e: 'delete', template: TemplateItem): void;
}>();

const searchQuery = ref('');

// Filter templates based on search query
const filteredTemplates = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.templates;
  }

  const query = searchQuery.value.toLowerCase();
  return props.templates.filter(template => {
    // Search in ID
    if (template.id.toLowerCase().includes(query)) {
      return true;
    }

    // Search in description
    if (template.description && template.description.toLowerCase().includes(query)) {
      return true;
    }

    // Search in keywords
    if (template.keywords.some(keyword => keyword.toLowerCase().includes(query))) {
      return true;
    }

    return false;
  });
});
</script>
