<template>
  <Dialog
    v-model:visible="visible"
    :modal="true"
    :closable="true"
    :dismissable-mask="true"
    header="Confirm Delete"
    :style="{ width: '450px' }"
    :pt="{
      root: { class: 'border-0' },
      header: { class: 'py-3 px-4 border-b bg-white rounded-t-lg' },
      headerTitle: { class: 'text-base font-medium' },
      content: { class: 'p-4 bg-white' },
      footer: { class: 'p-3 border-t bg-white rounded-b-lg flex justify-end gap-2' },
    }"
  >
    <div class="confirmation-content">
      <p class="mb-4">
        Are you sure you want to delete the template "{{
          template?.description || "No description"
        }}"? This action cannot be undone.
      </p>
    </div>
    <template #footer>
      <Button @click="onCancel" label="Cancel" outlined class="w-auto" />
      <Button @click="onConfirm" label="Delete" severity="danger" class="w-auto" />
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";

import { TemplateItem } from "../types";

interface Props {
  modelValue: boolean;
  template: TemplateItem | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "confirm"): void;
  (e: "cancel"): void;
}>();

const visible = ref(props.modelValue);

watch(
  () => props.modelValue,
  (value) => {
    visible.value = value;
  },
);

watch(
  () => visible.value,
  (value) => {
    emit("update:modelValue", value);
  },
);

const onConfirm = () => {
  emit("confirm");
  visible.value = false;
};

const onCancel = () => {
  emit("cancel");
  visible.value = false;
};
</script>
