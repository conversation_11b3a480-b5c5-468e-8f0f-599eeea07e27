<template>
  <div class="template-preview">
    <h3 class="text-lg font-semibold mb-4">Test Templates</h3>
    <p class="text-sm text-gray-500 mb-4">
      Type <code>/</code> to trigger template suggestions in the editor below.
    </p>
    <div class="border rounded-lg mb-4">
      <Tiptap v-model="content" :autofocus="false" />
    </div>
    <div class="mt-4">
      <h4 class="text-md font-medium mb-2">Preview:</h4>
      <div
        class="bg-gray-50 p-3 rounded-lg prose max-w-none"
        v-html="content"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Tiptap } from '@/components/WysiwgEditor';

const content = ref('<p>Type / to trigger template suggestions</p>');

defineExpose({
  content
});
</script>

<style scoped>
.prose :deep(p) {
  margin: 0;
}
</style>
