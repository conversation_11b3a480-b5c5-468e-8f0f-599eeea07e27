<template>
  <div class="template-form w-full">
    <form @submit.prevent="saveTemplate" class="w-full">
      <!-- Header with title and buttons -->
      <div class="mb-4 flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold">
            {{ isEditing && template.id ? "Chỉnh sửa template" : "Tạo template mới" }}
          </h3>
        </div>
        <div class="flex gap-2">
          <Button
            type="button"
            @click="$emit('cancel')"
            label="Hủy"
            outlined
            size="small"
            class="w-auto"
          />
          <Button
            type="button"
            @click="clearForm"
            label="Clear"
            outlined
            size="small"
            class="w-auto"
          />
          <Button
            type="submit"
            :label="isEditing && template.id ? 'Cập nhật' : 'Lưu'"
            :loading="isSaving"
            severity="primary"
            size="small"
            class="w-auto"
            icon="pi pi-save"
            iconPos="right"
          />
        </div>
      </div>

      <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
        <!-- Template ID -->
        <div class="field">
          <label for="template-id" class="mb-1 block text-sm font-medium text-gray-700"> ID </label>
          <InputText
            id="template-id"
            v-model="formData.id"
            :disabled="isEditing && template.id !== ''"
            class="w-full"
            :class="{ 'p-disabled': isEditing && template.id !== '' }"
            placeholder="Không được nhập trùng ID"
            required
          />
        </div>

        <!-- Template Description -->
        <div class="field">
          <label for="template-description" class="mb-1 block text-sm font-medium text-gray-700">
            Mô tả
          </label>
          <InputText
            id="template-description"
            v-model="formData.description"
            class="w-full"
            placeholder="Nhập mô tả"
            required
          />
        </div>
      </div>

      <!-- Template Keywords -->
      <div class="field mb-4">
        <label for="template-keywords" class="mb-1 block text-sm font-medium text-gray-700">
          Keywords
        </label>
        <InputText
          id="template-keywords"
          v-model="keywordsInput"
          class="w-full"
          placeholder="keyword1, keyword2, keyword3"
          required
        />
      </div>

      <!-- Template Content Editor -->
      <div class="field mb-4">
        <label class="mb-1 block text-sm font-medium text-gray-700"> Template Content </label>
        <Tiptap
          v-model="formData.content"
          :autofocus="true"
          placeholder="Nhập nội dung template"
          minHeight="250px"
        />
      </div>
    </form>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { Tiptap } from "@/components/WysiwgEditor";
import { TemplateItem } from "../types";

interface Props {
  template: TemplateItem;
  isEditing?: boolean;
  isSaving?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isEditing: false,
  isSaving: false,
});

const emit = defineEmits<{
  (e: "save", template: TemplateItem): void;
  (e: "cancel"): void;
}>();

// Create a copy of the template to avoid modifying the original
const formData = ref<TemplateItem>({
  id: props.template.id,
  keywords: [...props.template.keywords],
  description: props.template.description || "",
  content: props.template.content,
});

// For keywords input field (comma-separated)
const keywordsInput = ref(props.template.keywords.join(", "));

// Watch for changes in the template prop
watch(
  () => props.template,
  (newTemplate) => {
    formData.value = {
      id: newTemplate.id,
      keywords: [...newTemplate.keywords],
      description: newTemplate.description || "",
      content: newTemplate.content,
    };
    keywordsInput.value = newTemplate.keywords.join(", ");
  },
  { deep: true },
);

// Get current date and time in a formatted string
const getCurrentDateTime = () => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, "0");
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");

  return `${hours}:${minutes} - ${day}/${month}/${year}`;
};

// Clear form fields
const clearForm = () => {
  formData.value = {
    ...formData.value,
    description: "",
    content: "<p></p>",
  };
  keywordsInput.value = "";
};

const saveTemplate = () => {
  // Parse keywords from comma-separated input
  const keywords = keywordsInput.value
    .split(",")
    .map((keyword) => keyword.trim())
    .filter((keyword) => keyword !== "");

  const updatedTemplate: TemplateItem = {
    ...formData.value,
    keywords,
  };

  emit("save", updatedTemplate);
};
</script>
