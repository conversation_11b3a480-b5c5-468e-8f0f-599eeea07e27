<template>
  <div class="card flex justify-center">
    <!-- Scoped Slot cho Button -->
    <slot name="button" :togglePopover="togglePopover">
      <!-- But<PERSON> mặc định nếu không có slot -->
      <i
        v-show="isCtrlPressed"
        class="pi pi-wrench cursor-pointer text-gray-300"
        @click="togglePopover"
      />
    </slot>

    <!-- Component Popover -->
    <Popover
      ref="popoverRef"
      :pt="{ root: { style: { '--p-popover-arrow-offset': '1rem' } } }"
      @show="emit('popover-open')"
      @hide="emit('popover-close')"
    >
      <div class="flex min-w-[15rem] max-w-[25rem] flex-col">
        <span class="block font-medium">{{ title }}</span>

        <div class="flex flex-wrap gap-2">
          <slot name="custom_component" />

          <template v-for="(setting, idx) in settingsSchema" :key="idx">
            <div v-if="setting.type !== 'group'" class="w-full">
              <component
                :is="getComponentType(setting.type)"
                v-model="localSettings[setting.field_name]"
                :label="setting.label"
                :field-name="setting.field_name"
              />
            </div>
            <div v-else class="w-full">
              <Fieldset class="pb-3">
                <template #legend>
                  <span class="block">{{ setting.label }}</span>
                </template>
                <div class="space-y-2">
                  <template
                    v-for="(childSetting, childIdx) in setting.children"
                    v-if="Array.isArray(setting.children)"
                    :key="childIdx"
                  >
                    <component
                      v-if="childSetting.field_name"
                      :is="getComponentType(childSetting.type)"
                      v-model="localSettings[childSetting.field_name]"
                      :label="childSetting.label"
                      :field-name="childSetting.field_name"
                    />
                  </template>
                </div>
              </Fieldset>
            </div>
          </template>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { useDebounceFn } from "@vueuse/core";
import Popover from "primevue/popover";
import { ComponentPublicInstance, onMounted, onUnmounted } from "vue";
import { ref, watch } from "vue";

import { UniversalSetting } from "@/api/extend-types";
import SettingCheckbox from "@/components/Settings/SettingCheckbox.vue";
import SettingMultiNumberInput from "@/components/Settings/SettingMultiNumberInput.vue";
import SettingSelectButton from "@/components/Settings/SettingSelectButton.vue";
import SettingTextInput from "@/components/Settings/SettingTextInput.vue";
import SettingToggleButton from "@/components/Settings/SettingToggleButton.vue";
import SettingToggleSwitch from "@/components/Settings/SettingToggleSwitch.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";

interface PopoverInstance extends ComponentPublicInstance {
  toggle: (event: MouseEvent) => void;
  visible: boolean;
}

interface Props {
  title?: string;
  settingsSchema: UniversalSetting[];
  settingName: string;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "popover-open"): void;
  (e: "popover-close"): void;
}>();

const { title = "Tùy chỉnh", settingsSchema, settingName } = props;

// Khởi tạo localSettings dưới dạng Record
const localSettings = ref<Record<string, any>>({});

// Reference đến component Popover
const popoverRef = ref<PopoverInstance | null>(null);

// Khởi tạo hook đồng bộ hóa
const { syncSetting, getSetting } = useComponentSetting();

// Hàm để tạo localSettings từ settingsSchema
const createLocalSettings = (schema: UniversalSetting[]) => {
  schema.forEach((setting) => {
    if (setting.field_name) {
      localSettings.value[setting.field_name] = {}; // Gán giá trị cho field_name
    }
    // Nếu có children, xử lý chúng
    if (setting.children) {
      setting.children.forEach((child) => {
        if (child.field_name) {
          localSettings.value[child.field_name] = {}; // Gán giá trị cho field_name của child
        }
      });
    }
  });
};

// Khởi tạo localSettings
createLocalSettings(settingsSchema);

// Hàm helper để unwrap value
const unwrapValue = (obj: any) => {
  const result: Record<string, any> = {};

  Object.entries(obj).forEach(([key, val]) => {
    if (val && typeof val === "object" && "value" in val) {
      result[key] = val.value;
    } else {
      result[key] = val;
    }
  });

  return result;
};

// Hàm load và parse setting
const loadSetting = () => {
  if (!settingName) return;

  const savedSetting = getSetting(settingName);

  if (!savedSetting) return;

  try {
    // Reset localSettings về giá trị mặc định trước khi gán giá trị mới
    createLocalSettings(settingsSchema);

    // Gán giá trị từ setting đã lưu
    Object.entries(savedSetting).forEach(([key, value]) => {
      if (key in localSettings.value) {
        localSettings.value[key] = value;
      }
    });
  } catch (error) {
    console.error("Error loading settings:", error);
    // Nếu có lỗi, reset về giá trị mặc định
    createLocalSettings(settingsSchema);
  }
};

// Hàm để toggle visibility của Popover
const togglePopover = (event: MouseEvent) => {
  if (popoverRef.value) {
    popoverRef.value.toggle(event);
    const isVisible = popoverRef.value.visible;

    if (isVisible) {
      loadSetting();
    } else {
    }
  }
};

// Hàm xác định loại component dựa trên loại setting
const getComponentType = (type: UniversalSetting["type"]) => {
  const componentTypes: Record<string, any> = {
    toggle_button: SettingToggleButton,
    toggle_switch: SettingToggleSwitch,
    select: SettingSelectButton,
    checkbox: SettingCheckbox,
    text: SettingTextInput,
    "multi-number": SettingMultiNumberInput,
    group: "div",
  };
  return componentTypes[type] || "div";
};

// Tạo hàm debounced cho việc sync setting
const debouncedSync = useDebounceFn((settingName: string, value: any) => {
  if (!settingName) return;
  syncSetting(settingName, value);
}, 500);

// Hàm xử lý update
const handleUpdate = (settingName: string | undefined, value: any) => {
  if (!settingName) return;
  const unwrappedValue = unwrapValue(value);
  debouncedSync(settingName, unwrappedValue);
};

// Watch settings để sync
watch(
  localSettings,
  (newLocalSettings) => {
    handleUpdate(settingName, newLocalSettings);
  },
  { deep: true },
);

const isCtrlPressed = ref(false);

// Xử lý sự kiện khi phím được nhấn
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey) {
    isCtrlPressed.value = true;
  }
};

const handleKeyUp = (event: KeyboardEvent) => {
  if (!event.ctrlKey) {
    isCtrlPressed.value = false;
  }
};

// Thêm event listeners khi component được mount
onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
  window.addEventListener("keyup", handleKeyUp);
});

// Cleanup event listeners khi component bị unmount
onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
  window.removeEventListener("keyup", handleKeyUp);
});
</script>
