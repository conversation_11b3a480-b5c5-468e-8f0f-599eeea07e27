<template>
  <div class="flex w-full flex-col gap-3">
    <div v-for="option in options" :key="option.field_name" class="flex items-center">
      <Checkbox v-model="selectedOptions" :value="option.field_name" :inputId="option.field_name" />
      <label :for="option.field_name" class="ml-2">{{ option.label }}</label>
    </div>
  </div>
</template>

<script setup lang="ts">
import Checkbox from "primevue/checkbox";
import { computed, PropType } from "vue";

import {UniversalSetting} from "@/api/extend-types";


const emit = defineEmits(["update:modelValue"]);

const props = defineProps({
  modelValue: {
    type: Object as PropType<UniversalSetting>,
    required: true,
  },
});

const options = computed(() => {
  return props.modelValue.options || [];
});

const selectedOptions = computed<string[]>({
  get: () => {
    return options.value
      .filter((option) => option.value)
      .map((option) => option.field_name)
      .filter((fieldName): fieldName is string => fieldName !== undefined);
  },
  set: (newSelectedOptions: string[]) => {
    const updatedOptions = options.value.map((option) => ({
      ...option,
      value: newSelectedOptions.includes(option?.field_name ?? ""),
    }));
    emit("update:modelValue", { ...props.modelValue, options: updatedOptions });
  },
});
</script>
