<!-- src/components/Settings/SettingMultiNumberInput.vue -->
<template>
  <div class="flex flex-col">
    <label :for="props.id" class="mb-1">{{ props.label }}</label>
    <InputText
      :id="props.id"
      v-model="localValue"
      @input="onInput"
      placeholder="Enter numbers separated by commas"
      class="w-full"
    />
    <small class="text-gray-500 mt-1">Separate numbers with commas (e.g., 1,2,3)</small>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: number[];
  label: string;
  id?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number[]): void;
}>();

// Khởi tạo localValue từ modelValue
const localValue = ref<string>(Array.isArray(props.modelValue) ? props.modelValue.join(",") : "");

const onInput = () => {
  const numbers = localValue.value
    .split(",")
    .map((num) => num.trim())
    .filter((num) => num !== "")
    .map(Number)
    .filter((num) => !isNaN(num));

  emit("update:modelValue", numbers);
};

// Watch sự thay đổi của modelValue từ parent
watch(
  () => props.modelValue,
  (newVal) => {
    if (Array.isArray(newVal)) {
      const joined = newVal.join(",");
      if (joined !== localValue.value) {
        localValue.value = joined;
      }
    }
  }
);
</script>
