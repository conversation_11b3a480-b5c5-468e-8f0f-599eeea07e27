<template>
  <div class="flex items-center gap-2">
    <label v-if="label" class="mb-1">{{ label }}</label>
    <ToggleButton v-model="localValue" onLabel="Checkbox" offLabel="Text" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: any;
  label?: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: any): void;
}>();

const localValue = ref(props.modelValue);

watch(localValue, (newValue) => {
  emit("update:modelValue", newValue);
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== localValue.value) {
      localValue.value = newVal;
    }
  },
);
</script>
