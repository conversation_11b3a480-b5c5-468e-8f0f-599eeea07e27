<template>
  <div class="flex items-center gap-2">
    <label v-if="label" class="mb-1">{{ label }}</label>
    <ToggleSwitch v-model="localValue" />
  </div>
</template>

<script setup lang="ts">
import ToggleSwitch from 'primevue/toggleswitch';
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: any;
  label?: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: any): void;
}>();

const localValue = ref(props.modelValue);

watch(localValue, (newValue) => {
  emit("update:modelValue", newValue);
});

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== localValue.value) {
      localValue.value = newVal;
    }
  }
);
</script> 