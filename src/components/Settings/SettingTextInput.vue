<!-- src/components/Settings/SettingTextInput.vue -->
<template>
  <div class="flex flex-col">
    <label :for="props.modelValue.field_name" class="mb-1">{{ props.modelValue.label }}</label>
    <InputText
      :id="props.modelValue.field_name"
      v-model="localValue"
      @input="onInput"
      class="w-full"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { PropType } from "vue";

import {UniversalSetting} from "@/api/extend-types";

const emit = defineEmits(["update:modelValue"]);

const props = defineProps({
  modelValue: {
    type: Object as PropType<UniversalSetting>,
    required: true,
  },
});

const localValue = ref<string>(props.modelValue.value || "");

const onInput = () => {
  emit("update:modelValue", { ...props.modelValue, value: localValue.value });
};

watch(
  () => props.modelValue.value,
  (newVal) => {
    if (newVal !== localValue.value) {
      localValue.value = newVal;
    }
  }
);
</script>
