<template>
  <div class="flex flex-col items-start gap-2">
    <SelectButton
      v-model="localValue"
      :options="props.modelValue.options"
      option-label="label"
      option-value="value"
      data-key="value"
      aria-labelledby="custom"
    >
      <template #option="slotProps">
        {{ slotProps.option.label }}
      </template>
    </SelectButton>
  </div>
</template>

<script setup lang="ts">
import { PropType, ref, watch } from "vue";

import { UniversalSetting } from "@/api/extend-types";

const emit = defineEmits(["update:modelValue"]);

const props = defineProps({
  modelValue: {
    type: Object as PropType<UniversalSetting>,
    required: true,
  },
});

const localValue = ref(props.modelValue.value);

watch(localValue, (newValue) => {
  emit("update:modelValue", { ...props.modelValue, value: newValue });
});
</script>
