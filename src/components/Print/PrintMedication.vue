<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { onMounted, ref } from "vue";
import { computed } from "vue";
import { useRoute } from "vue-router";

import { MedicationData } from "@/api/extend-types";
import useAttachment from "@/hooks/useAttachment";
import useAttachmentDataParser from "@/hooks/useAttachmentDataParser";
import useLocation from "@/hooks/useLocation";
import usePerson from "@/hooks/usePerson";
import { useConfigurations } from "@/hooks/useSetting";
import useUser from "@/hooks/useUser";
import { useAuth } from "@/pages/auth/config/auth-composable";

import PrintLayout from "./PrintLayout.vue";

const route = useRoute();
const medicationId = route.params.id as string;
const personId = route.params.personId as string;
const attachmentId = route?.params?.attachmentId as string;
// Current date in Vietnamese format
const currentDate = new Date().toLocaleDateString("vi-VN");

const { getPerson, currentPerson } = usePerson();
const medicationData = ref<MedicationData | null>(null);
const { getSetting, fetchSettings } = useConfigurations();
const { getFullAddress } = useLocation();
const { getAttachment } = useAttachment();
const { getDoctorById } = useAttachmentDataParser();
const { getUserById } = useUser({ autoLoad: true });
const doctorNames = ref<string>("");

// Fetch data
const fetchMedicationData = async () => {
  if (attachmentId) {
    const attachment = await getAttachment({ id: Number(attachmentId) });
    if (attachment) {
      const doctorIds = getDoctorById(attachment?.data || []);

      if (Array.isArray(doctorIds)) {
        const doctors = doctorIds
          .map((id) => getUserById(id))
          .filter(Boolean)
          .map((doctor) => doctor?.name);
        doctorNames.value = doctors.join(", ");
      } else if (doctorIds) {
        const doctor = getUserById(doctorIds);
        if (doctor) {
          doctorNames.value = doctor.name;
        }
      }
    }
  }
  if (medicationId) {
    try {
      // Fetch toa thuoc settings
      await fetchSettings("toa_thuoc", "toa_thuoc");
      const setting = getSetting("toa_thuoc", "toa_thuoc");

      if (setting?.value?.toa_thuoc) {
        // Tìm toa thuốc có ten matching với medicationId
        const medication = setting.value.toa_thuoc.find(
          (med: MedicationData) => med.ten === medicationId,
        );

        if (medication) {
          medicationData.value = medication;
        }
      }

      // Fetch person data if needed
      await getPerson({ id: Number(personId) });
    } catch (error) {
      console.error("Error fetching medication data:", error);
    }
  }
};

const fullAddress = computed(
  () =>
    getFullAddress.value({
      province_id: currentPerson.value?.province_id,
      district_id: currentPerson.value?.district_id,
      ward_id: currentPerson.value?.ward_id,
      address_number: currentPerson.value?.address_number,
    }) || "-",
);

onMounted(fetchMedicationData);
</script>

<template>
  <PrintLayout v-if="medicationData" title="ĐƠN THUỐC">
    <template #content>
      <!-- Patient Info Section -->
      <div class="mb-6 space-y-2">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <p>
              Họ tên: <span class="font-medium">{{ currentPerson?.full_name }}</span>
            </p>
            <p>
              Địa chỉ liên hệ: <span class="font-medium">{{ fullAddress }}</span>
            </p>
            <p>
              Số điện thoại liên hệ: <span class="font-medium">{{ currentPerson?.phone }}</span>
            </p>
          </div>
          <div>
            <p>
              Ngày sinh:
              <span class="font-medium">{{
                currentPerson?.date_of_birth
                  ? useDateFormat(currentPerson?.date_of_birth, "DD/MM/YYYY").value
                  : "-"
              }}</span>
            </p>
            <p>Cân nặng: <span class="font-medium">- kg</span></p>
            <p>
              Giới tính:
              <span class="font-medium">{{ currentPerson?.gender === "male" ? "Nam" : "Nữ" }}</span>
            </p>
          </div>
        </div>
        <p>
          Số thẻ bảo hiểm y tế (nếu có):
          <span class="font-medium">-</span>
        </p>
        <p class="font-medium">Chẩn đoán: {{ medicationData.chuan_doan }}</p>
      </div>

      <!-- Medications Table -->
      <div class="mb-6">
        <table class="w-full border-collapse border">
          <thead>
            <tr class="bg-gray-50">
              <th class="border p-2 text-center">STT</th>
              <th class="border p-2">Tên thuốc</th>
              <th class="border p-2 text-center">SL</th>
              <th class="border p-2">Cách dùng</th>
              <th class="border p-2 text-center">Sáng</th>
              <th class="border p-2 text-center">Trưa</th>
              <th class="border p-2 text-center">Chiều</th>
              <th class="border p-2 text-center">Tối</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(med, index) in medicationData.thuoc" :key="index">
              <td class="border p-2 text-center">{{ index + 1 }}</td>
              <td class="border p-2 font-medium">{{ med.ten }}</td>
              <td class="border p-2 text-center">{{ med.so_luong }} {{ med.don_vi }}</td>
              <td class="border p-2">{{ med.cach_dung }}</td>
              <td class="border p-2 text-center font-medium">{{ med.sang || "-" }}</td>
              <td class="border p-2 text-center font-medium">{{ med.trua || "-" }}</td>
              <td class="border p-2 text-center font-medium">{{ med.chieu || "-" }}</td>
              <td class="border p-2 text-center font-medium">{{ med.toi || "-" }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Instructions -->
      <div class="mb-8">
        <h3 class="mb-2 font-medium">Lời dặn:</h3>
        <ul class="list-inside list-disc space-y-1">
          <li v-for="(instruction, index) in medicationData.loi_dan" :key="index">
            {{ instruction }}
          </li>
        </ul>
      </div>

      <!-- Signature Section -->
      <div class="mt-8 flex justify-between">
        <!-- Cột bên trái - Người bệnh -->
        <div class="w-1/2">
          <div class="w-1/2">
            <p class="invisible mb-1">Hồ Chí Minh, {{ currentDate }}</p>
            <p class="text-center font-medium">Người bệnh</p>
            <div class="h-16"></div>
            <p class="text-center font-medium">
              {{ currentPerson?.full_name }}
            </p>
          </div>
        </div>

        <!-- Cột bên phải - Bác sĩ -->
        <div class="w-1/2 text-center">
          <div class="ml-auto w-1/2">
            <p class="mb-1">Hồ Chí Minh, {{ currentDate }}</p>
            <p class="text-center font-medium">Bác sĩ khám bệnh</p>
            <div class="h-16"></div>
            <p class="text-center font-medium">
              {{ doctorNames || "-" }}
            </p>
          </div>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
