<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

import type { PersonAssignmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Money from "@/base-components/Money.vue";
import { useAppointment } from "@/hooks/useAppointment";
import useEconomy from "@/hooks/useEconomy";
import usePerson from "@/hooks/usePerson";
import { useAuth } from "@/pages/auth/config/auth-composable";

import PrintLayout from "./PrintLayout.vue";

const route = useRoute();
const paymentId = Number(route.params.id);

const currentDate = new Date().toLocaleDateString("vi-VN");

const { currentUser } = useAuth();
const { getPayment, currentPayment } = useEconomy();
const { getPerson, currentPerson } = usePerson();
const { getLatestAppointment } = useAppointment();
const latestAppointment = ref<string>("-");

// Fetch data
const fetchPaymentData = async () => {
  if (paymentId) {
    const payment = await getPayment({ id: paymentId });
    if (payment?.person_id) {
      await getPerson({ id: payment.person_id, include_relation: true });
      const appointment = await getLatestAppointment({
        person_id: payment.person_id,
      });
      latestAppointment.value = appointment
        ? useDateFormat(appointment.start_time, "DD/MM/YYYY").value
        : "-";
    }
  }
};

onMounted(fetchPaymentData);

// Helper functions
const getPaymentMethodAmount = (method: string) => {
  if (!currentPayment.value) return 0;
  switch (method) {
    case "cash":
      return currentPayment.value.cash;
    case "credit_card":
      return currentPayment.value.credit_card;
    case "bank":
      return currentPayment.value.bank;
    case "mpos":
      return currentPayment.value.mpos;
    case "momo":
      return currentPayment.value.momo;
    default:
      return 0;
  }
};

const getPaymentMethodLabel = (method: string) => {
  switch (method) {
    case "cash":
      return "Tiền mặt";
    case "credit_card":
      return "Thẻ tín dụng";
    case "bank":
      return "Chuyển khoản";
    case "mpos":
      return "mPOS";
    case "momo":
      return "MoMo";
    default:
      return method;
  }
};

const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

const doctorNames = computed(() => {
  if (!currentPerson.value?.assignment?.length) return "-";
  return currentPerson.value.assignment
    .filter((a: PersonAssignmentResponse) => a.role === "doctor")
    .map((a: PersonAssignmentResponse) => a.user?.name)
    .filter(Boolean)
    .join(", ");
});
</script>

<template>
  <PrintLayout
    v-if="currentPayment && currentPerson"
    title="PHIẾU THU"
    :document-no="String(currentPayment.id)"
  >
    <template #content>
      <!-- Customer Info -->
      <div class="mb-3">
        <h2 class="font-medium">Họ và tên: {{ currentPerson.full_name }}</h2>
        <p class="text-gray-600">Số điện thoại: {{ currentPerson.phone }}</p>
        <p class="text-gray-600">Email: {{ currentPerson.email }}</p>
      </div>

      <!-- Payment Details Table -->
      <div class="w-full">
        <table class="w-full border-collapse border">
          <thead>
            <tr class="bg-gray-100">
              <th class="border p-2 font-medium">STT</th>
              <th class="border p-2 font-medium">Tên SP/DV</th>
              <th class="border p-2 font-medium">Ghi chú</th>
              <th class="border p-2 font-medium">Đơn giá</th>
              <th class="border p-2 font-medium">SL</th>
              <th class="border p-2 font-medium">Giảm giá</th>
              <th class="border p-2 font-medium">Thành tiền</th>
              <th class="border p-2 font-medium">Thanh toán</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(allocation, index) in currentPayment.allocations" :key="allocation.id">
              <td class="border p-2 text-center">{{ index + 1 }}</td>
              <td class="border p-2">
                <div class="font-medium">
                  <template v-if="allocation.bill_item">
                    <AttachmentTitle :attachment="allocation.bill_item.attachment" />
                  </template>
                  <template v-else-if="allocation.installment">
                    {{ allocation.installment.name }}
                  </template>
                </div>
                <div class="text-xs text-gray-500">
                  <template v-if="allocation.bill_item">
                    Hóa đơn: #{{ allocation.bill_item.bill_id }}
                  </template>
                  <template v-else-if="allocation.installment">
                    {{ getInstallmentKindText(allocation.installment.kind) }}
                    #{{ allocation.installment.installment_number }}
                  </template>
                </div>
              </td>
              <td class="border p-2">
                {{ allocation.bill_item?.note || allocation.installment?.note || "-" }}
              </td>
              <td class="border p-2 text-right">
                <template v-if="allocation.bill_item">
                  <Money :amount="allocation.bill_item.attachment.price" variant="default" />
                </template>
                <template v-else>-</template>
              </td>
              <td class="border p-2 text-center">
                {{ allocation.bill_item ? allocation.bill_item.attachment.quantity : "-" }}
              </td>
              <td class="border p-2 text-right">
                <template v-if="allocation.bill_item">
                  <Money :amount="allocation.bill_item.attachment.discount" variant="default" />
                </template>
                <template v-else>-</template>
              </td>
              <td class="border p-2 text-right">
                <Money
                  :amount="allocation.bill_item?.amount || allocation.installment?.amount || 0"
                  variant="default"
                />
              </td>
              <td class="border p-2 text-right">
                <Money :amount="allocation.amount" variant="default" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Payment Summary -->
      <div class="mt-4 flex justify-end">
        <div class="w-64">
          <div class="flex justify-between border-b py-2">
            <span class="font-medium">Thành tiền:</span>
            <Money :amount="currentPayment.total_amount" class="font-bold" variant="default" />
          </div>
          <div class="flex justify-between py-2">
            <span class="font-medium">Thực nhận:</span>
            <Money :amount="currentPayment.total_amount" class="font-bold" variant="default" />
          </div>
        </div>
      </div>

      <!-- Payment Methods -->
      <div class="my-2">
        <h4 class="mb-2 font-medium">Hình thức thanh toán:</h4>
        <div class="grid grid-cols-5 gap-4">
          <div
            v-for="method in ['cash', 'credit_card', 'bank', 'mpos', 'momo']"
            :key="method"
            class="rounded border p-2"
          >
            <div class="text-sm text-gray-600">
              {{ getPaymentMethodLabel(method) }}
            </div>
            <div class="font-medium">
              <Money :amount="getPaymentMethodAmount(method)" variant="default" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="flex justify-between">
        <!-- Left Side -->
        <div class="w-1/2">
          <p class="mb-1">
            Bác sĩ phụ trách:
            <span class="font-medium">{{ doctorNames }}</span>
          </p>
          <p class="mb-1">
            Lịch hẹn tái khám:
            <span class="font-medium">{{ latestAppointment }}</span>
          </p>
          <p class="invisible mb-1">?</p>
          <div class="w-1/2">
            <div class="flex items-center justify-center">
              <p class="font-medium">Người thanh toán</p>
            </div>
            <div class="h-16"></div>
            <div class="flex items-center justify-center">
              <p class="font-medium">
                {{ currentPerson.full_name }}
              </p>
            </div>
          </div>
        </div>

        <!-- Right Side -->
        <div class="w-1/2 text-right">
          <p class="invisible mb-1">?</p>
          <p class="invisible mb-1">?</p>
          <div class="ml-auto w-1/2">
            <div class="flex items-center justify-center">
              <p class="mb-1 font-medium">Hồ Chí Minh, {{ currentDate }}</p>
            </div>
            <div class="flex items-center justify-center">
              <p class="font-medium">Người nhận tiền</p>
            </div>
            <div class="h-16"></div>
            <div class="flex items-center justify-center">
              <p class="font-medium">{{ currentUser?.name }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
