<template>
  <div class="flex items-end justify-center font-['Times_New_Roman',_serif]">
    <div class="flex items-center justify-center bg-white">
      <div class="mx-auto my-auto w-[210mm] p-[2mm] print:scale-90 print:text-[13px]">
        <MedicalHeader :answers="answers" />
        <div class="content">
          <AdministrativeInfo :person="currentPerson" :answers="answers" />
          <MedicalHistory :question-groups="medicalHistoryGroups" :answers="answers" />
          <ExaminationInfo
            :question-groups="examinationInfoGroups"
            :answers="answers"
            :answers-dsh="answersDsh"
            :doctor-name="doctorName"
          />
          <SummaryAndDischarge
            :question-groups="summaryAndDischargeGroups"
            :answers="answers"
            :doctor-name="doctorName"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AdministrativeInfo from "./AdministrativeInfo.vue";
import MedicalHistory from "./MedicalHistory.vue";
import ExaminationInfo from "./ExaminationInfo.vue";
import SummaryAndDischarge from "./SummaryAndDischarge.vue";
import MedicalHeader from "./MedicalHeader.vue";
import { useRoute } from "vue-router";
import usePerson from "@/hooks/usePerson";
import { onMounted, computed } from "vue";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  FORM_ID,
  questionsLyDo,
  questionsHoiBenh,
  questionsKhamBenh,
  questionsTongKet,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/BenhAnNgoaiTru";
import type { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { questions as questionsDsh } from "@/pages/customer/components/MedicalExam/DynamicComponent/DauSinhHieu";

interface QuestionGroup {
  title: string;
  questions: Questions;
}

const route = useRoute();
const personId = Number(route.params.id);
const formIdDsh = "dsh";

const { getPerson, currentPerson } = usePerson();

const allQuestionGroups: QuestionGroup[] = [
  {
    title: "II. Lý do vào viện",
    questions: questionsLyDo,
  },
  {
    title: "III. Hỏi bệnh",
    questions: questionsHoiBenh,
  },
  {
    title: "IV. Khám bệnh",
    questions: questionsKhamBenh,
  },
  {
    title: "V. Tổng kết bệnh án",
    questions: questionsTongKet,
  },
];

// Fetch all answers using useDynamicForm
const { answers } = useDynamicForm(
  FORM_ID,
  {
    ...questionsLyDo,
    ...questionsHoiBenh,
    ...questionsKhamBenh,
    ...questionsTongKet,
  },
  personId,
);

const { answers: answersDsh } = useDynamicForm(formIdDsh, { ...questionsDsh }, personId);

const medicalHistoryGroups = allQuestionGroups.slice(0, 2);
const examinationInfoGroups = allQuestionGroups.slice(2, 3);
const summaryAndDischargeGroups = allQuestionGroups.slice(3, 4);

const doctorName = computed(() => {
  if (!currentPerson.value?.assignment) {
    return null;
  }
  const doctorAssignment = currentPerson.value.assignment.find(
    (assignment) => assignment.role === "doctor",
  );
  return doctorAssignment?.user?.name ?? null;
});

onMounted(async () => {
  if (currentPerson.value?.id) {
    return;
  }
  if (personId) {
    await getPerson({ id: personId });
  }
});
</script>

<style>
@media print {
  @page {
    size: auto;
    margin: 0;
  }
}
</style>
