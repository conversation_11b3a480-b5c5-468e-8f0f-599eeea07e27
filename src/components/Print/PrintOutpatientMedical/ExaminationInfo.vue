<template>
  <div class="mb-3">
    <template v-for="(group, groupIndex) in questionGroups" :key="groupIndex">
      <div class="mb-4">
        <h2 class="mb-2.5 text-xl font-bold uppercase">{{ group.title }}</h2>

        <div
          v-for="(item, questionId, index) in group.questions"
          :key="questionId"
          class="mb-2.5 pl-2"
        >
          <!-- Specific handling for 'toan_than' which includes the vital signs block -->
          <div v-if="questionId === 'toan_than'" class="flex">
            <div class="mr-2.5 w-4/5 truncate">
              <h3 class="mb-2.5 text-lg font-bold">
                {{ index + 1 }}. {{ item.question }}:
                <span class="font-normal">
                  {{ (answers[questionId as string] as any)?.text || "" }}
                </span>
              </h3>
            </div>
            <!-- Keep Vital Signs block as is -->
            <div class="w-1/5 border border-black p-[5px]">
              <ul>
                <li class="mb-[5px] flex items-baseline truncate italic">
                  <span class="mr-[5px] whitespace-nowrap">Mạch:</span>
                  <span class="mr-1">{{ answersDsh.mach?.text || "..." }}</span>
                  <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  <span>lần/ph</span>
                </li>
                <li class="mb-[5px] flex items-baseline truncate italic">
                  <span class="mr-[5px] whitespace-nowrap">Nhiệt độ:</span>
                  <span class="mr-1">{{ answersDsh.nhiet_do?.text || "..." }}</span>
                  <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  <span
                    class="align-[3pt] font-serif text-[6.5pt] font-normal italic text-black no-underline"
                    >o</span
                  >C
                </li>
                <li class="mb-[5px] flex items-baseline truncate italic">
                  <span class="mr-[5px] whitespace-nowrap">Huyết áp:</span>
                  <span class="mr-1">{{ answersDsh.huyet_ap?.text || "..." }}</span>
                  <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  <span>mmHg</span>
                </li>
                <li class="mb-[5px] flex items-baseline truncate italic">
                  <span class="mr-[5px] whitespace-nowrap">Nhịp thở:</span>
                  <span class="mr-1">{{ answersDsh.nhip_tho?.text || "..." }}</span>
                  <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  <span>lần/ph</span>
                </li>
                <li class="flex items-baseline truncate italic">
                  <span class="mr-[5px] whitespace-nowrap">Cân nặng:</span>
                  <span class="mr-1">{{ answersDsh.can_nang?.text || "..." }}</span>
                  <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  <span>Kg</span>
                </li>
              </ul>
            </div>
          </div>

          <!-- Handle 'hinh_ve_mo_ta' separately -->
          <div v-else-if="questionId === 'hinh_ve_mo_ta'" class="break-before-page">
            <h3 class="mb-2.5 text-lg font-bold">
              {{ index + 1 }}. {{ item.question }}
              <!-- Answer for hinh_ve_mo_ta is not displayed, only the drawing -->
            </h3>
            <div class="flex w-full items-end justify-between">
              <p>Phải</p>
              <p>Thẳng</p>
              <p>Trái</p>
              <p>Hàm trên và họng</p>
              <p>Hàm dưới</p>
              <p style="line-height: 1.3">Phân loại khe hở<br />môi vòm miệng</p>
            </div>
            <div class="mb-5 flex w-full items-end justify-end">
              <div class="w-[35%]">
                <ul>
                  <li class="mb-[5px]">1 và 4 là khe hở môi</li>
                  <li class="mb-[5px]">2 và 5 là khe hở xương ổ răng</li>
                  <li class="mb-[5px]">3 và 6 là khe hở cung hàm</li>
                  <li class="mb-[5px]">7 và 8 là khe hở vòm miệng cứng</li>
                  <li></li>
                  9 là khe hở vòm miệng mềm
                </ul>
              </div>
            </div>
            <div class="flex justify-center">
              <div class="w-1/2">
                <TeethAdultMinimal />
              </div>
            </div>
            <div class="flex justify-center">
              <div class="w-1/2">
                <TeethChildrenMinimal />
              </div>
            </div>
          </div>

          <!-- Default rendering for other questions -->
          <div v-else>
            <div class="text-lg">
              <span class="font-bold"> {{ index + 1 }}. {{ item.question }}: </span>
              <span class="font-normal">
                {{ (answers[questionId as string] as any)?.text || "" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="mb-3 flex justify-between">
      <div class="w-1/2 text-center">
        <p class="mb-2.5">&nbsp;</p>
        <!-- Empty paragraph for alignment -->
        <h2 class="mb-[100px] text-xl font-bold uppercase">Giám đốc bệnh viện</h2>
        <p class="flex items-baseline justify-center truncate">
          <span class="mr-1 whitespace-nowrap">Họ và tên:</span>
          <span class="h-4 w-2/5 border-b border-dashed border-black"></span>
        </p>
      </div>
      <div class="w-1/2 text-center">
        <p class="mb-2.5">{{ formattedExamDate }}</p>
        <h2 class="mb-[100px] text-xl font-bold uppercase">Bác sĩ khám bệnh</h2>
        <p class="truncate">Họ và tên: {{ answers.tk_bac_si_kham?.text || "..." }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TeethAdultMinimal from "@/base-components/Icons/TeethAdultMinimal.vue";
import TeethChildrenMinimal from "@/base-components/Icons/TeethChildrenMinimal.vue";
import { Answer } from "@/hooks/useDynamicForm";
import { formatExamDate } from "@/utils/helper";
import type { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { computed } from "vue";

interface QuestionGroup {
  title: string;
  questions: Questions;
}

const props = defineProps<{
  questionGroups: QuestionGroup[];
  answers: Record<string, Answer>;
  answersDsh: Record<string, Answer>;
  doctorName: string | null;
}>();

const formattedExamDate = computed(() => {
  return formatExamDate(props.answers?.dieu_tri_tu_ngay?.text, "Ngày");
});
</script>
