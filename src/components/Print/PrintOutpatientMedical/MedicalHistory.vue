<template>
  <template v-for="(group, groupIndex) in questionGroups" :key="groupIndex">
    <div class="mb-2.5 flex items-center text-base">
      <h2 class="text-xl font-bold uppercase">{{ group.title }}:</h2>
    </div>

    <div class="mb-4 pl-2">
      <div
        v-for="(item, questionId, index) in group.questions"
        :key="questionId"
        class="mb-2.5 text-lg"
      >
        <span class="font-bold"> {{ index + 1 }}. {{ item.question }}: </span>
        <span class="font-normal">
          {{ (answers[questionId as string] as any)?.text || "" }}
        </span>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { Answer } from "@/hooks/useDynamicForm";
import type { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";

interface QuestionGroup {
  title: string;
  questions: Questions;
}

defineProps<{
  questionGroups: QuestionGroup[];
  answers: Record<string, Answer>;
}>();
</script>
