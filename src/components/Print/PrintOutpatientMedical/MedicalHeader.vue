<template>
  <div class="mb-4 flex items-end justify-between">
    <div class="text-base">
      <div class="truncate">Sở Y tế: TP HCM</div>
      <div class="truncate">Bệnh viện: CTCP Up Dental</div>
      <div class="truncate">Khoa: RHM</div>
    </div>
    <div class="truncate text-center text-xl font-bold uppercase">
      Bệnh án ngoại trú<br />Chuyên khoa răng hàm mặt
    </div>
    <div class="text-base">
      <div class="truncate">Số ngoại trú: {{ answers?.tk_so_ngoai_tru?.text || "..." }}</div>
      <div class="truncate">Số lưu trú: {{ answers?.tk_so_luu_tru?.text || "..." }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Answer } from "@/hooks/useDynamicForm";

defineProps<{ answers: Record<string, Answer> }>();
</script>
