<template>
  <div class="mb-5 mt-2.5 break-before-page text-base">
    <h2 class="mb-2.5 text-xl font-bold uppercase">Tổng kết bệnh án</h2>
    <div class="border border-black p-2.5">
      <div class="mb-2.5">
        <h3 class="mb-2.5 text-lg font-bold">
          1. <PERSON>u<PERSON> trình bệnh lý và diễn biến lâm sàng:
          <span class="font-normal">
            {{ (answers["tk_qua_trinh_benh_ly"] as any)?.text || "" }}
          </span>
        </h3>
        <div class="mb-5 w-full border-b border-dashed border-black"></div>
        <div class="mb-2.5 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="mb-2.5">
        <h3 class="mb-2.5 text-lg font-bold">
          2. <PERSON><PERSON><PERSON> t<PERSON><PERSON> kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán:
          <span class="font-normal">
            {{ (answers["tk_tom_tat_ket_qua"] as any)?.text || "" }}
          </span>
        </h3>
        <div class="mb-5 w-full border-b border-dashed border-black"></div>
        <div class="mb-2.5 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="mb-2.5 truncate">
        <h3 class="mb-2.5 text-lg font-bold">3. Chuẩn đoán ra viện:</h3>
        <div class="mb-2.5 flex items-end truncate text-base">
          <span class="mr-1 truncate">- Bệnh chính: </span>
          <span style="flex: 1 1 0%">
            {{ (answers["tk_chuan_doan_benh_chinh"] as any)?.text || "" }}
          </span>
          <span
            ><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text"
          /></span>
        </div>
        <div class="flex items-end text-base">
          <span class="truncate">- Bệnh kèm theo: <span class="italic">(Nếu có)</span></span
          ><span class="truncate" style="flex: 1 1 0%">{{
            (answers["tk_chuan_doan_benh_kem_theo"] as any)?.text || ""
          }}</span
          ><span
            ><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 mr-[5px] h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text" /><input
              class="m-0 h-4 w-4 rounded-none border-[0.5px] border-black"
              type="text"
          /></span>
        </div>
      </div>
      <div class="mb-2.5">
        <h3 class="mb-2.5 text-lg font-bold">
          4. Phương pháp điều trị:
          <span class="font-normal">
            {{ (answers["tk_phuong_phap_dieu_tri"] as any)?.text || "" }}
          </span>
        </h3>
        <div class="mb-5 w-full border-b border-dashed border-black"></div>
        <div class="mb-2.5 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="mb-2.5">
        <h3 class="mb-2.5 text-lg font-bold">
          5. Tình trạng người bệnh ra viện:
          <span class="font-normal">
            {{ (answers["tk_tinh_trang_ra_vien"] as any)?.text || "" }}
          </span>
        </h3>
        <div class="mb-5 w-full border-b border-dashed border-black"></div>
        <div class="mb-2.5 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="mb-2.5">
        <!-- Typo fixed: lmb-10 -> mb-2.5 -->
        <h3 class="mb-2.5 text-lg font-bold">
          6. Huớng dẫn điều trị và các chế độ tiếp theo:
          <span class="font-normal">
            {{ (answers["tk_huong_dieu_tri_tiep"] as any)?.text || "" }}
          </span>
        </h3>
        <div class="mb-5 w-full border-b border-dashed border-black"></div>
        <div class="mb-2.5 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="mb-2.5 mt-5 flex">
        <table class="w-full border-collapse border border-black">
          <tbody>
            <tr>
              <td colspan="2" class="border border-black p-0.5 text-center font-bold">
                Hồ sơ, phim, ảnh
              </td>
              <td rowspan="4" class="h-full border border-black p-0.5 text-center">
                <div class="flex h-[110px] flex-col justify-between">
                  <p class="font-bold">Người giao hồ sơ</p>
                  <p class="flex items-baseline justify-center truncate px-1">
                    <span class="mr-1 whitespace-nowrap">Họ và tên:</span>
                    <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  </p>
                </div>
              </td>
              <td rowspan="8" class="h-full w-[30%] border border-black p-0.5 text-center">
                <div class="flex h-[227px] flex-col justify-between">
                  <div>
                    <p>{{ formattedExamDate }}</p>
                    <h3 class="truncate text-lg font-bold">Bác sĩ điều trị</h3>
                  </div>
                  <p class="flex items-baseline justify-center truncate px-1">
                    <span class="mr-1 whitespace-nowrap">Họ và tên:</span>
                    {{ answers.tk_bac_si_kham?.text || "..." }}
                  </p>
                </div>
              </td>
            </tr>
            <tr>
              <td class="border border-black p-0.5 text-center font-bold">Loại</td>
              <td class="border border-black p-0.5 text-center font-bold">Số tờ</td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- X - quang</td>
              <td class="border border-black p-0.5"></td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- CT Scanner</td>
              <td class="border border-black p-0.5"></td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- Siêu âm</td>
              <td class="border border-black p-0.5"></td>
              <td rowspan="4" class="h-full w-[30%] border border-black p-0.5 text-center">
                <div class="flex h-[110px] flex-col justify-between">
                  <p class="font-bold">Người nhận hồ sơ</p>
                  <p class="flex items-baseline justify-center truncate px-1">
                    <span class="mr-1 whitespace-nowrap">Họ và tên:</span>
                    <span class="h-4 flex-1 border-b border-dashed border-black"></span>
                  </p>
                </div>
              </td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- Xét nghiệm</td>
              <td class="border border-black p-0.5"></td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- Khác</td>
              <td class="border border-black p-0.5"></td>
            </tr>
            <tr>
              <td class="border border-black p-0.5">- Toàn bộ hồ sơ</td>
              <td class="border border-black p-0.5"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Answer } from "@/hooks/useDynamicForm";
import { formatExamDate } from "@/utils/helper";
import { computed } from "vue";

const props = defineProps<{
  answers: Record<string, Answer>;
  doctorName: string | null;
}>();

const formattedExamDate = computed(() => {
  return formatExamDate(props.answers?.dieu_tri_tu_ngay?.text, "Ngày");
});
</script>
