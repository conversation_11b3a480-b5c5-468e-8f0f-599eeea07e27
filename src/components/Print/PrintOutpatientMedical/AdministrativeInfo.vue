<template>
  <div class="mb-3 text-[18px]">
    <h2 class="mb-2 text-xl font-bold uppercase"><PERSON><PERSON> <PERSON><PERSON><PERSON> chính</h2>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline">
        <span class="mr-1">1. Họ và tên <i class="italic">(In hoa):</i></span>
        <span v-if="person?.full_name" class="font-bold uppercase" style="flex: 1 1 0%">
          {{ person.full_name }}
        </span>
        <div v-else class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline justify-between">
        <span class="mr-2.5">2. Sinh ngày: {{ formattedDob }}</span>
        <span class="flex"
          ><span class="mr-2.5">Tuổi: </span><span class="truncate">{{ age ?? "" }}</span></span
        >
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline">
        <span class="mr-2.5">3. Giới tính:</span
        ><span class="mr-[50px] flex"
          >1. Nam<label class="group relative ml-[5px] cursor-pointer select-none text-base"
            ><input
              type="checkbox"
              :checked="person?.gender === 'Nam' || person?.gender === 'male'"
              class="peer absolute h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark absolute left-0 top-0 h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white peer-checked:after:absolute peer-checked:after:left-[3px] peer-checked:after:top-0 peer-checked:after:h-[10px] peer-checked:after:w-[5px] peer-checked:after:rotate-45 peer-checked:after:border-b-2 peer-checked:after:border-r-2 peer-checked:after:border-solid peer-checked:after:border-black"
            ></span></label></span
        ><span class="flex"
          >2. Nữ<label class="group relative ml-[5px] cursor-pointer select-none text-base"
            ><input
              type="checkbox"
              :checked="person?.gender === 'Nữ' || person?.gender === 'female'"
              class="peer absolute h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark absolute left-0 top-0 h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white peer-checked:after:absolute peer-checked:after:left-[3px] peer-checked:after:top-0 peer-checked:after:h-[10px] peer-checked:after:w-[5px] peer-checked:after:rotate-45 peer-checked:after:border-b-2 peer-checked:after:border-r-2 peer-checked:after:border-solid peer-checked:after:border-black"
            ></span></label
        ></span>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">4. Nghề nghiệp:</span>
        <span v-if="jobName" style="flex: 1 1 0%">{{ jobName }}</span>
        <div v-else class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">5. Dân tộc:</span>
        <div class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">6. Ngoại kiều:</span>
        <div class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline truncate">
        <span class="mr-1">7. Địa chỉ:</span>
        <span v-if="fullAddress" style="flex: 1 1 0%">{{ fullAddress }}</span>
        <div v-else class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-1">8. Số điện thoại:</span>
        <span v-if="person?.phone" style="flex: 1 1 0%">{{ person.phone }}</span>
        <div v-else class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline truncate">
        <span class="mr-1">9. Nơi làm việc:</span>
        <div class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">10. Đối tượng:</span>
        <span class="flex items-center">
          <i class="text-[13px] italic">1.BHYT </i
          ><label
            class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
            ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
            ></span></label></span
        ><span class="flex items-center">
          <i class="text-[13px] italic">2.Thu phí</i
          ><label
            class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
            ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
            ></span></label></span
        ><span class="flex items-center">
          <i class="text-[13px] italic">3.Miễn</i
          ><label
            class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
            ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
            ></span></label></span
        ><span class="flex items-center">
          <i class="text-[13px] italic">4.Khác</i
          ><label
            class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
            ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
              class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
            ></span></label
        ></span>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 flex w-1/2 items-baseline truncate">
        <span class="mr-1 whitespace-nowrap">11. BHYT giá trị đến:</span>
        <span class="ml-2 mr-1">Ngày</span>
        <div class="h-4 w-full border-b border-dashed border-black"></div>
        <span class="ml-2 mr-1">Tháng</span>
        <div class="h-4 w-full border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">Số thẻ BHYT</span
        ><span class="flex" style="width: 100%"
          ><input
            class="m-0 mr-[5px] h-4 w-4 flex-1 rounded-none border-[0.5px] border-black"
            type="text" /><input
            class="m-0 mr-[5px] h-4 w-4 flex-1 rounded-none border-[0.5px] border-black"
            type="text" /><input
            class="m-0 mr-[5px] h-4 w-4 flex-1 rounded-none border-[0.5px] border-black"
            type="text" /><input
            class="m-0 mr-[5px] h-4 w-4 flex-1 rounded-none border-[0.5px] border-black"
            type="text" /><input
            class="m-0 h-4 w-4 flex-1 rounded-none border-[0.5px] border-black"
            type="text"
        /></span>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="w-[43%] truncate">12. Họ tên, địa chỉ người nhà khi cần báo tin:</div>
      <div class="w-[57%] truncate">
        <div class="h-4 border-b border-dashed border-black"></div>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 w-1/2 truncate">
        <div class="h-4 border-b border-dashed border-black"></div>
      </div>
      <div class="flex w-1/2 items-baseline truncate">
        <span class="mr-2.5">Điện thoại số:</span>
        <div class="h-4 flex-1 border-b border-dashed border-black"></div>
      </div>
    </div>
    <div class="mb-2 flex items-baseline">
      <div class="mr-2.5 truncate">
        <span class="mr-2.5">13. Đến khám bệnh lúc: {{ formattedExamTime }}</span
        ><span class="mr-2.5">{{ formattedExamDate }}</span>
      </div>
    </div>
    <div class="mb-2">
      <div class="flex items-baseline">
        <span class="mr-2.5">14. Chuẩn đoán và xử lý của nơi giới thiệu:</span>
        <div class="mr-2.5 h-4 flex-1 border-b border-dashed border-black"></div>
        <span class="flex"
          ><span class="mr-[30px] flex"
            ><i class="mr-[5px] italic">1.Y tế </i
            ><label
              class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
              ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
                class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
              ></span></label></span
          ><span class="mr-[30px] flex"
            ><i class="mr-[5px] italic">2.Tự đến</i
            ><label
              class="group ml-[5px] inline-flex cursor-pointer select-none items-center text-base"
              ><input type="checkbox" class="peer relative h-0 w-0 cursor-pointer opacity-0" /><span
                class="checkmark relative h-4 w-4 border border-black bg-white group-hover:bg-white peer-checked:bg-white"
              ></span></label></span
        ></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PersonResponse } from "@/api/bcare-types-v2";
import { Answer } from "@/hooks/useDynamicForm";
import useLocation from "@/hooks/useLocation";
import useTerm from "@/hooks/useTerm";
import { formatExamDate, formatExamTime } from "@/utils/helper";
import dayjs from "dayjs";
import { computed } from "vue";

const props = defineProps<{
  person: PersonResponse | null;
  answers: Record<string, Answer>;
}>();

const { getFullAddress } = useLocation();
const { getTermNameById } = useTerm();

const formattedDob = computed(() => {
  if (props.person?.date_of_birth) {
    return dayjs(props.person.date_of_birth).format("DD / MM / YYYY");
  }
  return null;
});

const age = computed(() => {
  if (props.person?.date_of_birth) {
    return dayjs().diff(props.person.date_of_birth, "year");
  }
  return null;
});

const fullAddress = computed(() => {
  if (!props.person) return "-";
  return getFullAddress.value({
    address_number: props.person.address_number,
    ward_id: props.person.ward_id,
    district_id: props.person.district_id,
    province_id: props.person.province_id,
  });
});

const jobName = computed(() => {
  if (props.person?.job_id) {
    return getTermNameById("nghe_nghiep", props.person.job_id);
  }
  return null;
});

const formattedExamTime = computed(() => {
  return formatExamTime(props.answers?.dieu_tri_tu_ngay?.text);
});

const formattedExamDate = computed(() => {
  return formatExamDate(props.answers?.dieu_tri_tu_ngay?.text);
});
</script>
