<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useRoute } from "vue-router";

import usePerson from "@/hooks/usePerson";
import NiengRang from "@/pages/customer/components/PlanTab/DynamicComponent/NiengRang.vue";
import TongQuat from "@/pages/customer/components/PlanTab/DynamicComponent/TongQuat.vue";

import PrintPersonInfo from "./PrintPersonInfo.vue";

const route = useRoute();
const personId = Number(route.params.personId);
const type = route.params.type as string;

// Fetch person data
const { getPerson, currentPerson } = usePerson();

const Component = computed(() => {
  switch (type) {
    case "general":
      return TongQuat;
    case "braces":
      return NiengRang;
    default:
      return null;
  }
});

const title = computed(() => {
  switch (type) {
    case "general":
      return "Kế hoạch điều trị tổng quát";
    case "braces":
      return "Kế hoạch điều trị niềng răng";
    default:
      return "Kế hoạch điều trị";
  }
});

// Fetch person data when component mounts
onMounted(async () => {
  if (personId) {
    await getPerson({ id: personId });
  }
});
</script>

<template>
  <PrintLayout :title="title">
    <template #content>
      <!-- Person Info Section -->
      <PrintPersonInfo :person="currentPerson" />

      <!-- Plan Content -->
      <component v-if="Component" :is="Component" :person-id="personId" />
    </template>
  </PrintLayout>
</template>
