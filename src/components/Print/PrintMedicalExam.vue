<script setup lang="ts">
import { computed, markRaw, onMounted } from "vue";

import usePerson from "@/hooks/usePerson";
// Import các component
import CanKhop from "@/pages/customer/components/MedicalExam/DynamicComponent/CanKhop.vue";
import ChiDinh from "@/pages/customer/components/MedicalExam/DynamicComponent/ChiDinh.vue";
import DanhGiaCN from "@/pages/customer/components/MedicalExam/DynamicComponent/DanhGiaCN.vue";
import DauSinhHieu from "@/pages/customer/components/MedicalExam/DynamicComponent/DauSinhHieu.vue";
import KhamCoLuoi from "@/pages/customer/components/MedicalExam/DynamicComponent/KhamCoLuoi.vue";
import KhamNgoaiMat from "@/pages/customer/components/MedicalExam/DynamicComponent/KhamNgoaiMat.vue";
import KhamRang from "@/pages/customer/components/MedicalExam/DynamicComponent/KhamRang.vue";
import LyDoKham from "@/pages/customer/components/MedicalExam/DynamicComponent/LyDoKham.vue";
import TienSuNhaKhoa from "@/pages/customer/components/MedicalExam/DynamicComponent/TienSuNhaKhoa.vue";
import TienSuYKhoa from "@/pages/customer/components/MedicalExam/DynamicComponent/TienSuYKhoa.vue";
import VanDong from "@/pages/customer/components/MedicalExam/DynamicComponent/VanDong.vue";

import PrintLayout from "./PrintLayout.vue";
import PrintPersonInfo from "./PrintPersonInfo.vue";

const props = defineProps<{
  personId: number;
  tabIndex: number;
}>();

const { getPerson, currentPerson } = usePerson();

// Xác định stage dựa vào tabIndex
const currentStage = computed(() => (props.tabIndex < 3 ? 1 : 2));

// Phân nhóm components theo giai đoạn
const stageComponents = {
  1: [TienSuYKhoa, TienSuNhaKhoa, LyDoKham],
  2: [DauSinhHieu, KhamNgoaiMat, KhamRang, CanKhop, KhamCoLuoi, DanhGiaCN, VanDong, ChiDinh],
};

const componentsToRender = markRaw(stageComponents[currentStage.value as 1 | 2]);
const title = `Phiếu khám giai đoạn ${currentStage.value}`;

// Fetch person data when component mounts
onMounted(async () => {
  if (props.personId) {
    await getPerson({ id: props.personId });
  }
});
</script>

<template>
  <PrintLayout :title="title">
    <template #content>
      <PrintPersonInfo :person="currentPerson" />
      <div v-for="(Comp, index) in componentsToRender" :key="index">
        <component :is="Comp" :person-id="props.personId" />
      </div>
    </template>

    <template #footer>
      <div class="flex justify-end">
        <!-- Left Side -->
        <div class="w-1/2 text-right">
          <p class="invisible mb-1">?</p>
          <p class="invisible mb-1">?</p>
          <div class="ml-auto w-1/2">
            <div class="flex items-center justify-center">
              <p class="mb-1 font-medium">Khách hàng xác nhận</p>
            </div>
            <div class="flex items-center justify-center">
              <p class="font-medium">(Ký tên)</p>
            </div>
            <div class="h-12"></div>
            <div class="flex items-center justify-center">
              <p class="font-medium">{{ currentPerson?.full_name }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
