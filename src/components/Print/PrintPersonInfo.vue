<template>
  <div class="mb-3">
    <h2 class="font-medium">Họ và tên: {{ person?.full_name }}</h2>
    <p class="text-gray-600"><PERSON><PERSON> điện thoại: {{ person?.phone }}</p>
    <p class="text-gray-600">Email: {{ person?.email }}</p>
  </div>
</template>

<script setup lang="ts">
import { PersonResponse } from "@/api/bcare-types-v2";

const props = defineProps<{
  person: PersonResponse | null;
}>();
</script>

<style scoped></style>
