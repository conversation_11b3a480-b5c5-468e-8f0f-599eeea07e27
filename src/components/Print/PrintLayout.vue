<!-- PrintLayout.vue -->
<script setup lang="ts">
import config from "@/config/config";

interface ClinicInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
}

interface PrintLayoutProps {
  clinicInfo?: ClinicInfo;
  title: string;
  documentNo?: string | number;
  printDate?: string | Date;
  showTime?: boolean;
}

const props = withDefaults(defineProps<PrintLayoutProps>(), {
  clinicInfo: () => config.clinic,
  printDate: () => new Date().toISOString(),
  showTime: true
});

const formatDateTime = (date: string | Date) => {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  const dateFormat = new Intl.DateTimeFormat("vi-VN", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).format(dateObj);

  if (!props.showTime) return dateFormat;

  const timeFormat = new Intl.DateTimeFormat("vi-VN", {
    hour: "2-digit",
    minute: "2-digit",
  }).format(dateObj);

  return `${dateFormat} ${timeFormat}`;
};
</script>

<template>
  <div class="print-wrapper">
    <div id="print-content" class="print-layout bg-white">
      <!-- Header with 2 columns -->
      <div class="layout-header flex justify-between border-b pb-4">
        <!-- Left column - Clinic Info -->
        <div class="flex-1">
          <h1 class="text-xl font-bold">{{ clinicInfo.name }}</h1>
          <p class="text-gray-600">{{ clinicInfo.address }}</p>
          <p class="text-gray-600">Số điện thoại: {{ clinicInfo.phone }}</p>
        </div>

        <!-- Right column - Document Info -->
        <div class="flex-1 text-right">
          <h2 class="mb-2 text-xl font-bold uppercase">{{ props.title }}</h2>
          <p v-if="props.documentNo" class="mb-1 text-gray-600">
            Số: {{ props.documentNo }}
          </p>
          <p class="text-gray-600">Ngày: {{ formatDateTime(props.printDate) }}</p>
        </div>
      </div>

      <!-- Content -->
      <div class="layout-content my-4">
        <slot name="content"></slot>
      </div>

      <!-- Footer -->
      <div class="layout-footer mt-5">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<style>
.print-layout {
  width: 210mm;
  padding: 6mm;
  margin: 0 auto;
}

@media print {
  :root {
    font-size: 13px;
  }

  @page {
    size: auto;
    margin: 0;
  }
}

</style>
