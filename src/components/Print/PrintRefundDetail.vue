<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

import type { PersonAssignmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Money from "@/base-components/Money.vue";
import { useAppointment } from "@/hooks/useAppointment";
import useEconomy from "@/hooks/useEconomy";
import usePerson from "@/hooks/usePerson";
import { useAuth } from "@/pages/auth/config/auth-composable";

import PrintLayout from "./PrintLayout.vue";

const route = useRoute();
const filename = route.query.filename as string;
const paymentId = Number(route.params.id);
const currentDate = new Date().toLocaleDateString("vi-VN");

const { currentUser } = useAuth();
const { getPayment, currentPayment } = useEconomy();
const { getPerson, currentPerson } = usePerson();
const { getLatestAppointment } = useAppointment();
const latestAppointment = ref<string>("-");

const fetchPaymentData = async () => {
  if (paymentId) {
    const payment = await getPayment({ id: paymentId });
    if (payment?.person_id) {
      await getPerson({ id: payment.person_id, include_relation: true });
      const appointment = await getLatestAppointment({
        person_id: payment.person_id,
      });
      latestAppointment.value = appointment
        ? useDateFormat(appointment.start_time, "DD/MM/YYYY").value
        : "-";
    }
  }
};

onMounted(fetchPaymentData);

const getPaymentMethodAmount = (method: string) => {
  if (!currentPayment.value) return 0;
  switch (method) {
    case "cash":
      return currentPayment.value.cash || 0;
    case "credit_card":
      return currentPayment.value.credit_card || 0;
    case "bank":
      return currentPayment.value.bank || 0;
    case "mpos":
      return currentPayment.value.mpos || 0;
    case "momo":
      return currentPayment.value.momo || 0;
    default:
      return 0;
  }
};

const getPaymentMethodLabel = (method: string) => {
  switch (method) {
    case "cash":
      return "Tiền mặt";
    case "credit_card":
      return "Thẻ tín dụng";
    case "bank":
      return "Chuyển khoản";
    case "mpos":
      return "mPOS";
    case "momo":
      return "MoMo";
    default:
      return method;
  }
};

const doctorNames = computed(() => {
  if (!currentPerson.value?.assignment?.length) return "-";
  return currentPerson.value.assignment
    .filter((a: PersonAssignmentResponse) => a.role === "doctor")
    .map((a: PersonAssignmentResponse) => a.user?.name)
    .filter(Boolean)
    .join(", ");
});
</script>

<template>
  <PrintLayout
    v-if="currentPayment && currentPerson"
    title="PHIẾU HOÀN PHÍ"
    :document-no="String(currentPayment.id)"
  >
    <title>talaconma</title>
    <template #content>
      <!-- Customer Info -->
      <div class="mb-3">
        <h2 class="font-medium">Họ và tên: {{ currentPerson.full_name }}</h2>
        <p class="text-gray-600">Số điện thoại: {{ currentPerson.phone }}</p>
        <p class="text-gray-600">Email: {{ currentPerson.email }}</p>
      </div>

      <!-- Refund Details Table -->
      <div class="w-full">
        <table class="w-full border-collapse border">
          <thead>
            <tr class="bg-gray-100">
              <th class="border p-2 font-medium">STT</th>
              <th class="border p-2 font-medium">Tên</th>
              <th class="border p-2 font-medium">Người thêm</th>
              <th class="border p-2 font-medium">Thành tiền</th>
              <th class="border p-2 font-medium">Hoàn phí</th>
              <th class="border p-2 font-medium">Ghi chú</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(allocation, index) in currentPayment.allocations" :key="allocation.id">
              <td class="border p-2 text-center">{{ index + 1 }}</td>
              <td class="border p-2">
                <!-- Bill Item -->
                <div v-if="allocation.bill_item" class="font-medium">
                  <AttachmentTitle :attachment="allocation.bill_item.attachment ?? null" />
                  <div class="text-xs text-gray-500">
                    Hoá đơn: #{{ allocation.bill_item.bill_id }}
                  </div>
                </div>

                <!-- Installment -->
                <div v-if="allocation.installment" class="font-medium">
                  {{ allocation.installment.name }}
                  <div class="text-xs text-gray-500">
                    Trả góp: #{{ allocation.installment.plan_id }}
                  </div>
                </div>
              </td>
              <td class="border p-2 text-center">
                {{
                  allocation.bill_item?.user?.name || allocation.installment?.creator?.name || "-"
                }}
              </td>
              <td class="border p-2 text-right">
                <Money
                  :amount="allocation.bill_item?.amount"
                  variant="default"
                  v-if="allocation.bill_item?.amount"
                />
                <span v-else>-</span>
              </td>
              <td class="border p-2 text-right font-medium">
                <Money :amount="Math.abs(allocation.amount)" variant="default" />
              </td>
              <td class="border p-2 text-center">
                {{ allocation.note || "-" }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Total Amount -->
      <div class="mt-4 flex justify-end">
        <div class="w-64">
          <div class="flex justify-between border-b py-2">
            <span class="font-medium">Tổng tiền hoàn phí:</span>
            <Money
              :amount="Math.abs(currentPayment.total_amount)"
              class="font-bold"
              variant="default"
            />
          </div>
        </div>
      </div>

      <!-- Payment Methods -->
      <div class="my-2">
        <h4 class="mb-2 font-medium">Hình thức hoàn phí:</h4>
        <div class="grid grid-cols-5 gap-4">
          <div
            v-for="method in ['cash', 'credit_card', 'bank', 'mpos', 'momo']"
            :key="method"
            class="rounded border p-2"
          >
            <div class="text-sm text-gray-600">
              {{ getPaymentMethodLabel(method) }}
            </div>
            <div class="font-medium">
              <Money :amount="Math.abs(getPaymentMethodAmount(method))" variant="default" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="flex justify-between">
        <!-- Left Side -->
        <div class="w-1/2">
          <div class="w-1/2">
            <!-- Empty element for alignment -->
            <div class="flex items-center justify-center">
              <p class="invisible mb-1 font-medium">Hồ Chí Minh</p>
            </div>
            <div class="flex items-center justify-center">
              <p class="font-medium">Khách hàng</p>
            </div>
            <div class="h-16"></div>
            <div class="flex items-center justify-center">
              <p class="font-medium">
                {{ currentPerson.full_name }}
              </p>
            </div>
          </div>
        </div>

        <!-- Right Side -->
        <div class="w-1/2 text-right">
          <div class="ml-auto w-1/2">
            <div class="flex items-center justify-center">
              <p class="mb-1 font-medium">Hồ Chí Minh, {{ currentDate }}</p>
            </div>
            <div class="flex items-center justify-center">
              <p class="font-medium">Người lập phiếu</p>
            </div>
            <div class="h-16"></div>
            <div class="flex items-center justify-center">
              <p class="font-medium">{{ currentUser?.name }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
