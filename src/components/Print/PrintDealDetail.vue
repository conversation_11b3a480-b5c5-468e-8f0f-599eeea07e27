<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useRoute } from "vue-router";

import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Money from "@/base-components/Money.vue";
import useAttachment from "@/hooks/useAttachment";
import useDeal from "@/hooks/useDeal";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";

import PrintLayout from "./PrintLayout.vue";
import PrintTable from "./PrintTable.vue";

// Props
const route = useRoute();
const dealId = Number(route.params.id);
const personId = Number(route.params.personId);
// API & Store
const { fetchDealDetails, currentDeal } = useDeal();
const { listInstallmentPlans, installmentPlans } = useInstallmentPlan();
const { listAttachments, attachments } = useAttachment({ useStore: false });

// Fetch data
const fetchDealData = async () => {
  if (dealId) {
    await fetchDealDetails({ id: dealId, include_relation: true });
    await listInstallmentPlans({ filter: { deal_id: dealId } });
    await listAttachments({ filter: { person_id: personId }, attachment_kind: "proposal" });
  }
};

onMounted(fetchDealData);

// Get current installment plan (first item from array)
const currentInstallmentPlan = computed(() => installmentPlans.value?.[0]);

// Computed for Section I (Treatment Plan)
const treatmentPlanColumns = computed(() => [
  // { field: "tooth_status", header: "Tình trạng răng" },
  { field: "treatment", header: "Nội dung điều trị" },
  { field: "quantity", header: "Số lượng" },
  { field: "price_range", header: "Khoảng giá", template: "price_range" },
  { field: "unit_price", header: "Đơn giá", template: "money" },
  { field: "discount", header: "Giảm giá", template: "discount" }, // Changed to show % directly
  { field: "total", header: "Thành tiền", template: "money" },
]);

const treatmentPlanData = computed(() => {
  if (!attachments.value) return [];
  return attachments.value.map((att) => {
    const metaData = att.data?.find((d) => d.kind === "meta")?.data?.price;
    const priceRange = metaData ? JSON.parse(metaData) : { min: 0, max: 0 };

    const subtotal = att.price * att.quantity;
    const discountValue = att.discount || 0;

    // If discount > 100, treat as fixed amount, otherwise calculate percentage
    const discountAmount = discountValue > 100 ? discountValue : subtotal * (discountValue / 100);
    const total = subtotal - discountAmount;

    return {
      treatment: att.title,
      quantity: att.quantity,
      price_range: {
        min: priceRange.min,
        max: priceRange.max,
      },
      unit_price: att.price,
      discount: discountAmount,
      total,
    };
  });
});

// Computed for Section II (Accessories)
const accessoryColumns = computed(() => [
  { field: "product.name", header: "Tên", width: "3/4" },
  { field: "price", header: "Giá", template: "money", width: "1/4" },
]);

const accessories = computed(
  () => currentDeal.value?.attachments?.filter((a) => a.parent_id > 0) || [],
);

// Computed for Section III (Braces)
const bracesColumns = computed(() => [
  { field: "name", header: "Tên", width: "3/4" },
  { field: "price", header: "Giá", template: "money", width: "1/4" },
]);

const bracesData = computed(() => {
  if (!currentDeal.value) return [];

  const mainAttachment = currentDeal.value?.attachments?.find((a) => !a.parent_id);
  const discounts = currentDeal.value?.discount_usages || [];

  const data = [];

  if (mainAttachment) {
    data.push({
      name: mainAttachment.product?.name,
      price: mainAttachment.price * mainAttachment.quantity,
    });
  }

  discounts.forEach((discount) => {
    if (discount.value > 0) {
      data.push({
        name: `Khuyến mãi: ${discount.discount?.name || "-"}`,
        price: -discount.value,
      });
    }
  });

  return data;
});

// Summary computeds
const totalAmount = computed(() => {
  const mainAttachment = currentDeal.value?.attachments?.find((a) => !a.parent_id);
  if (!mainAttachment) return 0;

  const baseAmount = mainAttachment.price * mainAttachment.quantity;
  const totalDiscount = (currentDeal.value?.discount_usages || []).reduce(
    (sum, d) => sum + d.value,
    0,
  );

  return baseAmount - totalDiscount;
});

const downPayment = computed(() => currentInstallmentPlan.value?.down_payment || 0);
const remainingAmount = computed(() => totalAmount.value - downPayment.value);
const installmentCount = computed(() => currentInstallmentPlan.value?.total_installments || 0);
const installmentAmount = computed(() =>
  installmentCount.value ? remainingAmount.value / installmentCount.value : 0,
);

// Add these computed properties
const accessoriesTotal = computed(() =>
  accessories.value.reduce((sum, item) => sum + item.price * (item.quantity || 1), 0),
);

const treatmentPlanTotal = computed(() =>
  treatmentPlanData.value.reduce((sum, item) => sum + item.total, 0),
);
</script>

<template>
  <PrintLayout title="KẾ HOẠCH CHI PHÍ DỰ KIẾN">
    <template #content>
      <PrintPersonInfo v-if="currentDeal?.person" :person="currentDeal.person" />

      <!-- Section I -->
      <h3 class="my-4 font-bold">I. Kế hoạch thu phí tổng quát</h3>
      <PrintTable :columns="treatmentPlanColumns" :data="treatmentPlanData">
        <template #money="{ value }">
          <Money :amount="value" variant="default" />
        </template>
        <template #discount="{ item }">
          <Money :amount="item.discount" variant="default" />
          <!-- <span>{{ item.discount }}</span> -->
        </template>
        <template #price_range="{ item }">
          <template v-if="item.price_range.min && item.price_range.max">
            <Money :amount="item.price_range.min" variant="default" /> -
            <Money :amount="item.price_range.max" variant="default" />
          </template>
          <template v-else>-</template>
        </template>
      </PrintTable>
      <!-- Treatment Plan Summary -->
      <div class="mt-4 flex justify-end">
        <div class="w-64">
          <div class="flex justify-between py-1">
            <span class="font-medium">Tổng tiền: </span>
            <Money :amount="treatmentPlanTotal" class="font-medium" variant="default" />
          </div>
        </div>
      </div>

      <!-- Section II -->
      <h3 class="my-4 font-bold">II. Phí niềng</h3>
      <PrintTable :columns="bracesColumns" :data="bracesData">
        <template #money="{ value }">
          <Money :amount="value" variant="default" />
        </template>
        <template #name="{ item }">
          <template v-if="item.attachment">
            <AttachmentTitle :attachment="item.attachment" />
          </template>
          <template v-else>{{ item.name }}</template>
        </template>
      </PrintTable>
      <!-- Braces Summary -->
      <div class="mt-4 flex justify-end">
        <div class="w-64">
          <div class="flex justify-between py-1">
            <span class="font-medium">Tổng tiền:</span>
            <Money :amount="totalAmount" class="font-medium" variant="default" />
          </div>
        </div>
      </div>

      <!-- Section III -->
      <h3 class="my-4 font-bold">III. Khí cụ - Khác (Chi phí dự trù)</h3>
      <PrintTable :columns="accessoryColumns" :data="accessories">
        <template #money="{ value }">
          <Money :amount="value" variant="default" />
        </template>
        <template #product.name="{ item }">
          <AttachmentTitle :attachment="item" />
        </template>
      </PrintTable>
      <!-- Accessories Summary -->
      <div class="mt-4 flex justify-end">
        <div class="w-64">
          <div class="flex justify-between py-1">
            <span class="font-medium">Tổng tiền:</span>
            <Money :amount="accessoriesTotal" class="font-medium" variant="default" />
          </div>
        </div>
      </div>

      <!-- Final Summary Section -->
      <div class="mt-8 border-t pt-4">
        <!-- Payment Information Grid -->
        <div class="grid grid-cols-2 gap-2">
          <!-- Left Column -->
          <div class="space-y-3">
            <div class="flex items-start">
              <span class="w-28 font-bold">Gói niềng:</span>
              <span class="font-bold">
                {{ currentDeal?.attachments?.find((a) => !a.parent_id)?.product?.name || "-" }}
              </span>
            </div>
            <div class="flex items-start">
              <span class="w-28 text-gray-600">Tổng phí:</span>
              <Money :amount="totalAmount" class="font-medium" variant="default" />
            </div>
            <div class="flex items-start">
              <span class="w-28 text-gray-600">Tổng cọc:</span>
              <Money :amount="downPayment" class="font-medium" variant="default" />
            </div>
            <div class="flex items-start">
              <span class="w-28 text-gray-600">Còn lại:</span>
              <Money :amount="remainingAmount" class="font-medium" variant="default" />
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-3">
            <div class="invisible flex items-start">
              <span class="w-32">Placeholder</span>
            </div>
            <div class="flex items-start">
              <span class="w-32 text-gray-600">Số lần thu phí:</span>
              <span class="font-medium">{{ installmentCount }} lần</span>
            </div>
            <div class="flex items-start">
              <span class="w-32 text-gray-600">Số phí mỗi lần:</span>
              <Money :amount="installmentAmount" class="font-medium" variant="default" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </PrintLayout>
</template>
