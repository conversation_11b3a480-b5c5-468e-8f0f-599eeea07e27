<script setup lang="ts">
interface Column {
  field: string;
  header: string;
  template?: string;
  width?: string;
}

interface PrintTableProps<T> {
  columns: Column[];
  data?: T[];
  showIndex?: boolean;
}

const props = withDefaults(defineProps<PrintTableProps<any>>(), {
  showIndex: true,
});

const getCellValue = (item: any, field: string) => {
  return field.split(".").reduce((obj, key) => obj?.[key], item);
};
</script>

<template>
  <table class="w-full">
    <thead>
      <tr class="bg-gray-100">
        <th v-if="showIndex" class="w-16 border p-2">STT</th>
        <th
          v-for="col in columns"
          :key="col.field"
          class="border p-2"
          :class="col.width ? `w-${col.width}` : ''"
        >
          {{ col.header }}
        </th>
      </tr>
    </thead>

    <tbody>
      <tr v-for="(item, index) in data" :key="index">
        <td v-if="showIndex" class="w-16 border p-2 text-center">
          {{ index + 1 }}
        </td>
        <td
          v-for="col in columns"
          :key="col.field"
          class="border p-2"
          :class="col.width ? `w-${col.width}` : ''"
        >
          <slot
            v-if="col.template"
            :name="col.template"
            :item="item"
            :value="getCellValue(item, col.field)"
          >
            {{ getCellValue(item, col.field) }}
          </slot>
          <template v-else>
            {{ getCellValue(item, col.field) }}
          </template>
        </td>
      </tr>

      <tr v-if="data?.length === 0">
        <td
          :colspan="showIndex ? columns.length + 1 : columns.length"
          class="border p-1 text-center"
        >
          <slot name="empty">Không có dữ liệu</slot>
        </td>
      </tr>
    </tbody>
  </table>
</template>
