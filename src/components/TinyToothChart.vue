<script lang="ts" setup>
import <PERSON><PERSON> from "primevue/button";
import { computed, ref } from "vue";

import Lucide from "@/base-components/Lucide";

const props = defineProps<{
  modelValue: Record<string, boolean>;
  defaultArchMode?: "adult" | "children";
  label?: string;
  disabled?: boolean;
}>();

const emit = defineEmits(["update:modelValue"]);

const isChildrenMode = ref(props.defaultArchMode === "children");
const selectedTeeth = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const toggleArchMode = () => {
  if (!props.disabled) {
    isChildrenMode.value = !isChildrenMode.value;
  }
};

const triTueCuaLamDaiCa = computed(() => (isChildrenMode.value ? 4 : 0));
const toothNumberPerArch = computed(() => (isChildrenMode.value ? 5 : 8));

const getToothNumber = (quadrant: number, position: number): number => {
  const quadrantMap = {
    1: 19 - position,
    2: 20 + position,
    3: 30 + position,
    4: 49 - position,
    5: 56 - position,
    6: 60 + position,
    7: 70 + position,
    8: 86 - position,
  };
  return quadrantMap[quadrant as keyof typeof quadrantMap] || 0;
};

const toggleTooth = (toothNumber: number | string) => {
  if (!props.disabled) {
    if (selectedTeeth.value[toothNumber]) {
      delete selectedTeeth.value[toothNumber];
    } else {
      selectedTeeth.value[toothNumber] = true;
    }
    emit("update:modelValue", selectedTeeth.value);
  }
};

const isSelected = (toothNumber: number | string) => {
  return selectedTeeth.value[toothNumber];
};

const isUpperSelected = computed(() => selectedTeeth.value["upper"]);
const isLowerSelected = computed(() => selectedTeeth.value["lower"]);

const toggleUpper = () => {
  if (!props.disabled) {
    if (selectedTeeth.value["upper"]) {
      delete selectedTeeth.value["upper"];
    } else {
      selectedTeeth.value["upper"] = true;
    }
    emit("update:modelValue", selectedTeeth.value);
  }
};

const toggleLower = () => {
  if (!props.disabled) {
    if (selectedTeeth.value["lower"]) {
      delete selectedTeeth.value["lower"];
    } else {
      selectedTeeth.value["lower"] = true;
    }
    emit("update:modelValue", selectedTeeth.value);
  }
};
</script>

<template>
  <div :class="[isChildrenMode ? 'mx-auto w-8/12' : 'w-full', props.disabled ? 'opacity-100' : '']">
    <!-- Upper teeth -->
    <div
      :class="isUpperSelected ? 'bg-orange-200' : ''"
      class="flex w-full justify-center rounded"
      @click="toggleUpper"
    >
      <div
        v-for="quadrant in [1 + triTueCuaLamDaiCa, 2 + triTueCuaLamDaiCa]"
        :key="`upper-${quadrant}`"
        :class="{
          'pr-1': quadrant === 1 + triTueCuaLamDaiCa,
          'pl-1': quadrant === 2 + triTueCuaLamDaiCa,
        }"
        class="relative flex flex-1"
      >
        <div
          v-for="position in toothNumberPerArch"
          :key="`upper-${quadrant}-${position}`"
          class="relative flex-1 p-1"
        >
          <div class="flex h-full flex-col items-center justify-center">
            <Button
              :class="{
                'opacity-20': !isSelected(getToothNumber(quadrant, position)) && props.disabled,
              }"
              :label="getToothNumber(quadrant, position).toString()"
              :severity="isSelected(getToothNumber(quadrant, position)) ? 'warn' : 'secondary'"
              class="aspect-square rounded-full p-0 font-light md:w-11/12 md:text-xs xl:w-9/12 xl:text-base"
              rounded
              @click.stop="toggleTooth(getToothNumber(quadrant, position))"
            />
          </div>
          <div
            v-if="position < toothNumberPerArch"
            class="absolute bottom-0 right-0 z-10 w-2/5 translate-x-1/2 transform"
          >
            <Lucide
              :class="
                isSelected(
                  `${getToothNumber(quadrant, position)}-${getToothNumber(quadrant, position + 1)}`,
                )
                  ? 'text-orange-400'
                  : 'text-slate-200 hover:text-slate-400'
              "
              class="mx-auto h-full w-full cursor-pointer"
              icon="Triangle"
              @click.stop="
                toggleTooth(
                  `${getToothNumber(quadrant, position)}-${getToothNumber(quadrant, position + 1)}`,
                )
              "
            />
          </div>
          <div
            v-if="getToothNumber(quadrant, position) == 11 + triTueCuaLamDaiCa * 10"
            class="absolute -right-1 bottom-0 z-10 w-2/5 translate-x-1/2 transform"
          >
            <Lucide
              :class="
                isSelected(`${11 + triTueCuaLamDaiCa * 10}-${21 + triTueCuaLamDaiCa * 10}`)
                  ? 'text-orange-400'
                  : 'text-slate-200 hover:text-slate-400'
              "
              class="mx-auto h-full w-full cursor-pointer"
              icon="Triangle"
              @click.stop="
                toggleTooth(`${11 + triTueCuaLamDaiCa * 10}-${21 + triTueCuaLamDaiCa * 10}`)
              "
            />
          </div>
        </div>
        <div
          v-if="quadrant === 1 + triTueCuaLamDaiCa"
          class="absolute bottom-[2rem] right-0 top-0 w-[2px] translate-x-[1px] transform bg-gray-200"
        ></div>
      </div>
    </div>

    <!-- Separator -->
    <div class="relative">
      <div class="absolute inset-x-0 top-1/2 h-[2px] -translate-y-[1px] bg-gray-200"></div>
      <div
        class="relative z-10 mx-auto w-20 cursor-pointer rounded border bg-white text-center text-xs"
        @click.stop="toggleArchMode"
      >
        {{ props.label }}
      </div>
    </div>

    <!-- Lower teeth -->
    <div
      :class="isLowerSelected ? 'bg-orange-200' : ''"
      class="flex w-full justify-center rounded"
      @click="toggleLower"
    >
      <div
        v-for="quadrant in [4 + triTueCuaLamDaiCa, 3 + triTueCuaLamDaiCa]"
        :key="`lower-${quadrant}`"
        :class="{
          'pr-1': quadrant === 4 + triTueCuaLamDaiCa,
          'pl-1': quadrant === 3 + triTueCuaLamDaiCa,
        }"
        class="relative flex flex-1"
      >
        <div
          v-for="position in toothNumberPerArch"
          :key="`lower-${quadrant}-${position}`"
          class="relative flex-1 p-1"
        >
          <div class="flex h-full flex-col items-center justify-center">
            <Button
              :class="{
                'opacity-20': !isSelected(getToothNumber(quadrant, position)) && props.disabled,
              }"
              :label="getToothNumber(quadrant, position).toString()"
              :severity="isSelected(getToothNumber(quadrant, position)) ? 'warn' : 'secondary'"
              class="aspect-square rounded-full p-0 md:w-11/12 md:text-xs xl:w-9/12 xl:text-base"
              rounded
              @click.stop="toggleTooth(getToothNumber(quadrant, position))"
            />
          </div>
          <div
            v-if="position < toothNumberPerArch"
            class="absolute right-0 top-0 z-10 w-2/5 translate-x-1/2 transform"
          >
            <Lucide
              :class="
                isSelected(
                  `${getToothNumber(quadrant, position)}-${getToothNumber(quadrant, position + 1)}`,
                )
                  ? 'text-orange-400'
                  : 'text-slate-200 hover:text-slate-400'
              "
              class="mx-auto h-full w-full scale-y-[-1] cursor-pointer"
              icon="Triangle"
              @click.stop="
                toggleTooth(
                  `${getToothNumber(quadrant, position)}-${getToothNumber(quadrant, position + 1)}`,
                )
              "
            />
          </div>
          <div
            v-if="getToothNumber(quadrant, position) == 41 + triTueCuaLamDaiCa * 10"
            class="absolute -right-1 top-0 z-10 w-2/5 translate-x-1/2 transform"
          >
            <Lucide
              :class="
                isSelected(`${31 + triTueCuaLamDaiCa * 10}-${41 + triTueCuaLamDaiCa * 10}`)
                  ? 'text-orange-400'
                  : 'text-slate-200 hover:text-slate-400'
              "
              class="mx-auto h-full w-full scale-y-[-1] cursor-pointer"
              icon="Triangle"
              @click.stop="
                toggleTooth(`${31 + triTueCuaLamDaiCa * 10}-${41 + triTueCuaLamDaiCa * 10}`)
              "
            />
          </div>
        </div>
        <div
          v-if="quadrant === 4 + triTueCuaLamDaiCa"
          class="absolute bottom-0 right-0 top-[2rem] w-[2px] translate-x-[1px] transform bg-gray-200"
        ></div>
      </div>
    </div>
  </div>
</template>
