<script setup lang="ts">
import { computed } from "vue";
import { CommonStatus } from "@/api/bcare-enum";

const props = defineProps<{
  productTitle: string;
  operation?: string;
  status?: number;
}>();

// Determine if the attachment is active
const isActive = computed(() => props.status === CommonStatus.ACTIVE);

// Determine if we have an operation to display
const hasOperation = computed(() => !!props.operation && props.operation.trim() !== "");
</script>

<template>
  <div class="attachment-tree-lite w-full">
    <!-- Parent Attachment -->
    <div
      class="relative z-10 flex items-center rounded px-2 py-1.5"
      :class="{
        'border border-amber-100 bg-amber-50 text-warning': !isActive,
        'border border-green-100 bg-green-50 text-success': isActive,
      }"
    >
      <div class="flex w-full items-center">
        <div class="flex-grow">
          <span class="text-xs font-medium">{{ productTitle }}</span>
        </div>
      </div>
    </div>

    <!-- Child Operation (if exists) -->
    <div v-if="hasOperation" class="border border-transparent">
      <div class="relative z-10 flex items-center pt-1 pl-4 pr-1 text-slate-500">
        <span
          class="absolute -top-[1px] left-1.5 h-[12px] w-1.5 rounded-bl border-b border-l border-slate-200"
        ></span>
        <span class="flex-grow text-xs">
          {{ operation }}
        </span>
      </div>
    </div>
  </div>
</template>
