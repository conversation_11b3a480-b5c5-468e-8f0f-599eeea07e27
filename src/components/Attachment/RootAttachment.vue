<script setup lang="ts">
import {ref} from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import type { AttachmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Dot from "@/base-components/Dot.vue";
import ProductName from "@/base-components/ProductName.vue";
import useAttachment from "@/hooks/useAttachment";
import useMetadataParser from "@/hooks/useAttachmentDataParser";

const props = defineProps<{
  attachment: AttachmentResponse;
}>();

const { listAttachments } = useAttachment();
const childAttachments = ref<AttachmentResponse[]>([]);
const {
  metaDataByAttachment,
  processAttachments,
} = useMetadataParser();

const isExpanded = ref(false);

const toggleChildAttachments = async () => {
  isExpanded.value = !isExpanded.value;
  if (isExpanded.value && childAttachments.value.length === 0) {
    await loadChildAttachments();
  }
}

const loadChildAttachments = async () => {
  const response = await listAttachments({
    filter: {
      parent_id: props.attachment.id,
    },
    order_by: 'created_at DESC'
  });
  childAttachments.value = response?.attachments || [];
  processAttachments(childAttachments.value);

}

</script>

<template>
  <div>
    <div class="flex items-center">
      <div class="flex-grow rounded p-1.5 z-10" :class="{
        'bg-amber-50 text-warning border border-amber-200': attachment.status === AttachmentStatus.UNPAID,
        'bg-green-50 text-success border border-green-200': attachment.status === CommonStatus.ACTIVE,
      }">
        <AttachmentTitle :attachment="attachment" />
      </div>
      <div class="flex items-center ml-1">
        <i class="pi cursor-pointer border p-1 ml-1 rounded text-slate-400 hover:shadow text-xs"
           :class="isExpanded ? 'pi-chevron-up' : 'pi-chevron-down'"
           @click="toggleChildAttachments"/>
      </div>
    </div>
    <div v-if="isExpanded" class="mt-1 border border-transparent">
      <template v-if="childAttachments.length > 0">
        <div
          v-for="child in childAttachments"
          :key="child.id"
          class="relative pl-4 text-muted"
        >
          <span class="absolute -top-3 left-1 h-6 w-2 rounded-bl border-b border-l border-slate-300" />

          <template v-if="metaDataByAttachment[child.id].operation">
            <span v-for="(operationName, operationId) in metaDataByAttachment[child.id].operation || []" :key="operationId">
              {{ operationName }}
            </span>
          </template>
          <template v-else>
            <AttachmentTitle :attachment="child" />
          </template>
          <Dot class="px-2" /> <DateTime :time="child.created_at" />
        </div>
      </template>
    </div>
  </div>
</template>
