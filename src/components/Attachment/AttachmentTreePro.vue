<script setup lang="ts">
import Chip from "primevue/chip";
import { computed, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import type { AttachmentListRequest, AttachmentResponse, Product } from "@/api/bcare-types-v2";
import { ProductFilters } from "@/api/product";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Counter from "@/base-components/Counter.vue";
import Money from "@/base-components/Money.vue";
import ProductPrice from "@/base-components/ProductPrice.vue";
import AttachmentDiscountInput from "@/components/Attachment/AttachmentDiscountInput.vue";
import OperationSelect from "@/components/Operation/OperationSelect.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import useAttachment from "@/hooks/useAttachment";

// Define props with defaults
const props = withDefaults(
  defineProps<{
    attachment: AttachmentResponse;
    initialChildAttachments?: AttachmentResponse[];
    selectedOperations?: { id: number | string; name: string }[];
    mode?: "operation" | "product" | "mixed" | "display";
    searchProductFilter?: ProductFilters;
    showDiscountInput?: boolean;
  }>(),
  {
    initialChildAttachments: () => [],
    selectedOperations: () => [],
    mode: "product",
    searchProductFilter: () => ({ type: "service", collection: "primary" }),
    showDiscountInput: false,
  },
);

// Define emits
const emit = defineEmits([
  "onAttachmentClick",
  "removeAttachment",
  "removeOperation",
  "productSelected",
  "update:selectedOperations",
  "updateChildAttachments",
  "update-quantity",
  "update:selectedProducts",
  "update:discount",
]);

// Refs
const operationPop = ref();
const productPopRef = ref();
const childAttachments = ref<AttachmentResponse[]>(props.initialChildAttachments);
const selectedProducts = ref<Product[]>(props.initialChildAttachments.map((att) => att.product));
const initialAttachmentIds = ref<number[]>(props.initialChildAttachments.map((att) => att.id));

// Hooks
const { addAttachment, deleteAttachment, listAttachments } = useAttachment();
const { attachments: childAttachmentFetched, listAttachments: listChildAttachments } =
  useAttachment();

// Computed properties
const showOperations = computed(() => props.mode === "operation" || props.mode === "mixed");
const showProducts = computed(() => props.mode === "product" || props.mode === "mixed");

// Handlers for popup operations
const toggleOperationPop = (event: MouseEvent) => {
  operationPop.value?.popoverRef.toggle(event);
};

const toggleProductPop = (event: MouseEvent) => {
  productPopRef.value?.popoverRef.toggle(event);
};

// Handle remove operation
const handleRemoveOperation = (operationId: number | string) => {
  emit("removeOperation", props.attachment.id, operationId);
};

// Handle attachment click
const handleAttachmentClick = (event: MouseEvent) => {
  emit("onAttachmentClick", event, props.attachment.id, props.attachment.product_id);
};

// Handle remove attachment
const handleRemoveAttachment = () => {
  emit("removeAttachment", props.attachment.id);
};

// Update selected operations
const updateSelectedOperations = (newOperations: { id: number | string; name: string }[]) => {
  emit("update:selectedOperations", newOperations);
};

// Handle product selection
const handleProductSelection = async (newSelectedProducts: Product[]) => {
  selectedProducts.value = newSelectedProducts;

  // Emit product selected event
  emit("productSelected", newSelectedProducts);

  // Create new child attachments for newly selected products
  for (const product of newSelectedProducts) {
    if (!childAttachments.value.some((attachment) => attachment.product_id === product.id)) {
      const newAttachment = {
        person_id: props.attachment.person_id,
        kind: "product",
        parent_id: props.attachment.id,
        product_id: product.id,
        deal_id: props.attachment.deal_id,
        status: AttachmentStatus.TEMP,
        quantity: 1,
      };

      const addedAttachment = await addAttachment(newAttachment);
      if (addedAttachment) {
        childAttachments.value.push(addedAttachment);
      }
    }
  }

  // Remove only non-initial attachments that are no longer selected
  const attachmentsToRemove = childAttachments.value.filter(
    (attachment) =>
      !newSelectedProducts.some((product) => product.id === attachment.product_id) &&
      !initialAttachmentIds.value.includes(attachment.id),
  );

  // Update childAttachments while preserving initial attachments
  childAttachments.value = childAttachments.value.filter(
    (attachment) =>
      newSelectedProducts.some((product) => product.id === attachment.product_id) ||
      initialAttachmentIds.value.includes(attachment.id),
  );

  const deletePromises = attachmentsToRemove.map((attachment) =>
    deleteAttachment({ id: attachment.id }).catch((error) => {
      console.error("Error deleting attachment:", error);
    }),
  );

  // Wait for all delete operations to complete
  await Promise.all(deletePromises);

  emit("updateChildAttachments", childAttachments.value);
};

// Fetch child attachments
const fetchChildAttachments = async () => {
  // Only fetch if initialChildAttachments is empty
  if (props.initialChildAttachments.length === 0) {
    try {
      const req: AttachmentListRequest = {
        filter: {
          person_id: props.attachment.person_id,
          parent_id: props.attachment.id,
          kind: "product",
          status: CommonStatus.ACTIVE,
        },
      };
      const response = await listChildAttachments(req);
      childAttachments.value = [...childAttachmentFetched.value];
      selectedProducts.value = childAttachments.value.map((attachment) => attachment.product);
      // Store initial attachment IDs
      initialAttachmentIds.value = childAttachments.value.map((att) => att.id);
    } catch (error) {
      console.error("Error fetching child attachments:", error);
    }
  }
};

// Handle quantity changes for attachment
const localQuantity = computed({
  get: () => props.attachment.quantity,
  set: (newQty: number) => {
    emit("update-quantity", props.attachment.id, newQty);
  },
});

const handleChildQuantityUpdate = async (child: AttachmentResponse, newQty: number) => {
  if (newQty <= 0) {
    try {
      // Call API to delete attachment
      await deleteAttachment({ id: child.id });
      // Remove attachment from the list
      childAttachments.value = childAttachments.value.filter((att) => att.id !== child.id);
      // Remove corresponding product from selectedProducts
      selectedProducts.value = selectedProducts.value.filter(
        (product) => product.id !== child.product_id,
      );
      // Emit events to update child attachments and selected products if needed
      emit("updateChildAttachments", childAttachments.value);
      emit("update:selectedProducts", selectedProducts.value);
    } catch (error) {
      console.error("Error deleting attachment:", error);
    }
  } else {
    // Update quantity as normal
    emit("update-quantity", child.id, newQty, props.attachment.id);
  }
};

// Watch for changes in attachment to fetch child attachments
watch(
  () => props.attachment,
  () => props.mode !== "display" && fetchChildAttachments(),
  { immediate: true, deep: true },
);

// Single watcher for both props
watch(
  () => props.initialChildAttachments,
  (newAttachments) => {
    childAttachments.value = newAttachments;
    selectedProducts.value = newAttachments.map((att) => att.product);
  },
  { immediate: true, deep: true },
);

// Separate discount handler
const handleDiscountUpdate = (attachmentId: number, amount: number, isChild: boolean = false) => {
  emit("update:discount", attachmentId, amount, isChild ? props.attachment.id : undefined);
};
</script>

<template>
  <div class="w-full">
    <!-- Attachment Header -->
    <div
      class="relative z-20 flex items-center rounded px-2 py-1.5"
      :class="{
        'border border-amber-200 bg-amber-50 text-warning':
          attachment.status !== CommonStatus.ACTIVE,
        'border border-green-200 bg-green-50 text-success':
          attachment.status === CommonStatus.ACTIVE,
      }"
    >
      <div
        @click="handleAttachmentClick"
        class="flex flex-grow cursor-pointer items-center"
        :class="{ 'pr-4': props.mode !== 'display' }"
      >
        <div class="flex-grow">
          <AttachmentTitle :attachment="attachment" />
        </div>
        <div class="flex items-center gap-1">
          <div class="w-24 text-right">
            <Money v-if="attachment.price" :amount="attachment.price" />
            <ProductPrice v-else :id="attachment.product_id" />
          </div>
          <div class="w-20">
            <template v-if="props.mode === 'display'">
              <span class="font-semibold">&times; {{ localQuantity }}</span>
            </template>
            <Counter
              v-else
              v-model="localQuantity"
              prefix="&times; "
              class="inline-block font-semibold"
              @click.stop
            />
          </div>
          <div v-if="showDiscountInput">
            <AttachmentDiscountInput
              :attachment="attachment"
              :discount="attachment.discount"
              @update:discount="(val) => handleDiscountUpdate(attachment.id, val)"
            />
          </div>
        </div>
      </div>
      <button
        v-if="props.mode !== 'display'"
        @click="handleRemoveAttachment"
        class="absolute right-1 top-1/2 -translate-y-1/2 transform text-red-500"
      >
        <i class="pi pi-times text-xs" />
      </button>
    </div>

    <!-- Child Attachments Section -->
    <div class="mt-1 border border-transparent">
      <div
        v-for="child in childAttachments"
        :key="child.id"
        @click="toggleProductPop"
        class="relative z-10 flex items-center pl-4 pr-2 text-muted"
        :class="{ 'pr-6': props.mode !== 'display', 'pr-2': props.mode === 'display' }"
      >
        <span
          class="absolute -top-3 left-1 h-6 w-2 rounded-bl border-b border-l border-slate-300"
        ></span>
        <span class="flex-grow">
          <AttachmentTitle :attachment="child" />
        </span>
        <div class="flex items-center gap-1">
          <div class="w-24 text-right">
            <Money v-if="child.price" :amount="child.price" />
            <ProductPrice v-else :id="child.product_id" />
          </div>
          <div class="w-20">
            <template v-if="props.mode === 'display'">
              <span class="font-semibold">&times; {{ child.quantity }}</span>
            </template>
            <Counter
              v-else
              v-model="child.quantity"
              prefix="&times; "
              class="inline-block font-semibold"
              @click.stop
              @update:modelValue="(val) => handleChildQuantityUpdate(child, val)"
            />
          </div>
          <div v-if="showDiscountInput">
            <AttachmentDiscountInput
              :attachment="child"
              :discount="child.discount"
              @update:discount="(val) => handleDiscountUpdate(child.id, val, true)"
            />
          </div>
        </div>
      </div>
      <div class="relative pl-4">
        <template v-if="showOperations">
          <span
            class="absolute -top-3 left-1 h-6 w-2 rounded-bl border-b border-l border-slate-300"
          ></span>
          <div v-if="selectedOperations.length" class="flex flex-wrap items-center gap-1">
            <Chip
              v-for="operation in selectedOperations"
              :key="operation.id"
              :label="operation.name"
              :removable="props.mode !== 'display'"
              @click="toggleOperationPop"
              :class="{ 'cursor-pointer': props.mode !== 'display' }"
              @remove.stop="handleRemoveOperation(operation.id)"
            />
          </div>
          <span
            v-else-if="props.mode !== 'display'"
            class="cursor-pointer underline decoration-dotted"
            @click="toggleOperationPop"
          >
            Chọn nội dung điều trị
          </span>
        </template>

        <!-- Products Section -->
        <div v-if="showProducts && props.mode !== 'display'">
          <span
            class="absolute -top-3 left-1 h-6 w-2 rounded-bl border-b border-l border-slate-300"
          ></span>
          <span class="cursor-pointer underline decoration-dotted" @click="toggleProductPop">
            Chọn sản phẩm phụ
          </span>
        </div>
      </div>
    </div>

    <!-- Operation Popup Component -->
    <OperationSelect
      v-if="showOperations && props.mode !== 'display'"
      :product-ids="[props.attachment.product_id]"
      :modelValue="selectedOperations"
      @update:modelValue="updateSelectedOperations"
      ref="operationPop"
    />

    <!-- Product Popup Component -->
    <SearchProduct
      v-if="showProducts && props.mode !== 'display'"
      ref="productPopRef"
      :multiple="true"
      :defaultFilters="props.searchProductFilter"
      :selectedProducts="selectedProducts"
      @update:selectedProducts="handleProductSelection"
    />
  </div>
</template>
