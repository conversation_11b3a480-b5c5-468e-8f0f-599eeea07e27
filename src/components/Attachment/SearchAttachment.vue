<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @show="handleFetchData()">
    <div class="w-[30rem]">
      <div v-if="filteredAttachments.length > 0" class="p-2">
        <ul class="m-0 h-[25vh] space-y-1 p-1 snap-y list-none overflow-hidden overflow-y-auto overscroll-contain scroll-py-1">
          <li
            v-for="attachment in filteredAttachments"
            :key="attachment.id"
            class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
            :class="{
              'ring-1 ring-highlight bg-soft text-primary':
                isAttachmentSelected(attachment),
            }"
            @click="handleItemClick(attachment)"
          >
            <div class="flex-1">
              <span class="hyphens-auto font-medium">
                <HighlightText v-if="attachment.product_id" :highlight="searchQuery" :text="attachment.product.name" />
                <HighlightText v-else :highlight="searchQuery" :text="attachment.title" />
              </span>
              <div class="text-sm">
                <DateTime :time="attachment.created_at" class="text-slate-500"/>
                <i class="pi pi-circle-fill text-[3px] px-2 align-middle text-slate-400"/>
                <span v-if="attachment.plan_id" class="text-success">Gói trả góp</span>
                <Money v-else :amount="attachment.payment" />
              </div>
            </div>
            <div class="ml-3 text-gray-600 dark:text-gray-400">
              <div
                v-if="isAttachmentSelected(attachment)"
                class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                :class="{ 'bg-primary': isAttachmentSelected(attachment) }"
              ></div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else class="p-2">
        <Empty />
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <IconField class="mb-2">
          <InputText
            autofocus="true"
            v-model="searchQuery"
            class="w-full text-sm"
            placeholder="Tìm kiếm"
            type="text"
          />
          <InputIcon :class="searchQuery ? 'pi-times' : 'pi-search'" class="pi cursor-pointer" @click="() => {searchQuery = ''}"/>
        </IconField>

        <!-- Filter Buttons -->
        <div class="flex flex-wrap gap-2 text-sm justify-end">
          <SelectButton
            :pt="{ root: { class: 'w-full grid grid-cols-3' } }"
            v-if="filterKind"
            v-model="selectedKind"
            :options="kindOptions"
            optionLabel="name"
            optionValue="value"
            aria-labelledby="kind-filter"
            @change="handleFetchData"
            class=""
          />
          <SelectButton
            :pt="{ root: { class: 'w-full grid grid-cols-3' } }"
            v-if="filterProductType"
            v-model="selectedProductType"
            :options="productTypeOptions"
            optionLabel="name"
            optionValue="value"
            aria-labelledby="product-type-filter"
            @change="handleFetchData"
            class=""
          />
          <SelectButton
            :pt="{ root: { class: 'w-full grid grid-cols-3' } }"
            v-if="filterProductCategory"
            v-model="selectedProductCategory"
            :options="productCategoryOptions"
            optionLabel="name"
            optionValue="value"
            aria-labelledby="product-category-filter"
            @change="handleFetchData"
            class=""
          />
        </div>
      </div>
    </div>
  </Popover>
</template>

<script lang="ts" setup>
import Popover from 'primevue/popover';
import { computed, onMounted, PropType,ref } from 'vue';

import {CommonStatus} from "@/api/bcare-enum";
import type {AttachmentDynamicQuery,AttachmentFilter, AttachmentListRequest, AttachmentResponse} from '@/api/bcare-types-v2';
import DateTime from "@/base-components/DateTime.vue";
import Empty from "@/base-components/Empty";
import HighlightText from '@/base-components/HighlightText.vue';
import Money from "@/base-components/Money.vue";
import useAttachment from '@/hooks/useAttachment';
import { normalizeVietnamese } from "@/utils/string";

const props = defineProps({
  personId: {
    type: Number,
    required: true,
  },
  selectedAttachment: {
  type: Object as PropType<AttachmentResponse | null>, // Allow null
    default: null,
  },
  selectedAttachments: {
    type: Array as PropType<AttachmentResponse[]>,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultFilters: {
    type: Object as () => Partial<AttachmentFilter & { product_type?: string; product_category?: number }>,
    default: () => ({}),
  },
  filterKind: {
    type: Boolean,
    default: false,
  },
  filterProductType: {
    type: Boolean,
    default: false,
  },
  filterProductCategory: {
    type: Boolean,
    default: false,
  },
  searchMode: {
    type: String as PropType<'alone_attachment' | 'paid_attachment' | 'all_attachment'>,
    default: 'paid_attachment',
    validator: (value: string) => {
      return ['alone_attachment', 'paid_attachment', 'all_attachment'].includes(value);
    }
  },
});

const emit = defineEmits<{
(e: 'update:selectedAttachment', value: AttachmentResponse): void;
(e: 'update:selectedAttachments', value: AttachmentResponse[]): void;
}>();

const { attachments, listAttachments, queryAttachments, isLoading } = useAttachment();

const searchQuery = ref('');

const isAttachmentSelected = (attachment: AttachmentResponse) => {
  if (props.multiple) {
    return props.selectedAttachments.some(selected => selected.id === attachment.id);
  } else {
    return props.selectedAttachment?.id === attachment.id;
  }
};

const selectedKind = ref(props.defaultFilters.kind || '');
const selectedProductType = ref(props.defaultFilters.product_type || '');
const selectedProductCategory = ref(props.defaultFilters.product_category || 0);

const kindOptions = [
  { name: 'Sản phẩm - Dịch vụ', value: 'product' },
  { name: 'Công việc', value: 'operation' },
];

const productTypeOptions = [
  { name: 'Dịch vụ', value: 'service' },
  { name: 'Sản phẩm', value: 'item' },
  { name: 'Quà tặng', value: 'gift' },
];

const productCategoryOptions = [
  { name: 'Category 1', value: 1 },
  { name: 'Category 2', value: 2 },
];

const filteredAttachments = computed(() => {
  const normalizedSearch = normalizeVietnamese(searchQuery.value.toLowerCase());
  return attachments.value.filter(attachment => {
    const normalizedName = attachment.product
      ? normalizeVietnamese(attachment.product.name.toLowerCase())
      : normalizeVietnamese(attachment.title.toLowerCase());
    return normalizedName.includes(normalizedSearch);
  });
});

const handleItemClick = (attachment: AttachmentResponse) => {
  if (props.multiple) {
    const newSelection = [...props.selectedAttachments];
    const index = newSelection.findIndex(a => a.id === attachment.id);
    if (index === -1) {
      newSelection.push(attachment);
    } else {
      newSelection.splice(index, 1);
    }
    emit('update:selectedAttachments', newSelection);
  } else {
    emit('update:selectedAttachment', attachment);
    popoverRef.value?.hide();
  }
};

const fetchAttachments = async () => {
  const request: AttachmentListRequest = {
    product_type: selectedProductType.value || undefined,
    product_category: selectedProductCategory.value || undefined,
    filter: {
      person_id: props.personId,
      kind: selectedKind.value || undefined,
      status: CommonStatus.ACTIVE,
    },
    order_by: 'created_at DESC',
    page_size: 100,
    page: 1,
  };

  await listAttachments(request);
};

const fetchAloneAttachments = async () => {
  const request: Partial<AttachmentDynamicQuery> = {
    table: "attachment",
    selects: [],
    filters: [
      {
        field: "deal_id",
        operator: "ISNULL",
      },
      {
        field: "parent_id",
        operator: "ISNULL",
      },
      {
        field: "kind",
        operator: "EQ",
        value: selectedKind.value,
      },
      {
        field: "person_id",
        operator: "EQ",
        value: props.personId.toString(),
      },
      {
        field: "status",
        operator: "EQ",
        value: CommonStatus.ACTIVE.toString(),
      },
    ],
    sort: [
      {
        field: "id",
        order: "DESC",
      },
    ],
    group_by: [],
    aggregations: [],
    limit: 100,
  };

  await queryAttachments(request);
};

const handleFetchData = async () => {
  switch (props.searchMode) {
    case 'alone_attachment':
      await fetchAloneAttachments();
      break;
    case 'paid_attachment':
      await fetchAttachments();
      break;
    case 'all_attachment':
      console.warn('All attachment mode is not implemented yet');
      break;
    default:
      await fetchAttachments();
  }
};

onMounted(() => {
  if (props.defaultFilters.kind) {
    selectedKind.value = props.defaultFilters.kind;
  }
  if (props.defaultFilters.product_type) {
    selectedProductType.value = props.defaultFilters.product_type;
  }
  if (props.defaultFilters.product_category) {
    selectedProductCategory.value = props.defaultFilters.product_category;
  }
});

const popoverRef = ref();

defineExpose({
  popoverRef,
  fetchAttachments,
  handleFetchData
});
</script>
