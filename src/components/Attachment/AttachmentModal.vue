<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";

import { ExtendedAttachmentAddRequest, ExtendedAttachmentResponse } from "@/api/extend-types";
import useAttachment from "@/hooks/useAttachment";
import useAttachmentMeta from "@/hooks/useAttachmentDataMeta";
import { simpleGlobalId } from "@/utils/string";

type ExtendedAttachment =
  | ExtendedAttachmentAddRequest
  | (ExtendedAttachmentResponse & { temp_id?: string; temp_parent_id?: string });

const props = defineProps<{
  personId: number;
  initialAttachments?: ExtendedAttachmentResponse[];
}>();

const emits = defineEmits<{
  (event: "close"): void;
  (event: "attachmentsUpdated", attachmentsData: ExtendedAttachmentResponse[]): void;
}>();

const title = ref("Chỉnh sửa Attachments");
const isOpen = ref(false);

const createDefaultAttachment = (): ExtendedAttachmentAddRequest => ({
  person_id: props.personId,
  kind: "proposal",
  title: "",
  quantity: 1,
  price: undefined,
  discount: undefined,
  parent_id: undefined,
  meta: {
    price: { min: undefined, max: undefined, discount: undefined },
  },
  temp_id: simpleGlobalId(),
});

const attachments = ref<ExtendedAttachment[]>([]);

const populateMetaFromExisting = (tempAttachments: ExtendedAttachmentAddRequest[]) => {
  // Tạo Set các title từ init data để biết attachment nào là mới
  const initTitles = new Set(initialAttachments.value?.map((att) => att.title?.toLowerCase()));

  return tempAttachments.map((tempAtt) => {
    // Tìm attachment có cùng title trong proposalAttachments để lấy metadata và id
    const existingAtt = proposalAttachments.value.find(
      (existing) => existing.title?.toLowerCase() === tempAtt.title?.toLowerCase(),
    );

    if (existingAtt && "id" in existingAtt) {
      const existingMeta = getMetaValues(existingAtt.id);

      // Luôn lấy cả id và metadata từ existing attachment
      return {
        ...tempAtt,
        id: existingAtt.id, // Luôn giữ id nếu có
        price: existingAtt.price,
        discount: existingAtt.discount,
        meta: {
          price: {
            min: existingMeta.price.min,
            max: existingMeta.price.max,
            discount: existingMeta.price.discount,
          },
        },
      };
    }
    return tempAtt;
  });
};

const { deleteAttachment } = useAttachment({ useStore: false });

// Thêm function để xóa attachment không cần thiết
const removeUnusedAttachments = async (initAttachments: ExtendedAttachmentAddRequest[]) => {
  const initTitles = new Set(initAttachments.map((att) => att.title?.toLowerCase()));

  // Lọc ra các attachment cần xóa (có trong proposal nhưng không có trong init)
  const attachmentsToDelete = proposalAttachments.value.filter(
    (att) => !initTitles.has(att.title?.toLowerCase()),
  );

  // Xóa từng attachment
  await Promise.all(
    attachmentsToDelete.map(async (att) => {
      if ("id" in att) {
        await deleteAttachment({ id: att.id });
      }
    }),
  );
};

const initialAttachments = ref<ExtendedAttachment[]>([]);

const open = async (initAtts?: ExtendedAttachment[]) => {
  isOpen.value = true;
  initialAttachments.value = initAtts || []; // Lưu lại init data

  // Load existing proposal attachments first
  await loadProposalAttachments();

  if (initAtts && initAtts.length > 0) {
    // Nếu là temp attachments (từ createInitialAttachments)
    if (!("id" in initAtts[0])) {
      // Xóa các attachment không cần thiết trước
      await removeUnusedAttachments(initAtts as ExtendedAttachmentAddRequest[]);

      // Populate metadata từ existing attachments vào init data
      attachments.value = populateMetaFromExisting(initAtts as ExtendedAttachmentAddRequest[]);
    } else {
      // Nếu là attachments có sẵn
      attachments.value = initAtts.map((att) => ({ ...att }));
    }
  } else {
    attachments.value = [createDefaultAttachment()];
  }
};

const close = () => {
  isOpen.value = false;
  emits("close");
};

const { addAttachment, updateAttachment, isLoading } = useAttachment({ useStore: false });

const { getMetaValues, syncMetaData, initializeAttachment, loadInitialMetaData } =
  useAttachmentMeta({
    price: { min: 0, max: 0, discount: 0 },
  });

const PRICE_RANGE_OPTIONS = [
  { min: 300000, max: 900000 },
  { min: 400000, max: 1000000 },
  { min: 800000, max: 1500000 },
  { min: 1500000, max: 3500000 },
  { min: 3000000, max: 5000000 },
  { min: 3000000, max: 7000000 },
];

const priceRangeSelectOptions = computed(() =>
  PRICE_RANGE_OPTIONS.map((range) => ({
    value: range,
    label: `${range.min.toLocaleString()} - ${range.max.toLocaleString()}`,
  })),
);

const updatePriceRange = (index: number, range: { min: number; max: number }) => {
  const attachment = attachments.value[index];
  if (!attachment.meta) {
    attachment.meta = { price: { min: 0, max: 0, discount: undefined } };
  }
  attachment.meta.price.min = range.min;
  attachment.meta.price.max = range.max;

  if ("id" in attachment) {
    initializeAttachment(attachment.id);
    getMetaValues(attachment.id).price = attachment.meta.price;
    syncMetaData(attachment.id, "price");
  }
};

const updateDiscount = (index: number, value: number) => {
  const attachment = attachments.value[index];
  if (!attachment.meta) {
    attachment.meta = { price: { min: undefined, max: undefined, discount: undefined } };
  }

  // Cập nhật cả discount thường và trong meta
  attachment.discount = value;
  attachment.meta.price.discount = value;

  if ("id" in attachment) {
    initializeAttachment(attachment.id);
    getMetaValues(attachment.id).price = attachment.meta.price;
    syncMetaData(attachment.id, "price");
  }
};

const submitForm = async () => {
  const results: ExtendedAttachmentResponse[] = [];
  const processedTempIds = new Set<string>();

  for (const attachment of attachments.value) {
    // Case UPDATE: If attachment has id
    if ("id" in attachment) {
      const { meta, ...attachmentData } = attachment;
      const updatedAttachment = await updateAttachment({
        ...attachmentData,
        kind: "proposal",
      });
      if (updatedAttachment) {
        results.push({ ...updatedAttachment, meta });
        syncMetaData(updatedAttachment.id, "price");
      }
      continue;
    }

    // Case ADD: For attachments without id
    if (processedTempIds.has(attachment.temp_id!)) continue;

    const { meta, temp_id, ...attachmentData } = attachment;
    const newAttachment = await addAttachment({
      ...attachmentData,
      kind: "proposal",
    });

    if (newAttachment) {
      results.push({ ...newAttachment, meta });
      if (meta?.price) {
        initializeAttachment(newAttachment.id);
        getMetaValues(newAttachment.id).price = meta.price;
        syncMetaData(newAttachment.id, "price");
      }
      processedTempIds.add(temp_id!);
    }
  }

  if (results.length > 0) {
    emits("attachmentsUpdated", results);
    handleClose();
  }
};

const handleClose = () => {
  close();
  attachments.value = [];
};

const addAttachmentRow = () => {
  // Temporarily disabled
  // const newAttachment = createDefaultAttachment();
  // attachments.value.push(newAttachment);
};

const removeAttachmentRow = (index: number) => {
  // Temporarily disabled
  // attachments.value.splice(index, 1);
};

const { listAttachments } = useAttachment({ useStore: false });
const proposalAttachments = ref<ExtendedAttachmentResponse[]>([]);

const loadProposalAttachments = async () => {
  const response = await listAttachments({
    filter: {
      person_id: props.personId,
    },
    attachment_kind: "proposal",
  });

  if (response?.attachments) {
    proposalAttachments.value = response.attachments;

    // Initialize và load metadata cho mỗi attachment
    await Promise.all(
      proposalAttachments.value.map(async (attachment) => {
        if ("id" in attachment) {
          initializeAttachment(attachment.id);
          await loadInitialMetaData(attachment.id);
        }
      }),
    );
  }
};

onMounted(async () => {
  if (props.initialAttachments) {
    await Promise.all(
      props.initialAttachments.map(async (attachment) => {
        if ("id" in attachment) {
          initializeAttachment(attachment.id);
          await loadInitialMetaData(attachment.id);
        }
      }),
    );
  }
});

watch(
  () => props.initialAttachments,
  async (newAttachments) => {
    if (newAttachments) {
      attachments.value = newAttachments.map((att) => ({ ...att }));
      await Promise.all(
        newAttachments.map(async (attachment) => {
          if ("id" in attachment) {
            initializeAttachment(attachment.id);
            await loadInitialMetaData(attachment.id);
          }
        }),
      );
    }
  },
);

// Expose for testing
defineExpose({
  open,
  close,
  loadProposalAttachments,
});
</script>

<template>
  <Dialog
    v-model:visible="isOpen"
    :pt="{ header: { class: 'pt-3 pr-3 pb-2 border-b' }, footer: { class: 'p-0' } }"
    :draggable="false"
    block-scroll
    dismissable-mask
    modal
    @hide="handleClose"
  >
    <template #header>
      <h3 class="text-lg font-medium">{{ title }}</h3>
    </template>
    <div class="mt-3">
      <div class="mb-1 flex space-x-2 text-sm font-medium text-muted">
        <div class="w-80">Tên</div>
        <div class="w-20">Số lượng</div>
        <div class="w-32">Đơn giá</div>
        <div class="w-80">Khoảng giá</div>
        <div class="w-28">Chiết khấu (%)</div>
      </div>
      <div
        v-for="(attachment, index) in attachments"
        :key="'id' in attachment ? attachment.id : attachment.temp_id"
        class="group relative mb-2 flex items-center space-x-2"
      >
        <div class="group relative flex w-80 items-center">
          <InputText v-model="attachment.title" fluid />
        </div>
        <div class="w-20">
          <div
            class="flex h-[42px] items-center justify-center rounded border bg-gray-50 text-sm text-gray-500"
          >
            {{ attachment.quantity }}
          </div>
        </div>
        <div class="w-32">
          <InputNumber
            v-model="attachment.price"
            :min="0"
            class="w-full text-sm"
            pt:pcInputText:root:class="w-full"
          />
        </div>
        <div class="w-80">
          <Select
            :model-value="
              'id' in attachment
                ? {
                    min: getMetaValues(attachment.id).price.min,
                    max: getMetaValues(attachment.id).price.max,
                  }
                : { min: attachment.meta?.price.min, max: attachment.meta?.price.max }
            "
            :options="priceRangeSelectOptions"
            option-label="label"
            :option-value="(option) => option.value"
            fluid
            class="h-[42px]"
            @change="(e) => updatePriceRange(index, e.value)"
          />
        </div>
        <div class="w-28">
          <InputNumber
            :model-value="attachment.discount"
            :min="0"
            :max="100"
            suffix=" %"
            class="w-full text-sm"
            pt:pcInputText:root:class="w-full"
            @update:model-value="(value) => updateDiscount(index, value)"
          />
        </div>
        <div class="flex items-center space-x-2">
          <!-- Temporarily disabled delete button -->
          <!-- <Button
             @click="removeAttachmentRow(index)"
             icon="pi pi-trash"
             severity="danger"
             size="small"
             outlined
             class="size-10 text-xs"
             aria-label="Remove attachment"
           /> -->
        </div>
      </div>
      <!-- Temporarily disabled add button -->
      <!-- <Button
        icon="pi pi-plus"
        @click="addAttachmentRow"
        severity="success"
        outlined
        class="mt-2 size-10 text-xs"
      /> -->
    </div>

    <template #footer>
      <div class="flex w-full items-center justify-end border-t p-4">
        <Button class="mr-3 w-20" severity="secondary" outlined @click="handleClose"> Hủy </Button>
        <Button class="w-20" severity="primary" :loading="isLoading" @click="submitForm">
          Lưu
        </Button>
      </div>
    </template>
  </Dialog>
</template>
