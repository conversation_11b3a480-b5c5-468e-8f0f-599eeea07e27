<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus } from "@/api/bcare-enum";
import type { AttachmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import HugeiconsDentalTooth from "@/base-components/Icons/ToothIcon.vue";

interface Props {
  parent: AttachmentResponse;
  operations: Record<string | number, string>;
  showOperations?: boolean;
  teethData?: Record<string, boolean>;
}

const props = defineProps<Props>();
const localExpanded = ref(props.showOperations ?? true);

// Update localExpanded when showOperations changes
watch(
  () => props.showOperations,
  (newValue) => {
    if (newValue !== undefined) {
      localExpanded.value = newValue;
    }
  },
);

const isExpanded = computed({
  get: () => localExpanded.value,
  set: (value) => {
    localExpanded.value = value;
  },
});

const teethList = computed(() => {
  if (!props.teethData) return [];
  return Object.entries(props.teethData)
    .filter(([_, value]) => value)
    .map(([index]) => index);
});
</script>

<template>
  <div>
    <div class="flex items-center">
      <div
        class="flex-grow rounded border p-1.5"
        :class="{
          'border-gray-200 bg-gray-50 text-gray-700': parent.status === CommonStatus.TEMP,
          'border-amber-200 bg-amber-50 text-warning': parent.status === AttachmentStatus.UNPAID,
          'border-green-200 bg-green-50 text-success': parent.status === CommonStatus.ACTIVE,
        }"
      >
        <AttachmentTitle :attachment="props.parent" />
      </div>

      <div v-if="operations && Object.keys(operations).length" class="ml-1 flex items-center">
        <i
          class="pi ml-1 cursor-pointer rounded border p-1 text-xs text-slate-400 hover:shadow"
          :class="isExpanded ? 'pi-chevron-up' : 'pi-chevron-down'"
          @click="isExpanded = !isExpanded"
        />
      </div>

      <span
        v-if="teethList.length"
        class="ml-1 inline-flex items-center gap-0.5 text-sm text-orange-500"
      >
        <HugeiconsDentalTooth class="h-4 w-4" /> {{ teethList.join(", ") }}
      </span>
    </div>

    <div
      v-if="isExpanded && operations && Object.keys(operations).length"
      class="mt-1 border border-transparent"
    >
      <div class="relative pl-4 text-muted">
        <span
          class="absolute -top-1 left-1 h-4 w-2 rounded-bl border-b border-l border-slate-300"
        ></span>
        <div v-for="(operation, id) in operations" :key="id" class="mb-1">
          {{ operation }}
        </div>
      </div>
    </div>
  </div>
</template>
