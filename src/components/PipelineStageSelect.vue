<template>
  <div class="flex items-center" @click.stop>
    <i :class="['pi pi-filter mr-1 text-info', iconClass]"></i>
    <TreeSelect
      v-model="internalSelectedValue"
      :options="treeNodes"
      :placeholder="placeholder"
      fluid
      :filter="true"
      :selectionMode="selectionMode"
      :display="selectionMode === 'multiple' ? 'chip' : undefined"
      :maxSelectedLabels="selectionMode === 'multiple' ? 1 : undefined"
      @update:model-value="handleSelectionChange"
      :disabled="!props.editable"
      :pt:root:class="['border-none p-0 shadow-none bg-transparent opacity-100']"
      :pt:label:class="[
        'border-none p-0 shadow-none break-words whitespace-normal opacity-100',
        props.editable
          ? 'underline decoration-dotted underline-offset-2'
          : 'decoration-none cursor-default opacity-100',
        labelClass,
      ]"
      pt:dropdown:class="hidden"
    >
    </TreeSelect>
  </div>
</template>

<script lang="ts" setup>
import type { TreeNode } from "primevue/treenode";
import { onMounted, ref, watch } from "vue";

import type { PipelineResponse, StageResponse } from "@/api/bcare-types-v2";
import usePipeline from "@/hooks/usePipeline";

type SelectionMode = "single" | "multiple" | "checkbox";

interface Props {
  pipelineId?: number;
  modelValue?: number | number[];
  placeholder?: string;
  iconClass?: string;
  labelClass?: string;
  selectionMode?: SelectionMode;
  editable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  pipelineId: undefined,
  modelValue: undefined,
  placeholder: "Sale pipeline",
  iconClass: "",
  labelClass: "",
  selectionMode: "single",
  editable: true,
});

const emit = defineEmits(["update:modelValue"]);

const { pipelines, getPipelineById, loadPipelines, getTopLevelStagesByPipelineID } = usePipeline({
  autoLoad: false,
});

const internalSelectedValue = ref<any>(null);
const treeNodes = ref<TreeNode[]>([]);

watch(
  () => props.modelValue,
  (newValue) => {
    if (props.selectionMode === "single") {
      // Handle single selection mode
      internalSelectedValue.value = newValue ? { [newValue as number]: true } : null;
    } else {
      // Handle multiple selection mode
      if (Array.isArray(newValue) && newValue.length > 0) {
        const selectionObject: Record<string, boolean> = {};
        newValue.forEach((id) => {
          selectionObject[id.toString()] = true;
        });
        internalSelectedValue.value = selectionObject;
      } else {
        internalSelectedValue.value = null;
      }
    }
  },
);

const handleSelectionChange = (value: any) => {
  if (props.selectionMode === "single") {
    // Handle single selection mode
    const selectedId = value ? parseInt(Object.keys(value)[0]) : undefined;
    emit("update:modelValue", selectedId);
  } else {
    // Handle multiple selection mode
    const selectedIds = value ? Object.keys(value).map((key) => parseInt(key)) : [];
    emit("update:modelValue", selectedIds);
  }
};

const createTreeNodes = () => {
  if (props.pipelineId) {
    const pipeline = getPipelineById(props.pipelineId);
    if (pipeline) {
      treeNodes.value = createStageNodes(getTopLevelStagesByPipelineID(props.pipelineId) ?? []);
    }
  } else {
    treeNodes.value = pipelines.value.map((pipeline: PipelineResponse) => ({
      key: `pipeline-${pipeline.id}`,
      label: pipeline.name,
      children: createStageNodes(getTopLevelStagesByPipelineID(pipeline.id) ?? []).map(
        (stageNode) => ({
          ...stageNode,
          label: `${pipeline.name} > ${stageNode.label}`,
          children: stageNode.children?.map((childNode) => ({
            ...childNode,
            label: childNode.label,
          })),
        }),
      ),
    }));
  }
};

const createStageNodes = (stages: StageResponse[]): TreeNode[] => {
  return stages.map((stage) => ({
    key: `${stage.id}`,
    label: stage.name,
    children: stage.children
      ? stage.children.map((childStage) => ({
          key: `${childStage.id}`,
          label: `${stage.name} > ${childStage.name}`,
        }))
      : undefined,
  }));
};

onMounted(async () => {
  await loadPipelines();
  createTreeNodes();

  // Initialize the internal value based on the selection mode
  if (props.selectionMode === "single") {
    if (props.modelValue) {
      internalSelectedValue.value = { [props.modelValue as number]: true };
    }
  } else {
    if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      const selectionObject: Record<string, boolean> = {};
      props.modelValue.forEach((id) => {
        selectionObject[id.toString()] = true;
      });
      internalSelectedValue.value = selectionObject;
    }
  }
});

watch(
  () => props.pipelineId,
  () => {
    createTreeNodes();
  },
);
</script>
