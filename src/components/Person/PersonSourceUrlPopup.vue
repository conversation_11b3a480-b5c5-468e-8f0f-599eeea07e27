<template>
  <div class="flex items-center">
    <a
      v-tooltip="'Xem thông tin Nguồn & URL'"
      class="inline-flex cursor-pointer items-center gap-2 text-gray-700 decoration-1 underline-offset-2 hover:underline hover:decoration-dotted"
      @click="toggle"
    >
      Xem
      <i class="pi pi-link text-sm" />
    </a>

    <Popover
      ref="popoverRef"
      :pt="{
        content: {
          class: 'surface-0 p-0',
        },
      }"
    >
      <div class="w-[500px] p-0">
        <div
          class="sticky top-0 flex items-center justify-between border-b border-slate-200 bg-slate-50 p-3"
        >
          <h3 class="text-sm font-medium">Nguồn & URL</h3>
        </div>

        <div class="p-3">
          <div v-if="sourceChannel" class="mb-2">
            <div class="text-sm font-medium text-slate-500">Nguồn:</div>
            <div class="text-sm">{{ sourceChannel }}</div>
          </div>

          <div v-if="url" class="mb-2">
            <div class="text-sm font-medium text-slate-500">URL:</div>
            <div class="break-all text-sm">
              <a
                :href="url"
                target="_blank"
                class="text-blue-500 hover:text-blue-700 hover:underline"
              >
                {{ url }}
              </a>
            </div>
          </div>

          <div v-if="!sourceChannel && !url" class="p-4 text-center text-slate-500">
            Không có dữ liệu
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import Popover from "primevue/popover";
import { ref, computed } from "vue";

const props = defineProps<{
  url?: string;
  sourceChannel?: string;
}>();

const popoverRef = ref();

const toggle = (event: Event) => {
  popoverRef.value.toggle(event);
};
</script>
