import { Issue, PersonMeta, ReferredData } from "@/api/bcare-types-v2";

export const hasComplainIssue = (issues: Issue[] | null) => {
  return Array.isArray(issues) && issues.some((issue) => issue.type === "complain");
};

export const personPayload = {
  id: 0,
  full_name: "",
  date_of_birth: "",
  gender: "female",
  province_id: 0,
  district_id: 0,
  ward_id: 0,
  address_number: "",
  phone: "",
  email: "",
  job_id: 0,
  source_id: 0,
  status: 2,
  is_system_created: false,
  person_field: {
    treatment_id: 0,
    treatment_status_id: 11,
    description: "",
    has_zalo: "unknown",
    secondary_phone: "",
    code: "",
    medical_condition: "",
    special_note: "",
    account_id: undefined,
  } as PersonMeta,
  referred_by: {
    referrer_id: 0,
    relationship: "",
    note: "",
  } as ReferredData,
  modified: [""],
};

export const optionsRelationShip = [
  { value: "ong_ba", label: "Ông / bà" },
  { value: "cha_me", label: "Cha / mẹ" },
  { value: "anh_chi_em", label: "Anh / chị / em" },
  { value: "nguoi_than", label: "<PERSON><PERSON>ời thân" },
  { value: "ban_be", label: "Bạn bè" },
  { value: "dong_nghiep", label: "Đồng nghiệp" },
];
