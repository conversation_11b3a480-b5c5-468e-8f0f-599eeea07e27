<template>
  <div>
    <div class="flex flex-wrap gap-2">
      <!-- Person Presets -->
      <template v-for="preset in personPresets" :key="preset.type">
        <Button
          :label="preset.name"
          :icon="getButtonProps(preset).icon"
          :severity="getButtonProps(preset).severity"
          :outlined="getButtonProps(preset).outlined"
          @click="() => handleSelectPreset(preset.type)"
          :class="{
            'border border-gray-300 text-sm': true,
            'border-0': selectedPreset === preset.type,
          }"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import { computed } from "vue";

import {
  PERSON_FILTER_PRESETS,
  PersonFilterPreset,
  usePersonDatatableStore,
} from "@/stores/person-datatable-store";

interface Props {
  modelValue?: PersonFilterPreset;
}
const props = withDefaults(defineProps<Props>(), {
  modelValue: PERSON_FILTER_PRESETS.ALL,
});

const emit = defineEmits<{
  "update:modelValue": [value: PersonFilterPreset];
}>();

const personStore = usePersonDatatableStore();

// Preset Definitions
interface PresetDefinition {
  type: PersonFilterPreset;
  name: string;
  icon: string;
  severity?: "success" | "info" | "danger" | "primary" | "secondary" | "warning" | "contrast";
}

const personPresets: PresetDefinition[] = [
  { type: PERSON_FILTER_PRESETS.ALL, name: "Tất cả", icon: "pi pi-list", severity: "primary" },
  {
    type: PERSON_FILTER_PRESETS.NEW,
    name: "Khách mới",
    icon: "pi pi-user-plus",
    severity: "success",
  },
  {
    type: PERSON_FILTER_PRESETS.RETURNING,
    name: "Khách quay lại",
    icon: "pi pi-refresh",
    severity: "info",
  },
  {
    type: PERSON_FILTER_PRESETS.REFUNDED,
    name: "Hoàn phí",
    icon: "pi pi-undo",
    severity: "danger",
  },
];

const selectedPreset = computed<PersonFilterPreset>({
  get: () => props.modelValue,
  set: (value: PersonFilterPreset) => emit("update:modelValue", value),
});

const getButtonProps = (preset: PresetDefinition) => {
  const isSelected = selectedPreset.value === preset.type;
  if (isSelected) {
    return {
      severity: preset.severity || "primary",
      outlined: false,
      icon: preset.icon,
    };
  } else {
    return {
      severity: "secondary",
      outlined: true,
      icon: preset.icon,
    };
  }
};

const handleSelectPreset = (presetType: PersonFilterPreset): void => {
  if (selectedPreset.value !== presetType) {
    selectedPreset.value = presetType;
    personStore.applyFilterPreset(presetType);
  }
};
</script>
