<template>
  <div class="flex items-center">
    <a
      v-tooltip="'Xem danh sách submissions'"
      class="inline-flex cursor-pointer items-center gap-2 text-gray-700 decoration-1 underline-offset-2 hover:underline hover:decoration-dotted"
      @click="toggle"
    >
      Xem
      <i class="pi pi-list text-sm" />
    </a>

    <Popover
      ref="popoverRef"
      :pt="{
        content: {
          class: 'surface-0 p-0',
        },
      }"
    >
      <div class="max-h-[400px] w-[500px] overflow-auto">
        <div
          class="sticky top-0 flex items-center justify-between border-b border-slate-200 bg-slate-50 p-3"
        >
          <h3 class="text-sm font-medium">Form Submissions</h3>
          <span class="text-xs text-slate-500">{{ submissions?.length || 0 }} submissions</span>
        </div>
        <div class="divide-y divide-slate-200">
          <div
            v-for="submission in submissions"
            :key="submission.id"
            class="p-3 transition-colors hover:bg-slate-50"
          >
            <div class="mb-1 flex justify-between">
              <span class="text-sm font-medium">{{ submission.full_name }}</span>
              <DateTime :time="submission.created_at" show-time />
            </div>
            <div class="mb-1 text-xs text-slate-600">{{ submission.email }}</div>
            <div class="mt-2 flex flex-wrap gap-2">
              <Badge
                v-tooltip="{
                  value: submission.source_url,
                  pt: { text: { class: 'max-w-[300px] break-words' } },
                }"
                value="Source URL"
                severity="info"
                class="cursor-pointer"
              />

              <Badge
                v-tooltip="{
                  value: submission.referrer_url,
                  pt: { text: { class: 'max-w-[300px] break-words' } },
                }"
                value="Referrer URL"
                severity="secondary"
                class="cursor-pointer"
              />

              <Badge :value="submission.form_name" severity="success" />
            </div>
          </div>
        </div>
        <div v-if="!submissions?.length" class="p-4 text-center text-slate-500">
          Không có dữ liệu
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import Badge from "primevue/badge";
import Popover from "primevue/popover";
import { ref } from "vue";

import { FormSubmission } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";

const props = defineProps<{
  submissions?: FormSubmission[];
}>();

const popoverRef = ref();

const toggle = (event: Event) => {
  popoverRef.value.toggle(event);
};
</script>
