<script setup lang="ts">
import { watchEffect, computed, ref } from "vue";
import Timeline from "primevue/timeline";
import Tabs from "primevue/tabs";
import TabList from "primevue/tablist";
import Tab from "primevue/tab";
import { groupBy } from "lodash";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import utc from "dayjs/plugin/utc";
import "dayjs/locale/vi";

dayjs.extend(isBetween);
dayjs.extend(utc);

import type { TrackSortResponse } from "@/api/bcare-types-v2";
import useTrack from "@/hooks/useTrack";

import DateTime from "@/base-components/DateTime.vue";
import TrackHistoryCard from "@/components/Track/TrackHistoryCard.vue";
import Empty from "@/base-components/Empty";

interface Props {
  personId?: number;
  initialLayout?: "vertical" | "horizontal";
}

interface DateGroup {
  id: string;
  label: string;
  tracks: TrackSortResponse[];
  year: number;
}

const props = withDefaults(defineProps<Props>(), {
  initialLayout: "horizontal",
});

const { listTracks, tracks, isLoading, error } = useTrack();
const currentLayout = ref<"vertical" | "horizontal">(props.initialLayout);
const selectedGroupIndex = ref<number>(0);

watchEffect(async () => {
  if (props.personId) {
    await listTracks({
      filter: { person_id: props.personId },
      include_relation: true,
      order_by: "begin DESC",
    });
    selectedGroupIndex.value = 0;
  } else {
    tracks.value = [];
  }
});

const dateGroups = computed<DateGroup[]>(() => {
  if (!tracks.value?.length) return [];

  // Nhóm tracks theo năm
  const tracksByYear = groupBy(tracks.value, (track) => dayjs(track.begin).year());

  // Tạo nhóm cho mỗi năm, sắp xếp năm từ mới đến cũ
  const years = Object.keys(tracksByYear)
    .map(Number)
    .sort((a, b) => b - a);

  return years.map((year) => ({
    id: `year-${year}`,
    label: `${year}`,
    tracks: tracksByYear[year],
    year,
  }));
});

const filteredTracks = computed(() => {
  if (!dateGroups.value[selectedGroupIndex.value]) return [];
  return dateGroups.value[selectedGroupIndex.value].tracks;
});

const horizontalTimelinePT = computed(() => ({
  root: "flex grow overflow-x-auto py-4",
  event: "flex flex-col relative min-w-[20rem]",
  eventOpposite: "hidden",
  eventSeparator: [
    "relative",
    "h-auto",
    "pt-4",
    "pb-4",
    'before:content-[""]',
    "before:absolute",
    "before:top-1/2",
    "before:left-0",
    "before:w-full",
    "before:h-0.5",
    "before:bg-gradient-to-r before:from-primary-100 before:via-primary-300 before:to-primary-100",
    "before:-translate-y-1/2",
    "before:z-0",
  ].join(" "),
  eventMarker: "relative z-10 w-full",
  eventConnector:
    "!absolute !top-1/2 !-translate-y-1/2 !left-0 !w-full bg-gradient-to-r from-primary-100 via-primary-300 to-primary-100 !h-0.5",
  eventContent: "px-2 pt-0 pb-4 flex-1",
}));
</script>

<template>
  <div class="person-track-history-prime mt-4">
    <div v-if="isLoading" class="p-4 text-center"><Empty /></div>
    <div v-else-if="error" class="p-4 text-center text-red-500">Lỗi: {{ error.message }}</div>
    <div v-else-if="!tracks.length" class="p-4 text-center text-gray-500"><Empty /></div>
    <div v-else>
      <Tabs
        v-model:value="selectedGroupIndex"
        scrollable
        unstyled
        :pt="{
          tablist: 'no-scrollbar flex w-full overflow-x-auto border-b border-gray-100 mb-4',
        }"
      >
        <TabList>
          <Tab
            v-for="(group, index) in dateGroups"
            :key="group.id"
            :value="index"
            unstyled
            :pt="{
              root: ({ context }) => ({
                class: [
                  'mr-2 whitespace-nowrap rounded-t-md px-4 py-2 text-sm font-medium transition-colors cursor-pointer',
                  context.active ? 'bg-primary/10 text-primary' : 'hover:bg-gray-100',
                ],
              }),
            }"
          >
            <div class="flex items-center gap-1.5">
              <i class="pi pi-calendar text-blue-500"></i>
              <span>{{ group.label }}</span>
            </div>
          </Tab>
        </TabList>
      </Tabs>

      <Timeline
        :value="filteredTracks"
        :layout="currentLayout"
        :pt="horizontalTimelinePT"
        :align="currentLayout === 'vertical' ? 'alternate' : 'top'"
      >
        <template #marker="slotProps">
          <div class="relative z-10 flex h-5 w-full items-center justify-center">
            <div
              class="absolute bottom-full left-1/2 z-10 mb-0.5 -translate-x-1/2 whitespace-nowrap text-center text-[10px] leading-tight text-gray-500"
            >
              <DateTime :time="slotProps.item.begin" format="DD/MM/YY" size="sm" />
            </div>
            <span
              class="relative z-20 flex size-4 items-center justify-center rounded-full border-2 border-primary-400 bg-white"
            />
          </div>
        </template>

        <template #content="slotProps">
          <TrackHistoryCard :track="slotProps.item" />
        </template>
      </Timeline>
    </div>
  </div>
</template>
