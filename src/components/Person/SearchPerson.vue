<template>
  <div class="relative mt-0">
    <FormInput
      id="input-search"
      ref="inputRef"
      v-model="personPayload.search"
      class="w-full"
      type="text"
      autocomplete="off"
      placeholder="T<PERSON><PERSON> kiếm khách hàng"
      @update:model-value="handleSearchPerson"
      @focus="handleFocus"
      @keydown="handleKeydown"
    />
    <Lucide
      :icon="personPayload.search ? 'X' : 'Search'"
      class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4 cursor-pointer text-slate-500"
      @click="personPayload.search ? clearSearch() : handleFocus()"
    />

    <!-- Dropdown Menu -->
    <div v-if="showDropdown" class="absolute left-0 top-[100%] z-50 mt-1 w-full">
      <div
        class="rounded-md border-transparent bg-white shadow-[0px_3px_10px_#00000017] dark:border-transparent dark:bg-darkmode-600"
      >
        <div v-if="isLoading" class="p-2">
          <!-- Skeleton loading -->
          <div v-for="i in 3" :key="i" class="flex items-center gap-3 p-2">
            <div class="h-8 w-8 animate-pulse rounded-full bg-gray-200"></div>
            <div class="flex-1">
              <div class="mb-2 h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
              <div class="h-3 w-1/2 animate-pulse rounded bg-gray-200"></div>
            </div>
          </div>
        </div>
        <div v-else-if="personList.length > 0" class="pb-1">
          <div class="block px-4 py-3 font-medium">Kết quả tìm kiếm</div>
          <ul
            class="m-0 max-h-[50vh] snap-y list-none overflow-hidden overflow-y-auto overscroll-contain px-2"
          >
            <li
              v-for="person in personList"
              :key="person.id"
              class="group flex cursor-pointer snap-start items-center rounded-md p-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="handlePersonClick(person)"
            >
              <PersonAvatar :person="person" :issues="person.issues" />
              <div class="ml-2 flex-1">
                <span class="hyphens-auto font-medium"
                  ><HighlightText :text="person.full_name" :highlight="personPayload.search"
                /></span>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  <HighlightText :text="person.phone" :highlight="personPayload.search" />
                </div>
              </div>
              <div class="invisible text-gray-600 group-hover:visible dark:text-gray-400">
                <Button
                  variant="soft-secondary"
                  rounded
                  size="sm"
                  class="p-2"
                  @click.stop="handleButtonClick(person)"
                >
                  <Lucide icon="CornerRightDown" class="h-4 w-4 hover:scale-110" />
                </Button>
              </div>
            </li>
          </ul>
        </div>
        <div v-else-if="personPayload.search && !personList.length" class="p-2">
          <div class="block px-4 py-3">Không tìm thấy khách hàng</div>
          <div class="block px-4 pb-3">
            Nhấn
            <Tag value="Enter" />
            để tạo mới
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import _ from "lodash";
import { onMounted, onUnmounted, reactive, ref, watch } from "vue";

import Button from "@/base-components/Button";
import { FormInput } from "@/base-components/Form";
import HighlightText from "@/base-components/HighlightText.vue";
import Lucide from "@/base-components/Lucide";
import PersonAvatar from "@/components/Person/PersonAvatar.vue";
import usePerson from "@/hooks/usePerson";
import { useRouter } from "vue-router";
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";

const props = defineProps({
  autoClear: {
    type: Boolean,
    default: true,
  },
});

const router = useRouter();
const breakpoints = useBreakpoints(breakpointsTailwind);
const isMobile = breakpoints.smaller("lg");

const personPayload = reactive({
  page_size: 0,
  page: 0,
  filter: {
    gender: "",
    phone: "",
    email: "",
    job_id: 0,
    source_id: 0,
    status: 2,
  },
  doctor_id: 0,
  treatment_id: 0,
  treatment_status_id: 0,
  search: "",
  order_by: "",
});

const { listPersons, persons: personList } = usePerson();

const isLoading = ref(false);
const showDropdown = ref(false);
const hasSearched = ref(false);

const handleSearchPerson = (val: string) => {
  isLoading.value = true;
  showDropdown.value = true;
  personPayload.search = val;
  personPayload.page = 1;
  personPayload.page_size = 20;
  if (val.length >= 2) {
    debouncedSearch();
  } else {
    showDropdown.value = false;
    hasSearched.value = false;
  }
};

const debouncedSearch = _.debounce(async () => {
  try {
    await listPersons({ ...personPayload });
    hasSearched.value = true;
  } finally {
    isLoading.value = false;
  }
}, 200);

const handleFocus = () => {
  if (personPayload.search.length >= 2 && hasSearched.value) {
    showDropdown.value = true;
  } else if (personPayload.search.length >= 2) {
    debouncedSearch();
  }
};

const clearSearch = () => {
  personPayload.search = "";
  showDropdown.value = false;
  hasSearched.value = false;
};

watch(
  () => personPayload.search,
  (newValue) => {
    if (newValue.length < 2) {
      showDropdown.value = false;
      hasSearched.value = false;
    }
  },
);

// Close dropdown when clicking outside
const closeDropdownOnOutsideClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (showDropdown.value && !target.closest("#input-search") && !target.closest(".absolute.z-50")) {
    showDropdown.value = false;
  }
};

// Khai báo các sự kiện
const emit = defineEmits(["person-selected", "action-clicked", "enter-pressed"]);
// Hàm xử lý khi click vào person item
const handlePersonClick = (person: any) => {
  if (isMobile.value) {
    router.push({
      path: `/customer/profile/${person.id}`,
    });
    return;
  }

  emit("person-selected", person);
};

// Cập nhật hàm handleButtonClick
const handleButtonClick = (person: any) => {
  emit("action-clicked", person);
  showDropdown.value = false;
  if (props.autoClear) {
    clearSearch();
  }
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && personPayload.search) {
    event.preventDefault();
    emit("enter-pressed", personPayload.search);
  }
};

// Add event listener when component is mounted
onMounted(() => {
  document.addEventListener("click", closeDropdownOnOutsideClick);
});

// Remove event listener when component is unmounted
onUnmounted(() => {
  document.removeEventListener("click", closeDropdownOnOutsideClick);
});
</script>
