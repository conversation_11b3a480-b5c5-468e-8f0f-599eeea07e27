<script lang="ts" setup>
import { cloneDeep, omit } from "lodash";
import { reactive, ref } from "vue";

import { PersonAddRequest, PersonResponse, PersonUpdateRequest } from "@/api/bcare-types-v2";
import Button from "@/base-components/Button";
import { vFocus } from "@/base-components/Focus";
import Lucide from "@/base-components/Lucide";
import { DateTimeInput } from "@/components/DateTimeInput";
import SearchPeople from "@/components/Person/SearchPeople.vue";
import { useDatePicker } from "@/composables/useDatePicker";
import { useModifiedFields } from "@/composables/useModifiedFields";
import useLocation from "@/hooks/useLocation";
import usePerson from "@/hooks/usePerson";
import useTerm from "@/hooks/useTerm";
import { optionsRelationShip, personPayload } from "@/components/Person/utils";
import { parseGoTime } from "@/utils/time-helper";

const { getBundleTerms } = useTerm({ autoLoad: true });
const { getWards, getDistricts, getProvinces } = useLocation({ autoLoad: true });
const { addPerson, getPerson, currentPerson, updatePerson } = usePerson();

const props = defineProps<{
  personId?: number;
  leftButton?: boolean;
  initialValues?: Partial<PersonAddRequest>;
}>();
const emits = defineEmits<{
  (event: "close"): void;
  (event: "personUpdated", personData: PersonResponse): void;
  (event: "personAdded", personData: PersonResponse, button: "left" | "right"): void;
}>();
const title = ref("Thêm mới khách hàng");

const isOpen = ref(false);

const open = () => {
  isOpen.value = true;
  formData.full_name = props.initialValues?.full_name || "";
  formData.phone = props.initialValues?.phone || "";
  reloadPerson();
};

const close = () => {
  isOpen.value = false;
  emits("close");
};
// Expose methods to parent component
defineExpose({ open, close });

const formData = reactive(cloneDeep(personPayload));
const { dateValue, handleDateUpdate } = useDatePicker(
  () => formData.date_of_birth,
  (value) => (formData.date_of_birth = value ?? ""),
);

const { modified, track, reset } = useModifiedFields(formData);

const handleClose = () => {
  close();
  Object.assign(formData, cloneDeep(personPayload));
  reset();
  if (props.initialValues) {
    props.initialValues.full_name = "";
    props.initialValues.phone = "";
  }
};

const reloadPerson = async () => {
  if (props.personId) {
    title.value = "Chỉnh sửa khách hàng";
    await getPerson({ id: props.personId, full_name: "", phone: "", email: "" });

    Object.assign(formData, currentPerson.value);

    formData.referred_by = currentPerson.value?.referred_by ?? {
      referrer_id: 0,
      note: undefined,
      relationship: undefined,
    };

    dateValue.value = parseGoTime(currentPerson.value?.date_of_birth ?? "");

    track();
  }
};

const createOrUpdatePerson = async (button: "left" | "right") => {
  let payload: PersonUpdateRequest | PersonAddRequest;

  if (formData.id !== 0) {
    // Cập nhật người
    payload = {
      ...formData,
      modified: modified.value,
    } as PersonUpdateRequest;

    if (!formData.referred_by?.referrer_id) {
      payload = omit(payload, "referred_by");
    }
    const res = await updatePerson(payload);
    if (!res) return;

    emits("personUpdated", res);
  } else {
    payload = { ...formData } as PersonAddRequest;

    if (!formData.referred_by?.referrer_id) {
      payload = omit(payload, "referred_by");
    }

    const res = await addPerson(payload);
    if (!res) return;

    emits("personAdded", res, button);
  }
  handleClose();
};

const handleFormatEmail = () => {
  if (formData.email && !formData.email.includes("@")) {
    formData.email = formData.email + "@gmail.com";
  }
};
</script>
<template>
  <Dialog
    v-model:visible="isOpen"
    :draggable="false"
    block-scroll
    dismissable-mask
    modal
    @hide="handleClose"
    :pt="{
      header: { class: 'font-medium p-1 pl-3 text-lg' },
      footer: { class: 'p-3 border-t border-slate-200 flex items-center justify-end' },
    }"
  >
    <div class="grid grid-cols-12 gap-x-4">
      <div class="col-span-12 flex flex-col space-y-2 sm:col-span-7">
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Tên khách hàng</span>
          <InputText
            v-model="formData.full_name"
            v-focus
            class="fluid font-medium"
            fluid
            type="text"
          />
        </div>

        <div class="flex gap-3">
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Số chính</span>
            <InputText v-model="formData.phone" class="fluid font-medium" type="text" />
          </div>
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Số phụ</span>
            <InputText v-model="formData.person_field.secondary_phone" class="w-full" type="text" />
          </div>
        </div>

        <div class="flex gap-3">
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Mã hồ sơ</span>
            <InputText v-model="formData.person_field.code" class="w-full" type="text" />
          </div>
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Giới tính</span>
            <Select
              v-model="formData.gender"
              :options="[
                { name: 'Nữ', value: 'female' },
                { name: 'Nam', value: 'male' },
              ]"
              class="w-full text-base"
              option-label="name"
              option-value="value"
              show-clear
            >
              <template #option="slotProps">
                {{ slotProps.option.name }}
              </template>
            </Select>
          </div>
        </div>

        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Email</span>
          <InputText
            v-model.trim="formData.email"
            class="w-full"
            type="email"
            @blur="handleFormatEmail"
          />
        </div>

        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Link chat</span>
          <InputText v-model.trim="formData.person_field.pancake_link" class="w-full" type="text" />
        </div>

        <div class="flex gap-3">
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Nghề nghiệp</span>
            <Select
              v-model="formData.job_id"
              :options="getBundleTerms('nghe_nghiep')"
              class="fluid text-base"
              option-label="name"
              option-value="id"
              show-clear
              filter
            >
              <template #option="slotProps">
                {{ slotProps.option.name }}
              </template>
            </Select>
          </div>
          <div class="w-1/2">
            <span class="text-xs text-slate-500">Ngày sinh</span>
            <DateTimeInput
              v-model="dateValue"
              dateFormat="dd/MM/yy"
              @update:model-value="handleDateUpdate"
            />
          </div>
        </div>

        <div class="flex gap-3">
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Người giới thiệu</span>
            <SearchPeople v-model.number="formData.referred_by!.referrer_id!" />
          </div>
          <div class="flex w-1/2 flex-col">
            <span class="py-0.5 text-xs text-slate-500">Mối quan hệ</span>
            <Select
              v-model="formData.referred_by!.relationship"
              :options="optionsRelationShip"
              class="fluid text-base"
              option-label="label"
              option-value="value"
              show-clear
            >
              <template #option="slotProps">
                {{ slotProps.option.label }}
              </template>
            </Select>
          </div>
        </div>
        <div class="flex flex-grow flex-col">
          <span class="py-0.5 text-xs text-slate-500">Ghi chú</span>
          <Textarea
            v-model.trim="formData.person_field.description"
            auto-resize
            class="fluid flex-grow text-base"
            rows="3"
          />
        </div>
      </div>

      <div class="col-span-12 space-y-2 sm:col-span-5">
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Tỉnh - Thành phố</span>
          <Select
            v-model="formData.province_id"
            :options="getProvinces"
            class="fluid text-base"
            filter
            option-label="name"
            option-value="id"
            show-clear
            @change="
              () => {
                formData.district_id = 0;
              }
            "
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Quận - Huyện</span>
          <Select
            v-model="formData.district_id"
            :options="getDistricts(formData.province_id ?? 0)"
            class="fluid text-base"
            filter
            option-label="name"
            option-value="id"
            show-clear
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Phường - Xã</span>
          <Select
            v-model="formData.ward_id"
            :options="getWards(formData.district_id ?? 0)"
            class="fluid text-base"
            filter
            option-label="name"
            option-value="id"
            show-clear
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Địa chỉ</span>
          <InputText v-model="formData.address_number" class="fluid" type="text" />
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Nguồn khách hàng</span>
          <Select
            v-model="formData.source_id"
            :options="getBundleTerms('nguon')"
            class="fluid text-base"
            filter
            option-label="name"
            option-value="id"
            show-clear
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Loại điều trị</span>
          <Select
            v-model="formData.person_field.treatment_id"
            :options="getBundleTerms('loai_dieu_tri')"
            class="fluild text-base"
            option-label="name"
            option-value="id"
            show-clear
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Trạng thái điều trị</span>
          <Select
            v-model.number="formData.person_field.treatment_status_id"
            :options="getBundleTerms('trang_thai_dieu_tri')"
            class="fluid text-base"
            option-label="name"
            option-value="id"
            show-clear
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </Select>
        </div>
        <div class="flex flex-col">
          <span class="py-0.5 text-xs text-slate-500">Sử dụng zalo?</span>
          <SelectButton
            v-model="formData.person_field.has_zalo"
            :options="[
              { name: 'Có', value: 'yes' },
              { name: 'Không', value: 'no' },
            ]"
            data-key="value"
            option-value="value"
            option-label="name"
          >
            <template #option="slotProps">
              {{ slotProps.option.name }}
            </template>
          </SelectButton>
        </div>
        <div class="flex flex-col" v-if="!formData.id">
          <span class="py-0.5 text-xs text-slate-500">Hệ thống tạo?</span>
          <div class="flex items-center gap-2">
            <Checkbox v-model="formData.is_system_created" :binary="true" size="large" />
          </div>
        </div>
      </div>
    </div>

    <template #header>
      <h3 class="text-lg font-medium">{{ title }}</h3>
    </template>
    <template #footer>
      <div v-show="leftButton">
        <Button
          type="submit"
          class="mr-auto"
          variant="soft-success"
          @click="createOrUpdatePerson('left')"
        >
          <Lucide class="h-4 w-4" icon="ArrowDownLeft" />
          Lưu và phục vụ
        </Button>
      </div>
      <div class="flex items-center gap-2">
        <Button class="w-20" type="button" variant="outline-secondary" @click="handleClose">
          Hủy
        </Button>
        <Button class="w-20" variant="primary" @click="createOrUpdatePerson('right')">Lưu</Button>
      </div>
    </template>
  </Dialog>
</template>
