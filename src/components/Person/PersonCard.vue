<template>
  <div v-if="props.person" :class="['flex items-center', sizeClasses.container[props.size]]">
    <PersonAvatar
      v-if="showAvatar"
      :class="[sizeClasses.avatar[props.size], 'mr-1.5']"
      :person="person"
      :issues="person?.issues"
      :size="props.size"
    />
    <div :class="['font-medium leading-none', sizeClasses.text[props.size]]">
      <div
        :class="[
          'flex cursor-pointer items-center leading-4 hover:text-primary',
          sizeClasses.text[props.size],
        ]"
      >
        <div class="relative flex items-center gap-1">
          <span
            :class="[
              sizeClasses.text[props.size],
              !props.person?.id && 'italic text-gray-400',
              'max-w-[150px] break-words sm:max-w-[200px] md:max-w-[250px]',
            ]"
            @click.stop="handleSubmit"
          >
            {{ props.person?.id ? props.person?.full_name ?? "" : "Không xác định" }}
          </span>
          <span
            v-if="showCode && props.person?.id && props.person?.person_field?.code"
            class="rounded border border-warning/50 bg-orange-50 px-0.5 text-xs font-semibold text-warning/80 dark:bg-secondary"
          >
            {{ props.person?.person_field?.code }}
          </span>
          <span v-if="showGender && props.person?.id">
            <GenderInfo :gender="props.person?.gender" />
          </span>
          <span
            v-if="showIconTab && props.person?.id"
            :class="[
              'absolute -right-7 z-50 flex h-5 w-5 flex-none cursor-pointer items-center justify-center rounded-full bg-secondary transition hover:shadow-md',
              sizeClasses.icon[props.size],
            ]"
            @click.stop="emits('submitNewTab')"
          >
            <Lucide icon="ExternalLink" :class="['h-3 w-3', sizeClasses.icon[props.size]]" />
          </span>
        </div>
      </div>

      <div
        :class="['group relative flex items-center', sizeClasses.subtext[props.size]]"
        v-if="props.person?.id || $slots['bottom-info']"
      >
        <slot v-if="$slots['bottom-info']" name="bottom-info" />
        <component :is="renderBottomInfo[bottomInfo]" v-else :person="person" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { breakpointsTailwind, useBreakpoints } from "@vueuse/core";

import { PersonResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { GenderInfo } from "../InfoText";

import { PersonAvatar, PersonInfoTags, PersonPhone } from "./";

const props = withDefaults(
  defineProps<{
    person?: PersonResponse;
    bottomInfo?: string;
    showAvatar?: boolean;
    showIconTab?: boolean;
    showGender?: boolean;
    showCode?: boolean;
    size?: "small" | "normal" | "large";
    submitType?: "name" | "new-tab" | "modal";
    queryParams?: Record<string, string>;
    showPhone?: boolean;
  }>(),
  {
    showAvatar: true,
    showIconTab: false,
    showGender: false,
    showCode: false,
    showPhone: true,
    bottomInfo: "phone",
    size: "normal",
    submitType: "name",
    queryParams: () => ({}),
  },
);

const emits = defineEmits<{
  (event: "submitName"): void;
  (event: "submitNewTab"): void;
}>();

const router = useRouter();
const breakpoints = useBreakpoints(breakpointsTailwind);
const isMobile = breakpoints.smaller("lg");

const renderBottomInfo: { [key: string]: any } = {
  phone: props.showPhone ? PersonPhone : null,
  info_tags: PersonInfoTags,
};

const sizeClasses: Record<string, Record<string, string>> = {
  container: {
    small: "text-xs",
    normal: "text-sm",
    large: "text-base",
  },
  avatar: {
    small: "h-8 w-8",
    normal: "h-11 w-11",
    large: "h-12 w-12",
  },
  text: {
    small: "text-xs",
    normal: "text-sm",
    large: "text-base",
  },
  subtext: {
    small: "text-xs font-light",
    normal: "text-sm font-normal",
    large: "text-base",
  },
  icon: {
    small: "h-3 w-3",
    normal: "h-4 w-4",
    large: "h-5 w-5",
  },
};

const handleSubmit = () => {
  if (!props.person?.id) return;

  // Nếu là màn hình mobile, luôn chuyển hướng đến trang chi tiết
  if (isMobile.value) {
    router.push({
      path: `/customer/profile/${props.person.id}`,
      query: props.queryParams,
    });
    return;
  }

  switch (props.submitType) {
    case "new-tab":
      const url = router.resolve({
        path: `/customer/profile/${props.person.id}`,
        query: props.queryParams,
      });
      window.open(url.href, "_blank", "noopener,noreferrer");
      break;

    case "modal":
      // Emit event for modal if needed
      emits("submitName");
      break;

    case "name":
    default:
      emits("submitName");
      break;
  }
};
</script>
