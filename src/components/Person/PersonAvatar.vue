<!-- PersonAvatar.vue -->
<template>
  <Avatar
    v-tooltip="{
      value: issueTitle,
      escape: false,
      pt: { text: { style: 'min-width: 10rem;' } },
    }"
    v-bind="$attrs"
    :label="avatarLabel"
    :shape="props.shape"
    :style="avatarStyle"
    :class="[
      'font-bold subpixel-antialiased',
      sizeClass,
      { 'border-2 border-orange-600': hasComplainIssue(issues) },
    ]"
  />
</template>

<script setup lang="ts">
import Color from "color";
import Avatar from "primevue/avatar";
import { computed } from "vue";

import { Issue, Person, PersonResponse } from "@/api/bcare-types-v2";
import { hasComplainIssue } from "@/components/Person/utils";
import { useHashColor } from "@/composables/useHashColor";
import { getAvatarLabel } from "@/utils/avatar";

interface Props {
  person?: Person | PersonResponse;
  fullName?: string;
  issues?: Issue[];
  shape?: "circle" | "square";
  size?: "small" | "normal" | "large" | number;
}

const props = withDefaults(defineProps<Props>(), {
  issues: () => [],
  size: "normal",
  shape: "square",
});

defineOptions({
  inheritAttrs: false,
});

const avatarLabel = computed(() => {
  if (props.person) {
    return props.person.full_name?.trim()
      ? getAvatarLabel(props.person.full_name).toUpperCase()
      : "?";
  }
  if (props.fullName?.trim()) {
    return getAvatarLabel(props.fullName).toUpperCase();
  }
  return "?";
});

const issueTitle = computed(() => {
  const complainIssue = props.issues?.find((issue) => issue.type === "complain");
  return complainIssue ? complainIssue.description : "";
});

const avatarColor = computed(() => {
  const name = props.person?.full_name || props.fullName || "";
  return useHashColor(name, "PersonAvatar");
});

const sizeClasses = {
  small: "size-7",
  normal: "size-10",
  large: "size-16",
};

const sizeClass = computed(() => {
  if (typeof props.size === "number") {
    return `size-[${props.size}px]`;
  }
  return sizeClasses[props.size as keyof typeof sizeClasses];
});

const textColor = computed(() => Color(avatarColor.value).darken(0.7).hex());

const avatarStyle = computed(() => ({
  backgroundColor: avatarColor.value,
  color: textColor.value,
  ...(typeof props.size === "number"
    ? { width: `${props.size}px`, height: `${props.size}px` }
    : {}),
}));
</script>
