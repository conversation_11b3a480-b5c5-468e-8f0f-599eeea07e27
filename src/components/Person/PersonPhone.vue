<template>
  <div class="flex items-center" v-if="props.person?.phone && canViewPhone()">
    <Tippy
      variant="primary"
      :content="GLOBAL_TEXT.COPIED"
      :options="{ trigger: 'click' }"
      @click.stop="copyToClipboard(props.person?.phone ?? '')"
    >
      {{ maskedPhone }}
    </Tippy>
    <div class="ml-2 h-5 w-5" v-if="props.person?.id || props.person?.phone">
      <span
        class="invisible flex h-5 w-5 cursor-pointer items-center justify-center rounded-full bg-success text-white shadow-md transition hover:bg-success/80 group-hover:visible"
        @click.stop="handleCallPerson"
      >
        <Lucide icon="Phone" class="h-3 w-3 -rotate-90" />
      </span>
    </div>
    <div class="ml-1 h-5 w-5">
      <FileIcon
        v-if="props.person?.person_field?.has_zalo === 'yes'"
        variant="zalo"
        class="invisible h-5 w-5 group-hover:visible"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

import { PersonResponse } from "@/api/bcare-types-v2";
import FileIcon from "@/base-components/FileIcon";
import Lucide from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy";
import { usePermissions } from "@/composables/usePermissions";
import { GLOBAL_TEXT } from "@/constants";
import { useCallInfoStore } from "@/stores/call-info";
import { copyToClipboard } from "@/utils/helper";

const props = defineProps<{
  person?: PersonResponse;
}>();

const { canViewPhone } = usePermissions();
const callInfoStore = useCallInfoStore();

const maskedPhone = computed(() => {
  if (!props.person?.phone) return '';

  const phone = props.person.phone;
  const lastThreeDigits = phone.slice(-3);
  const asterisks = '*'.repeat(4);

  return `${asterisks}${lastThreeDigits}`;
});

const handleCallPerson = () => {
  if (props.person) {
    callInfoStore.showWithPerson(props.person);
  }
};
</script>
