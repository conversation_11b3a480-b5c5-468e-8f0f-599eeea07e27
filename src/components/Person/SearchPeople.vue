<template>
  <div class="relative w-full">
    <!-- Selected Items -->
    <div v-show="selectedPeople.length > 0" class="flex flex-wrap gap-2">
      <Chip v-for="item in selectedPeople" :key="item.id" removable @remove="removeItem(item)">
        <template v-if="mode === 'user' && isUserResponse(item)">
          <UserAvatar :user="item" :size="30" />
          <span class="font-medium text-primary">{{ peopleName(item.name) }}</span>
        </template>
        <template v-else-if="mode === 'person' && isPersonResponse(item)">
          <PersonAvatar :person="item" shape="circle" :size="30" />
          <span class="font-medium text-primary">{{ peopleName(item.full_name) }}</span>
        </template>
      </Chip>
    </div>

    <!-- Search Input -->
    <IconField v-if="showInput" ref="inputFieldRef">
      <InputText
        ref="inputRef"
        v-model.trim="searchQuery"
        autocomplete="off"
        class="fluid w-full text-sm"
        type="text"
        :placeholder="placeholder"
        @focus="handleFocus"
        @keydown="handleKeydown"
        @update:model-value="handleSearchPerson"
      />
      <InputIcon
        :class="searchQuery ? 'pi-times' : 'pi-search'"
        class="pi cursor-pointer"
        @click="searchQuery ? clearSearch() : handleFocus()"
      />
    </IconField>

    <!-- Dropdown Menu -->
    <div v-if="showDropdown" class="absolute left-0 top-full z-50 w-full">
      <div
        class="rounded-md border-transparent bg-white shadow-[0px_3px_10px_#00000017] dark:border-transparent dark:bg-darkmode-600"
      >
        <!-- Loading State -->
        <div v-if="isLoading" class="p-2">
          <div v-for="i in 3" :key="i" class="flex items-center gap-3 p-2">
            <div class="h-8 w-8 animate-pulse rounded-full bg-gray-200"></div>
            <div class="flex-1">
              <div class="mb-2 h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
              <div class="h-3 w-1/2 animate-pulse rounded bg-gray-200"></div>
            </div>
          </div>
        </div>

        <!-- Results List -->
        <div v-else-if="filteredList.length > 0">
          <ul
            class="m-0 max-h-[50vh] snap-y list-none overflow-hidden overflow-y-auto overscroll-contain p-2"
          >
            <li
              v-for="item in filteredList"
              :key="item.id"
              class="group flex cursor-pointer snap-start items-center rounded-md p-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="handleItemClick(item)"
            >
              <template v-if="mode === 'user' && isUserResponse(item)">
                <UserAvatar :user="item" />
                <div class="ml-2 flex-1">
                  <span class="hyphens-auto font-medium">
                    <HighlightText :highlight="searchQuery" :text="item.name" />
                  </span>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    <HighlightText :highlight="searchQuery" :text="item.email" />
                  </div>
                </div>
              </template>
              <template v-else-if="mode === 'person' && isPersonResponse(item)">
                <PersonAvatar :person="item" />
                <div class="ml-2 flex-1">
                  <span class="hyphens-auto font-medium">
                    <HighlightText :highlight="searchQuery" :text="item.full_name" />
                  </span>
                  <div v-if="showPhone" class="text-sm text-gray-500 dark:text-gray-400">
                    <HighlightText :highlight="searchQuery" :text="item.phone" />
                  </div>
                </div>
                <div v-if="showActionButton">
                  <Button
                    variant="outlined"
                    severity="info"
                    rounded
                    icon="pi pi-arrow-right"
                    @click.stop="emit('action-click', item)"
                  />
                </div>
              </template>
            </li>
          </ul>
        </div>

        <!-- No Results -->
        <div v-else-if="searchQuery && !filteredList.length" class="p-2">
          <div class="block px-4 py-3">Không tìm thấy kết quả</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onClickOutside, useDebounceFn } from "@vueuse/core";
import { computed, onMounted, ref, watch } from "vue";

import { PersonResponse, UserResponse } from "@/api/bcare-types-v2";
import { personGet } from "@/api/bcare-v2";
import HighlightText from "@/base-components/HighlightText.vue";
import PersonAvatar from "@/components/Person/PersonAvatar.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import usePerson from "@/hooks/usePerson";
import useUser from "@/hooks/useUser";
import { getShortName } from "@/utils/string";

// Types
type Mode = "person" | "user";
type Item = PersonResponse | UserResponse;

interface Props {
  modelValue: number | number[] | null;
  multiple?: boolean;
  mode?: Mode;
  placeholder?: string;
  shortenName?: boolean;
  showPhone?: boolean;
  showActionButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  mode: "person",
  placeholder: "",
  shortenName: false,
  showPhone: true,
  showActionButton: false,
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: number | number[] | null];
  "enter-pressed": [value: string];
  "action-click": [value: Item];
}>();

// Composables
const { persons, listPersons } = usePerson();
const { filteredUsers, listUsers, getUsersByIds, searchUsers } = useUser({ autoLoad: true });

// Refs
const searchQuery = ref("");
const showDropdown = ref(false);
const isLoading = ref(false);
const selectedPeople = ref<Item[]>([]);
const inputRef = ref<HTMLElement | null>(null);
const inputFieldRef = ref<HTMLElement | null>(null);

// Type Guards
const isUserResponse = (item: Item): item is UserResponse => {
  return "email" in item;
};

const isPersonResponse = (item: Item): item is PersonResponse => {
  return "full_name" in item;
};

// Computed
const model = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const showInput = computed(() => props.multiple || selectedPeople.value.length === 0);

const filteredList = computed<Item[]>(() =>
  props.mode === "person" ? persons.value : filteredUsers.value,
);

// Methods
const peopleName = (name: string) => {
  return props.shortenName ? getShortName(name) : name;
};

const debouncedSearch = useDebounceFn(async () => {
  try {
    isLoading.value = true;
    if (props.mode === "person") {
      await listPersons({ search: searchQuery.value, page: 1, page_size: 20 });
    } else {
      await searchUsers(searchQuery.value);
    }
  } catch (error) {
    console.error("Error searching:", error);
  } finally {
    isLoading.value = false;
  }
}, 200);

const handleSearchPerson = (val: string | undefined) => {
  if (val === undefined) {
    return;
  }
  searchQuery.value = val;
  showDropdown.value = true;

  if (val.length > 0) {
    debouncedSearch();
  } else {
    showDropdown.value = false;
  }
};

const handleFocus = () => {
  if (searchQuery.value.length > 0) {
    showDropdown.value = true;
    debouncedSearch();
  }
};

const clearSearch = () => {
  searchQuery.value = "";
  showDropdown.value = false;
};

const handleItemClick = (item: Item) => {
  if (props.multiple) {
    const index = selectedPeople.value.findIndex((i) => i.id === item.id);
    if (index === -1) {
      selectedPeople.value.push(item);
    } else {
      selectedPeople.value.splice(index, 1);
    }
  } else {
    selectedPeople.value = [item];
  }
  updateModelValue();
  showDropdown.value = false;
  clearSearch();
};

const removeItem = (item: Item) => {
  const index = selectedPeople.value.findIndex((i) => i.id === item.id);
  if (index !== -1) {
    selectedPeople.value.splice(index, 1);
    updateModelValue();
  }
};

const updateModelValue = () => {
  model.value = props.multiple
    ? selectedPeople.value.map((item) => item.id)
    : selectedPeople.value[0]?.id || null;
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && searchQuery.value) {
    event.preventDefault();
    emit("enter-pressed", searchQuery.value);
  }
};

const getPersonsByIds = async (ids: number[]): Promise<PersonResponse[]> => {
  const personPromises = ids.map((id) =>
    personGet({ id }).then((r) => (r.code === 0 ? r.data : null)),
  );
  const persons = await Promise.all(personPromises);
  return persons.filter((person): person is PersonResponse => person !== null);
};

const initializeSelectedPeople = async () => {
  if (model.value) {
    const ids = Array.isArray(model.value) ? model.value : [model.value];
    if (ids.length > 0) {
      const fetchFunction = props.mode === "person" ? getPersonsByIds : getUsersByIds;
      const result = await fetchFunction(ids);
      selectedPeople.value = Array.isArray(result) ? result : [result];
    }
  } else {
    selectedPeople.value = [];
  }
};

// Watchers & Lifecycle
watch(model, initializeSelectedPeople, { immediate: true });

onClickOutside(inputFieldRef, () => {
  showDropdown.value = false;
});
</script>
