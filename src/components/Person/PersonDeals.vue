<script lang="ts" setup>
import { inject, ref } from "vue";

import { DealResponse } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import useDeal from "@/hooks/useDeal";
import DealName from "@/components/Deal/DealName.vue";
import { dealValue } from "@/utils/dealUtils";
import DateTime from "@/base-components/DateTime.vue";

const props = defineProps<{
  dealList: DealResponse[];
}>();

const selectedDeal = ref<DealResponse>();
const dealSelectedCallback = inject("dealSelectedCallback") as (deal: DealResponse) => void;
const { updateDeal } = useDeal({ useStore: false });

const updateDealStage = async (dealId: number, newStageId: number) => {
  try {
    await updateDeal({ id: dealId, stage_id: newStageId });
  } catch (error) {
    console.error("Error updating stage:", error);
  }
};
</script>

<template>
  <Fieldset
    :pt="{
      root: {
        class:
          'border-t border-b-0 border-l-0 border-r-0 border-slate-200/60 dark:border-darkmode-400 rounded-none',
      },
      toggleButton: { class: 'p-1 bg-transparent w-full justify-start' },
      legendLabel: { class: 'font-light text-sm' },
    }"
    :toggleable="true"
    class="p-3"
    legend="Deals"
  >
    <Listbox
      v-model="selectedDeal"
      :options="dealList"
      class="w-full overflow-y-auto overscroll-none border-none p-[1px]"
      list-style="max-height:250px"
      option-label="name"
      unstyled
      @update:model-value="dealSelectedCallback"
    >
      <template #option="slotProps">
        <div
          :class="{
            'bg-soft text-primary ring-1 ring-highlight': slotProps.option.id === selectedDeal?.id,
          }"
          class="group mb-2 flex w-full cursor-pointer snap-start flex-wrap items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
        >
          <div class="flex-1">
            <DealName
              :name="slotProps.option.name || 'Deal chưa đặt tên'"
              :state="slotProps.option.state"
              :shine="false"
            />
            <DateTime
              :date="slotProps.option.created_at"
              showTime
              class="text-slate-500"
              size="xs"
              :show-icon="false"
            />
            <div class="mt-1">
              <PipelineStageSelect
                v-model="slotProps.option.stage_id"
                :pipeline-id="2"
                class="text-sm"
                icon-class="text-xs"
                @update:model-value="updateDealStage(slotProps.option.id, $event)"
              />
            </div>
          </div>

          <div class="mr-2 text-right text-sm font-medium text-success dark:text-gray-300">
            <Money :amount="dealValue(slotProps.option)" />
          </div>

          <div class="ml-auto text-gray-600 dark:text-gray-400">
            <div
              :class="{
                'bg-primary': slotProps.option.id === selectedDeal?.id,
                invisible: slotProps.option.id !== selectedDeal?.id,
              }"
              class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
            ></div>
          </div>
        </div>
      </template>
    </Listbox>
  </Fieldset>
</template>
