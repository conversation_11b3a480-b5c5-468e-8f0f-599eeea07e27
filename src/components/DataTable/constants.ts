export type AllKeys<T> = T extends object
  ? {
      [K in keyof T]: `${Exclude<K, symbol>}${AllKeys<T[K]> extends never ? "" : `.${AllKeys<T[K]>}`}`;
    }[keyof T]
  : never;

export type ItemFilterType = {
  id: string;
  placeholder?: string;
  title: string;
} & (
  | {
      type: "date";
      selectionMode?: "multiple" | "range" | "single";
    }
  | { type: "select"; option: { title: string; value: string | number }[] }
  | { type: "text" }
);

export interface ColumnDefinition<T> {
  field: AllKeys<T> | "actions";
  header: string;
  sortable?: boolean;
  type?: "string" | "date";
  class?: string;
  style?: {
    width?: string;
    minWidth?: string;
  };
  filterField?: string;
  filterMatchMode?: string;
  filterType?: FilterType;
  filterPlaceholder?: string;
  filterOptions?: { title: string; value: string | number | boolean }[];
  showFilterMenu?: boolean;
  showFilterClear?: boolean;
}

export type FilterType = "text" | "select" | "date" | "dateRange" | "custom";
