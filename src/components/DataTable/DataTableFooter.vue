<script setup lang="ts">
import Paginator, { type PageState } from "primevue/paginator";
import { computed } from "vue";

interface Props {
  totalRecords: number;
  page?: number;
  rows?: number;
  rowsPerPageOptions?: number[];
}

const props = withDefaults(defineProps<Props>(), {
  totalRecords: 0,
  page: 1,
  rows: 10,
  rowsPerPageOptions: () => [10, 20, 50, 100],
});

const emit = defineEmits<{
  (e: "pageChange", event: PageState): void;
}>();

// Calculate text for paginator
const paginatorText = computed(() => {
  const total = props.totalRecords || 0;
  if (total === 0) return "0 mục";
  const first = (props.page - 1) * props.rows + 1;
  const last = Math.min(first + props.rows - 1, total);
  return `Hiển thị ${first} - ${last} của ${total} mục`;
});

const handlePageChange = (event: PageState) => {
  emit("pageChange", event);
};
</script>

<template>
  <div class="relative flex h-[40px] w-full items-center justify-center">
    <!-- Left: Bulk Actions Slot -->
    <div class="absolute left-0 top-1/2 -translate-y-1/2 transform">
      <slot name="bulkActions">
        <!-- Default content if no slot is provided -->
        <div class="flex h-[40px] items-center gap-2"></div>
        <!-- Placeholder for alignment -->
      </slot>
    </div>

    <!-- Right: Paginator -->
    <div class="flex items-center gap-4">
      <span class="whitespace-nowrap text-sm font-normal">
        {{ paginatorText }}
      </span>
      <Paginator
        :first="(page - 1) * rows"
        :rows="rows"
        :totalRecords="totalRecords"
        :rowsPerPageOptions="rowsPerPageOptions"
        @page="handlePageChange"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
        :pt="{
          root: { class: '!bg-transparent !p-0' },
          pcCurrentPageReport: { root: { class: '!text-sm !font-normal !px-2' } },
          pcRowsPerPageDropdown: { root: { class: 'h-8' }, input: { class: '!text-sm' } },
          pcFirstPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
          pcPrevPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
          pcNextPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
          pcLastPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
        }"
      />
    </div>
  </div>
</template>
