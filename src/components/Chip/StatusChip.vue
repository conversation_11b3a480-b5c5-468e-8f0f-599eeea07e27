<script setup lang="ts">
import Chip from "primevue/chip";
import { computed } from "vue";

import { TaskStatusEnum } from "@/api/bcare-enum";

const props = withDefaults(
  defineProps<{
    status?: TaskStatusEnum;
    size?: "small" | "normal" | "large";
    customText?: string;
    showDot?: boolean;
  }>(),
  {
    size: "normal",
    showDot: true,
  },
);

const statusMap = {
  [TaskStatusEnum.DONE]: { class: "bg-blue-500 text-white", text: "Hoàn thành" },
  [TaskStatusEnum.CLOSE]: { class: "bg-red-500 text-white", text: "Đã đóng" },
  [TaskStatusEnum.PROCESSING]: { class: "bg-green-500 text-white", text: "<PERSON>ang thực hiện" },
  [TaskStatusEnum.REVIEWER]: { class: "bg-purple-400 text-white", text: "Ch<PERSON> duyệt" },
  [TaskStatusEnum.NEWS]: { class: "bg-yellow-500 text-white", text: "Mới tạo" },
  default: { class: "bg-gray-500 text-white", text: "Không xác định" },
};

const sizeMap = {
  small: { class: "px-2 py-0.5 text-xs", dotClass: "w-1.5 h-1.5" },
  large: { class: "text-base py-2 px-4", dotClass: "w-3 h-3" },
  normal: { class: "text-sm py-1 px-3", dotClass: "w-2 h-2" },
};

const statusInfo = computed(() => statusMap[props.status || "default"] || statusMap.default);
const sizeInfo = computed(() => sizeMap[props.size]);

const displayText = computed(() => props.customText || statusInfo.value.text);
</script>

<template>
  <Chip :class="[statusInfo.class, sizeInfo.class, 'inline-flex items-center rounded-full']">
    <span
      v-if="showDot"
      :class="['mr-1 flex-shrink-0 rounded-full bg-white', sizeInfo.dotClass]"
    ></span>
    <span class="whitespace-nowrap">{{ displayText }}</span>
  </Chip>
</template>
