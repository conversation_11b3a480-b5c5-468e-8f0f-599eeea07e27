<script setup lang="ts">
import Chip from "primevue/chip";
import { computed } from "vue";

import { TaskPriorityEnum } from "@/api/bcare-enum";

const props = defineProps<{
  priority: TaskPriorityEnum | string | number | undefined;
  size?: "small" | "normal" | "large";
  text?: string;
}>();

const priorityMap = {
  [TaskPriorityEnum.HIGH]: {
    class: "text-red-500",
    icon: "pi pi-flag-fill",
    text: "Cao",
  },
  [TaskPriorityEnum.MEDIUM]: {
    class: "text-yellow-500",
    icon: "pi pi-flag-fill",
    text: "Trung bình",
  },
  [TaskPriorityEnum.LOW]: {
    class: "text-blue-500",
    icon: "pi pi-flag-fill",
    text: "Thấp",
  },
  default: {
    class: "text-gray-500",
    icon: "pi pi-minus",
    text: "Bình thường",
  },
};

const sizeMap = {
  small: { iconClass: "text-[12px]" },
  large: { iconClass: "text-sm" },
  normal: { iconClass: "text-base" },
};

const priorityInfo = computed(() => {
  const priorityKey =
    typeof props.priority === "string" ? parseInt(props.priority, 10) : props.priority;
  return priorityMap[priorityKey as keyof typeof priorityMap] || priorityMap.default;
});

const sizeInfo = computed(() => sizeMap[props.size || "normal"]);
</script>

<template>
  <Chip
    :class="[
      'flex w-fit cursor-pointer items-center gap-2 bg-transparent p-0',
      sizeInfo.iconClass,
    ]"
  >
    <i :class="[priorityInfo.icon, priorityInfo.class, sizeInfo.iconClass]"></i>
    <span class="break-words text-gray-700 hover:decoration-dotted hover:underline underline-offset-2 decoration-1">
      {{ props.text || priorityInfo.text }}
    </span>
  </Chip>
</template>
