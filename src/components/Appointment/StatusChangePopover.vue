<template>
  <div class="flex items-center">
    <Chip
      class="cursor-pointer rounded-full px-1.5 py-1 text-sm font-medium"
      v-bind="getStatusChip(appointment.status)"
      @click="toggle($event)"
    >
      <template #icon> </template>
      <!-- For status 3 (<PERSON><PERSON>g hẹn), show only the time -->
      <span v-if="appointment.status === 3" class="flex items-center">
        <Lucide icon="Check" class="mr-1 h-3 w-3" />
        <span class="text-sm">
          <span class="font-bold" v-if="!appointment.arrived_at">Đ<PERSON> đến</span>
          <span v-else>{{ timeFormat(appointment.arrived_at) }}</span>
        </span>
      </span>
      <!-- For other statuses, show the status name -->
      <span v-else class="flex items-center gap-1">
        <Lucide :icon="getStatusChip(appointment.status).icon" class="h-3 w-3" />
        {{ statusLabels?.[appointment.status] || "Không xác đ<PERSON>" }}
      </span>
    </Chip>

    <Popover ref="op">
      <div class="w-64 p-3">
        <h3 class="mb-2 text-lg font-medium">Thay đổi trạng thái</h3>
        <div class="flex max-h-60 flex-col gap-2 overflow-y-auto">
          <div
            v-for="(label, statusId) in statusLabels"
            :key="statusId"
            class="flex cursor-pointer items-center rounded-md p-2 hover:bg-slate-100"
            :class="{ 'bg-slate-100': appointment.status === Number(statusId) }"
            @click="handleStatusChange(Number(statusId))"
          >
            <Lucide
              :icon="(statusConfig[Number(statusId)]?.icon as Icon) || 'HelpCircle'"
              class="mr-2 h-4 w-4"
              :class="
                statusConfig[Number(statusId)]?.color.replace('bg-', 'text-') || 'text-slate-500'
              "
            />
            <span>{{ label }}</span>
            <Lucide
              v-if="appointment.status === Number(statusId)"
              icon="Check"
              class="ml-auto h-4 w-4 text-success"
            />
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import Chip from "primevue/chip";
import Popover from "primevue/popover";
import { ref } from "vue";

import { AppointmentResponse, AppointmentUpdateRequest } from "@/api/bcare-types-v2";
import Lucide, { Icon } from "@/base-components/Lucide";
import { useAppointment } from "@/hooks/useAppointment";

const props = defineProps<{
  appointment: AppointmentResponse;
  statusLabels: Record<number, string>;
}>();

const emit = defineEmits<{
  (e: "statusChanged"): void;
}>();

const { updateAppointment } = useAppointment();
const op = ref();

// Date formatter
const timeFormat = (date: string) => {
  if (!date) return "";
  return useDateFormat(date, "HH:mm").value;
};

// Status configuration
const statusConfig: Record<number, { icon: Icon; color: string }> = {
  1: { icon: "Lock", color: "bg-red-500" },
  2: { icon: "CalendarPlus", color: "bg-info" },
  3: { icon: "CheckCircle", color: "bg-success" },
  4: { icon: "AlertTriangle", color: "bg-warning" },
  5: { icon: "RefreshCcw", color: "bg-green-500" },
  6: { icon: "PlusCircle", color: "bg-blue-500" },
};

const toggle = (event: Event) => {
  op.value.toggle(event);
};

const handleStatusChange = async (newStatus: number) => {
  if (!props.appointment.id) return;

  try {
    const request: AppointmentUpdateRequest = {
      id: props.appointment.id,
      status: newStatus,
      modified: ["status"],
    };

    const success = await updateAppointment(request);
    if (success) {
      op.value.hide();
      // Emit event to parent component to reload data
      emit("statusChanged");
    }
  } catch (error) {
    console.error("Error updating appointment status:", error);
  }
};

const getStatusChip = (statusId: number) => {
  const status = props.statusLabels?.[statusId];
  const { icon = "HelpCircle", color = "bg-slate-500" } = statusConfig[statusId] || {};

  return {
    label: status || "Không xác định",
    icon: icon as Icon,
    class: `${color} text-white`,
  };
};
</script>
