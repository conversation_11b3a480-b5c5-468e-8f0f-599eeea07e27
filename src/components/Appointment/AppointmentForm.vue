<script setup lang="ts">
import { computed } from "vue";

import { AppointmentAddRequest, AppointmentUpdateRequest } from "@/api/bcare-types-v2";
import DateTimeInput from "@/components/DateTimeInput/DateTimeInput.vue";
import { FormField } from "@/components/Form";
import { APPOINTMENT_IN_OPTIONS } from "@/constants";
import useConstant from "@/hooks/useConstant";

const props = defineProps<{
  modelValue: AppointmentAddRequest | AppointmentUpdateRequest;
  date: Date;
  time: Date | null;
  duration: number;
}>();

const emits = defineEmits<{
  (e: "update:modelValue", value: AppointmentAddRequest | AppointmentUpdateRequest): void;
  (e: "update:date", value: Date): void;
  (e: "update:time", value: Date | null): void;
  (e: "update:duration", value: number): void;
  (e: "dateChange", value: Date): void;
}>();

// Get constants for appointment types and statuses
const { constants } = useConstant();

// Computed properties for select options
const appointmentTypeOptions = computed(() => {
  if (!constants.value?.appointment_type) return [];
  return Object.entries(constants.value.appointment_type).map(([value, label]) => ({
    value: Number(value),
    label,
  }));
});

const appointmentStatusOptions = computed(() => {
  if (!constants.value?.appointment_status) return [];
  return Object.entries(constants.value.appointment_status).map(([value, label]) => ({
    value: Number(value),
    label,
  }));
});

// Handle date change
const handleDateChange = (value: any) => {
  if (value) {
    emits("update:date", value as Date);
    emits("dateChange", value as Date);
  }
};

// Handle form data changes
const updateFormData = (field: string, value: any) => {
  emits("update:modelValue", { ...props.modelValue, [field]: value });
};
// Emit default values if not set
if (!props.modelValue.type) {
  emits("update:modelValue", { ...props.modelValue, type: 1 });
}
if (!props.modelValue.status) {
  emits("update:modelValue", { ...props.modelValue, status: 2 });
}
</script>

<template>
  <div class="box">
    <div class="p-2">
      <FormField label="Ngày hẹn">
        <DatePicker
          :modelValue="date"
          :showIcon="true"
          dateFormat="dd/mm/yy"
          fluid
          inline
          @update:modelValue="handleDateChange"
          selectOtherMonths
        />
      </FormField>

      <FormField label="Giờ hẹn">
        <DateTimeInput
          :modelValue="time"
          :current-date="date"
          timeOnly
          @update:modelValue="(val) => emits('update:time', val)"
        />
      </FormField>

      <FormField label="Hẹn trong (Phút)">
        <Select
          :modelValue="duration"
          :options="APPOINTMENT_IN_OPTIONS"
          optionLabel="label"
          optionValue="value"
          @update:modelValue="(val) => emits('update:duration', val)"
          fluid
        />
      </FormField>

      <FormField label="Loại hẹn">
        <Select
          v-model="modelValue.type"
          :options="appointmentTypeOptions"
          optionLabel="label"
          optionValue="value"
          fluid
        />
      </FormField>
    </div>

    <div class="border-y border-dashed border-primary/40 p-2">
      <FormField label="Ghi chú">
        <Textarea v-model="modelValue.notes" rows="4" class="w-full" auto-resize />
      </FormField>
    </div>

    <div class="p-2">
      <FormField label="Trạng thái">
        <Select
          v-model="modelValue.status"
          :options="appointmentStatusOptions"
          optionLabel="label"
          optionValue="value"
          fluid
        />
      </FormField>
    </div>
  </div>
</template>
