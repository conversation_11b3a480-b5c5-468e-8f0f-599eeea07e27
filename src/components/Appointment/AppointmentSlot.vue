<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  doctorName: string;
  doctorId: number;
  appointments: any[];
  startTime: string;
  endTime: string;
}>();

const emits = defineEmits<{
  (
    e: "select",
    value: {
      startTime: string;
      endTime: string;
      doctorId: number;
      doctorName: string;
      appointments: any[];
    },
  ): void;
}>();

// Check if slot is full (6 or more appointments)
const isFull = computed(() => props.appointments.length >= 6);

// Check if slot has warning (exactly 6 appointments)
const hasWarning = computed(() => props.appointments.length === 6);

// Get slot status class
const slotStatusClass = computed(() => {
  if (isFull.value) return "bg-red-50 border-red-200";
  if (hasWarning.value) return "bg-yellow-50 border-yellow-200";
  return "bg-green-50 border-green-200";
});

// Handle slot selection
const handleSelect = () => {
  emits("select", {
    startTime: props.startTime,
    endTime: props.endTime,
    doctorId: props.doctorId,
    doctorName: props.doctorName,
    appointments: props.appointments,
  });
};
</script>

<template>
  <div
    :class="[
      'cursor-pointer rounded-lg border p-3 transition-all hover:shadow-md',
      slotStatusClass,
    ]"
    @click="handleSelect"
  >
    <div class="flex items-center justify-between">
      <h4 class="font-medium">{{ doctorName }}</h4>
      <Badge
        :value="appointments.length"
        :severity="isFull ? 'danger' : hasWarning ? 'warning' : 'success'"
      />
    </div>

    <div class="mt-2 text-sm">
      <div v-if="isFull" class="flex items-center text-red-600">
        <i class="pi pi-exclamation-triangle mr-1"></i>
        <span>Khung giờ đã đầy</span>
      </div>
      <div v-else-if="hasWarning" class="flex items-center text-yellow-600">
        <i class="pi pi-exclamation-circle mr-1"></i>
        <span>Khung giờ sắp đầy</span>
      </div>
      <div v-else class="flex items-center text-green-600">
        <i class="pi pi-check-circle mr-1"></i>
        <span>Còn {{ 6 - appointments.length }} chỗ trống</span>
      </div>
    </div>
  </div>
</template>
