<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import ProgressSpinner from "primevue/progressspinner";
import Tag from "primevue/tag";
import { computed, ref } from "vue";

import DateTime from "@/base-components/DateTime.vue";
import PersonCard from "@/components/Person/PersonCard.vue";
import { UserAvatar } from "@/components/User";
import { useAppointment } from "@/hooks/useAppointment";
import useUser from "@/hooks/useUser";

// Thêm hook useUser để lấy thông tin người dùng
const { getUserById } = useUser({ autoLoad: true });
const { generateTimeSlots } = useAppointment();

const props = defineProps<{
  appointmentsBySlot: Map<string, any[]>;
  selectedDate: Date;
  isLoading: boolean;
}>();

const emits = defineEmits<{
  (
    e: "slotSelect",
    value: {
      startTime: string;
      endTime: string;
      appointmentCount: number;
    },
  ): void;
}>();

// Get all time slots using the utility function
const timeSlots = computed(() => generateTimeSlots(props.selectedDate));

// Get appointment count for each slot
const getAppointmentCount = (slotKey: string) => {
  return props.appointmentsBySlot.get(slotKey)?.length || 0;
};

// Get slot status
const getSlotStatus = (slotKey: string) => {
  const count = getAppointmentCount(slotKey);
  if (count >= 6) return "danger";
  if (count === 5) return "warning";
  return "success";
};

// Map status to Tag severity
const getTagSeverity = (slotKey: string) => {
  const status = getSlotStatus(slotKey);
  switch (status) {
    case "danger":
      return "danger";
    case "warning":
      return "warn";
    case "success":
      return "success";
    default:
      return "info";
  }
};

// Handle slot selection
const handleSlotSelect = (slot: any) => {
  emits("slotSelect", {
    startTime: slot.startTime,
    endTime: slot.endTime,
    appointmentCount: getAppointmentCount(slot.key),
  });
};

// Get tag icon based on status
const getTagIcon = (status: string) => {
  switch (status) {
    case "danger":
      return "pi pi-users";
    case "warning":
      return "pi pi-users";
    case "success":
      return "pi pi-user";
    default:
      return "pi pi-user";
  }
};

// Get tag value with count
const getTagValue = (slotKey: string) => {
  const count = getAppointmentCount(slotKey);
  return `${count} KH`;
};

// Sort appointments by creation date
const getSortedAppointmentsForSlot = (slotKey: string) => {
  const appointments = props.appointmentsBySlot.get(slotKey) || [];
  return [...appointments].sort((a, b) => {
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });
};

// Check if appointment is over limit (6th or later)
const isAppointmentOverLimit = (index: number) => {
  return index >= 5; // 0-based index, so 5 is the 6th appointment
};

// Toggle show all appointments
const showAllAppointments = ref(false);

const toggleAllAppointments = () => {
  showAllAppointments.value = !showAllAppointments.value;
};
</script>

<template>
  <div class="rounded-lg bg-white p-4 shadow">
    <h2 class="mb-4 flex items-center">
      <DateTime :time="selectedDate.toISOString()" size="lg" />

      <div class="flex items-center">
        <i
          v-tooltip="
            showAllAppointments ? 'Ẩn danh sách khách hàng' : 'Hiển thị danh sách khách hàng'
          "
          :class="[showAllAppointments ? 'pi-eye' : 'pi-eye-slash']"
          class="pi cursor-pointer p-2 text-xl text-slate-500 transition-all hover:text-slate-700"
          @click="toggleAllAppointments"
        ></i>
      </div>
    </h2>

    <div v-if="isLoading" class="flex h-64 items-center justify-center">
      <ProgressSpinner />
    </div>

    <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
      <!-- Time slots -->
      <div
        v-for="slot in timeSlots"
        :key="slot.key"
        class="rounded-lg border shadow-sm transition-all"
        :class="{
          'border-green-200': getSlotStatus(slot.key) === 'success',
          'border-yellow-200': getSlotStatus(slot.key) === 'warning',
          'border-red-200': getSlotStatus(slot.key) === 'danger',
        }"
      >
        <!-- Slot header -->
        <div
          class="flex cursor-pointer items-center justify-between rounded-lg p-4 transition-all hover:bg-gray-50"
          :class="{
            'bg-green-50': getSlotStatus(slot.key) === 'success',
            'bg-yellow-50': getSlotStatus(slot.key) === 'warning',
            'bg-red-50': getSlotStatus(slot.key) === 'danger',
            'rounded-b-lg': !showAllAppointments || getAppointmentCount(slot.key) === 0,
          }"
          @click="handleSlotSelect(slot)"
        >
          <div class="flex flex-col">
            <h3 class="text-lg font-medium">{{ slot.displayTime }}</h3>
            <div class="mt-1 text-sm">
              <div v-if="getAppointmentCount(slot.key) >= 6" class="flex items-center text-red-600">
                <i class="pi pi-exclamation-triangle mr-1"></i>
                <span>Khung giờ đã đầy</span>
              </div>
              <div
                v-else-if="getAppointmentCount(slot.key) === 5"
                class="flex items-center text-yellow-600"
              >
                <i class="pi pi-exclamation-circle mr-1"></i>
                <span>Khung giờ sắp đầy</span>
              </div>
              <div v-else class="flex items-center text-green-600">
                <i class="pi pi-check-circle mr-1"></i>
                <span>Còn {{ 6 - getAppointmentCount(slot.key) }} chỗ trống</span>
              </div>
            </div>
          </div>

          <div class="flex items-center gap-2">
            <Tag
              :value="getTagValue(slot.key)"
              :severity="getTagSeverity(slot.key)"
              :icon="getTagIcon(getSlotStatus(slot.key))"
              rounded
            />
          </div>
        </div>

        <!-- Appointment list -->
        <div
          v-if="showAllAppointments && getAppointmentCount(slot.key) > 0"
          class="w-full border-t border-gray-200 p-3"
        >
          <div class="mb-2 text-sm font-medium text-gray-500">
            Danh sách khách hàng ({{ getAppointmentCount(slot.key) }})
          </div>
          <div class="flex w-full flex-col gap-2">
            <div
              v-for="(appointment, index) in getSortedAppointmentsForSlot(slot.key)"
              :key="appointment.id"
              class="flex w-full flex-wrap items-center gap-2 rounded-lg border border-gray-200 p-2"
              :class="{ 'bg-yellow-50': isAppointmentOverLimit(index) }"
            >
              <!-- Thời gian chi tiết -->
              <div
                class="flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
              >
                <i class="pi pi-clock mr-1 text-yellow-500"></i>
                <span>
                  {{ useDateFormat(appointment.start_time, "HH:mm").value }} -
                  {{ useDateFormat(appointment.end_time, "HH:mm").value }}
                </span>
              </div>

              <!-- Thông tin khách hàng -->
              <PersonCard
                :person="appointment.person"
                showAvatar
                showGender
                size="small"
                submitType="new-tab"
                bottomInfo="info_tags"
              />

              <div class="flex flex-grow flex-wrap items-center gap-2">
                <!-- Thay thế phần Ghi chú bằng thông tin người tạo -->
                <div class="flex items-center gap-2" v-if="appointment.creator_id">
                  <UserAvatar :userId="appointment.creator_id" size="small" />
                  <span class="text-sm font-medium">{{
                    getUserById(appointment.creator_id)?.name || "-"
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
