<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed,onUnmounted, reactive, ref, watch } from "vue";

import {
  AppointmentAddRequest,
  AppointmentResponse,
  AppointmentUpdateRequest,
} from "@/api/bcare-types-v2";
import { formDataDefault,useAppointment } from "@/hooks/useAppointment";
import { deepClone } from "@/utils/helper";

import AppointmentForm from "./AppointmentForm.vue";
import TimeSlotSelector from "./TimeSlotSelector.vue";

const props = defineProps<{
  isOpen: boolean;
  personId: number;
  selectedItem?: AppointmentResponse;
}>();

const emits = defineEmits<{
  (event: "onClose"): void;
  (event: "updateAppointmentSuccess"): void;
}>();

// State
const formData = reactive<AppointmentAddRequest | AppointmentUpdateRequest>({
  ...deepClone(formDataDefault),
  person_id: props.personId,
});

const appointmentDate = ref<Date>(new Date());
const appointmentTime = ref<Date | null>(null);
const appointmentDuration = ref<number>(60);
const isLoading = ref(false);
const appointmentsByTimeSlot = ref<Map<string, any[]>>(new Map());
const isLoadingAppointments = ref(false);
const showWarning = ref(false);
const warningMessage = ref("");

// Hooks
const {
  fetchAppointmentList,
  roundToMinute,
  createAppointment,
  updateAppointment,
  clearAppointments,
  generateTimeSlots,
  groupAppointmentsBySlot,
  mapAppointmentToSlot,
} = useAppointment();

// Methods
const fetchAppointmentsForDate = async (date: Date) => {
  try {
    isLoadingAppointments.value = true;
    const formattedDate = useDateFormat(date, "YYYY-MM-DD").value;

    // Fetch all appointments for this date
    const appointmentsResult = await fetchAppointmentList({
      from_date: `${formattedDate}T00:00:00+07:00`,
      to_date: `${formattedDate}T23:59:59+07:00`,
      order_by: "created_at asc",
      has_doctor: "no",
    });

    if (appointmentsResult?.appointments) {
      // Group appointments by time slot using the new utility function
      appointmentsByTimeSlot.value = groupAppointmentsBySlot(appointmentsResult.appointments);

      // Check if current appointment would be the 7th in its slot
      if (appointmentTime.value && props.selectedItem?.id) {
        checkIfAppointmentHasWarning(appointmentTime.value);
      }
    }
  } catch (error) {
    console.error("Error fetching appointments:", error);
  } finally {
    isLoadingAppointments.value = false;
  }
};

const handleDateChange = async (newDate: Date) => {
  appointmentDate.value = newDate;
  await fetchAppointmentsForDate(newDate);
};

const handleTimeSlotSelect = (slotInfo: {
  startTime: string;
  endTime: string;
  appointmentCount: number;
}) => {
  appointmentTime.value = new Date(slotInfo.startTime);

  // Calculate duration in minutes
  const start = new Date(slotInfo.startTime).getTime();
  const end = new Date(slotInfo.endTime).getTime();
  appointmentDuration.value = Math.round((end - start) / (1000 * 60));

  // Check if this would be the 7th appointment in this slot
  if (slotInfo.appointmentCount >= 6) {
    showWarning.value = true;
    warningMessage.value = "Khung giờ này đã có 6 lịch hẹn";
  } else {
    showWarning.value = false;
  }
};

const checkIfAppointmentHasWarning = (time: Date) => {
  // Create a mock appointment to use with mapAppointmentToSlot
  const mockAppointment = {
    start_time: time.toISOString(),
    end_time: new Date(time.getTime() + appointmentDuration.value * 60 * 1000).toISOString(),
  } as AppointmentResponse;

  const slotKey = mapAppointmentToSlot(mockAppointment);
  const appointmentsInSlot = appointmentsByTimeSlot.value.get(slotKey) || [];

  // If this is an existing appointment, exclude it from the count
  const relevantAppointments = props.selectedItem?.id
    ? appointmentsInSlot.filter((a) => a.id !== props.selectedItem?.id)
    : appointmentsInSlot;

  if (relevantAppointments.length >= 6) {
    showWarning.value = true;
    warningMessage.value =
      "Khung giờ này đã có 6 lịch hẹn. Bạn vẫn có thể đặt lịch nhưng có thể sẽ phải chờ đợi.";
  } else {
    showWarning.value = false;
  }
};

const handleSubmit = async () => {
  try {
    if (!appointmentTime.value) {
      return;
    }

    isLoading.value = true;
    const startTime = roundToMinute(appointmentTime.value);
    const endTime = new Date(startTime.getTime() + appointmentDuration.value * 60 * 1000);

    const appointmentData: AppointmentAddRequest = {
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      person_id: props.personId,
      doctor_id: 0,
      status: formData.status ?? 2,
      type: formData.type ?? 1,
      notes: formData.notes,
    };

    if (props.selectedItem?.id) {
      // Update existing appointment
      const updateData = {
        id: props.selectedItem.id,
        ...appointmentData,
      };

      const res = await updateAppointment(updateData);
      if (res) {
        resetForm();
        emits("updateAppointmentSuccess");
        emits("onClose");
      }
    } else {
      // Create new appointment
      const res = await createAppointment(appointmentData);
      if (res) {
        resetForm();
        emits("updateAppointmentSuccess");
        emits("onClose");
      }
    }
  } catch (error) {
    console.error("Error submitting appointment:", error);
  } finally {
    isLoading.value = false;
  }
};

// Add a new reset function
const resetForm = () => {
  clearAppointments();
  appointmentsByTimeSlot.value.clear();
  appointmentTime.value = null;
  appointmentDate.value = new Date();
  appointmentDuration.value = 60;
  formData.notes = "";
  formData.status = 2; // Reset về trạng thái mặc định
  formData.type = 1; // Reset về loại mặc định
  formData.extra_notes = "";
  showWarning.value = false;
  warningMessage.value = "";
};

// Initialize data when modal opens/closes
watch(
  () => props.isOpen,
  async (isOpen) => {
    if (!isOpen) {
      resetForm();
      return;
    }

    try {
      if (props.selectedItem) {
        // Edit mode
        appointmentDate.value = new Date(props.selectedItem.start_time);
        appointmentTime.value = new Date(props.selectedItem.start_time);

        // Calculate duration
        const start = new Date(props.selectedItem.start_time).getTime();
        const end = new Date(props.selectedItem.end_time).getTime();
        appointmentDuration.value = Math.round((end - start) / (1000 * 60));

        // Update form data
        Object.assign(formData, {
          notes: props.selectedItem.notes,
          status: props.selectedItem.status,
          type: props.selectedItem.type,
          extra_notes: props.selectedItem.extra_notes,
        });
      } else {
        // Create mode
        Object.assign(formData, deepClone(formDataDefault));
        appointmentDate.value = new Date();
        appointmentTime.value = null;
        appointmentDuration.value = 60;
      }

      // Fetch appointments for the selected date
      await fetchAppointmentsForDate(appointmentDate.value);
    } catch (error) {
      console.error("Error initializing modal:", error);
    }
  },
  { immediate: true },
);

// Watch for time changes to check for warnings
watch(appointmentTime, (newTime) => {
  if (newTime) {
    checkIfAppointmentHasWarning(newTime);
  } else {
    showWarning.value = false;
  }
});
</script>

<template>
  <Dialog
    :visible="props.isOpen"
    modal
    fluid
    @update:visible="emits('onClose')"
    :style="{ width: '98vw', height: '!99vh' }"
    :pt="{
      header: { class: 'px-3 py-1' },
      content: { class: 'bg-slate-100 py-4' },
      footer: { class: 'flex justify-end items-center pt-4 border-t border-slate-200' },
    }"
    closeOnEscape
    dismissableMask
    blockScroll
    header="Đặt lịch hẹn tư vấn"
    destroyOnHide
  >
    <div class="grid grid-cols-1 gap-4 md:grid-cols-12">
      <!-- Form bên trái -->
      <div class="md:col-span-4">
        <AppointmentForm
          :modelValue="formData"
          @update:modelValue="val => Object.assign(formData, val)"
          v-model:date="appointmentDate"
          v-model:time="appointmentTime"
          v-model:duration="appointmentDuration"
          @date-change="handleDateChange"
        />

        <!-- Warning message -->
        <Message v-if="showWarning" severity="warn" :closable="false" class="mt-4">
          <div class="align-items-center flex">
            <i class="pi pi-exclamation-triangle mr-2"></i>
            <span>{{ warningMessage }}</span>
          </div>
        </Message>
      </div>

      <!-- Time slot selector bên phải -->
      <div class="md:col-span-8">
        <TimeSlotSelector
          :appointments-by-slot="appointmentsByTimeSlot"
          :selected-date="appointmentDate"
          :is-loading="isLoadingAppointments"
          @slot-select="handleTimeSlotSelect"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex items-center gap-2">
        <Button
          label="Hủy"
          icon="pi pi-times"
          class="p-button-outlined"
          @click="emits('onClose')"
        />
        <Button label="Lưu" icon="pi pi-save" :loading="isLoading" @click="handleSubmit" />
      </div>
    </template>
  </Dialog>
</template>
