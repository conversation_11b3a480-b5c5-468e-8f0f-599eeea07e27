<script setup lang="ts">
import { TagInfo, TagResponse } from "@/api/bcare-types-v2";
import { Size } from "@/api/types";
import { getTagColorPair } from "@/composables/useHashColor";
import { computed, ref, nextTick, watch } from "vue";

interface Props {
  tag: TagResponse | TagInfo;
  size?: Size;
  shape?: "pill" | "rounded" | "square";
  deletable?: boolean;
  modifiable?: boolean;
  maxWidth?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: "normal",
  shape: "pill",
  deletable: false,
  modifiable: true,
  maxWidth: undefined,
});

const emit = defineEmits<{
  (e: "delete", tagId: number): void;
  (e: "update", updatedTag: TagResponse | TagInfo): void;
  (e: "click", tag: TagResponse | TagInfo): void;
}>();

// Local state for inline editing
const isEditing = ref(false);
const editedName = ref("");
const inputRef = ref<HTMLInputElement | null>(null);
const hiddenSpanRef = ref<HTMLSpanElement | null>(null);
const inputWidth = ref("60px"); // Start with a minimum width

// Generate colors based on tag category
const { backgroundColor, textColor } = computed(() =>
  getTagColorPair(props.tag.category || "default"),
).value;

// Size styling - Adjust padding for icon space
const sizeClasses = computed(() => {
  switch (props.size) {
    case "small":
      // Less horizontal padding overall, icon/text will handle spacing
      return "text-xs py-0.5 px-1.5 leading-tight";
    case "large":
      // Less horizontal padding overall
      return "text-base py-1.5 px-3 leading-tight";
    default:
      // Less horizontal padding overall
      return "text-sm py-1 px-2 leading-tight";
  }
});

const iconSizeClasses = computed(() => {
  switch (props.size) {
    case "small":
      return "text-xs";
    case "large":
      return "text-base";
    default:
      return "text-sm";
  }
});

// Shape styling
const shapeClasses = computed(() => {
  switch (props.shape) {
    case "rounded":
      return "rounded-md";
    case "square":
      return "rounded-sm";
    default:
      return "rounded-full"; // pill shape
  }
});

// Watch for changes to editedName and adjust input width
watch(
  [editedName, isEditing],
  ([newName, editing]) => {
    if (editing) {
      nextTick(() => {
        adjustInputWidth();
      });
    }
  },
  { immediate: true },
); // Also run immediately when editing starts

// Function to adjust input width based on content
const adjustInputWidth = () => {
  if (hiddenSpanRef.value) {
    // Use same padding as input/span for calculation + a small buffer
    const extraPadding = 10; // Adjust buffer as needed
    const contentWidth = hiddenSpanRef.value.offsetWidth + extraPadding;
    const minWidth = 60; // Minimum width to prevent excessive shrinking
    inputWidth.value = `${Math.max(contentWidth, minWidth)}px`;
  } else {
    // Fallback width if span isn't ready
    inputWidth.value = `${Math.max(editedName.value.length * 8 + 10, 60)}px`; // Estimate
  }
};

const handleDelete = (event: Event) => {
  event.stopPropagation();
  emit("delete", props.tag.id);
};

const handleClick = () => {
  if (props.modifiable && !isEditing.value) {
    startEditing();
  } else if (!props.modifiable) {
    // Only emit click if not modifiable, otherwise click starts editing
    emit("click", props.tag);
  }
};

const startEditing = () => {
  editedName.value = props.tag.name;
  isEditing.value = true;
  nextTick(() => {
    adjustInputWidth(); // Adjust width immediately
    if (inputRef.value) {
      inputRef.value.focus();
      inputRef.value.select(); // Select text for easier editing
    }
  });
};

const saveEdit = () => {
  const trimmedName = editedName.value.trim();
  if (trimmedName && trimmedName !== props.tag.name) {
    const updatedTag = { ...props.tag, name: trimmedName };
    emit("update", updatedTag);
  }
  isEditing.value = false;
};

const cancelEdit = () => {
  isEditing.value = false;
  // No need to reset editedName, it will be set on next edit start
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    event.preventDefault();
    saveEdit();
  } else if (event.key === "Escape") {
    cancelEdit();
  }
};

// Use mousedown + blur to handle clicks outside that save
let isBlurring = false; // Flag to prevent double save on Enter + Blur
const handleMouseDownOutside = () => {
  isBlurring = true; // Indicate potential blur event is coming
};

const handleBlur = () => {
  if (isBlurring && isEditing.value) {
    saveEdit();
  }
  isBlurring = false; // Reset flag
};
</script>

<template>
  <div
    class="group box-border inline-flex select-none items-center overflow-hidden transition-colors duration-150 ease-in-out"
    :class="[
      sizeClasses,
      shapeClasses,
      props.modifiable && !isEditing ? 'cursor-pointer' : 'cursor-default',
    ]"
    :style="{
      backgroundColor: backgroundColor,
      color: textColor,
      maxWidth: props.maxWidth,
    }"
    @click="handleClick"
    @mousedown.stop="handleMouseDownOutside"
    v-tooltip="!isEditing ? (props.tag as TagResponse).description : null"
  >
    <!-- Prefix Icon/Button Area -->
    <div class="mr-1 flex flex-shrink-0 items-center justify-center">
      <!-- Delete Button (Shown on hover if deletable) -->
      <button
        v-if="props.deletable && !isEditing"
        @click.stop="handleDelete"
        :class="[
          'flex items-center justify-center transition-opacity duration-150 ease-in-out',
          // Hidden by default, shown on group-hover
          'hidden opacity-70 group-hover:flex group-hover:opacity-100',
        ]"
        :style="{ color: textColor }"
        type="button"
        aria-label="Delete tag"
      >
        <i class="pi pi-times" :class="iconSizeClasses" />
      </button>

      <!-- Tag Icon (Shown by default, hidden on hover if deletable) -->
      <span
        :class="[
          'flex items-center justify-center',
          // Hide icon on hover ONLY if delete button is shown then
          props.deletable && !isEditing ? 'group-hover:hidden' : '',
        ]"
      >
        <i class="pi pi-tag" :class="iconSizeClasses" />
      </span>
    </div>

    <!-- Display mode -->
    <span
      v-if="!isEditing"
      class="overflow-hidden text-ellipsis whitespace-nowrap"
      :class="{ 'opacity-0': isEditing }"
    >
      {{ props.tag.name }}
    </span>

    <!-- Edit mode - Use v-show for potentially smoother transitions -->
    <input
      v-show="isEditing"
      ref="inputRef"
      v-model="editedName"
      type="text"
      class="m-0 box-border whitespace-nowrap border-none bg-transparent !p-0 !px-0.5 outline-none focus:outline-none focus:ring-0"
      :class="[sizeClasses]"
      :style="{
        color: textColor,
        width: inputWidth,
        lineHeight: 'inherit', // Inherit line height from parent
        height: 'auto', // Let height be determined by line-height/padding
      }"
      @keydown="handleKeydown"
      @blur="handleBlur"
      @mousedown.stop
    />

    <!-- Hidden span to measure text width - ensure identical styling -->
    <span
      ref="hiddenSpanRef"
      class="invisible absolute whitespace-nowrap px-0.5"
      :class="[sizeClasses]"
      aria-hidden="true"
    >
      {{ editedName || props.tag.name }}
      <!-- Use edited or original name for measurement -->
    </span>
  </div>
</template>
