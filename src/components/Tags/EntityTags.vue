<script setup lang="ts">
import { computed, ref, watch } from "vue";

import type { TagInfo, TagResponse } from "@/api/bcare-types-v2";
import TagChip from "@/components/Tags/TagChip.vue";
import useTag from "@/hooks/useTag";

import { defineAsyncComponent } from "vue";
import { Size } from "@/api/types";

const TagSelectorPopover = defineAsyncComponent(
  () => import("@/components/Tags/TagSelectorPopover.vue"),
);

type EntityType = "person" | "deal";

interface Props {
  entityId: number | null;
  entityType: EntityType;
  initialTags?: TagInfo[];
  placeholder?: string;
  size?: Size;
}

const props = withDefaults(defineProps<Props>(), {
  initialTags: () => [],
  placeholder: undefined,
  size: "normal",
});

const {
  groupedTags,
  addTagToPerson,
  addTagToDeal,
  deleteTagFrom<PERSON>erson,
  deleteTagFromDeal,
  isLoading: isTagLoading,
} = useTag({ useStore: true, autoLoad: true });

const emit = defineEmits<{
  (event: "reload"): void;
}>();

const displayedTags = ref<TagInfo[] | TagResponse[]>([]);
const tagPopoverRef = ref<InstanceType<typeof TagSelectorPopover> | null>(null);

watch(
  () => props.initialTags,
  (newTags) => {
    displayedTags.value = [...(newTags || [])];
  },
  { immediate: true, deep: true },
);

const currentTagIdsSet = computed(() => new Set(displayedTags.value.map((t) => t.id)));

const popoverGroupedTags = computed((): Record<string, TagResponse[]> => {
  return (groupedTags.value || []).reduce(
    (acc, group) => {
      acc[group.category] = group.items;
      return acc;
    },
    {} as Record<string, TagResponse[]>,
  );
});

const addElementBaseClasses = "flex items-center gap-1 cursor-pointer";

const placeholderClasses = computed(() => {
  const sizeClasses = {
    small: "text-xs",
    normal: "text-sm",
    large: "text-base",
  };
  return [
    addElementBaseClasses,
    sizeClasses[props.size || "small"],
    "text-gray-500 dark:text-gray-400",
  ];
});

const iconClasses = computed(() => {
  const sizeClasses = {
    small: "text-xs",
    normal: "text-sm",
    large: "text-base",
  };
  const colorClass = props.placeholder ? "text-amber-500" : "";
  return [sizeClasses[props.size || "small"], colorClass];
});

const toggleAddTagPopover = (event: Event) => {
  tagPopoverRef.value?.toggle(event);
};

const handleTagSelectedFromPopover = async (tagToAdd: TagResponse) => {
  if (!tagToAdd) return;
  tagPopoverRef.value?.hide();

  try {
    if (props.entityType === "person" && props.entityId) {
      await addTagToPerson({ person_id: props.entityId, tag_id: tagToAdd.id });
    } else if (props.entityType === "deal" && props.entityId) {
      await addTagToDeal({ deal_id: props.entityId, tag_id: tagToAdd.id });
    }
    emit("reload");
  } catch (error) {
    console.error(`Failed to add tag to ${props.entityType}:`, error);
  }
};

const handleDeleteTag = async (tagIdToDelete: number) => {
  try {
    if (props.entityType === "person" && props.entityId) {
      await deleteTagFromPerson({ tag_id: tagIdToDelete, person_id: props.entityId });
    } else if (props.entityType === "deal" && props.entityId) {
      await deleteTagFromDeal({ tag_id: tagIdToDelete, deal_id: props.entityId });
    }
    emit("reload");
  } catch (error) {
    console.error(
      `[handleDeleteTag] Failed to delete tag ${tagIdToDelete} from ${props.entityType} ${props.entityId}:`,
      error,
    );
  }
};
</script>

<template>
  <div class="flex flex-wrap items-center gap-1">
    <!-- Display existing tags -->
    <TagChip
      v-for="tag in displayedTags"
      :key="`displayed-${props.entityType}-${tag.id}`"
      :tag="tag"
      :size="props.size"
      :deletable="true"
      :modifiable="false"
      @delete="handleDeleteTag"
    />

    <div @click="toggleAddTagPopover" :class="placeholderClasses" v-tooltip.bottom="'Thêm thẻ'">
      <i :class="['pi pi-tag', iconClasses]"></i>
      <span
        class="whitespace-normal break-words border-none p-0 underline decoration-dotted underline-offset-2 shadow-none"
      >
        {{ placeholder }}
      </span>
    </div>

    <TagSelectorPopover
      ref="tagPopoverRef"
      :all-grouped-tags="popoverGroupedTags"
      :current-tag-ids="currentTagIdsSet"
      :is-loading="isTagLoading"
      @tag-selected="handleTagSelectedFromPopover"
      appendTo="body"
    />
  </div>
</template>
