<script setup lang="ts">
import { ref, computed, watch } from "vue";
import Popover from "primevue/popover";
import Checkbox from "primevue/checkbox";
import InputText from "primevue/inputtext";
import TagChip from "./TagChip.vue"; // Use TagChip for display inside popover
import type { TagInfo, TagResponse } from "@/api/bcare-types-v2"; // Ensure this path is correct
import useTag from "@/hooks/useTag";

interface Props {
  modelValue: TagInfo[]; // Array of selected tags from the parent
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:modelValue", value: TagInfo[]): void;
}>();

const popoverRef = ref<InstanceType<typeof Popover> | null>(null);
const searchTag = ref("");
const { groupedTags } = useTag({ useStore: true, autoLoad: true });

const selectedTagsMap = ref<Record<number, boolean>>({});

watch(
  () => props.modelValue,
  (newValue) => {
    const newMap: Record<number, boolean> = {};
    newValue.forEach((tag) => {
      newMap[tag.id] = true;
    });
    selectedTagsMap.value = newMap;
  },
  { immediate: true, deep: true },
);

const filteredGroupedTags = computed(() => {
  const lowerSearch = searchTag.value.trim().toLowerCase();
  if (!lowerSearch) {
    return groupedTags.value;
  }

  return groupedTags.value
    .map((group) => ({
      ...group,
      items: group.items.filter((tag) => tag.name.toLowerCase().includes(lowerSearch)),
    }))
    .filter((group) => group.items.length > 0);
});

const handleTagSelection = (tag: TagResponse, shouldBeSelected: boolean) => {
  const currentSelected = [...props.modelValue];
  const index = currentSelected.findIndex((t) => t.id === tag.id);
  const isCurrentlySelected = index > -1;

  if (shouldBeSelected && !isCurrentlySelected) {
    currentSelected.push({ id: tag.id, name: tag.name, category: tag.category || "" });
    emit("update:modelValue", currentSelected);
    selectedTagsMap.value[tag.id] = true;
  } else if (!shouldBeSelected && isCurrentlySelected) {
    currentSelected.splice(index, 1);
    emit("update:modelValue", currentSelected);
    selectedTagsMap.value[tag.id] = false;
  }
};

const togglePopover = (event: Event, target?: HTMLElement) => {
  popoverRef.value?.toggle(event, target);
};

defineExpose({
  popoverRef,
  togglePopover,
});
</script>

<template>
  <Popover
    ref="popoverRef"
    :pt="{
      root: {
        class:
          'w-80 shadow-lg border border-surface-200 dark:border-surface-700 rounded-md overflow-hidden',
      },
      content: { class: 'p-0' },
    }"
  >
    <div class="flex flex-col gap-2 p-2">
      <!-- Search Input Field -->
      <InputText
        v-model="searchTag"
        placeholder="Search tags..."
        class="p-inputtext-sm w-full"
        autofocus
      />
      <!-- Scrollable Container for Tag List -->
      <div class="flex max-h-60 flex-col gap-2 overflow-y-auto">
        <!-- Check if there are tags to display -->
        <template v-if="filteredGroupedTags.length > 0">
          <!-- Loop through each category group -->
          <div
            v-for="group in filteredGroupedTags"
            :key="group.category"
            class="flex flex-col gap-1"
          >
            <!-- Sticky Category Header -->
            <div
              class="sticky top-0 z-10 -mx-2 border-b border-surface-100 bg-surface-50 px-2 py-1 text-sm font-semibold text-surface-600 dark:border-surface-700 dark:bg-surface-800 dark:text-surface-400"
            >
              {{ group.category }}
            </div>
            <!-- Selectable Row for each Tag -->
            <div
              v-for="tag in group.items"
              :key="tag.id"
              class="flex cursor-pointer items-center gap-2 rounded px-2 py-1 transition-colors duration-150 hover:bg-surface-100 dark:hover:bg-surface-700"
              @click="handleTagSelection(tag, !selectedTagsMap[tag.id])"
            >
              <Checkbox
                :modelValue="selectedTagsMap[tag.id] || false"
                :inputId="`tag-selector-${tag.id}`"
                :value="tag.id"
                binary
                @update:modelValue="(selected) => handleTagSelection(tag, Boolean(selected))"
                @click.stop
              />
              <TagChip
                :tag="tag"
                size="small"
                :modifiable="false"
                :deletable="false"
                class="pointer-events-none flex-1 truncate"
              />
            </div>
          </div>
        </template>
        <div v-else class="p-4 text-center text-sm text-surface-500">
          No tags found{{ searchTag ? " matching your search" : "" }}.
        </div>
      </div>
    </div>
  </Popover>
</template>
