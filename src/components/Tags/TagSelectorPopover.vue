<script setup lang="ts">
import Popover from "primevue/popover";
import { computed, ref } from "vue";

import type { TagResponse } from "@/api/bcare-types-v2";
import TagChip from "@/components/Tags/TagChip.vue";
import { Size } from "@/api/types";

interface Props {
  allGroupedTags: Record<string, TagResponse[]>;
  currentTagIds: Set<number>;
  isLoading?: boolean;
  size?: Size;
}

interface Emits {
  (e: "tag-selected", tag: TagResponse): void;
  (e: "update:visible", value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  currentTagIds: () => new Set<number>(),
});

const emit = defineEmits<Emits>();

const popoverRef = ref<InstanceType<typeof Popover> | null>(null);

const availableGroupedTags = computed(() => {
  const result: Record<string, TagResponse[]> = {};
  const currentIds = props.currentTagIds;

  for (const category in props.allGroupedTags) {
    const availableTags = props.allGroupedTags[category].filter((tag) => !currentIds.has(tag.id));
    if (availableTags.length > 0) {
      result[category] = availableTags;
    }
  }
  return result;
});

const handleTagClick = (tag: TagResponse) => {
  emit("tag-selected", tag);
};

defineExpose({
  toggle: (event: Event) => popoverRef.value?.toggle(event),
  show: (event: Event) => popoverRef.value?.show(event),
  hide: () => popoverRef.value?.hide(),
});
</script>

<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-1' } }">
    <div class="max-h-60 min-w-[200px] overflow-y-auto p-2">
      <div v-if="isLoading" class="text-center text-slate-500">
        <ProgressSpinner />
      </div>
      <div
        v-else-if="Object.keys(availableGroupedTags).length === 0"
        class="text-center text-slate-500"
      >
        Không có tag nào để thêm.
      </div>
      <div v-else class="space-y-2">
        <div v-for="(tagsInCategory, category) in availableGroupedTags" :key="category">
          <div class="mb-1 text-sm font-semibold text-slate-500 dark:text-slate-500">
            {{ category }}
          </div>
          <div class="flex flex-wrap gap-1">
            <TagChip
              v-for="tag in tagsInCategory"
              :key="`available-${tag.id}`"
              :tag="tag"
              :size="props.size"
              :modifiable="false"
              :deletable="false"
              class="cursor-pointer transition-transform hover:scale-105"
              @click="handleTagClick(tag)"
            />
          </div>
        </div>
      </div>
    </div>
  </Popover>
</template>
