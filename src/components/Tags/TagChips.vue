<script setup lang="ts">
import { computed } from "vue";
import type { TagInfo } from "@/api/bcare-types-v2";
import TagChip from "@/components/Tags/TagChip.vue";

type TagChipSize = "small" | "normal" | "large";

interface Props {
  tags?: TagInfo[];
  tagIds?: number[];
  size?: TagChipSize;
  placeholder?: string;
  entityId: number;
}

const props = withDefaults(defineProps<Props>(), {
  tags: () => [],
  tagIds: undefined,
  size: "small",
  placeholder: "Thêm tag",
});

const emit = defineEmits<{
  (event: "open-edit", payload: { event: Event; entityId: number }): void;
}>();

const handleOpenEdit = (event: Event) => {
  if ((event.target as HTMLElement).closest(".p-chip")) {
    return;
  }
  emit("open-edit", { event, entityId: props.entityId });
};

const resolvedTags = computed(() => {
  if (props.tagIds && props.tagIds.length > 0) {
    return [];
  }
  return props.tags || [];
});
</script>

<template>
  <div class="flex cursor-pointer flex-wrap items-center gap-1" @click="handleOpenEdit">
    <template v-if="resolvedTags.length > 0">
      <TagChip
        v-for="tag in resolvedTags"
        :key="`simple-tag-${props.entityId}-${tag.id}`"
        :tag="tag"
        :size="props.size"
        :deletable="false"
        :modifiable="false"
        class="pointer-events-none"
      />
    </template>
  </div>
</template>
