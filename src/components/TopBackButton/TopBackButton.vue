<script setup lang="ts">
import Button from "@/base-components/Button";
import Lucide from "@/base-components/Lucide";

const emit = defineEmits(["click"]);
const props = defineProps<{
  title: string;
}>();
</script>

<template>
  <div class="flex items-center">
    <Button
      class="mr-2 border-slate-300 px-2 text-slate-600 dark:text-slate-300"
      @click="emit('click')"
    >
      <Lucide icon="ChevronLeft" class="h-4 w-4" />
    </Button>
    <h2 class="mr-auto text-lg font-medium">{{ props.title }}</h2>
  </div>
</template>
