<script setup lang="ts">
import { computed } from "vue";

import { getContrastTextColor, useHashColor } from "@/composables/useHashColor";

interface StatisticCardProps<T = any> {
  label: string;
  value: T;
  icon: string;
  valueKey?: keyof T;
  chartKey?: string;
  isActive?: boolean;
  color?: string;
}

const props = withDefaults(defineProps<StatisticCardProps>(), {
  valueKey: undefined,
  chartKey: undefined,
  isActive: false,
  color: undefined,
});

// Base color computation
const baseColor = computed(() => {
  return (
    props.color || (props.chartKey ? useHashColor(props.chartKey, "StatisticCard") : "#60A5FA")
  );
});

// Sử dụng màu gốc cho background của icon, và màu trắng cho icon
const bgColor = computed(() => baseColor.value);

// Icon luôn màu trắng
const iconColor = computed(() => "#FFFFFF");

// Border color nhạt hơn một chút
const borderColor = computed(() => {
  return getContrastTextColor(baseColor.value, { mode: "lighten", amount: 0.2 });
});

const cardBorderColor = computed(() => {
  return props.isActive ? bgColor.value : borderColor.value;
});

const formattedValue = computed(() => {
  const val = props.valueKey ? props.value[props.valueKey] : props.value;
  if (typeof val === "number") {
    return Number.isInteger(val) ? val : val.toFixed(2);
  }
  return val;
});

const emit = defineEmits<{
  (e: "click", chartKey: string): void;
}>();
</script>

<template>
  <div
    class="group relative overflow-hidden rounded-xl border p-4 shadow-md transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
    :style="{
      borderColor: cardBorderColor,
      '--tw-ring-color': cardBorderColor,
    }"
    :class="{
      'ring-1': isActive,
    }"
    @click="props.chartKey && emit('click', props.chartKey)"
  >
    <div class="relative z-10 flex items-center justify-between">
      <div class="flex-1 space-y-2">
        <div class="text-sm font-medium text-gray-600">{{ label }}</div>
        <div class="text-2xl font-medium text-gray-900">
          {{ formattedValue }}
        </div>
      </div>

      <!-- Icon Container with Shine Effect -->
      <div
        class="shine-effect relative flex h-14 w-14 items-center justify-center overflow-hidden rounded-full border transition-transform duration-300 group-hover:scale-110"
        :style="{
          backgroundColor: bgColor,
          borderColor: borderColor,
        }"
      >
        <i
          :class="icon"
          class="relative z-10 text-2xl transition-all duration-300"
          :style="{ color: iconColor }"
        />
      </div>
    </div>
  </div>
</template>

<style>
@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    transparent 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 60%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine 2.5s infinite;
}
</style>
