// composables/useStageWidth.ts
import { computed, nextTick,ref } from 'vue';

export function useStageWidth(stagesCount: number) {
  const containerRef = ref<HTMLElement | null>(null);
  const containerWidth = ref(0);
  const MIN_STAGE_WIDTH = 300;

  const stageWidth = computed(() => {
    const maxVisibleStages = Math.floor(containerWidth.value / MIN_STAGE_WIDTH);
    const calculatedWidth = containerWidth.value / Math.min(maxVisibleStages, stagesCount);
    return Math.max(calculatedWidth, MIN_STAGE_WIDTH);
  });

  const containerStyle = computed(() => {
    const totalWidth = stageWidth.value * stagesCount;
    return totalWidth > containerWidth.value ? { width: `${totalWidth}px` } : {};
  });

  const updateContainerWidth = async () => {
    await nextTick();
    if (containerRef.value) {
      containerWidth.value = containerRef.value.offsetWidth;
    }
  };

  const scrollToEnd = () => {
    if (containerRef.value) {
      containerRef.value.scrollLeft = containerRef.value.scrollWidth - containerRef.value.clientWidth;
    }
  };

  const scrollToStart = () => {
    if (containerRef.value) {
      containerRef.value.scrollLeft = 0;
    }
  };

  return {
    containerRef,
    containerWidth,
    stageWidth,
    containerStyle,
    updateContainerWidth,
    scrollToEnd,
    scrollToStart
  };
}
