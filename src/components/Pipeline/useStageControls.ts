// composables/useStageControls.ts
import { useStorage } from '@vueuse/core';

interface StageControl {
  viewMode: 'normal' | 'maximize' | 'minimize';
  energySaver: boolean;
}

export function useStageControls(pipelineId: number) {
  const stageControls = useStorage<Record<string, StageControl>>(
    `pipeline_${pipelineId}_stage_controls`,
    {},
    localStorage,
    { mergeDefaults: true }
  );

  const initializeStageControl = (stageId: number) => {
    if (!stageControls.value[stageId]) {
      stageControls.value = {
        ...stageControls.value,
        [stageId]: {
          viewMode: 'normal',
          energySaver: false
        }
      };
    }
  };

  const toggleEnergySaver = (stageId: number) => {
    initializeStageControl(stageId);
    stageControls.value = {
      ...stageControls.value,
      [stageId]: {
        ...stageControls.value[stageId],
        energySaver: !stageControls.value[stageId].energySaver
      }
    };
  };

  const setViewMode = (stageId: number, mode: StageControl['viewMode']) => {
    initializeStageControl(stageId);
    stageControls.value = {
      ...stageControls.value,
      [stageId]: {
        ...stageControls.value[stageId],
        viewMode: mode
      }
    };
  };

  return {
    stageControls,
    initializeStageControl,
    toggleEnergySaver,
    setViewMode
  };
}
