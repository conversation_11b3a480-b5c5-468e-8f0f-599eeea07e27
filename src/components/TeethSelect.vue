<script lang="ts" setup>
import { ref } from "vue";

import TinyToothChart from "./TinyToothChart.vue";

interface Props {
  modelValue: Record<string, boolean>;
  defaultArchMode?: "adult" | "children";
}

const props = defineProps<Props>();
const emit = defineEmits(["update:modelValue"]);

const popoverRef = ref();

const closePopover = () => {
  popoverRef.value?.hide();
};

defineExpose({
  popoverRef,
});
</script>

<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }">
    <div class="w-[50vw]">
      <div class="p-2">
        <TinyToothChart
          :defaultArchMode="defaultArchMode || 'adult'"
          :model-value="modelValue"
          label="Hôm nay"
          @update:modelValue="$emit('update:modelValue', $event)"
        />
      </div>
      <div class="flex justify-end border-t p-2">
        <Button class="text-sm" label="OK" @click="closePopover" />
      </div>
    </div>
  </Popover>
</template>
