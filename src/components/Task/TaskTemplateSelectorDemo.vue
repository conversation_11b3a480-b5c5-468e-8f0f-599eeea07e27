<template>
  <div class="p-4">
    <h3 class="mb-4 text-lg font-semibold">Task Template Selector Demo</h3>
    
    <div class="space-y-4">
      <!-- Manual trigger buttons for testing -->
      <div class="flex gap-2">
        <Button
          label="Test Template Selector (Manual)"
          icon="pi pi-list"
          @click="showTemplateSelector"
          class="bg-primary px-4 py-2 font-medium text-white hover:bg-primary-600"
        />
        
        <Button
          label="Test with Task ID"
          icon="pi pi-check"
          @click="testWithTaskId"
          class="bg-green-600 px-4 py-2 font-medium text-white hover:bg-green-700"
        />
      </div>

      <!-- Input for testing with specific task ID -->
      <div class="flex items-center gap-2">
        <label class="text-sm font-medium">Test Task ID:</label>
        <InputNumber
          v-model="testTaskId"
          placeholder="Enter task ID"
          class="w-32"
        />
        <label class="text-sm font-medium">Serial:</label>
        <InputNumber
          v-model="testTaskSerial"
          placeholder="Serial (optional)"
          class="w-32"
        />
      </div>

      <!-- Status display -->
      <div v-if="lastResult" class="rounded-md bg-gray-100 p-3">
        <h4 class="font-medium">Last Result:</h4>
        <pre class="mt-2 text-sm">{{ JSON.stringify(lastResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- Task Template Selector Component -->
    <TaskTemplateSelector
      ref="taskTemplateSelector"
      :person-id="personId"
      @task-created="handleTaskCreated"
      @template-selected="handleTemplateSelected"
      @cancelled="handleCancelled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Button from "primevue/button";
import InputNumber from "primevue/inputnumber";
import TaskTemplateSelector from "./TaskTemplateSelector.vue";

interface Props {
  personId?: number;
}

const props = withDefaults(defineProps<Props>(), {
  personId: 0,
});

// Refs
const taskTemplateSelector = ref<InstanceType<typeof TaskTemplateSelector>>();
const testTaskId = ref<number>(1);
const testTaskSerial = ref<number | undefined>(undefined);
const lastResult = ref<any>(null);

// Methods
const showTemplateSelector = async () => {
  if (taskTemplateSelector.value) {
    await taskTemplateSelector.value.showTemplateSelector();
    lastResult.value = { action: "Manual template selector opened" };
  }
};

const testWithTaskId = async () => {
  if (taskTemplateSelector.value && testTaskId.value) {
    const result = await taskTemplateSelector.value.checkAndShowTemplateSelector(
      testTaskId.value,
      testTaskSerial.value
    );
    lastResult.value = {
      action: "Checked task",
      taskId: testTaskId.value,
      serial: testTaskSerial.value,
      dialogShown: result,
    };
  }
};

// Event handlers
const handleTaskCreated = () => {
  lastResult.value = { action: "Task created successfully" };
  console.log("Task created successfully");
};

const handleTemplateSelected = (templateId: string) => {
  lastResult.value = { action: "Template selected", templateId };
  console.log("Template selected:", templateId);
};

const handleCancelled = () => {
  lastResult.value = { action: "Template selection cancelled" };
  console.log("Template selection cancelled");
};
</script>
