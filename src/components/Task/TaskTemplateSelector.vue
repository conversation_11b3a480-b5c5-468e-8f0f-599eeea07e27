<template>
  <!-- Template Selection Dialog -->
  <Dialog
    v-model:visible="isDialogVisible"
    :closable="false"
    :pt="dialogPT"
    class="w-80"
    position="bottomright"
    :draggable="false"
    :header="`Bạn có muốn đặt task mới cho ${currentPersonName || 'khách hàng này'}?`"
  >
    <div class="flex flex-col gap-3">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-3">
        <ProgressSpinner style="width: 24px; height: 24px" strokeWidth="6" />
        <span class="ml-2 text-xs text-surface-600 dark:text-surface-400"> Đang tải... </span>
      </div>

      <!-- Template Selection -->
      <div v-else>
        <Select
          v-model="selectedTemplateId"
          :options="selectOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Chọn template cho task mới"
          fluid
          @change="handleTemplateSelection"
        >
          <template #option="slotProps">
            <div
              :class="[
                slotProps.option.isDanger
                  ? 'text-red-700 dark:text-red-400'
                  : 'text-surface-800 dark:text-surface-100',
              ]"
            >
              <div class="text-sm font-medium">{{ slotProps.option.label }}</div>
            </div>
          </template>
        </Select>
      </div>

      <!-- No Templates State -->
      <div
        v-if="!isLoading && templates.length === 0"
        class="flex flex-col items-center justify-center py-3 text-center"
      >
        <i class="pi pi-folder-open mb-2 text-xl text-surface-400"></i>
        <p class="text-xs text-surface-600 dark:text-surface-400">Không có template</p>
      </div>
    </div>
  </Dialog>

  <!-- TaskCreateForm Component -->
  <TaskCreateForm
    ref="taskCreateForm"
    @reload-data="handleTaskCreated"
    :person-id="currentPersonId"
  />
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import Dialog from "primevue/dialog";
import Select from "primevue/select";
import ProgressSpinner from "primevue/progressspinner";
import { useTaskTemplateSelector } from "@/composables/useTaskTemplateSelector";
import { dialogPT } from "@/config/primevue-pts";
import TaskCreateForm from "@/pages/task/components/TaskCreateForm.vue";

interface Props {
  personId?: number;
}

interface Emits {
  (e: "task-created"): void;
  (e: "template-selected", templateId: string): void;
  (e: "cancelled"): void;
}

const props = withDefaults(defineProps<Props>(), {
  personId: 0,
});

const emit = defineEmits<Emits>();

// Ref for TaskCreateForm
const taskCreateForm = ref();

// Internal person_id state that can be updated dynamically
const currentPersonId = ref(props.personId);

// Use the composable with callbacks
const {
  isDialogVisible,
  selectedTemplateId,
  selectOptions,
  isLoading,
  templates,
  currentPersonName,
  checkAndShowTemplateSelector,
  handleTemplateSelection: handleTemplateSelectionBase,
  closeDialog,
  showTemplateSelector,
  setPersonId: setPersonIdComposable,
} = useTaskTemplateSelector({
  onTemplateSelected: async (templateId: string) => {
    // Open TaskCreateForm and apply template
    if (taskCreateForm.value) {
      taskCreateForm.value.open();
      // Apply template after a short delay to ensure form is open
      await nextTick();
      taskCreateForm.value.applyTemplate?.(templateId);
    }
    emit("template-selected", templateId);
  },
  onCancelled: () => {
    emit("cancelled");
  },
  onTaskCreated: () => {
    emit("task-created");
  },
});

// Initialize person name if personId is provided
if (props.personId) {
  setPersonIdComposable(props.personId);
}

// Enhanced template selection handler
const handleTemplateSelection = async () => {
  await handleTemplateSelectionBase();
};

// Handle task creation completion
const handleTaskCreated = () => {
  console.log("Task created successfully from template");
  emit("task-created");
};

// Method to set person_id dynamically
const setPersonId = async (newPersonId: number) => {
  // Update the internal person_id state
  currentPersonId.value = newPersonId;

  // Update composable state and load person name
  await setPersonIdComposable(newPersonId);

  // Also update TaskCreateForm if it exists
  if (taskCreateForm.value && taskCreateForm.value.setPersonId) {
    taskCreateForm.value.setPersonId(newPersonId);
  }
};




// Expose methods for parent components
defineExpose({
  checkAndShowTemplateSelector,
  showTemplateSelector,
  closeDialog,
  setPersonId,
});
</script>
