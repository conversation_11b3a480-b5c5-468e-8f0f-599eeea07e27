<template>
  <div class="p-4">
    <h3 class="mb-4 text-lg font-semibold">Task Template Selector - Person ID Test</h3>

    <div class="space-y-4">
      <!-- Test Configuration -->
      <div class="rounded-lg border border-gray-200 p-4">
        <h4 class="mb-3 font-medium">Test Configuration</h4>

        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <label class="text-sm font-medium">Person ID:</label>
            <InputNumber
              v-model="testPersonId"
              placeholder="Enter person ID"
              class="w-32"
            />
          </div>

          <Button
            label="Update Person ID"
            icon="pi pi-refresh"
            @click="updatePersonId"
            class="bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
          />
        </div>
      </div>

      <!-- Test Actions -->
      <div class="space-y-3">
        <h4 class="font-medium">Test Actions</h4>

        <div class="flex flex-wrap gap-2">
          <Button
            label="Open Template Selector"
            icon="pi pi-list"
            @click="openTemplateSelector"
            class="bg-primary px-4 py-2 font-medium text-white hover:bg-primary-600"
          />

          <Button
            label="Simulate Task Completion"
            icon="pi pi-check"
            @click="simulateTaskCompletion"
            class="bg-green-600 px-4 py-2 font-medium text-white hover:bg-green-700"
          />
        </div>
      </div>

      <!-- Current State Display -->
      <div class="rounded-md bg-gray-100 p-4">
        <h4 class="font-medium">Current State:</h4>
        <div class="mt-2 space-y-1 text-sm">
          <div><strong>Person ID:</strong> {{ currentPersonId }}</div>
          <div><strong>Last Action:</strong> {{ lastAction || 'None' }}</div>
          <div><strong>Timestamp:</strong> {{ lastTimestamp || 'None' }}</div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <h4 class="font-medium text-blue-800">Test Instructions:</h4>
        <ol class="mt-2 list-decimal list-inside space-y-1 text-sm text-blue-700">
          <li>Set a Person ID above (e.g., 123)</li>
          <li>Click "Open Template Selector" to manually test</li>
          <li>Select a template from the dialog</li>
          <li>Verify that the TaskCreateForm opens with the correct Person ID populated</li>
          <li>Check the form's person_id field to ensure it matches your test value</li>
        </ol>
      </div>

      <!-- Expected Behavior -->
      <div class="rounded-lg border border-green-200 bg-green-50 p-4">
        <h4 class="font-medium text-green-800">Expected Behavior:</h4>
        <ul class="mt-2 list-disc list-inside space-y-1 text-sm text-green-700">
          <li>When TaskCreateForm opens from template selector, person_id should be populated</li>
          <li>Template application should NOT overwrite the person_id</li>
          <li>The task should be created for the specified customer/person</li>
          <li>Person ID should persist even after template is applied</li>
          <li><strong>StateSelectBtn flow:</strong> When changing state via StateSelectBtn, person_id should be set dynamically</li>
        </ul>
      </div>

      <!-- Test StateSelectBtn Flow -->
      <div class="rounded-lg border border-purple-200 bg-purple-50 p-4">
        <h4 class="font-medium text-purple-800">Test StateSelectBtn Flow:</h4>
        <div class="mt-2 space-y-2">
          <Button
            label="Test Dynamic Person ID Setting"
            icon="pi pi-user"
            @click="testDynamicPersonId"
            class="bg-purple-600 px-4 py-2 font-medium text-white hover:bg-purple-700"
          />
          <p class="text-sm text-purple-700">
            This simulates the flow when StateSelectBtn triggers template selector with a specific person_id
          </p>
        </div>
      </div>
    </div>

    <!-- Task Template Selector Component -->
    <TaskTemplateSelector
      ref="taskTemplateSelector"
      :person-id="currentPersonId"
      @task-created="handleTaskCreated"
      @template-selected="handleTemplateSelected"
      @cancelled="handleCancelled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Button from "primevue/button";
import InputNumber from "primevue/inputnumber";
import TaskTemplateSelector from "./TaskTemplateSelector.vue";

// Refs
const taskTemplateSelector = ref<InstanceType<typeof TaskTemplateSelector>>();
const testPersonId = ref<number>(123);
const currentPersonId = ref<number>(123);
const lastAction = ref<string>("");
const lastTimestamp = ref<string>("");

// Methods
const updatePersonId = () => {
  currentPersonId.value = testPersonId.value;
  lastAction.value = `Updated Person ID to ${testPersonId.value}`;
  lastTimestamp.value = new Date().toLocaleTimeString();
};

const openTemplateSelector = async () => {
  if (taskTemplateSelector.value) {
    await taskTemplateSelector.value.showTemplateSelector();
    lastAction.value = "Opened template selector manually";
    lastTimestamp.value = new Date().toLocaleTimeString();
  }
};

const simulateTaskCompletion = async () => {
  // Simulate a task completion that would trigger the template selector
  const taskId = Math.floor(Math.random() * 1000) + 1;
  const serial = Math.floor(Math.random() * 5) + 1;

  if (taskTemplateSelector.value) {
    const dialogShown = await taskTemplateSelector.value.checkAndShowTemplateSelector(taskId, serial);
    lastAction.value = `Simulated task completion (ID: ${taskId}, Serial: ${serial}, Dialog: ${dialogShown ? 'Shown' : 'Not shown'})`;
    lastTimestamp.value = new Date().toLocaleTimeString();
  }
};

const testDynamicPersonId = async () => {
  // Simulate the StateSelectBtn flow where person_id is set dynamically
  const dynamicPersonId = Math.floor(Math.random() * 1000) + 100;

  if (taskTemplateSelector.value) {
    // First set the person_id dynamically (like StateSelectBtn would do)
    taskTemplateSelector.value.setPersonId(dynamicPersonId);

    // Then show the template selector
    await taskTemplateSelector.value.showTemplateSelector();

    lastAction.value = `Dynamic Person ID test (ID: ${dynamicPersonId})`;
    lastTimestamp.value = new Date().toLocaleTimeString();
  }
};

// Event handlers
const handleTaskCreated = () => {
  lastAction.value = "Task created successfully";
  lastTimestamp.value = new Date().toLocaleTimeString();
  console.log("Task created successfully for person ID:", currentPersonId.value);
};

const handleTemplateSelected = (templateId: string) => {
  lastAction.value = `Template selected: ${templateId}`;
  lastTimestamp.value = new Date().toLocaleTimeString();
  console.log("Template selected:", templateId, "for person ID:", currentPersonId.value);
};

const handleCancelled = () => {
  lastAction.value = "Template selection cancelled";
  lastTimestamp.value = new Date().toLocaleTimeString();
  console.log("Template selection cancelled");
};
</script>
