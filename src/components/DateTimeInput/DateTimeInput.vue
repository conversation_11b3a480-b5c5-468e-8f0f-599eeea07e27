<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed } from "vue";

interface Props {
  modelValue?: Date | null;
  dateFormat?: string;
  timeOnly?: boolean;
  currentDate?: Date;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  dateFormat: "DD/MM/YYYY",
  timeOnly: false,
  currentDate: undefined,
});

const emit = defineEmits<{
  (e: "update:modelValue", value: Date | null): void;
}>();

// Format conversion for time
const convertFormat = (format: string): string => {
  if (props.timeOnly) {
    return "HH:mm";
  }
  return format.replace(/dd/g, "DD").replace(/yyyy/g, "YYYY").replace(/yy/g, "YY");
};

const dateFormat = computed(() => convertFormat(props.dateFormat));

// Mask format for time
const maskFormat = computed(() => {
  if (props.timeOnly) {
    return "99:99";
  }
  return props.dateFormat.replace(/dd/g, "99").replace(/MM/g, "99").replace(/yy/g, "99");
});

const placeholder = computed(() => {
  if (props.timeOnly) {
    return "HH:mm";
  }
  return props.dateFormat.replace(/dd/g, "DD").replace(/MM/g, "MM").replace(/yy/g, "YY");
});

// Time validation
const isValidTime = (timeStr: string): boolean => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60;
};

// Parse datetime function
const parseDateTime = (value: string): Date | null => {
  if (props.timeOnly) {
    if (!isValidTime(value)) return null;

    const [hours, minutes] = value.split(":").map(Number);
    const date = props.modelValue ? new Date(props.modelValue) : new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  }

  return parseDate(value, props.dateFormat);
};

// Date validation
const isValidDate = (date: Date): boolean => {
  return date instanceof Date && !isNaN(date.getTime());
};

// Parse string to date
const parseDate = (dateStr: string, format: string): Date | null => {
  const parts = dateStr.split("/");
  if (parts.length !== 3) return null;

  let [day, month, year] = parts.map(Number);

  // Handle 2-digit year
  if (year < 100) {
    const currentYear = new Date().getFullYear();
    const century = Math.floor(currentYear / 100) * 100;
    year = year >= 50 ? century - 100 + year : century + year;
  }

  const date = new Date(year, month - 1, day);
  return isValidDate(date) ? date : null;
};

const onDateSelect = (date: Date) => {
  if (date && isValidDate(date)) {
    emit("update:modelValue", date);
  }
};

// Replace with computed getter/setter
const inputValue = computed({
  get: () => {
    if (!props.modelValue || !isValidDate(props.modelValue)) {
      return "";
    }
    return useDateFormat(props.modelValue, dateFormat.value).value;
  },
  set: (value: string) => {
    if (!value || value.includes("_")) {
      emit("update:modelValue", null);
      return;
    }

    // Nếu là time only mode
    if (props.timeOnly && isValidTime(value)) {
      const [hours, minutes] = value.split(":").map(Number);
      // Sử dụng currentDate từ prop hoặc modelValue hiện tại
      const baseDate = props.currentDate || props.modelValue || new Date();
      const newDate = new Date(baseDate);
      newDate.setHours(hours, minutes, 0, 0);
      emit("update:modelValue", newDate);
      return;
    }

    // Nếu là date mode
    const parsedDate = parseDate(value, props.dateFormat);
    if (parsedDate) {
      // Nếu có modelValue, giữ nguyên giờ từ modelValue
      if (props.modelValue) {
        parsedDate.setHours(props.modelValue.getHours(), props.modelValue.getMinutes(), 0, 0);
      }
      emit("update:modelValue", parsedDate);
    }
  },
});
</script>

<template>
  <div class="relative w-full">
    <InputMask
      v-model="inputValue"
      :mask="maskFormat"
      :placeholder="placeholder"
      :autoClear="false"
      class="w-full"
    />

    <div class="absolute right-2 top-2.5 h-full">
      <DatePicker
        :modelValue="props.modelValue"
        @update:modelValue="emit('update:modelValue', $event as Date | null)"
        :dateFormat="dateFormat"
        @date-select="onDateSelect"
        :timeOnly="timeOnly"
        showIcon
        hourFormat="24"
        :showSeconds="false"
        :pt="{
          root: {
            class:
              '[&_.p-datepicker-input]:hidden [&_.p-datepicker-input]:h-full [&_.p-datepicker-dropdown]:w-full [&_.p-datepicker-dropdown]:bg-transparent [&_.p-datepicker-dropdown]:border-transparent',
          },
        }"
      >
        <template #inputicon="slotProps">
          <i
            :class="[timeOnly ? 'pi pi-clock' : 'pi pi-calendar']"
            @click="slotProps.clickCallback"
          />
        </template>
      </DatePicker>
    </div>
  </div>
</template>
