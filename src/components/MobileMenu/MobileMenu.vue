<script setup lang="ts">
import SimpleBar from "simplebar";
import { twMerge } from "tailwind-merge";
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";

import { useMenuStore, type FormattedMenuItem } from "@/stores/menu-store";
import Lucide from "../../base-components/Lucide";

import { enter, leave } from "./mobile-menu";

import logoUrl from "../../assets/images/logo.svg";

const router = useRouter();
const menuStore = useMenuStore();

const { formattedMenu } = storeToRefs(menuStore);

const activeMobileMenu = ref(false);
const scrollableRef = ref<HTMLDivElement>();
const manuallyOpenedDropdowns = ref<Record<string, boolean>>({});

const setActiveMobileMenu = (active: boolean) => {
  activeMobileMenu.value = active;
};

const handleMenuClick = (menu: FormattedMenuItem) => {
  const key = menu.title;

  if (menu.subMenu) {
    manuallyOpenedDropdowns.value[key] = !manuallyOpenedDropdowns.value[key];
  } else if (menu.pageName) {
    menuStore.navigateTo(menu);
    setActiveMobileMenu(false);
  }
};

onMounted(() => {
  if (scrollableRef.value) {
    new SimpleBar(scrollableRef.value);
  }
});
</script>

<template>
  <!-- BEGIN: Mobile Menu -->
  <div
    :class="[
      'mobile-menu fixed z-[60] -mx-3 -mt-5 mb-6 w-full border-b border-white/[0.08] bg-primary/90 dark:bg-darkmode-800/90 sm:-mx-8 md:hidden',
      'before:fixed before:inset-x-0 before:z-10 before:h-screen before:w-full before:bg-black/90',
      !activeMobileMenu && 'before:invisible before:opacity-0',
      activeMobileMenu && 'before:visible before:opacity-100',
    ]"
  >
    <div class="flex h-[70px] items-center justify-between">
      <a href="" class="mr-auto flex">
        <img alt="Midone Tailwind HTML Admin Template" class="w-6" :src="logoUrl" />
      </a>
      <a href="#" @click="(e) => e.preventDefault()">
        <Lucide
          icon="BarChart2"
          class="h-8 w-8 -rotate-90 transform text-white"
          @click="() => setActiveMobileMenu(!activeMobileMenu)"
        />
      </a>
    </div>
    <div
      ref="scrollableRef"
      :class="
        twMerge([
          'left-0 top-0 z-20 -ml-[100%] h-screen w-[270px] bg-primary transition-all duration-300 ease-in-out dark:bg-darkmode-800',
          '[&[data-simplebar]]:fixed [&_.simplebar-scrollbar]:before:bg-black/50',
          activeMobileMenu && 'ml-0',
        ])
      "
    >
      <a
        href="#"
        :class="[
          'fixed right-0 top-0 mr-4 mt-4 transition-opacity duration-200 ease-in-out',
          !activeMobileMenu && 'invisible opacity-0',
          activeMobileMenu && 'visible opacity-100',
        ]"
        @click="(e) => e.preventDefault()"
      >
        <Lucide
          icon="XCircle"
          class="h-8 w-8 -rotate-90 transform text-white"
          @click="() => setActiveMobileMenu(!activeMobileMenu)"
        />
      </a>
      <ul class="py-2">
        <!-- BEGIN: First Child -->
        <template v-for="(menu, menuKey) in formattedMenu" :key="menuKey">
          <li v-if="(menu as any) === 'divider'" class="menu__divider my-6"></li>
          <li v-else>
            <a
              :href="
                (menu as FormattedMenuItem).subMenu && !(menu as FormattedMenuItem).pageName
                  ? '#'
                  : ((pageName: string | undefined) => {
                      try {
                        return pageName ? router.resolve({ name: pageName }).fullPath : '#';
                      } catch (err) {
                        return '#';
                      }
                    })((menu as FormattedMenuItem).pageName)
              "
              :class="[(menu as FormattedMenuItem).active ? 'menu menu--active' : 'menu']"
              @click.prevent="handleMenuClick(menu as FormattedMenuItem)"
            >
              <div class="menu__icon">
                <Lucide :icon="(menu as FormattedMenuItem).icon" />
              </div>
              <div class="menu__title">
                {{ (menu as FormattedMenuItem).title }}
                <div
                  v-if="(menu as FormattedMenuItem).subMenu"
                  :class="[
                    'menu__sub-icon',
                    manuallyOpenedDropdowns[(menu as FormattedMenuItem).title] &&
                      'rotate-180 transform',
                  ]"
                >
                  <Lucide icon="ChevronDown" />
                </div>
              </div>
            </a>
            <Transition @enter="enter" @leave="leave">
              <ul
                v-if="
                  (menu as FormattedMenuItem).subMenu &&
                  manuallyOpenedDropdowns[(menu as FormattedMenuItem).title]
                "
                :class="{
                  'menu__sub-open': manuallyOpenedDropdowns[(menu as FormattedMenuItem).title],
                }"
              >
                <li
                  v-for="(subMenu, subMenuKey) in (menu as FormattedMenuItem).subMenu"
                  :key="subMenuKey"
                >
                  <a
                    :href="
                      subMenu.subMenu && !subMenu.pageName
                        ? '#'
                        : ((pageName: string | undefined) => {
                            try {
                              return pageName ? router.resolve({ name: pageName }).fullPath : '#';
                            } catch (err) {
                              return '#';
                            }
                          })(subMenu.pageName)
                    "
                    :class="[subMenu.active ? 'menu menu--active' : 'menu']"
                    @click.prevent="handleMenuClick(subMenu as FormattedMenuItem)"
                  >
                    <div class="menu__icon">
                      <Lucide :icon="subMenu.icon" />
                    </div>
                    <div class="menu__title">
                      {{ subMenu.title }}
                      <div
                        v-if="subMenu.subMenu"
                        :class="[
                          'menu__sub-icon',
                          manuallyOpenedDropdowns[subMenu.title] && 'rotate-180 transform',
                        ]"
                      >
                        <Lucide icon="ChevronDown" />
                      </div>
                    </div>
                  </a>
                  <Transition @enter="enter" @leave="leave">
                    <ul
                      v-if="subMenu.subMenu && manuallyOpenedDropdowns[subMenu.title]"
                      :class="{ 'menu__sub-open': manuallyOpenedDropdowns[subMenu.title] }"
                    >
                      <li
                        v-for="(lastSubMenu, lastSubMenuKey) in subMenu.subMenu"
                        :key="lastSubMenuKey"
                      >
                        <a
                          :href="
                            lastSubMenu.subMenu && !lastSubMenu.pageName
                              ? '#'
                              : ((pageName: string | undefined) => {
                                  try {
                                    return pageName
                                      ? router.resolve({ name: pageName }).fullPath
                                      : '#';
                                  } catch (err) {
                                    return '#';
                                  }
                                })(lastSubMenu.pageName)
                          "
                          :class="[lastSubMenu.active ? 'menu menu--active' : 'menu']"
                          @click.prevent="handleMenuClick(lastSubMenu as FormattedMenuItem)"
                        >
                          <div class="menu__icon">
                            <Lucide :icon="lastSubMenu.icon" />
                          </div>
                          <div class="menu__title">{{ lastSubMenu.title }}</div>
                        </a>
                      </li>
                    </ul>
                  </Transition>
                </li>
              </ul>
            </Transition>
          </li>
        </template>
        <!-- END: First Child -->
      </ul>
    </div>
  </div>
  <!-- END: Mobile Menu -->

  <!-- BEGIN: Content -->
  <div
    class="relative z-0 mt-5 min-h-screen min-w-0 max-w-full flex-1 bg-slate-100 dark:bg-darkmode-700 md:max-w-none"
  >
    <RouterView />
  </div>
  <!-- END: Content -->
</template>
