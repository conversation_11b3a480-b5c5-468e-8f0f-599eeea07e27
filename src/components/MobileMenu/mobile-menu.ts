import { Router } from "vue-router";

import { FormattedMenu } from "../../layouts/SideMenu/side-menu";
import { slideDown, slideUp } from "../../utils/helper";

const linkTo = (
  menu: FormattedMenu,
  router: Router,
  setActiveMobileMenu: (active: boolean) => void,
) => {
  if (menu.subMenu) {
    menu.activeDropdown = !menu.activeDropdown;
  } else if (menu.pageName !== undefined) {
    setActiveMobileMenu(false);
    router.push({
      name: menu.pageName,
    });
  }
};

const enter = (el: Element) => {
  slideDown(el as HTMLElement, 100);
};

const leave = (el: Element) => {
  slideUp(el as HTMLElement, 100);
};

export { linkTo, enter, leave };
