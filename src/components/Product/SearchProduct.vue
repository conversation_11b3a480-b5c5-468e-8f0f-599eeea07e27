<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }">
    <div class="w-[30rem]">
      <div v-if="filteredProducts.length > 0" class="p-2">
        <ul
          class="m-0 h-[25vh] snap-y scroll-py-1 list-none space-y-1 overflow-hidden overflow-y-auto overscroll-contain p-1"
        >
          <li
            v-for="product in filteredProducts"
            :key="product.id"
            class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
            :class="{
              'bg-soft text-primary ring-1 ring-highlight': isProductSelected(product),
            }"
            @click="handleItemClick(product)"
          >
            <div class="flex-1">
              <span class="hyphens-auto font-medium">
                <HighlightText :highlight="searchQuery" :text="product.name" />
              </span>
              <div class="text-sm">
                <span class="text-slate-500">#{{ product.code }}</span>
                <i class="pi pi-circle-fill px-2 align-middle text-[3px] text-slate-400" />
                <Money :amount="product.price" class="text-success" />
              </div>
            </div>
            <div class="ml-3 text-gray-600 dark:text-gray-400">
              <div
                v-if="isProductSelected(product)"
                class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                :class="{ 'bg-primary': isProductSelected(product) }"
              ></div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else class="p-2">
        <Empty />
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <IconField class="mb-2">
          <InputText
            autofocus
            v-model="searchQuery"
            class="w-full text-sm"
            placeholder="Tìm kiếm"
            type="text"
          />
          <InputIcon
            :class="searchQuery ? 'pi-times' : 'pi-search'"
            class="pi cursor-pointer"
            @click="
              () => {
                searchQuery = '';
              }
            "
          />
        </IconField>

        <!-- Filter Buttons -->
        <div class="flex flex-col gap-2 text-sm">
          <SelectButton
            v-model="selectedType"
            :options="typeOptions"
            optionLabel="name"
            optionValue="value"
            :pt="{ root: { class: 'w-full grid grid-cols-3' } }"
            aria-labelledby="product-type-filter"
            @change="filterProducts"
          />

          <SelectButton
            v-if="props.showCollectionFilter"
            v-model="selectedCollection"
            :options="collectionOptions"
            optionLabel="name"
            optionValue="value"
            :pt="{ root: { class: 'w-full grid grid-cols-2' } }"
            aria-labelledby="collection-type-filter"
            @change="filterProducts"
          />
        </div>
      </div>
    </div>
  </Popover>
</template>

<script lang="ts" setup>
import Popover from "primevue/popover";
import { computed, onMounted, PropType, ref } from "vue";

import { CommonStatus } from "@/api/bcare-enum";
import type { Product } from "@/api/bcare-types-v2";
import { COLLECTION_TYPES, PRODUCT_TYPES, ProductFilters } from "@/api/product";
import Empty from "@/base-components/Empty";
import HighlightText from "@/base-components/HighlightText.vue";
import Money from "@/base-components/Money.vue";
import useProduct from "@/hooks/useProduct";
import { normalizeVietnamese } from "@/utils/string";

const props = defineProps({
  selectedProduct: {
    type: Object as PropType<Product | null>,
    default: null,
  },
  selectedProducts: {
    type: Array as PropType<Product[]>,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  defaultFilters: {
    type: Object as PropType<ProductFilters>,
    default: () => ({}),
  },
  showCollectionFilter: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits<{
  (e: "update:selectedProduct", value: Product | null): void;
  (e: "update:selectedProducts", value: Product[]): void;
}>();

const { products } = useProduct({ autoLoad: true });

const searchQuery = ref("");
const selectedType = ref(props.defaultFilters.type || "");
const selectedCollection = ref(props.defaultFilters.collection || "");

const typeOptions = [
  { name: "Dịch vụ", value: PRODUCT_TYPES.SERVICE },
  { name: "Sản phẩm", value: PRODUCT_TYPES.ITEM },
  { name: "Quà tặng", value: PRODUCT_TYPES.GIFT },
];

const collectionOptions = [
  { name: "Chính", value: COLLECTION_TYPES.PRIMARY },
  { name: "Phụ", value: COLLECTION_TYPES.SECONDARY },
];

const filteredProducts = computed(() => {
  const normalizedSearch = normalizeVietnamese(searchQuery.value.toLowerCase());
  return Object.values(products.value).filter((product: Product) => {
    // Filter out inactive products (status = 1)
    if (product.status === CommonStatus.INACTIVE) return false;

    const normalizedName = normalizeVietnamese(product.name.toLowerCase());
    const normalizedCode = normalizeVietnamese(product.code.toLowerCase());
    const normalizedPrice = normalizeVietnamese(product.price.toString().toLowerCase());

    const matchesSearch =
      normalizedName.includes(normalizedSearch) ||
      normalizedCode.includes(normalizedSearch) ||
      normalizedPrice.includes(normalizedSearch);

    const matchesType = !selectedType.value || product.type === selectedType.value;

    const matchesCollection =
      !selectedCollection.value || product.collection?.includes(selectedCollection.value);

    return matchesSearch && matchesType && matchesCollection;
  });
});

const isProductSelected = (product: Product) => {
  if (props.multiple) {
    return props.selectedProducts.some((selected) => selected.id === product.id);
  } else {
    return props.selectedProduct?.id === product.id;
  }
};

const handleItemClick = (product: Product) => {
  if (props.multiple) {
    const newSelection = [...props.selectedProducts];
    const index = newSelection.findIndex((p) => p.id === product.id);
    if (index === -1) {
      newSelection.push(product);
    } else {
      newSelection.splice(index, 1);
    }
    emit("update:selectedProducts", newSelection);
  } else {
    emit("update:selectedProduct", product);
    popoverRef.value?.hide();
  }
};

const filterProducts = () => {
  // No need to call API, just update the computed filteredProducts
};

onMounted(() => {
  if (props.defaultFilters.type) {
    selectedType.value = props.defaultFilters.type;
  }
  if (props.defaultFilters.collection) {
    selectedCollection.value = props.defaultFilters.collection;
  }
});

const popoverRef = ref();

defineExpose({
  popoverRef,
  filterProducts,
});
</script>
