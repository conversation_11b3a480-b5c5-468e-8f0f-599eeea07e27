<template>
  <div class="ml-2 mt-1">
    <div v-for="operation in operations" :key="operation.id" class="relative pl-3">
      <span
        class="absolute -top-4 left-0 h-7 w-2 rounded-bl border-b border-l border-slate-300"
      ></span>
      <div class="flex items-center text-slate-600">
        <span class="text-sm">{{ operation.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OperationResponse } from "@/api/bcare-types-v2";

defineProps<{
  operations: OperationResponse[];
}>();
</script>
