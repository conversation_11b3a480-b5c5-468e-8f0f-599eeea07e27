<template>
  <div class="relative w-full">
    <div
      ref="toggleContainer"
      @click="toggle"
      class="cursor-pointer break-words text-gray-700 decoration-1 underline-offset-2 hover:underline hover:decoration-dotted"
    >
      <!-- Single Selection Display -->
      <template v-if="!multiple || (multiple && selectedUsers.length === 1)">
        <div v-if="displayedUser" class="flex items-center gap-2">
          <UserAvatar :user="displayedUser" :size="avatarSize" />
          <span class="text-sm">{{ displayedUser.name }}</span>
        </div>
        <div v-else class="flex items-center gap-2 text-gray-500">
          <span class="text-sm text-gray-400">{{ placeholder }}</span>
          <i class="pi pi-user-plus text-gray-400" />
        </div>
      </template>

      <!-- Multiple Selection Display (only when multiple=true and more than 1 selection) -->
      <template v-else>
        <div
          class="inline-flex flex-wrap items-center gap-1 rounded px-1.5 py-0.5 transition-colors hover:bg-gray-100/50"
        >
          <template v-if="selectedUsers.length > 0">
            <UserAvatar
              v-for="user in selectedUsers.slice(0, maxDisplay)"
              :key="user.id"
              :user="user"
              :size="avatarSize"
              class="transition-transform hover:scale-110"
            />
            <div
              v-if="selectedUsers.length > maxDisplay"
              class="flex items-center justify-center rounded-full bg-gray-200 text-xs font-medium transition-transform hover:scale-110"
              :style="{
                width: `${avatarSize}px`,
                height: `${avatarSize}px`,
              }"
            >
              +{{ selectedUsers.length - maxDisplay }}
            </div>
          </template>
          <div v-else class="flex items-center gap-2 text-gray-500">
            <span class="text-sm text-gray-400">{{ placeholder }}</span>
            <i class="pi pi-user-plus text-gray-400" />
          </div>
        </div>
      </template>
    </div>

    <Popover ref="popover">
      <div :style="{ width: `${width}px`, minWidth: '300px' }">
        <InputText
          ref="searchInput"
          v-model="searchQuery"
          placeholder="Tìm kiếm người dùng..."
          class="mb-2 w-full"
          size="small"
        />

        <div class="relative">
          <!-- Department Name -->
          <div
            class="sticky top-0 z-10 flex items-center justify-between bg-white py-1.5 font-medium text-primary-500"
          >
            <span>{{ departmentName }}</span>
            <template v-if="multiple">
              <i :class="getDepartmentIconClass" @click="toggleAllUsers" class="cursor-pointer" />
            </template>
          </div>

          <div class="max-h-60 snap-y snap-mandatory overflow-y-auto scroll-smooth">
            <!-- Empty State -->
            <div
              v-if="filteredUsers.length === 0"
              class="flex flex-col items-center justify-center py-8 text-center"
            >
              <i class="pi pi-search mb-2 text-2xl text-gray-400" />
              <p class="text-sm text-gray-500">
                {{
                  searchQuery
                    ? `Không tìm thấy kết quả cho "${searchQuery}"`
                    : "Không có người dùng"
                }}
              </p>
            </div>

            <!-- User List -->
            <div v-else>
              <ul class="m-0 list-none p-0">
                <li
                  v-for="user in filteredUsers"
                  :key="user.id"
                  @click="selectUser(user)"
                  class="flex cursor-pointer snap-start items-center rounded-md p-2 hover:bg-gray-100"
                >
                  <UserAvatar :user="user" :size="avatarSize" class="mr-2" />
                  <span class="flex-1">
                    <HighlightText :text="user.name" :highlight="searchQuery" />
                  </span>
                  <i v-if="isUserSelected(user)" class="pi pi-check ml-auto text-blue-500" />
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Footer for multiple selection -->
        <div v-if="multiple" class="flex justify-end border-t border-gray-200 pt-2">
          <Button label="Xóa chọn" severity="danger" text @click="clearSelection" size="small" />
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup lang="ts">
import { useElementSize } from "@vueuse/core";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Popover from "primevue/popover";
import { computed, nextTick, ref } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import HighlightText from "@/base-components/HighlightText.vue";
import useDepartment from "@/hooks/useDepartment";
import useUser from "@/hooks/useUser";
import { normalizeVietnamese } from "@/utils/string";

import UserAvatar from "./UserAvatar.vue";

interface Props {
  modelValue?: number | number[];
  placeholder?: string;
  departmentId: number;
  multiple?: boolean;
  maxDisplay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  placeholder: "Chọn người dùng",
  multiple: false,
  maxDisplay: 8,
});

const emit = defineEmits<{
  "update:modelValue": [value: number | number[] | undefined];
}>();

// Composables
const { users } = useUser({ autoLoad: true });
const { getDepartmentNameById } = useDepartment();

// Refs
const popover = ref();
const searchInput = ref();
const searchQuery = ref("");
const toggleContainer = ref<HTMLElement | null>(null);
const { width } = useElementSize(toggleContainer);

// Constants
const avatarSize = 25;

// Computed
const departmentName = computed(() => getDepartmentNameById(props.departmentId));

const departmentUsers = computed(() =>
  users.value.filter((user) => user.department_id === props.departmentId),
);

const filteredUsers = computed(() => {
  const query = normalizeVietnamese(searchQuery.value.trim().toLowerCase());
  if (!query) return departmentUsers.value;

  return departmentUsers.value.filter(
    (user) =>
      normalizeVietnamese(user.name.toLowerCase()).includes(query) ||
      normalizeVietnamese(user.username.toLowerCase()).includes(query) ||
      (user.email && normalizeVietnamese(user.email.toLowerCase()).includes(query)),
  );
});

// Single selection computed
const selectedUser = computed(() =>
  props.multiple ? null : departmentUsers.value.find((user) => user.id === props.modelValue),
);

// Multiple selection computed
const selectedUsers = computed(() => {
  if (!props.multiple) return [];
  const selectedIds = Array.isArray(props.modelValue) ? props.modelValue : [];
  return departmentUsers.value.filter((user) => selectedIds.includes(user.id));
});

const getDepartmentIconClass = computed(() => {
  const selectedCount = selectedUsers.value.length;
  const totalCount = departmentUsers.value.length;

  return {
    "pi-check-square": selectedCount === totalCount,
    "pi-minus-circle": selectedCount > 0 && selectedCount < totalCount,
    "pi-square": selectedCount === 0,
    pi: true,
    "text-blue-500": true,
  };
});

// Add new computed for displayed user
const displayedUser = computed(() => {
  if (!props.multiple) {
    return selectedUser.value;
  }
  // When multiple=true and exactly 1 user is selected
  return selectedUsers.value.length === 1 ? selectedUsers.value[0] : null;
});

// Methods
const toggle = async (event: Event) => {
  popover.value.toggle(event);
  await nextTick();
  if (searchInput.value?.$el) {
    searchInput.value.$el.focus();
  }
};

const isUserSelected = (user: UserResponse): boolean => {
  if (props.multiple) {
    return Array.isArray(props.modelValue) && props.modelValue.includes(user.id);
  }
  return user.id === props.modelValue;
};

const selectUser = (user: UserResponse) => {
  if (props.multiple) {
    const currentValue = Array.isArray(props.modelValue) ? props.modelValue : [];
    const newValue = isUserSelected(user)
      ? currentValue.filter((id) => id !== user.id)
      : [...currentValue, user.id];
    emit("update:modelValue", newValue);
  } else {
    emit("update:modelValue", user.id);
    popover.value.hide();
  }
  searchQuery.value = "";
};

const toggleAllUsers = () => {
  if (!props.multiple) return;

  const allSelected = selectedUsers.value.length === departmentUsers.value.length;
  const newValue = allSelected ? [] : departmentUsers.value.map((user) => user.id);

  emit("update:modelValue", newValue);
};

const clearSelection = () => {
  emit("update:modelValue", props.multiple ? [] : undefined);
  searchQuery.value = "";
};
</script>
