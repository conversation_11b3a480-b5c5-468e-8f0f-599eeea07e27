<!-- UserAvatarGroup.vue -->
<template>
  <div class="flex items-center">
    <div
      class="group flex"
      :class="{
        '-space-x-3 transition-all duration-300 ease-in-out hover:translate-x-0.5':
          !expand && animationStyle === 'expand',
      }"
      @click.stop
    >
      <UserAvatar
        v-for="(user, index) in displayUsers"
        :user-id="user.id"
        :key="user.id"
        :size="size"
        :class="[
          'cursor-pointer transition-all duration-300',
          getAvatarAnimationClasses(index),
          { 'mr-2': expand && index < displayUsers.length - 1 },
        ]"
        :style="getAvatarStyle(index)"
        @click="handleAvatarClick(user)"
      />
      <div
        v-if="remainingCount > 0"
        @click.stop="handleRemainingCountClick"
        class="flex cursor-pointer items-center justify-center rounded-full bg-gray-200 font-semibold text-gray-600 transition-all duration-300"
        :class="[
          sizeClasses,
          { 'ml-2': expand },
          { 'ml-[-0.75rem]': !expand },
          animationStyle === 'lift' ? 'hover:shadow-md' : '',
        ]"
      >
        +{{ remainingCount }}
      </div>
    </div>
  </div>

  <Popover ref="remainingUsersPanel" @click.stop>
    <div class="max-h-60 overflow-y-auto" @click.stop>
      <div
        v-for="user in remainingUsers"
        :key="typeof user === 'number' ? user : user.id"
        class="flex cursor-pointer items-center p-1 px-0 hover:bg-gray-100"
        @click.stop="handleAvatarClick(typeof user === 'number' ? getUserById(user)! : user)"
      >
        <UserAvatar
          :user="typeof user === 'number' ? getUserById(user)! : user"
          :size="size"
          class="mr-1"
        />
        <span class="text-sm">{{
          typeof user === "number" ? getUserById(user)?.name : user.name
        }}</span>
      </div>
    </div>
  </Popover>
</template>

<script setup lang="ts">
import Popover from "primevue/popover";
import { computed, onMounted, ref, watch } from "vue";

import { User, UserResponse, UserShort } from "@/api/bcare-types-v2";
import useUser from "@/hooks/useUser";

import UserAvatar from "./UserAvatar.vue";

type AnimationStyle = "expand" | "lift" | "none";

interface Props {
  users: (UserResponse | User | UserShort)[] | number[];
  maxDisplay?: number;
  expand?: boolean;
  size?: "small" | "normal" | "large" | number;
  expandedSpacing?: number | string;
  animationStyle?: AnimationStyle;
}

const props = withDefaults(defineProps<Props>(), {
  users: () => [],
  maxDisplay: 5,
  expand: false,
  size: "normal",
  expandedSpacing: 2,
  animationStyle: "expand",
});

const emit = defineEmits<{
  (e: "click:avatar", user: UserResponse | User | UserShort): void;
  (e: "click:remaining-count", event: MouseEvent): void;
}>();

const { getUserById } = useUser({ autoLoad: true });

const displayUsers = ref<(UserResponse | User | UserShort)[]>([]);

const updateDisplayUsers = async () => {
  if (props.users.length > 0) {
    if (typeof props.users[0] === "number") {
      displayUsers.value = (props.users as number[])
        .slice(0, props.maxDisplay)
        .map((id) => getUserById(id))
        .filter((user): user is UserResponse => user !== undefined);
    } else {
      // If users is an array of objects
      displayUsers.value = (props.users as (UserResponse | User | UserShort)[]).slice(
        0,
        props.maxDisplay,
      );
    }
  } else {
    displayUsers.value = [];
  }
};

onMounted(updateDisplayUsers);

watch(() => props.users, updateDisplayUsers, { deep: true });

const remainingCount = computed(() => {
  return Math.max(0, props.users.length - props.maxDisplay);
});

const handleAvatarClick = (user: UserResponse | User | UserShort) => {
  emit("click:avatar", user);
};

const handleRemainingCountClick = (event: MouseEvent) => {
  remainingUsersPanel.value?.toggle(event);
  emit("click:remaining-count", event);
};

const sizeClasses = computed(() => {
  switch (props.size) {
    case "small":
      return "w-8 h-8 text-xs";
    case "large":
      return "w-12 h-12 text-base";
    default:
      return "w-10 h-10 text-sm";
  }
});

const remainingUsersPanel = ref<InstanceType<typeof Popover> | null>(null);

const remainingUsers = computed(() => props.users.slice(props.maxDisplay));

// Helper function to get animation classes based on animation style
const getAvatarAnimationClasses = (index: number) => {
  const classes = [];

  if (props.animationStyle === "expand") {
    if (!props.expand) {
      classes.push("group-hover:z-10");
      if (index !== 0) classes.push("group-hover:ml-2.5");
      if (index !== displayUsers.value.length - 1) classes.push("group-hover:mr-2.5");
    }
  } else if (props.animationStyle === "lift") {
    classes.push("hover:-translate-y-1 hover:z-20 relative hover:shadow-lg duration-300");
    if (!props.expand) classes.push("-ml-3");
  } else if (!props.expand) {
    classes.push("-ml-3");
  }

  return classes;
};

// Helper function to get avatar styles
const getAvatarStyle = (index: number) => {
  const styles: Record<string, string> = {};

  if (props.animationStyle === "expand" && !props.expand) {
    styles.zIndex = (displayUsers.value.length - index).toString();
  }

  return styles;
};
</script>
