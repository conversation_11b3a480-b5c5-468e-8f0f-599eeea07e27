<script setup lang="ts">
import { computed } from "vue";
// Import necessary types
import type { UserShort, DealUserResponse, DealUserRating } from "@/api/bcare-types-v2";
import UserAvatar from "@/components/User/UserAvatar.vue";

// Simplified interface - no need to extend UserShort
interface CombinedUserInfo {
  id: number; // This will store the user_id
  name: string;
  role?: string;
  averageScore?: number | null;
  // Add a unique key for rendering duplicates
  key: string;
}

const props = defineProps<{
  saleUser?: UserShort | null;
  dealAssignment?: DealUserResponse[] | any[];
}>();

const emit = defineEmits<{
  // Emit the simplified info or just the id? Let's emit the info object.
  (e: "user-click", user: Omit<CombinedUserInfo, "key">): void; // Exclude key from emitted object
}>();

// Role display names map (moved earlier for better organization)
const roleDisplayNames: Record<string, string> = {
  consultant_doctor: "BS Tư vấn",
  doctor_assistant: "Tr<PERSON> lý BS",
  treatment_doctor: "BS Điều trị",
  sale: "Sale",
};
const averageScoreCategory = "average_score"; // Define category constant

// Combine sale user and deal assignment users into an array
const combinedUsers = computed<CombinedUserInfo[]>(() => {
  const users: CombinedUserInfo[] = []; // Use an array instead of a Map

  // Process deal assignment
  if (props.dealAssignment) {
    props.dealAssignment.forEach((assignment: any) => {
      // Attempt to safely access properties, handle potential variations if 'any' type is used
      const userId = assignment?.user_id ?? assignment?._custom?.value?.user_id;
      const userName = assignment?.name ?? assignment?._custom?.value?.name;
      const assignmentRole = assignment?.role ?? assignment?._custom?.value?.role;
      // Get the ratings array
      const assignmentRatings: DealUserRating[] | undefined =
        assignment?.ratings ?? assignment?._custom?.value?.ratings;

      // Find the average score rating from the new structure
      const averageRatingObj = assignmentRatings?.find(
        (rating) => rating.category === averageScoreCategory,
      );
      // Get the rating value, default to null if not found
      const averageScoreValue = averageRatingObj?.rating ?? null;

      if (userId && userName && assignmentRole) {
        users.push({
          id: userId,
          name: userName,
          role: assignmentRole,
          // Use the value found from ratings array
          averageScore: averageScoreValue,
          key: `${userId}-${assignmentRole}`, // Create unique key
        });
      } else if (userId && userName && !assignmentRole) {
        // Handle case where role might be missing but is needed for the key
        console.warn(
          `User role missing for assignment with user_id: ${userId}. Assigning default key part.`,
        );
        users.push({
          id: userId,
          name: userName,
          role: undefined, // Keep role undefined if missing
          // Use the value found from ratings array
          averageScore: averageScoreValue,
          key: `${userId}-role_missing_${Math.random()}`, // Less ideal fallback key
        });
      } else if (userId && !userName) {
        console.warn(`User name missing for assignment with user_id: ${userId}`);
        // Optionally add with a placeholder name if needed
      }
    });
  }

  // Process sale user
  if (props.saleUser) {
    const saleRole = "sale"; // Explicitly define the role
    users.push({
      id: props.saleUser.id,
      name: props.saleUser.name,
      role: saleRole,
      averageScore: null,
      key: `${props.saleUser.id}-${saleRole}`, // Create unique key
    });
  }

  // Optional: Sort or further process the array if needed
  // users.sort((a, b) => a.name.localeCompare(b.name));

  return users;
});

// Pass the simplified user info object on click (without the 'key')
const handleUserClick = (user: CombinedUserInfo) => {
  const { key, ...userInfo } = user; // Destructure to remove 'key'
  emit("user-click", userInfo);
};
</script>

<template>
  <div v-if="combinedUsers.length > 0" class="flex w-fit flex-wrap gap-1">
    <div
      v-for="user in combinedUsers"
      :key="user.key"
      class="flex cursor-pointer items-center gap-2 rounded p-1 hover:bg-gray-100 dark:hover:bg-gray-700"
      @click="handleUserClick(user)"
    >
      <!-- Pass userId and name directly to UserAvatar -->
      <UserAvatar :user-id="user.id" :name="user.name" size="small" />
      <div class="flex flex-col text-xs">
        <span class="font-medium">{{ user.name }}</span>
        <span v-if="user.role" class="text-gray-500 dark:text-gray-400">
          {{ roleDisplayNames[user.role] || user.role }}
        </span>
      </div>
      <!-- Average score display -->
      <div
        v-if="user.averageScore !== null && user.averageScore !== undefined"
        class="ml-auto flex items-center"
      >
        <span
          class="rounded-md bg-amber-50 px-1.5 py-0.5 text-xs font-semibold text-amber-700 dark:bg-amber-900/50 dark:text-amber-300"
        >
          <i class="pi pi-star-fill mr-1 text-[0.6rem] text-yellow-500"></i>
          {{ user.averageScore }}
        </span>
      </div>
    </div>
  </div>
  <span v-else class="text-sm italic text-gray-400">Chưa gán</span>
</template>
