<script setup lang="ts">
import { computed } from "vue";
import type { TrackSortResponse, DealSort, UserResponse } from "@/api/bcare-types-v2";
import useUser from "@/hooks/useUser";

import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import DateTime from "@/base-components/DateTime.vue";
import DealName from "@/components/Deal/DealName.vue";
import AppointmentInfo from "../InfoText/AppointmentInfo.vue";

interface Props {
  track: TrackSortResponse;
}

const props = defineProps<Props>();

const { getUserById } = useUser();

// Helper function specific to this card's data needs
const cardDetails = computed(() => {
  const track = props.track;
  const appointmentDoctorIds =
    (track.appointments
      ?.map((apt) => apt.doctor_id)
      .filter((id) => id != null && id > 0) as number[]) || [];

  const uniqueDoctorIds = [...new Set(appointmentDoctorIds)];

  const doctors = uniqueDoctorIds
    .map((id) => getUserById(id))
    .filter((user): user is UserResponse => user !== null && user !== undefined);

  // TODO: Replace with actual payment status logic
  const paymentStatus = { text: "Đã thanh toán", isPaid: true };

  const dealInfo: DealSort | undefined = track.deal;

  return {
    doctors,
    paymentStatus,
    dealInfo,
    appointments: track.appointments || [],
  };
});
</script>

<template>
  <div
    class="box space-y-2 divide-y divide-dotted rounded-lg border border-gray-200 bg-white p-3 shadow-sm"
  >
    <!-- Combined Top Section: Name, Time, Status, Icon -->
    <div class="space-y-1.5">
      <!-- Wrapper for top info, no padding needed here -->
      <!-- First Row: Deal Name & Time -->
      <div class="relative flex items-center justify-between">
        <!-- Deal Name -->
        <DealName
          v-if="cardDetails.dealInfo"
          :name="cardDetails.dealInfo?.name || `Deal #${cardDetails.dealInfo?.id}`"
          nameClass="text-primary-700 font-semibold text-sm"
        />
        <span v-else class="block text-sm font-semibold text-gray-500">
          Lịch hẹn lẻ #{{ track.id }}
        </span>
        <!-- Time (Only HH:mm) -->
        <div class="flex items-center text-xs text-slate-500">
          <i class="pi pi-clock mr-1 text-xs" />
          <DateTime :time="track.begin" size="xs" show-time :show-icon="false" format="HH:mm" />
        </div>
      </div>

      <!-- Second Row: Payment Status & Add Icon -->
      <div class="flex items-center justify-between">
        <!-- Payment Status -->
        <span
          :class="[
            'inline-block shrink-0 rounded px-1.5 py-0.5 text-xs font-medium',
            cardDetails.paymentStatus.isPaid
              ? 'bg-green-100 text-green-700'
              : 'bg-orange-100 text-orange-700',
          ]"
        >
          {{ cardDetails.paymentStatus.text }}
        </span>
      </div>
    </div>

    <!-- Middle Section: Appointment Details (Divider will appear above this) -->
    <div class="space-y-1.5 pt-2 [&:empty]:hidden">
      <template v-if="cardDetails.appointments.length > 0">
        <div class="text-xs font-medium text-gray-500">Chi tiết hẹn:</div>
        <div
          v-for="apt in cardDetails.appointments"
          :key="apt.id"
          class="ml-1 border-l-2 border-primary-100 pl-2"
        >
          <AppointmentInfo :appointment="apt" />
        </div>
      </template>
      <div v-else class="pt-1 text-xs italic text-gray-400">Không có chi tiết lịch hẹn.</div>
    </div>

    <!-- Bottom Section: Doctors (Divider will appear above this) -->
    <div class="flex justify-end pt-2 [&:empty]:hidden">
      <template v-if="cardDetails.doctors.length > 0">
        <UserAvatarGroup
          animation-style="lift"
          size="small"
          :max-display="3"
          :users="cardDetails.doctors"
        />
      </template>
    </div>
  </div>
</template>
