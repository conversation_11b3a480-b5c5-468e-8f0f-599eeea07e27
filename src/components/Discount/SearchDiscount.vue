<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @show="onPopoverShow">
    <div class="w-[30rem]">
      <div v-if="filteredDiscounts.length > 0" class="p-2">
        <ul
          class="m-0 h-[25vh] snap-y scroll-py-1 list-none space-y-1 overflow-hidden overflow-y-auto overscroll-contain p-1"
        >
          <li
            v-for="discount in filteredDiscounts"
            :key="discount.id"
            class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
            :class="{
              'bg-soft text-primary ring-1 ring-highlight': isDiscountSelected(discount),
            }"
            @click="handleItemClick(discount)"
          >
            <div class="flex-1">
              <span class="hyphens-auto font-medium">
                <HighlightText
                  :highlight="searchQuery"
                  :text="discount.name"
                  :class="isEligibleDiscount(discount) ? 'font-semibold opacity-100' : 'opacity-70'"
                />
                <i
                  v-if="isEligibleDiscount(discount)"
                  class="pi pi-star pl-2 text-xs text-success"
                />
              </span>
              <div class="text-sm">
                <span class="text-success">
                  <template v-if="discount.type === 'percent'">
                    {{ formatDiscountValue(discount) }}
                  </template>
                  <Money v-else :amount="discount.value" />
                </span>
              </div>
            </div>
            <div class="ml-3 text-gray-600 dark:text-gray-400">
              <div
                v-if="isDiscountSelected(discount)"
                class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                :class="{ 'bg-primary': isDiscountSelected(discount) }"
              ></div>
            </div>
          </li>
        </ul>
      </div>
      <div v-else class="p-2">
        <Empty />
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <IconField class="mb-2">
          <InputText
            autofocus
            v-model="searchQuery"
            class="w-full text-sm"
            placeholder="Tìm kiếm"
            type="text"
          />
          <InputIcon
            :class="searchQuery ? 'pi-times' : 'pi-search'"
            class="pi cursor-pointer"
            @click="
              () => {
                searchQuery = '';
              }
            "
          />
        </IconField>
      </div>
    </div>
  </Popover>
</template>

<script lang="ts" setup>
import Popover from "primevue/popover";
import { computed, PropType, ref, watch } from "vue";

import type { Discount, EligibleDiscount } from "@/api/bcare-types-v2";
import Empty from "@/base-components/Empty";
import HighlightText from "@/base-components/HighlightText.vue";
import Money from "@/base-components/Money.vue";
import useDiscount from "@/hooks/useDiscount";
import { normalizeVietnamese } from "@/utils/string";

const props = defineProps({
  selectedDiscount: {
    type: Object as PropType<Discount | null>,
    default: null,
  },
  selectedDiscounts: {
    type: Array as PropType<Discount[]>,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  personId: {
    type: Number,
    required: true,
  },
  dealId: {
    type: Number,
    required: false,
  },
  productIds: {
    type: Array as PropType<number[]>,
    required: true,
  },
});

const emit = defineEmits<{
  (e: "update:selectedDiscount", value: Discount | null): void;
  (e: "update:selectedDiscounts", value: Discount[]): void;
}>();

const { discounts, listDiscounts, getEligibleDiscount, isLoading } = useDiscount();

const searchQuery = ref("");
const eligibleDiscounts = ref<EligibleDiscount[]>([]);

const filteredDiscounts = computed(() => {
  const normalizedSearch = normalizeVietnamese(searchQuery.value.toLowerCase());

  // Lọc các discounts dựa trên tìm kiếm
  const filtered = discounts.value.filter((discount) => {
    const normalizedName = normalizeVietnamese(discount.name.toLowerCase());
    return normalizedName.includes(normalizedSearch);
  });

  // Sắp xếp để đưa eligible discounts lên đầu
  return filtered.sort((a, b) => {
    const aEligible = isEligibleDiscount(a);
    const bEligible = isEligibleDiscount(b);
    if (aEligible && !bEligible) return -1;
    if (!aEligible && bEligible) return 1;
    return 0;
  });
});

const isDiscountSelected = (discount: Discount) => {
  if (props.multiple) {
    return props.selectedDiscounts.some((selected) => selected.id === discount.id);
  } else {
    return props.selectedDiscount?.id === discount.id;
  }
};

const isEligibleDiscount = (discount: Discount) => {
  return eligibleDiscounts.value.some((eligible) => eligible.discount.id === discount.id);
};

const handleItemClick = (discount: Discount) => {
  if (props.multiple) {
    const newSelection = [...props.selectedDiscounts];
    const index = newSelection.findIndex((d) => d.id === discount.id);
    if (index === -1) {
      newSelection.push(discount);
    } else {
      newSelection.splice(index, 1);
    }
    emit("update:selectedDiscounts", newSelection);
  } else {
    emit("update:selectedDiscount", discount);
    popoverRef.value?.hide();
  }
};

const fetchDiscounts = async () => {
  if (isLoading.value) return; // Tránh fetch nhiều lần nếu đang loading

  try {
    await listDiscounts({});

    if (props.dealId) {
      const eligibleDiscountsResponse = await getEligibleDiscount({
        person_id: props.personId,
        deal_id: props.dealId,
        product_ids: props.productIds,
      });
      if (eligibleDiscountsResponse) {
        eligibleDiscounts.value = eligibleDiscountsResponse.eligible_discounts || [];
      }
    } else {
      eligibleDiscounts.value = [];
    }
  } catch (error) {
    console.error("Error fetching discounts:", error);
    // Xử lý lỗi ở đây nếu cần
  }
};

const onPopoverShow = () => {
  if (discounts.value.length === 0) fetchDiscounts();
};

const formatDiscountValue = (discount: Discount): string => {
  if (discount.type === "percent") {
    return `${(discount.value * 100).toFixed(0)}%`;
  }
  return ""; // Trường hợp 'fixed' sẽ được xử lý bởi component Money trong template
};

watch(
  () => [props.personId, props.dealId, props.productIds],
  () => {
    if (popoverRef.value?.isVisible) {
      fetchDiscounts();
    }
  },
  { deep: true },
);

const popoverRef = ref();

defineExpose({
  popoverRef,
});
</script>
