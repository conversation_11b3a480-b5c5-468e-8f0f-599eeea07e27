// src/utils/avatarUtils.ts

export const getAvatarLabel = (name: string, wordCount: number = 1): string => {
  const words = name.trim().split(/\s+/);

  if (wordCount === 1) {
    // Lấy chữ cái đầu tiên của từ cuối cùng
    return words[words.length - 1].charAt(0).toUpperCase();
  } else if (wordCount === 2) {
    // Lấy chữ cái đầu tiên của hai từ cuối cùng
    if (words.length >= 2) {
      return (words[words.length - 2].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }
    // Nếu chỉ có một từ, trả về chữ cái đầu tiên của từ đó
    return words[0].charAt(0).toUpperCase();
  }
  // Nếu wordCount không phải 1 hoặc 2, tr<PERSON> về chữ cái đầu tiên của từ cuối cùng
  console.warn("Invalid wordCount. Using default behavior.");
  return words[words.length - 1].charAt(0).toUpperCase();
};

// Thêm các hàm liên quan khác nếu cần
export const getInitials = (fullName: string): string => {
  return fullName
    .split(" ")
    .map((name) => name.charAt(0).toUpperCase())
    .join("");
};
