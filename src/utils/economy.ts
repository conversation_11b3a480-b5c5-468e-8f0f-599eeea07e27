// useEconomy.ts
import { AttachmentResponse } from "@/api/bcare-types-v2";

export function getInstallmentStateSeverity(state: string): string {
  const severity = {
    pending: "warning",
    partial_paid: "info",
    paid: "success",
  };
  return severity[state as keyof typeof severity] || "info";
}

export function getInstallmentStateLabel(state: string): string {
  const labels = {
    pending: "Chờ thanh toán",
    partial_paid: "Thanh toán một phần",
    paid: "Đã thanh toán",
  };
  return labels[state as keyof typeof labels] || state;
}

export function getBillStateSeverity(state: string): string {
  const severityMap: Record<string, string> = {
    draft: "info",
    active: "warning",
    paid: "success",
    partially_paid: "warning",
    cancelled: "danger",
    refunded: "danger",
  };
  return severityMap[state] || "info";
}

export function getPaymentStateSeverity(state: string): string {
  const severityMap: Record<string, string> = {
    pending: "warning",
    completed: "success",
    failed: "danger",
  };
  return severityMap[state] || "info";
}

export function getKindLabel(kind: string): string {
  const labelMap: Record<string, string> = {
    product: "Sản phẩm",
    treatment: "Điều trị",
    operation: "Phẫu thuật",
  };
  return labelMap[kind] || kind;
}

export function calculateItemTotal(attachment: AttachmentResponse): number {
  return attachment.price * attachment.quantity - (attachment.discount || 0);
}

export function getPaymentMethodLabel(method: string): string {
  const labels = {
    cash: "Tiền mặt",
    credit_card: "Thẻ tín dụng",
    bank_transfer: "Chuyển khoản",
  };
  return labels[method as keyof typeof labels] || method;
}
