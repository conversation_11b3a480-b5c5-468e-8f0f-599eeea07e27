import axios from "axios";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { cloneDeep } from "lodash";
import { parseColor } from "tailwindcss/lib/util/color";

import { EnumFileUsageType } from "@/api/bcare-enum";
import { File, FileResponse, FileUsageResponse } from "@/api/bcare-types-v2";
import service, { BASE_URL } from "@/api/http";
import { parseGoTime } from "@/utils/time-helper";

dayjs.extend(duration);

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("vi-VN", { style: "currency", currency: "VND" }).format(amount);
};

const toObject = (string: string) => {
  return JSON.parse(string);
};

const randomNumbers = (from: number, to: number, length: number) => {
  const numbers = [0];
  for (let i = 1; i < length; i++) {
    numbers.push(Math.ceil(Math.random() * (from - to) + to));
  }

  return numbers;
};

const toRGB = (value: string) => {
  return parseColor(value).color.join(" ");
};

const slideUp = (el: HTMLElement, duration = 300, callback = () => {}) => {
  el.style.transitionProperty = "height, margin, padding";
  el.style.transitionDuration = duration + "ms";
  el.style.height = el.offsetHeight + "px";
  el.offsetHeight;
  el.style.overflow = "hidden";
  el.style.height = "0";
  el.style.paddingTop = "0";
  el.style.paddingBottom = "0";
  el.style.marginTop = "0";
  el.style.marginBottom = "0";
  window.setTimeout(() => {
    el.style.display = "none";
    el.style.removeProperty("height");
    el.style.removeProperty("padding-top");
    el.style.removeProperty("padding-bottom");
    el.style.removeProperty("margin-top");
    el.style.removeProperty("margin-bottom");
    el.style.removeProperty("overflow");
    el.style.removeProperty("transition-duration");
    el.style.removeProperty("transition-property");
    callback();
  }, duration);
};

const slideDown = (el: HTMLElement, duration = 300, callback = () => {}) => {
  el.style.removeProperty("display");
  let display = window.getComputedStyle(el).display;
  if (display === "none") display = "block";
  el.style.display = display;
  const height = el.offsetHeight;
  el.style.overflow = "hidden";
  el.style.height = "0";
  el.style.paddingTop = "0";
  el.style.paddingBottom = "0";
  el.style.marginTop = "0";
  el.style.marginBottom = "0";
  el.offsetHeight;
  el.style.transitionProperty = "height, margin, padding";
  el.style.transitionDuration = duration + "ms";
  el.style.height = height + "px";
  el.style.removeProperty("padding-top");
  el.style.removeProperty("padding-bottom");
  el.style.removeProperty("margin-top");
  el.style.removeProperty("margin-bottom");
  window.setTimeout(() => {
    el.style.removeProperty("height");
    el.style.removeProperty("overflow");
    el.style.removeProperty("transition-duration");
    el.style.removeProperty("transition-property");
    callback();
  }, duration);
};

function formatNumberWithCommas(number: number | string) {
  const parts = number.toString().split(".");
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  return parts.join(".");
}

function formatCreatedAtToDate(dateString: string) {
  if (!dateString) {
    return "";
  }
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${day}/${month}/${year}`;
}

function addLeadingZero(value: number): string {
  return value < 10 ? `0${value}` : `${value}`;
}

function calculateTimeDifference(startDateTimeString: string, endDateTimeString: string) {
  const startDateObject = parseGoTime(startDateTimeString);
  const endDateObject = parseGoTime(endDateTimeString);

  if (!startDateObject || !endDateObject) {
    return 0;
  }

  const timeDifference = endDateObject.getTime() - startDateObject.getTime();

  return timeDifference / (1000 * 60);
}

function calculateTimeDifferenceDate({
  endDateTime,
  startDateTime,
}: {
  startDateTime: Date;
  endDateTime: Date;
}) {
  const timeDifference = endDateTime.getTime() - startDateTime.getTime();
  const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
  const minutesDifference = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60));

  return { days: daysDifference, minutes: minutesDifference };
}

function formatTimer(elapsedTime: number) {
  const minutes = `0${Math.floor(elapsedTime / 60)}`.slice(-2);
  const seconds = `0${Math.floor(elapsedTime) % 60}`.slice(-2);
  return `${minutes}:${seconds}`;
}

function getLocalResourceUri(image: FileResponse | File): string {
  // Kiểm tra nếu image có thuộc tính storage
  if (image.storage === "local") {
    return `${BASE_URL}/storage${image.path}`; // Trả về đường dẫn cho file lưu trữ cục bộ
  }

  // Nếu không phải là local, có thể xử lý các loại khác hoặc ném lỗi
  throw new Error("Image is not stored locally");
}

function getLocalAvatarUri(imageUrl: string): string {
  return `${BASE_URL}/storage${imageUrl}`;
}

function getFileExcelUri(path: string): string {
  return `${BASE_URL}/storage${path}`;
}

function getResourceUri(image: FileUsageResponse): string {
  if (image.file.storage === "updental_app") {
    switch (image.usage_type) {
      case EnumFileUsageType.EXAMINATION_IMAGE:
      case EnumFileUsageType.X_RAY_IMAGE:
        return `https://dental.upcare.vn/storage/up/deal/${image.file.path}`;
      case EnumFileUsageType.DOCUMENT_FILE_IMAGE:
      case EnumFileUsageType.PROFILE_FILE_IMAGE:
      case EnumFileUsageType.FILE_IMAGE:
        return `https://dental.upcare.vn/storage/up/media/${image.file.path}`;
      default:
        return `${BASE_URL}/storage${image.file.path}`;
    }
  }
  if (image.file.storage === "local") return `${BASE_URL}/storage${image.file.path}`;
  return `${BASE_URL}/storage${image.file.path}`;
}

async function downloadImage(imageSrc: string, fileName: string) {
  const xhr = new XMLHttpRequest();
  xhr.open("GET", imageSrc, true);
  xhr.onload = function () {
    const urlCreator = window.URL || window.webkitURL;
    const imageUrl = urlCreator.createObjectURL(this.response);
    const tag = document.createElement("a");
    tag.href = imageUrl;
    tag.download = fileName;
    document.body.appendChild(tag);
    tag.click();
    document.body.removeChild(tag);
  };
  xhr.send();
}

const deepClone = <T = unknown>(obj: T): T => {
  return cloneDeep(obj);
};

function getFirstLetterOfLastWord(str: string): string {
  const words = str.trim().split(/\s+/);
  return words.length > 0 ? words[words.length - 1].charAt(0).toUpperCase() : "";
}

function getLastTwoWords(str: string): string {
  if (str.trim() === "") {
    return "";
  }

  const words = str.trim().split(" ");
  return words.slice(-2).join(" ");
}

function parseDateTimeLogs(isoDateTimeString: string) {
  // Kiểm tra nếu isoDateTimeString không có giá trị
  if (!isoDateTimeString) {
    return ""; // Hoặc trả về một giá trị mặc định khác
  }

  const dateTime = new Date(isoDateTimeString);

  // Kiểm tra nếu dateTime là một ngày hợp lệ
  if (isNaN(dateTime.getTime())) {
    return ""; // Hoặc trả về một giá trị mặc định khác
  }

  const year = dateTime.getFullYear();
  const month = String(dateTime.getMonth() + 1).padStart(2, "0");
  const day = String(dateTime.getDate()).padStart(2, "0");
  const hours = String(dateTime.getHours()).padStart(2, "0");
  const minutes = String(dateTime.getMinutes()).padStart(2, "0");
  const seconds = String(dateTime.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function highlightText(text: string) {
  const regex = /<(.*?)>/g;
  return text.replace(regex, (match, capture) => `<strong>${capture}</strong>`);
}

function removeHTMLTags(text: string) {
  return text.replace(/<\/?strong>/g, "");
}

const copyToClipboard = (text: string): void => {
  navigator.clipboard
    .writeText(text)
    .then(() => console.log("Đã sao chép"))
    .catch((err) => console.error("Lỗi sao chép:", err));
};

const handleDownload = async (path: string, fileName: string): Promise<void> => {
  const decodedPath = decodeURIComponent(path);
  const response = await service.download("/v1/download/file", { path: decodedPath }, true);

  try {
    const blob = new Blob([response as BlobPart]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error("Download failed:", error);
  }
};

const formatExamTime = (dateTimeStr?: string): string => {
  if (!dateTimeStr) return "11 giờ 0 phút";

  const timeStr = dateTimeStr.split(" ")[0];
  const [hours, minutes] = timeStr.split(":").map(Number);

  return `${hours} giờ ${minutes} phút`;
};

const formatExamDate = (dateTimeStr?: string, prefix = "Ngày"): string => {
  if (!dateTimeStr) return `${prefix} ... tháng ... năm ...`;

  const dateStr = dateTimeStr.split(" ")[1];
  const [day, month, year] = dateStr.split("/");

  return `${prefix} ${day} tháng ${month} năm ${year}`;
};

export {
  getLocalResourceUri,
  getLocalAvatarUri,
  formatCurrency,
  randomNumbers,
  toRGB,
  slideUp,
  slideDown,
  formatNumberWithCommas,
  addLeadingZero,
  deepClone,
  calculateTimeDifference,
  formatTimer,
  formatCreatedAtToDate,
  getResourceUri,
  downloadImage,
  toObject,
  calculateTimeDifferenceDate,
  getFirstLetterOfLastWord,
  getLastTwoWords,
  parseDateTimeLogs,
  highlightText,
  removeHTMLTags,
  copyToClipboard,
  getFileExcelUri,
  handleDownload,
  formatExamTime,
  formatExamDate,
};
