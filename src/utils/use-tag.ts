import { computed } from "vue";

interface ColorHash {
  (str: string, key?: string): string;
}

const statusColors: Record<string, string> = {
  "1": "#e53935",
  "2": "#4caf50",
  "3": "#F09778",
  "4": "#ffc107",
  "5": "#2196f3",
};

const useColorHash = (): ColorHash => {
  const colorSets: Record<string, string[]> = {
    source: ["#054982", "#1266AC", "#1C7DD5", "#F09778", "#FF9AB6"],
    status: ["#9370db", "#da70d6", "#ee82ee", "#dda0dd", "#ba55d3"],
    tertiary: ["#adff2f", "#7fff00", "#00ff00", "#32cd32", "#98fb98"],
  };

  const hashString = (str: string): number => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    return hash;
  };

  const hashColor = (str: string, key: string = "primary"): string => {
    if (statusColors.hasOwnProperty(str)) {
      return statusColors[str];
    }

    const colors = colorSets[key] || colorSets.primary;
    if (!colors) return "";
    const hash = hashString(str);
    const index = Math.abs(hash % colors.length);
    return colors[index];
  };

  return computed(() => hashColor).value;
};

export default useColorHash;
