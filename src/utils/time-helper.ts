import { parseExpression } from "cron-parser";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";
import localizedFormat from "dayjs/plugin/localizedFormat";
import relativeTime from "dayjs/plugin/relativeTime";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

import { TaskStateEnum } from "@/api/bcare-enum";
import { FilterOperator } from "@/api/bcare-enum";
import { Filter } from "@/api/bcare-types-v2";
import "dayjs/locale/vi";

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(relativeTime);
dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(localizedFormat);
dayjs.locale("vi");

// Helper function to parse both Golang time string and custom format
export const parseGoTime = (timeString: string): Date | null => {
  if (!timeString) {
    return null;
  }

  try {
    // Check if the timeString matches the custom format "HH:mm dd/MM/yyyy"
    const customFormatRegex = /^\d{2}:\d{2} \d{2}\/\d{2}\/\d{4}$/;
    // Check if the timeString matches the date-only format "DD/MM/YYYY"
    const dateOnlyRegex = /^\d{2}\/\d{2}\/\d{4}$/;

    let parsedDate;
    if (customFormatRegex.test(timeString)) {
      parsedDate = dayjs(timeString, "HH:mm DD/MM/YYYY");
    } else if (dateOnlyRegex.test(timeString)) {
      parsedDate = dayjs(timeString, "DD/MM/YYYY");
    } else {
      parsedDate = dayjs(timeString);
    }

    return parsedDate.isValid() ? parsedDate.toDate() : null;
  } catch (error) {
    console.error("Error parsing date:", error);
    return null;
  }
};

// Format as full date and time
export const formatLongDateTime = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";
  return date.format("LLLL"); // Localized full date and time
};

// Format as full date and time
export const formatShortDateTime = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";
  return date.format("HH:mm - DD/MM/YYYY");
};

// Format as short date
export const formatShortDate = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";
  return date.format("DD/MM/YYYY");
};

// Format as relative time
export const formatRelativeTime = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";

  const now = dayjs();
  const diffInSeconds = now.diff(date, "second");

  if (diffInSeconds < 60) return "vừa xong";
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
  return formatShortDate(timeString);
};

// Format as custom format
export const formatCustom = (timeString: string, formatString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";
  return date.format(formatString);
};

// Format as hours and minutes only
export const formatHoursMinutes = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) {
    console.error(`Invalid date returned for time string "${timeString}"`);
    return "Invalid Date";
  }
  return date.format("HH:mm");
};

// Format as hours and minutes, with date if not today
export const formatSmartDateTime = (timeString: string): string => {
  const date = dayjs(timeString);
  if (!date.isValid()) return "";

  if (date.isToday()) {
    return date.format("HH:mm");
  } else if (date.isYesterday()) {
    return `Hôm qua ${date.format("HH:mm")}`;
  } else if (dayjs().diff(date, "day") < 7) {
    return date.format("dddd, HH:mm"); // Localized day name
  }
  return date.format("DD/MM/YYYY, HH:mm");
};

export function formatFriendlyDateTime(dateStr: string): string {
  const date = dayjs(dateStr);
  const now = dayjs();

  // Định dạng ngày tháng
  const weekday = date.format("dddd"); // Thứ trong tuần
  const dayMonth = date.format("DD/MM"); // ngày/tháng
  const time = date.format("HH:mm"); // giờ:phút

  // Tính khoảng thời gian còn lại
  const diff = date.diff(now);
  const dur = dayjs.duration(diff);
  const days = Math.floor(dur.asDays());
  const hours = Math.floor(dur.asHours() % 24);

  let remainingTime = "";
  if (days > 0 && hours > 0) {
    remainingTime = `sau ${days} ngày ${hours} giờ`;
  } else if (days > 0) {
    remainingTime = `sau ${days} ngày`;
  } else if (hours > 0) {
    remainingTime = `sau ${hours} giờ`;
  }

  return `${weekday}, ngày ${dayMonth} lúc ${time} (${remainingTime})`;
}

export const calculateLateDays = (
  dueDate: string | Date,
  state: TaskStateEnum,
  completedAt?: string | Date,
): number => {
  const dueDateTime = dayjs(dueDate).startOf("day");

  // Nếu task đã hoàn thành (có completed_at) và không phải giá trị mặc định
  if (
    [TaskStateEnum.COMPLETED, TaskStateEnum.COMPLETED_EARLY].includes(state) &&
    completedAt &&
    completedAt !== "0001-01-01T00:00:00Z"
  ) {
    const completedDate = dayjs(completedAt).startOf("day");
    return completedDate.isAfter(dueDateTime) ? completedDate.diff(dueDateTime, "day") : 0;
  }

  // Nếu task chưa hoàn thành hoặc completed_at là giá trị mặc định, tính từ hiện tại
  const currentDate = dayjs().startOf("day");
  return currentDate.isAfter(dueDateTime) ? currentDate.diff(dueDateTime, "day") : 0;
};

export function getNextCronOccurrences(
  cronExpression: string,
  maxOccurrences: number = 3,
  endDate?: Date,
): Date[] {
  try {
    const currentDate = new Date();
    const interval = parseExpression(cronExpression, {
      currentDate,
      endDate,
      iterator: true,
    });

    const occurrences: Date[] = [];

    for (let i = 0; i < maxOccurrences; i++) {
      const next = interval.next();
      if (next.done) break;
      occurrences.push(next.value.toDate());
    }

    return occurrences;
  } catch (error) {
    console.error("Error parsing cron expression:", error);
    return [];
  }
}

/**
 * Trích xuất và định dạng date range từ filter payload
 * @param filters Mảng các filter
 * @param fieldName Tên trường ngày (mặc định là 'created_at')
 * @returns Thông tin về date range đã được định dạng
 */
export function extractDateRange(filters: Filter[] | undefined, fieldName: string = "created_at") {
  if (!filters?.length) return null;

  const fromFilter = filters.find(
    (f: Filter) => f.field === fieldName && f.operator === FilterOperator.GTE,
  )?.value;
  const toFilter = filters.find(
    (f: Filter) => f.field === fieldName && f.operator === FilterOperator.LT,
  )?.value;

  // Extract dates using regex match
  const [fromDateStr, toDateStr] = [fromFilter, toFilter].map((filter) => {
    // Use regex that captures the full date-time string including timezone
    return filter?.match(/time\((.*?)\)/)?.[1];
  });

  if (!fromDateStr || !toDateStr) return null;

  // Use dayjs for better date handling
  const fromDate = dayjs(fromDateStr);
  // Subtract 1 millisecond from toDate to get the end of previous day
  const toDate = dayjs(toDateStr).subtract(1, "millisecond");

  // Format dates
  const fromFormatted = fromDate.format("DD/MM/YYYY");
  const toFormatted = toDate.format("DD/MM/YYYY");

  // Check if dates are the same day
  const isOneDay = fromDate.format("YYYY-MM-DD") === toDate.format("YYYY-MM-DD");

  return {
    fromFormatted,
    toFormatted,
    isOneDay,
    fromDate,
    toDate,
  };
}

/**
 * Tạo chuỗi ngày tháng cho tên file
 * @param filters Mảng các filter
 * @param fieldName Tên trường ngày (mặc định là 'created_at')
 * @returns Chuỗi ngày tháng định dạng "dd_MM" hoặc "dd_MM-dd_MM"
 */
export function getDateStringForFilename(
  filters: Filter[] | undefined,
  fieldName: string = "created_at",
) {
  const dateRange = extractDateRange(filters, fieldName);
  if (!dateRange) return "";

  // Format ngày thành dd_MM
  const formatDate = (date: dayjs.Dayjs) => {
    return `${date.format("DD")}_${date.format("MM")}`;
  };

  // Tạo string ngày cho tên file
  return dateRange.isOneDay
    ? formatDate(dateRange.fromDate)
    : `${formatDate(dateRange.fromDate)}-${formatDate(dateRange.toDate)}`;
}
