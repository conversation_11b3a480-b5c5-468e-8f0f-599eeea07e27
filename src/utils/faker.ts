import dayjs from "dayjs";
import _ from "lodash";

interface Users {
  name: string;
  gender: string;
  email: string;
}

interface Products {
  name: string;
  category: string;
}

interface Categories {
  name: string;
  tags: string;
  slug: string;
}

interface News {
  title: string;
  superShortContent: string;
  shortContent: string;
  content: string;
}

interface Files {
  fileName: string;
  type: string;
  size: string;
}

interface Foods {
  name: string;
  image: string;
}
interface Taxonomys {
  id: number;
  name: string;
}
const imageAssets = import.meta.glob<{
  default: string;
}>("/src/assets/images/fakers/*.{jpg,jpeg,png,svg}", { eager: true });

const fakers = {
  fakeUsers() {
    const users: Array<Omit<Users, "email">> = [
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON><PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "female" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON>", gender: "male" },
      { name: "<PERSON> Travo<PERSON>", gender: "male" },
      { name: "<PERSON> Sc<PERSON>r", gender: "male" },
      { name: "<PERSON> Stallone", gender: "male" },
      { name: "<PERSON> Winslet", gender: "female" },
      { name: "<PERSON> <PERSON>", gender: "male" },
      { name: "<PERSON> <PERSON>", gender: "male" },
      { name: "<PERSON><PERSON> <PERSON>", gender: "male" },
      { name: "Nicolas Cage", gender: "male" },
      { name: "Hugh Jackman", gender: "male" },
      { name: "Edward Norton", gender: "male" },
      { name: "Bruce Willis", gender: "male" },
      { name: "Tom Hanks", gender: "male" },
      { name: "Charlize Theron", gender: "female" },
      { name: "Will Smith", gender: "male" },
      { name: "Sean Connery", gender: "male" },
      { name: "Keira Knightley", gender: "female" },
      { name: "Vin Diesel", gender: "male" },
      { name: "Matt Damon", gender: "male" },
      { name: "Richard Gere", gender: "male" },
      { name: "Catherine Zeta-Jones", gender: "female" },
    ];

    return _.sampleSize(users, 3).map((user) => {
      return {
        name: user.name,
        gender: user.gender,
        email: _.toLower(_.replace(user.name, / /g, "") + "@left4code.com"),
      };
    });
  },
  fakePhotos() {
    const photos = [];
    for (let i = 0; i < 15; i++) {
      photos[photos.length] =
        imageAssets["/src/assets/images/fakers/profile-" + _.random(1, 15) + ".jpg"].default;
    }
    return _.sampleSize(photos, 10);
  },
  fakeImages() {
    const images = [];
    for (let i = 0; i < 15; i++) {
      images[images.length] =
        imageAssets["/src/assets/images/fakers/preview-" + _.random(1, 15) + ".jpg"].default;
    }
    return _.sampleSize(images, 10);
  },
  fakeDates() {
    const dates = [];
    for (let i = 0; i < 5; i++) {
      dates[dates.length] = dayjs
        .unix(_.random(1586584776897, 1672333200000) / 1000)
        .format("DD MMMM YYYY");
    }
    return _.sampleSize(dates, 3);
  },
  fakeTimes() {
    const times = ["01:10 PM", "05:09 AM", "06:05 AM", "03:20 PM", "04:50 AM", "07:00 PM"];
    return _.sampleSize(times, 3);
  },
  fakeFormattedTimes() {
    const times = [
      _.random(10, 60) + " seconds ago",
      _.random(10, 60) + " minutes ago",
      _.random(10, 24) + " hours ago",
      _.random(10, 20) + " days ago",
      _.random(10, 12) + " months ago",
    ];
    return _.sampleSize(times, 3);
  },
  fakeTotals() {
    return _.shuffle([_.random(20, 220), _.random(20, 120), _.random(20, 50)]);
  },
  fakeTrueFalse() {
    return _.sampleSize([false, true, true], 1);
  },
  fakeStocks() {
    return _.shuffle([_.random(50, 220), _.random(50, 120), _.random(50, 50)]);
  },
  fakeProducts() {
    const products = [
      { name: "Chụp film CT", category: "Chuẩn đoán hình ảnh", price: "500.000" },
      {
        name: "Chụp film CT Hàm trên & hàm dưới",
        category: "Chuẩn đoán hình ảnh",
        price: "800.000",
      },
      { name: "Nhổ răng cối lớn", category: "Nhổ răng", price: "1.200.000" },
      { name: "Nhổ răng cửa", category: "Nhổ răng", price: "600.000" },
      {
        name: "Điều trị viêm nướu mức độ 1 (2 lần)",
        category: "Điều trị nha chu & cạo vôi",
        price: "600.000",
      },
      {
        name: "Điều trị viêm nha chu không phẫu thuật độ 2",
        category: "Điều trị nha chu & cạo vôi",
        price: "2.500.000",
      },
      {
        name: "Phẫu thuật nha chu (vùng)",
        category: "Điều trị nha chu & cạo vôi",
        price: "2.500.000",
      },
      {
        name: "Phẫu thuật nha chu (hàm)",
        category: "Điều trị nha chu & cạo vôi",
        price: "5.000.000",
      },
      { name: "Trám xoang II, IV", category: "Trám răng", price: "600.000" },
      { name: "Trám khe hở răng cửa", category: "Trám răng", price: "800.000" },
    ];
    return _.shuffle(products);
  },
  fakeCategories() {
    const categories = [
      { name: "PC & Laptop", tags: "Apple, Asus, Lenovo, Dell, Acer" },
      {
        name: "Smartphone & Tablet",
        tags: "Samsung, Apple, Huawei, Nokia, Sony",
      },
      { name: "Electronic", tags: "Sony, LG, Toshiba, Hisense, Vizio" },
      {
        name: "Home Appliance",
        tags: "Whirlpool, Amana, LG, Frigidaire, Samsung",
      },
      { name: "Photography", tags: "Canon, Nikon, Sony, Fujifilm, Panasonic" },
      { name: "Fashion & Make Up", tags: "Nike, Adidas, Zara, H&M, Levi’s" },
      {
        name: "Kids & Baby",
        tags: "Mothercare, Gini & Jony, H&M, Babyhug, Liliput",
      },
      { name: "Hobby", tags: "Bandai, Atomik R/C, Atlantis Models, Carisma" },
      {
        name: "Sport & Outdoor",
        tags: "Nike, Adidas, Puma, Rebook, Under Armour",
      },
    ];

    return _.sampleSize(categories, 3).map((category) => {
      return {
        name: category.name,
        tags: category.tags,
        slug: _.replace(_.replace(_.toLower(category.name), / /g, "-"), "&", "and"),
      };
    });
  },
  fakeNews() {
    const news = [
      {
        title: "Desktop publishing software like Aldus PageMaker",
        superShortContent: _.truncate(
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
          {
            length: 30,
            omission: "",
          },
        ),
        shortContent: _.truncate(
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
          {
            length: 150,
            omission: "",
          },
        ),
        content:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
      },
      {
        title: "Dummy text of the printing and typesetting industry",
        superShortContent: _.truncate(
          "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
          {
            length: 30,
            omission: "",
          },
        ),
        shortContent: _.truncate(
          "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
          {
            length: 150,
            omission: "",
          },
        ),
        content:
          "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).",
      },
      {
        title: "Popularised in the 1960s with the release of Letraset",
        superShortContent: _.truncate(
          'Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.',
          {
            length: 30,
            omission: "",
          },
        ),
        shortContent: _.truncate(
          'Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.',
          {
            length: 150,
            omission: "",
          },
        ),
        content:
          'Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.',
      },
      {
        title: "200 Latin words, combined with a handful of model sentences",
        superShortContent: _.truncate(
          "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
          {
            length: 50,
            omission: "",
          },
        ),
        shortContent: _.truncate(
          "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
          {
            length: 150,
            omission: "",
          },
        ),
        content:
          "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
      },
    ];
    return _.shuffle(news);
  },
  fakeFiles() {
    const files = [
      { fileName: "Celine Dion - Ashes.mp4", type: "MP4", size: "20 MB" },
      { fileName: "Laravel 7", type: "Empty Folder", size: "120 MB" },
      { fileName: fakers.fakeImages()[0], type: "Image", size: "1.2 MB" },
      { fileName: "Repository", type: "Folder", size: "20 KB" },
      { fileName: "Resources.txt", type: "TXT", size: "2.2 MB" },
      { fileName: "Routes.php", type: "PHP", size: "1 KB" },
      { fileName: "Dota 2", type: "Folder", size: "112 GB" },
      { fileName: "Documentation", type: "Empty Folder", size: "4 MB" },
      { fileName: fakers.fakeImages()[0], type: "Image", size: "1.4 MB" },
      { fileName: fakers.fakeImages()[0], type: "Image", size: "1 MB" },
    ];
    return _.shuffle(files);
  },
  fakeJobs() {
    const jobs = ["Frontend Engineer", "Software Engineer", "Backend Engineer", "DevOps Engineer"];
    return _.shuffle(jobs);
  },
  fakeNotificationCount() {
    return _.random(1, 7);
  },
  fakeTaxonomy() {
    const taxonomy = [
      {
        id: 1,
        name: "Danh mục",
      },
      {
        id: 2,
        name: "Sản phẩm",
      },
      {
        id: 3,
        name: "Tin tức",
      },
      {
        id: 4,
        name: "Sự kiện",
      },
      {
        id: 5,
        name: "Hướng dẫn",
      },
      {
        id: 6,
        name: "Câu hỏi thường gặp",
      },
      {
        id: 7,
        name: "Tài khoản",
      },
      {
        id: 8,
        name: "Liên hệ",
      },
      {
        id: 9,
        name: "Góp ý và phản hồi",
      },
      {
        id: 10,
        name: "Về chúng tôi",
      },
    ];
    return _.shuffle(taxonomy);
  },
  fakeFoods() {
    const foods = [
      {
        name: "Vanilla Latte",
        image: imageAssets["/src/assets/images/fakers/food-beverage-1.jpg"].default,
      },
      {
        name: "Milkshake",
        image: imageAssets["/src/assets/images/fakers/food-beverage-2.jpg"].default,
      },
      {
        name: "Soft Drink",
        image: imageAssets["/src/assets/images/fakers/food-beverage-3.jpg"].default,
      },
      {
        name: "Root Beer",
        image: imageAssets["/src/assets/images/fakers/food-beverage-4.jpg"].default,
      },
      {
        name: "Pocari",
        image: imageAssets["/src/assets/images/fakers/food-beverage-5.jpg"].default,
      },
      {
        name: "Ultimate Burger",
        image: imageAssets["/src/assets/images/fakers/food-beverage-6.jpg"].default,
      },
      {
        name: "Hotdog",
        image: imageAssets["/src/assets/images/fakers/food-beverage-7.jpg"].default,
      },
      {
        name: "Avocado Burger",
        image: imageAssets["/src/assets/images/fakers/food-beverage-8.jpg"].default,
      },
      {
        name: "Spaghetti Fettucine Aglio with Beef Bacon",
        image: imageAssets["/src/assets/images/fakers/food-beverage-9.jpg"].default,
      },
      {
        name: "Spaghetti Fettucine Aglio with Smoked Salmon",
        image: imageAssets["/src/assets/images/fakers/food-beverage-10.jpg"].default,
      },
      {
        name: "Curry Penne and Cheese",
        image: imageAssets["/src/assets/images/fakers/food-beverage-11.jpg"].default,
      },
      {
        name: "French Fries",
        image: imageAssets["/src/assets/images/fakers/food-beverage-12.jpg"].default,
      },
      {
        name: "Virginia Cheese Fries",
        image: imageAssets["/src/assets/images/fakers/food-beverage-13.jpg"].default,
      },
      {
        name: "Virginia Cheese Wedges",
        image: imageAssets["/src/assets/images/fakers/food-beverage-14.jpg"].default,
      },
      {
        name: "Fried/Grilled Banana",
        image: imageAssets["/src/assets/images/fakers/food-beverage-15.jpg"].default,
      },
      {
        name: "Crispy Mushroom",
        image: imageAssets["/src/assets/images/fakers/food-beverage-16.jpg"].default,
      },
      {
        name: "Fried Calamari",
        image: imageAssets["/src/assets/images/fakers/food-beverage-17.jpg"].default,
      },
      {
        name: "Chicken Wings",
        image: imageAssets["/src/assets/images/fakers/food-beverage-18.jpg"].default,
      },
      {
        name: "Snack Platter",
        image: imageAssets["/src/assets/images/fakers/food-beverage-19.jpg"].default,
      },
    ];
    return _.shuffle(foods);
  },
};

const fakerData: Array<{
  users: Users[];
  photos: string[];
  images: string[];
  dates: string[];
  times: string[];
  formattedTimes: string[];
  totals: number[];
  trueFalse: boolean[];
  stocks: number[];
  products: Products[];
  categories: Categories[];
  news: News[];
  files: Files[];
  jobs: string[];
  notificationCount: number;
  foods: Foods[];
  taxonomy: Taxonomys[];
}> = [];
for (let i = 0; i < 20; i++) {
  fakerData[fakerData.length] = {
    users: fakers.fakeUsers(),
    photos: fakers.fakePhotos(),
    images: fakers.fakeImages(),
    dates: fakers.fakeDates(),
    times: fakers.fakeTimes(),
    formattedTimes: fakers.fakeFormattedTimes(),
    totals: fakers.fakeTotals(),
    trueFalse: fakers.fakeTrueFalse(),
    stocks: fakers.fakeStocks(),
    products: fakers.fakeProducts(),
    categories: fakers.fakeCategories(),
    news: fakers.fakeNews(),
    files: fakers.fakeFiles(),
    jobs: fakers.fakeJobs(),
    notificationCount: fakers.fakeNotificationCount(),
    foods: fakers.fakeFoods(),
    taxonomy: fakers.fakeTaxonomy(),
  };
}

export default fakerData;
