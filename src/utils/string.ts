import { computed } from "vue";

export const joinRunes = (...params: (string | number)[]): string => {
  return params.map((param) => String(param)).join("__");
};

export function normalizeVietnamese(str: string): string {
  return str
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/đ/g, "d")
    .replace(/Đ/g, "D")
    .toLowerCase();
}

// Add fuzzy search function
export function fuzzySearch(text: string, searchTerm: string): boolean {
  if (!text || !searchTerm) return false;

  // Normalize both strings to lowercase and remove diacritics
  const normalizedText = normalizeVietnamese(text);
  const normalizedSearchTerm = normalizeVietnamese(searchTerm);

  // Split search term into words for multi-word search
  const searchWords = normalizedSearchTerm.split(/\s+/).filter((word) => word.length > 0);

  // Check if all search words are found in the normalized text
  return searchWords.every((word) => normalizedText.includes(word));
}

export function getShortName(fullName: string | (() => string)) {
  return computed(() => {
    const name = typeof fullName === "function" ? fullName() : fullName;
    const parts = name.split(" ");
    if (parts.length <= 1) return name;

    return (
      parts
        .slice(0, -1)
        .map((part) => part[0].toUpperCase())
        .join(".") +
      "." +
      parts[parts.length - 1]
    );
  });
}

export const selectAllText = (event: FocusEvent) => {
  const input = event.target as HTMLInputElement;
  input.select();
};

export const simpleGlobalId = () => {
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).substring(2, 11);
  return `${timestamp}-${randomPart}`;
};

/**
 * Normalizes Vietnamese phone numbers to standard 10-digit format
 * Handles cases where leading 0 is missing
 */
export function normalizeVietnamesePhone(phone: string): string {
  if (!phone) return phone;

  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, "");

  // Already correct format (10 digits starting with 0)
  if (/^0\d{9}$/.test(digits)) {
    return digits;
  }

  // Missing leading 0 (9 digits)
  if (/^\d{9}$/.test(digits)) {
    return `0${digits}`;
  }

  // Return original if not matching expected patterns
  return phone;
}
