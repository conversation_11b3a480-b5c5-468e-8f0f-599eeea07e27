import { createPinia } from "pinia";
import PrimeVue from "primevue/config";
import ConfirmationService from "primevue/confirmationservice";
import Tooltip from "primevue/tooltip";
import { createApp } from "vue";

import BcareStyle from "@/bcare-style";
import { lightbox } from "@/directives/Lightbox";
import "./assets/css/app.css";

import App from "./App.vue";
import router from "./router";

// Import your global PT configurations
import { dialogPT, drawerPT } from "@/config/primevue-pts"; // Adjusted path

const appTitle = import.meta.env.VITE_APP_TITLE;
document.title = appTitle;

const app = createApp(App)
  .use(router)
  .use(createPinia())
  .use(PrimeVue, {
    locale: {
      dayNames: ["Chủ Nhật", "T<PERSON><PERSON> Hai", "<PERSON><PERSON><PERSON> Ba", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
      dayNamesShort: ["CN", "T2", "T3", "T4", "T5", "T6", "T7"],
      dayNamesMin: ["CN", "T2", "T3", "T4", "T5", "T6", "T7"],
      monthNames: [
        "Tháng 1",
        "Tháng 2",
        "Tháng 3",
        "Tháng 4",
        "Tháng 5",
        "Tháng 6",
        "Tháng 7",
        "Tháng 8",
        "Tháng 9",
        "Tháng 10",
        "Tháng 11",
        "Tháng 12",
      ],
      monthNamesShort: [
        "Th 1",
        "Th 2",
        "Th 3",
        "Th 4",
        "Th 5",
        "Th 6",
        "Th 7",
        "Th 8",
        "Th 9",
        "Th 10",
        "Th 11",
        "Th 12",
      ],
      today: "Hôm nay",
      clear: "Xóa",
      firstDayOfWeek: 1,
      dateFormat: "dd/mm/yy",
    },
    pt: {
      dialog: dialogPT,
      drawer: drawerPT,
    },
    theme: {
      preset: BcareStyle,
      options: {
        prefix: "p",
        darkModeSelector: "none",
        cssLayer: {
          name: "primevue",
          order: "tailwind-base, primevue, tailwind-utilities",
        },
      },
    },
  })
  .use(ConfirmationService)
  .directive("lightbox", lightbox)
  .directive("tooltip", Tooltip);

app.mount("#app");
