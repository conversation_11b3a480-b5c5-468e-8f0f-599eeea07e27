<script setup lang="ts">
import { storeToRefs } from "pinia";

import { useFullscreenStore } from "@/stores/full-screen-store";

const fullscreenStore = useFullscreenStore();
const { isFullScreen } = storeToRefs(fullscreenStore);
</script>
<template>
  <div class="intro-y flex min-h-screen flex-col" :class="{ 'h-full': isFullScreen }">
    <!-- Header -->
    <div
      class="sticky top-0 z-40 col-span-12 flex flex-none flex-col bg-slate-100 px-1 py-5 lg:flex-row xl:col-span-12"
      :class="{ 'md:px-[22px]': isFullScreen }"
    >
      <div class="flex justify-between">
        <slot name="left-toolbar"></slot>
      </div>
      <div class="mt-5 flex flex-col sm:ml-auto md:flex-row lg:mt-0">
        <slot name="right-toolbar"></slot>
      </div>
    </div>

    <!-- Content -->
    <div class="relative flex-1 px-1" :class="{ 'md:px-[22px]': isFullScreen }">
      <slot></slot>
    </div>

    <!-- Footer -->
    <div
      class="sticky bottom-0 left-0 right-0 z-40 mx-1 flex-none border-t border-gray-200 bg-white px-4 py-2"
    >
      <slot name="footer">
        <!-- Default footer content if needed -->
      </slot>
    </div>
  </div>
</template>
