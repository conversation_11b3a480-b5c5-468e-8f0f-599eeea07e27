<script setup lang="ts">
import { storeToRefs } from "pinia";

import Button from "@/base-components/Button";
import FullScreenSwitcher from "@/components/FullScreenSwitcher";
import { useFullscreenStore } from "@/stores/full-screen-store";

const fullscreenStore = useFullscreenStore();
const { isFullScreen } = storeToRefs(fullscreenStore);
const { toggleFullScreen } = fullscreenStore;
</script>
<template>
  <div class="intro-y flex flex-col" :class="{ 'h-full': isFullScreen }">
    <div
      class="col-span-12 flex flex-none flex-col px-1 py-6 lg:flex-row xl:col-span-12"
      :class="{ 'md:px-[22px]': isFullScreen }"
    >
      <div class="flex justify-between">
        <slot name="left-toolbar"></slot>
      </div>
      <div class="mt-5 flex flex-col sm:ml-auto md:flex-row lg:mt-0">
        <slot name="right-toolbar"></slot>
        <div class="flex flex-col-reverse items-center border-l pl-3 sm:flex-row md:ml-3">
          <Button as="Span" variant="soft-secondary" class="bg-white" @click="toggleFullScreen">
            <FullScreenSwitcher />
          </Button>
        </div>
      </div>
    </div>

    <div class="no-scrollbar overflow-y-auto pb-10">
      <div class="relative z-30 flex" :class="{ 'flex-1 md:px-[22px]': isFullScreen }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
