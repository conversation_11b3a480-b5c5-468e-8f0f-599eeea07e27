<script setup lang="ts">
import { defineAsyncComponent } from "vue";
import { useResponsive } from "@/composables/useResponsive";

const TopMenuDesktopLayout = defineAsyncComponent(
  () => import("@/layouts/TopMenuDesktopLayout/TopMenuDesktopLayout.vue"),
);
const MobileLayout = defineAsyncComponent(() => import("@/layouts/MobileLayout/MobileLayout.vue"));

const { isMobile, isTablet } = useResponsive();
</script>

<template>
  <Suspense>
    <template #default>
      <MobileLayout v-if="isMobile || isTablet" />
      <TopMenuDesktopLayout v-else />
    </template>

    <template #fallback>
      <div class="flex h-screen w-screen items-center justify-center">
        <i class="pi pi-spin pi-spinner" style="font-size: 2rem" />
      </div>
    </template>
  </Suspense>
</template>
