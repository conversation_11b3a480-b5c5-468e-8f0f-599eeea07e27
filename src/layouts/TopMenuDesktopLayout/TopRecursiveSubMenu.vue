<script setup lang="ts">
import { useMenuStore, type FormattedMenuItem } from "@/stores/menu-store";
import TopMenuItem from "./TopMenuItem.vue";

const props = defineProps<{
  subMenuItems: FormattedMenuItem[];
  parentActiveDropdown: boolean;
}>();

const menuStore = useMenuStore();
const { navigateTo } = menuStore;
</script>

<template>
  <ul :class="{ 'side-menu__sub-open': parentActiveDropdown }">
    <li v-for="(subMenu, subMenuKey) in props.subMenuItems" :key="subMenuKey">
      <TopMenuItem
        :menu-item="subMenu"
        :custom-classes="subMenu.active ? 'top-menu--active' : ''"
        @navigate="navigateTo"
      />
      <TopRecursiveSubMenu
        v-if="subMenu.subMenu && subMenu.subMenu.length > 0"
        :sub-menu-items="subMenu.subMenu"
        :parent-active-dropdown="subMenu.activeDropdown || false"
      />
    </li>
  </ul>
</template>
