<script setup lang="ts">
import { storeToRefs } from "pinia";
import { nextTick, onMounted, provide, ref, watch } from "vue";
import { useRoute } from "vue-router";

import { useFullscreenStore } from "@/stores/full-screen-store";
import {
  useMenuStore,
  type FormattedMenuItem,
  type ProvideForceActiveMenuFunction,
} from "@/stores/menu-store";
import TopBar from "@/components/TopBar";
import TopRecursiveSubMenu from "./TopRecursiveSubMenu.vue";
import TopMenuItem from "./TopMenuItem.vue";

const fullscreenStore = useFullscreenStore();
const { isFullScreen } = storeToRefs(fullscreenStore);

const route = useRoute();

const menuStore = useMenuStore();
const { formattedMenu } = storeToRefs(menuStore);
const { navigateTo, setForceActiveMenu } = menuStore;

const mainContainer = ref<HTMLElement | null>(null);

const adjustFullscreenPosition = () => {
  if (isFullScreen.value && mainContainer.value) {
    const rect = mainContainer.value.getBoundingClientRect();
    mainContainer.value.style.marginTop = `-${rect.top}px`;
  } else if (mainContainer.value) {
    mainContainer.value.style.marginTop = "";
  }
};

watch([isFullScreen, () => route.path], ([newIsFullScreen]) => {
  if (newIsFullScreen !== undefined) {
    nextTick(adjustFullscreenPosition);
  }
});

provide<ProvideForceActiveMenuFunction>("forceActiveMenu", (pageName: string) => {
  setForceActiveMenu(pageName);
});

onMounted(() => {
  nextTick(adjustFullscreenPosition);
});
</script>

<template>
  <div class="py-5 md:py-0">
    <TopBar :class="isFullScreen ? 'z-0' : 'z-50'" layout="top-menu" />
    <!-- BEGIN: Top Menu -->
    <nav
      :class="[
        'top-nav relative z-40 hidden pt-28 md:block',
        'animate-[0.4s_ease-in-out_0.2s_intro-top-menu] opacity-0 animate-fill-mode-forwards',
      ]"
    >
      <ul class="flex flex-wrap px-6 xl:px-[50px]">
        <li v-for="(menu, menuKey) in formattedMenu" :key="menuKey">
          <TopMenuItem
            :menu-item="menu"
            :custom-classes="[
              menu.active ? 'top-menu--active' : '',
              {
                [`animate-[0.4s_ease-in-out_0.3s_intro-top-menu] animate-fill-mode-forwards [&:not(.top-menu--active)]:translate-y-[50px] [&:not(.top-menu--active)]:opacity-0 animate-delay-${(menuKey + 1) * 10}`]:
                  !menu.active,
              },
            ]"
            @navigate="navigateTo"
          />
          <TopRecursiveSubMenu
            v-if="menu.subMenu && menu.subMenu.length > 0"
            :sub-menu-items="menu.subMenu"
            :parent-active-dropdown="menu.activeDropdown || false"
          />
        </li>
      </ul>
    </nav>
    <!-- END: Top Menu -->
    <!-- BEGIN: Content -->
    <div
      ref="mainContainer"
      :class="[
        isFullScreen ? 'z-50 h-screen flex-col overflow-hidden' : 'z-0 mt-5 md:px-[22px]',
        'relative min-h-screen min-w-0 max-w-full flex-1 rounded-lg bg-slate-100 dark:bg-darkmode-700 md:max-w-none md:rounded-[35px_35px_0_0]',
        'before:block before:h-px before:w-full before:content-[\'\']',
      ]"
    >
      <RouterView />
    </div>
  </div>
</template>
