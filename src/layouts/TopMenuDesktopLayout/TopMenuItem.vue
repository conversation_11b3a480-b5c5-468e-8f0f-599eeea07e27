<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from "vue-router";
import type { FormattedMenuItem } from "@/stores/menu-store";
import Lucide from "@/base-components/Lucide";

const props = defineProps<{
  menuItem: FormattedMenuItem;
  customClasses?: string | Record<string, boolean> | (string | Record<string, boolean>)[];
}>();

const emit = defineEmits<{
  (e: "navigate", item: FormattedMenuItem): void;
}>();

const router = useRouter();

const resolvedHref = computed(() => {
  if (props.menuItem.subMenu && props.menuItem.subMenu.length > 0 && !props.menuItem.pageName) {
    return "#";
  }
  try {
    return props.menuItem.pageName
      ? router.resolve({ name: props.menuItem.pageName }).fullPath
      : "#";
  } catch (err) {
    console.error(`Error resolving route for pageName: ${props.menuItem.pageName}`, err);
    return "#";
  }
});

const handleClick = () => {
  emit("navigate", props.menuItem);
};
</script>

<template>
  <a :href="resolvedHref" :class="['top-menu', props.customClasses]" @click.prevent="handleClick">
    <div class="top-menu__icon">
      <Lucide :icon="props.menuItem.icon" />
    </div>
    <div class="top-menu__title">
      {{ props.menuItem.title }}
      <Lucide
        v-if="props.menuItem.subMenu && props.menuItem.subMenu.length > 0"
        class="top-menu__sub-icon"
        icon="ChevronDown"
      />
    </div>
  </a>
</template>
