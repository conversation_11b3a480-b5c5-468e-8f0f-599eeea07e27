<script setup lang="ts">
import { ref } from "vue";
import type { FormattedMenuItem } from "@/stores/menu-store";
import Lucide from "@/base-components/Lucide/Lucide.vue";

const props = withDefaults(
  defineProps<{
    menuItems: FormattedMenuItem[];
    onNavigate: (item: FormattedMenuItem) => void;
    level?: number;
  }>(),
  {
    level: 0,
  },
);

const openSubMenus = ref<Record<string, boolean>>({});

function toggleSubMenu(pageName: string | undefined) {
  if (pageName) {
    openSubMenus.value[pageName] = !openSubMenus.value[pageName];
  }
}

function handleItemClick(item: FormattedMenuItem) {
  if (item.subMenu && item.subMenu.length > 0) {
    toggleSubMenu(item.pageName || item.title);
  } else {
    props.onNavigate(item);
  }
}

const currentLevel = props.level;
</script>

<template>
  <ul>
    <li v-for="item in menuItems" :key="item.pageName || item.title">
      <div
        @click="handleItemClick(item)"
        :class="[
          'flex cursor-pointer items-center rounded-md px-3 py-2.5',
          'hover:bg-white/5 active:bg-white/15',
          item.active ? 'bg-white/10 font-semibold text-white' : 'text-white/90',
          currentLevel > 0 ? 'pl-' + (3 + currentLevel * 2) : '',
        ]"
      >
        <Lucide :icon="item.icon" class="mr-3 h-5 w-5 flex-shrink-0 text-white/90" />
        <span class="truncate">{{ item.title }}</span>
        <Lucide
          v-if="item.subMenu && item.subMenu.length > 0"
          icon="ChevronDown"
          :class="[
            'ml-auto h-5 w-5 transform text-white/90 transition-transform duration-200',
            openSubMenus[item.pageName || item.title] ? 'rotate-180' : '',
          ]"
        />
      </div>
      <template v-if="item.subMenu && item.subMenu.length > 0">
        <Transition
          enter-active-class="transition ease-out duration-200"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-150"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <div v-show="openSubMenus[item.pageName || item.title]" class="mt-2">
            <MobileRecursiveMenu
              :menu-items="item.subMenu"
              :on-navigate="onNavigate"
              :level="currentLevel + 1"
            />
          </div>
        </Transition>
      </template>
    </li>
  </ul>
</template>
