<script setup lang="ts">
import { ref, computed } from "vue";
import { RouterView, useRouter } from "vue-router";
import { useMenuStore, type FormattedMenuItem } from "@/stores/menu-store";
import { useRoute } from "vue-router";
import MobileRecursiveMenu from "./MobileRecursiveMenu.vue";
import Lucide from "@/base-components/Lucide/Lucide.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useAuthStore } from "@/stores/auth-store";
import useDepartment from "@/hooks/useDepartment";
import ScrollToTop from "@/base-components/ScrollToTop";

const menuStore = useMenuStore();
const authStore = useAuthStore();
const router = useRouter();
const route = useRoute();
const { getDepartmentNameById } = useDepartment();

const isMobileMenuOpen = ref(false);
const accountMenuPopover = ref<any>();

const formattedMenu = computed(() => menuStore.formattedMenu);
const currentUser = computed(() => authStore.currentUser);

function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
}

function handleNavigation(item: FormattedMenuItem) {
  if (item.pageName) {
    router.push({ name: item.pageName });
  }
  isMobileMenuOpen.value = false;
}

const currentTitle = computed(() => {
  const findActive = (items: FormattedMenuItem[]): FormattedMenuItem | undefined => {
    for (const item of items) {
      if (item.pageName === route.name) return item;
      if (item.subMenu) {
        const activeSub = findActive(item.subMenu);
        if (activeSub) return activeSub;
      }
    }
    return undefined;
  };
  const activeItem = findActive(formattedMenu.value);
  return activeItem?.title || (route.meta.breadcrumb as string) || "App";
});

const toggleAccountMenu = (event: Event) => {
  accountMenuPopover.value?.toggle(event);
};

const goToProfile = () => {
  if (currentUser.value?.id) {
    router.push({ name: "top-menu-dashboard-user" });
  }
  accountMenuPopover.value?.hide();
};

const handleLogout = async () => {
  authStore.logout();
  await router.push({ name: "login" });
  accountMenuPopover.value?.hide();
};

const handleGlobalSearch = () => {
  router.push({ name: "mobile-search" });
};

// Reference to main content for scroll tracking
const mainContentRef = ref<HTMLElement | null>(null);
</script>

<template>
  <div class="flex h-screen flex-col bg-gray-100 dark:bg-slate-900">
    <!-- Top Bar -->
    <header
      class="sticky top-0 z-30 flex h-14 flex-shrink-0 items-center border-b border-white/20 bg-primary px-4 shadow-md"
    >
      <div class="flex flex-shrink-0 items-center gap-2">
        <Button
          icon="pi pi-bars"
          @click="toggleMobileMenu"
          text
          rounded
          aria-label="Open menu"
          class="flex-none bg-white/10 text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
        />
        <h1 class="truncate text-base font-medium text-white">
          {{ currentTitle }}
        </h1>
      </div>
      <div class="flex-grow"></div>
      <div class="flex flex-shrink-0 items-center">
        <Button
          icon="pi pi-search"
          text
          rounded
          aria-label="Global Search"
          class="flex-none bg-white/10 text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
          @click="handleGlobalSearch"
        />
        <div class="mx-2 h-8 w-px bg-white/30"></div>
        <UserAvatar
          v-if="currentUser?.id"
          :user-id="currentUser.id"
          :size="40"
          @click="toggleAccountMenu"
          aria-haspopup="true"
          aria-controls="mobile_account_menu_popover"
          class="cursor-pointer"
        />
        <Popover
          ref="accountMenuPopover"
          id="mobile_account_menu_popover"
          appendTo="body"
          :pt="{ content: { class: '!p-0' } }"
        >
          <div
            class="w-64 rounded-md bg-white text-slate-700 shadow-lg dark:bg-slate-700 dark:text-slate-200"
          >
            <div
              v-if="currentUser"
              class="border-b border-slate-200 px-4 py-2.5 dark:border-slate-600"
            >
              <p class="font-semibold">{{ currentUser.name }}</p>
              <p class="text-sm text-slate-500 dark:text-slate-400">@{{ currentUser.username }}</p>
              <p
                v-if="currentUser.department_id"
                class="mt-1 text-xs text-slate-500 dark:text-slate-400"
              >
                {{ getDepartmentNameById(currentUser.department_id) }}
              </p>
            </div>
            <ul>
              <li>
                <button
                  @click="goToProfile"
                  class="flex w-full items-center px-4 py-2.5 text-left text-sm hover:bg-slate-100 active:bg-slate-200 dark:hover:bg-slate-600/80 dark:active:bg-slate-500/80"
                >
                  <Lucide icon="User" class="mr-3 h-5 w-5 flex-shrink-0" />
                  Hồ sơ
                </button>
              </li>
              <li>
                <button
                  @click="handleLogout"
                  class="flex w-full items-center px-4 py-2.5 text-left text-sm hover:bg-slate-100 active:bg-slate-200 dark:hover:bg-slate-600/80 dark:active:bg-slate-500/80"
                >
                  <Lucide icon="ToggleRight" class="mr-3 h-5 w-5 flex-shrink-0" />
                  Đăng xuất
                </button>
              </li>
            </ul>
          </div>
        </Popover>
      </div>
    </header>

    <!-- New Blur Backdrop (appears immediately) -->
    <div
      v-if="isMobileMenuOpen"
      @click="toggleMobileMenu"
      class="fixed inset-0 z-40 bg-black/30 backdrop-blur-sm"
      aria-hidden="true"
    ></div>

    <!-- Mobile Menu Drawer -->
    <Transition
      enter-active-class="transition ease-out duration-100"
      enter-from-class="transform -translate-x-full"
      enter-to-class="transform translate-x-0"
      leave-active-class="transition ease-in duration-100"
      leave-from-class="transform translate-x-0"
      leave-to-class="transform -translate-x-full"
    >
      <nav
        v-if="isMobileMenuOpen"
        class="-webkit-overflow-scrolling-touch fixed inset-y-0 left-0 z-50 flex h-full w-full max-w-xs flex-col overflow-y-auto bg-primary p-4 shadow-xl"
        role="dialog"
        aria-modal="true"
      >
        <div class="mb-2 flex items-center justify-between">
          <span class="text-base font-medium text-white">Menu</span>
          <Button
            icon="pi pi-times"
            @click="toggleMobileMenu"
            text
            rounded
            aria-label="Close menu"
            class="text-white/80 hover:bg-white/10 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
          />
        </div>
        <MobileRecursiveMenu :menu-items="formattedMenu" :on-navigate="handleNavigation" />
      </nav>
    </Transition>

    <!-- Main Content -->
    <main ref="mainContentRef" class="-webkit-overflow-scrolling-touch flex-1 overflow-y-auto">
      <div class="mx-auto w-full max-w-full overflow-x-auto">
        <RouterView v-slot="{ Component }">
          <template v-if="Component">
            <Suspense>
              <component :is="Component" />
              <template #fallback>
                <div class="flex h-full items-center justify-center p-4">
                  <i class="pi pi-spin pi-spinner text-xl" />
                </div>
              </template>
            </Suspense>
          </template>
        </RouterView>
      </div>
    </main>

    <!-- Back to Top Button -->
    <ScrollToTop :target="mainContentRef" />
  </div>
</template>

<!-- Styles for search button animation -->
<style></style>
