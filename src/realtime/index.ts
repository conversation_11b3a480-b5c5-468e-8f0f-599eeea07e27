import { useWebSocket, WebSocketReturn, WsMessage } from "./websocket";

export class Realtime implements IRealtimePublic {
  private webSocket: WebSocketReturn;
  private joinedRooms: Set<string> = new Set();
  private eventCallbacks: Map<string, Array<(msg: WsMessage) => void>> = new Map();

  constructor(url: string, onMessage?: (msg: WsMessage) => void, onData?: (data: string) => void) {
    this.webSocket = useWebSocket(
      url,
      (msg: WsMessage) => {
        onMessage?.(msg);
        this.handleMessage(msg);
      },
      onData,
    );
  }

  private handleMessage(msg: WsMessage): void {
    this.eventCallbacks.get(msg.event)?.forEach((callback) => callback(msg));
  }

  public getWs(): WebSocketReturn {
    return this.webSocket;
  }

  public async join(roomName: string): Promise<void> {
    try {
      await this.webSocket.ask("join", roomName);
      this.joinedRooms.add(roomName);
      console.log(`Joined ${roomName}`);
    } catch (error) {
      console.error("Join room error:", error);
      throw error;
    }
  }

  public async mustJoin(roomName: string): Promise<void> {
    try {
      await this.webSocket.ask("must-join", roomName);
      this.joinedRooms.add(roomName);
      console.log(`Joined ${roomName}`);
    } catch (error) {
      console.error("Must join room error:", error);
      throw error;
    }
  }

  public async leave(roomName: string): Promise<void> {
    if (!this.joinedRooms.has(roomName)) {
      console.warn(`You are not in room ${roomName}`);
      return;
    }

    try {
      await this.webSocket.ask("leave", roomName);
      this.joinedRooms.delete(roomName);
      console.log(`Left ${roomName}`);
    } catch (error) {
      console.error("Leave room error:", error);
      throw error;
    }
  }

  public async on(event: string, callback: (msg: WsMessage) => void): Promise<void> {
    try {
      await this.webSocket.ask("on", event);
      if (!this.eventCallbacks.has(event)) {
        this.eventCallbacks.set(event, []);
      }
      this.eventCallbacks.get(event)!.push(callback);
    } catch (error) {
      console.error("Register event handle error:", error);
      throw error;
    }
  }

  public async off(event: string, callback: (msg: WsMessage) => void): Promise<void> {
    const callbacks = this.eventCallbacks.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
        if (callbacks.length === 0) {
          this.eventCallbacks.delete(event);
          await this.webSocket.ask("off", event);
        }
      }
    }
  }

  public emit(event: string, data?: any, callback?: (resp: any) => void): void {
    if (typeof callback === "function") {
      this.webSocket.ask(event, data).then(callback);
    } else {
      this.webSocket.sendMessage({ event, data });
    }
  }

  public get broadcast() {
    return new BroadcastOperator(this, true);
  }

  public rooms(): string[] {
    return Array.from(this.joinedRooms);
  }

  public to(roomName: string): BroadcastOperator {
    return new BroadcastOperator(this).to(roomName);
  }

  public except(roomName: string): BroadcastOperator {
    return new BroadcastOperator(this).except(roomName);
  }
}

class BroadcastOperator implements IBroadcastOperator {
  private realtime: Realtime;
  private rooms: Set<string> = new Set();
  private exceptRooms: Set<string> = new Set();
  private isBroadcast: boolean;

  constructor(realtime: Realtime, isBroadcast: boolean = false) {
    this.realtime = realtime;
    this.isBroadcast = isBroadcast;
  }

  public to(roomName: string): BroadcastOperator {
    this.rooms.add(roomName);
    return this;
  }

  public except(roomName: string): BroadcastOperator {
    this.exceptRooms.add(roomName);
    return this;
  }

  public emit(event: string, data?: any): void {
    const metadata = {
      rooms: Array.from(this.rooms),
      except: Array.from(this.exceptRooms),
      broadcast: this.isBroadcast,
    };

    this.realtime.getWs().sendMessage({ event, data, metadata });
  }
}

// Interface cho BroadcastOperator
export interface IBroadcastOperator {
  to(roomName: string): IBroadcastOperator;
  except(roomName: string): IBroadcastOperator;
  emit(event: string, data?: any): void;
}

export interface IRealtimePublic {
  getWs: () => WebSocketReturn;
  join: (roomName: string) => Promise<void>;
  mustJoin: (roomName: string) => Promise<void>;
  leave: (roomName: string) => Promise<void>;
  on: (event: string, callback: (msg: WsMessage) => void) => Promise<void>;
  off: (event: string, callback: (msg: WsMessage) => void) => Promise<void>;
  emit: (event: string, data?: any, callback?: (resp: any) => void) => void;
  broadcast: IBroadcastOperator;
  rooms: () => string[];
  to: (roomName: string) => BroadcastOperator;
  except: (roomName: string) => BroadcastOperator;
}

export default Realtime;
