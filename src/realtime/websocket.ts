/* eslint-disable no-console */
/* eslint-disable max-statements */
import <PERSON><PERSON> from "js-cookie";
import { nanoid } from "nanoid";
import { Ref, ref, unref } from "vue";

import { COOKIE_ENUM } from "@/enums/cookie-enum";

export const isClient = typeof window !== "undefined";
export const NO_OP = () => {};

const isDev = import.meta.env.MODE === "development";
const pingInterval: number = 10000; // Ping interval in milliseconds
const heartbeatWaitTimeout: number = 15000; // Heartbeat wait timeout in milliseconds

export interface WsMessage {
  event: string;
  data?: any;
  metadata?: any;
  id?: string;
}

export interface WsResponseData {
  code: number; //#0 mean error
  message?: string;
  data?: any;
}

interface PendingRequest {
  id: string;
  resolve: (data: any) => void;
  reject: (error: any) => void;
}

const pendingRequests: Map<string, PendingRequest> = new Map();

function generateRequestId(): string {
  return nanoid();
}

export interface WebSocketReturn {
  supported: boolean;

  data: Ref<any | null>;

  messageEvent: Ref<MessageEvent | null>;
  errorEvent: Ref<Event | null>;
  errored: Ref<boolean>;
  isClosed: Ref<boolean>;
  isOpen: Ref<boolean>;
  isReady: Ref<boolean>;

  send: (data: string | ArrayBufferLike | Blob | ArrayBufferView) => void;
  sendMessage: (message: WsMessage) => void;
  ask: (event: string, data?: any, metadata?: any) => Promise<any>;

  addEvent: (event: "open" | "ready" | "close" | "message" | "error", callback: () => void) => void;
  close: Function;
  connect: Function;
  disconnect: Function;

  ws: WebSocket | null;
}

export function useWebSocket(
  url: string,
  onMessage: (message: WsMessage) => void,
  onData?: (data: string) => void,
  protocols?: string | string[],
  reconnection: boolean = true,
  reconnectionAttempts: number = 5,
  reconnectionDelay: number = 3000,
): WebSocketReturn {
  const supported = isClient && "WebSocket" in window;
  let ws: WebSocket | null = null;
  const messageEvent = ref(null) as Ref<MessageEvent | null>;
  const errorEvent = ref<Event | null>(null);
  const data = ref<any>(null);

  //Sec-WebSocket-Key
  const key = ref("");
  //Only ready when key is set
  const isReady = ref(false);
  const isOpen = ref(false);
  const isClosed = ref(false);
  const errored = ref(false);

  /* istanbul ignore next  */
  let lastMessage = (isDev && Date.now()) || undefined;

  let send: (data: string | ArrayBufferLike | Blob | ArrayBufferView) => void = NO_OP;

  let sendMessage: (message: WsMessage) => void = NO_OP;
  let ask: (event: string, data?: any, metadata?: any) => Promise<any> = undefined!;
  let close: (code?: number, reason?: string) => void = NO_OP;

  const eventListeners: Record<string, Array<() => void>> = {};
  let reconnectAttempts = 0;
  let reconnectTimer: number | null = null;
  let pingTimer: ReturnType<typeof setTimeout> | null = null;
  let waitForPongTimeout: ReturnType<typeof setTimeout> | null = null;

  const isConnected = ref(false); // Add a state to track if connect has been called

  const connect = (specifiedUrl = url) => {
    if (supported && !unref(isConnected)) {
      isConnected.value = true;
      ws = new WebSocket(specifiedUrl, protocols);

      ws.addEventListener("message", (x) => {
        if (x.data == "pong") {
          if (waitForPongTimeout) {
            clearTimeout(waitForPongTimeout);
            waitForPongTimeout = null;
          }
          return;
        } else if (x.data === "ping") {
          sendPong();
          return;
        }

        eventListeners.message?.forEach((callback) => {
          callback();
        });

        //WsMessage
        try {
          const message: WsMessage = JSON.parse(x.data);

          //first data from server
          if (!unref(isReady) && message.event == "auth" && message.data.length > 10) {
            key.value = message.data;
            isReady.value = true;
            eventListeners.ready?.forEach((callback) => {
              callback();
            });

            //Send ping khi da nhan duoc key
            sendPing();
            return;
          }

          //Nếu là broadcast thì tránh handle event đó
          if (message.metadata?.broadcast == true && message.metadata?.key == key.value) {
            return;
          }

          //If it is request-response msg
          if (message.id) {
            const pendingRequest = pendingRequests.get(message.id);
            if (pendingRequest) {
              const respData = message.data;
              if (typeof respData === "object" && "code" in respData) {
                if (respData.code === 0) {
                  pendingRequest.resolve(respData);
                } else {
                  pendingRequest.reject(respData);
                }
              } else {
                //Neu ko co field code thi cho resolve quyet dinh
                pendingRequest.resolve(respData);
              }

              pendingRequests.delete(message.id);
            }
          } else {
            onMessage(message);
          }
        } catch {
          onData?.(x.data);
          messageEvent.value = x;
          data.value = x.data;
        }

        if (onData) {
          onData(x.data);
        }
        messageEvent.value = x;
        data.value = x.data;

        // if the messages are too quick, we need to warn
        if (isDev) {
          if (Date.now() - lastMessage! < 2) {
            // console.warn(
            //   '[useWebSocket] message rate is too high, if you are using "data" or "messageEvent"' +
            //     " you might not get updated of all the messages. This is because of vue reactivity system tick rate" +
            //     " Use onData callback instead",
            // );
          }
          lastMessage = Date.now();
        }
      });

      ws.addEventListener("error", (error) => {
        errorEvent.value = error;
        errored.value = true;
        eventListeners.error?.forEach((callback) => {
          callback();
        });
        // if (reconnection && reconnectAttempts < reconnectionAttempts) {
        //     console.warn('WS attempting to reconnect', error);
        //     reconnectAttempts++;
        //     reconnectTimer = window.setTimeout(connect, reconnectionDelay);
        // }
      });

      ws.addEventListener("close", (ev) => {
        isOpen.value = false;
        isClosed.value = true;
        eventListeners.close?.forEach((callback) => {
          callback();
        });
        if (reconnection && reconnectAttempts < reconnectionAttempts) {
          isConnected.value = false;
          reconnectAttempts++;
          reconnectTimer = window.setTimeout(connect, reconnectionDelay);
          reconnectionDelay *= 2;
        }
        console.warn("WS has been closed due to", ev);
      });

      ws.addEventListener("open", () => {
        sendAuth();
        isOpen.value = true;
        isClosed.value = false;
        reconnectAttempts = 0;
        eventListeners.open?.forEach((callback) => {
          callback();
        });
        if (reconnectTimer) {
          clearTimeout(reconnectTimer);
        }
        reconnectionDelay = 1000;
      });

      send = (data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
        ws!.send(data);
      };

      sendMessage = (message: WsMessage) => {
        const jsonMessage = JSON.stringify(message);
        send(jsonMessage);
      };

      ask = (event: string, data?: any, metadata?: any): Promise<any> => {
        return new Promise((resolve, reject) => {
          const requestId = generateRequestId();
          const message: WsMessage = { event, data, metadata, id: requestId };
          try {
            sendMessage(message);
            pendingRequests.set(requestId, {
              id: requestId,
              resolve: (response: any) => resolve(response),
              reject: (error: any) => reject(error),
            });
          } catch (error) {
            // Xử lý lỗi khi gửi tin nhắn
            reject(error);
          }
        });
      };

      close = (code?: number, reason?: string) => {
        ws!.close(code, reason);
      };
    }
  };

  const addEvent = (
    event: "open" | "ready" | "close" | "message" | "error",
    callback: () => void,
  ) => {
    if (eventListeners[event]) eventListeners[event].push(callback);
    else eventListeners[event] = [callback];
  };

  const disconnect = () => {
    if (ws) {
      ws.close();
      ws = null;
    }
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
    if (pingTimer) {
      clearTimeout(pingTimer); // Sử dụng clearTimeout thay vì clearInterval
      pingTimer = null;
    }
    if (waitForPongTimeout) {
      clearTimeout(waitForPongTimeout);
      waitForPongTimeout = null;
    }
    isConnected.value = false;
    isReady.value = false;
  };

  const sendPing = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send("ping");

      waitForPongTimeout = setTimeout(() => {
        console.warn("Heartbeat wait timeout exceeded. Reconnecting...");
        ws?.close();
      }, heartbeatWaitTimeout);

      pingTimer = setTimeout(sendPing, pingInterval);
    } else {
      console.warn(ws?.readyState);
    }
  };

  const sendAuth = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      const accessToken = Cookie.get(COOKIE_ENUM.ACCESS_TOKEN);
      if (accessToken) {
        const authMessage: WsMessage = {
          event: "auth",
          data: accessToken,
        };
        ws.send(JSON.stringify(authMessage));
      } else {
        console.warn("Access token not found");
      }
    } else {
      console.warn(ws?.readyState);
    }
  };

  const sendPong = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send("pong");
    } else {
      console.warn("WebSocket is not open. Unable to send pong.");
    }
  };

  connect();

  return {
    supported,

    ws,
    connect,
    send,
    sendMessage,
    ask,
    disconnect,
    close,
    addEvent,

    messageEvent,
    errorEvent,

    data,

    isReady,
    isOpen,
    isClosed,
    errored,
  };
}
