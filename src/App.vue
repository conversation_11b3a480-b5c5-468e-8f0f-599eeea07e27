<script setup lang="ts">
import { defineAsyncComponent, onMounted, onUnmounted } from "vue";

import SimpleNoti from "@/base-components/Notification/SimpleNoti.vue";

import { useWsStore } from "./stores/ws-store";

const LazyConfirmDialog = defineAsyncComponent(() => import("primevue/confirmdialog"));
const LazyCallCenter = defineAsyncComponent(
  () => import("@/base-components/SipPhone/CallCenter.vue"),
);
const LazyModalCustomer = defineAsyncComponent(() => import("@/pages/customer/ModalCustomer.vue"));

const wsStore = useWsStore();
wsStore.

onMounted(() => wsStore.connect());
onUnmounted(() => wsStore.disconnect());
</script>

<template>
  <Suspense>
    <template #default>
      <div>
        <LazyConfirmDialog group="global" />
        <LazyCallCenter />
      </div>
    </template>

    <template #fallback>
      <div class="fixed bottom-4 right-4 w-80 rounded-lg bg-white p-4 shadow-lg">
        <i class="pi pi-spin pi-spinner mr-2" />
        Loading ...
      </div>
    </template>
  </Suspense>

  <SimpleNoti />
  <RouterView />
  <LazyModalCustomer />
</template>
