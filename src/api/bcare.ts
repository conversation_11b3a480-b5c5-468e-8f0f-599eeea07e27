import * as types from "./bcare-types";
import service from "./http";

export * from "./bcare-types";

//Deprecate

/**
 * @description
 * @param req
 */
export function userAdd(req: types.UserAddRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/add`, req);
}

/**
 * @description
 * @param req
 */
export function userGet(req: types.UserGetRequest) {
  return service.post<types.UserResponse>(`/v1/user/get`, req);
}

/**
 * @description
 * @param req
 */
export function userList(req: types.UserListRequest) {
  return service.post<types.GenericResponse<types.UserListResponse>>(`/v1/user/list`, req);
}

/**
 * @description
 * @param req
 */
export function userUpdate(req: types.UserUpdateRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/update`, req);
}

/**
 * @description
 * @param req
 */
export function userDelete(req: types.UserDeleteRequest) {
  return service.post<types.GenericResponse<types.UserResponse>>(`/v1/user/delete`, req);
}

// /**
//  * @description
//  * @param req
//  */
// export function scheduleUpdate(req: types.ScheduleUpdateRequest) {
//   return service.post<types.GenericResponse<types.Schedule>>(`/v1/schedule/update`, req)
// }
/**
 * @description
 * @param req
 */
export function authLogin(req: types.LoginRequest) {
  return service.post<types.GenericResponse<types.LoginResponse>>(`/v1/auth/login`, req);
}

/**
 * @description
 * @param req
 */
export function authRefresh(req: types.RefreshTokenRequest) {
  return service.post<types.GenericResponse<types.RefreshTokenResponse>>(`/v1/auth/refresh`, req);
}

/**
 * @description
 * @param req
 */
export function productAdd(req: types.ProductAddRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/add`, req);
}

/**
 * @description
 * @param req
 */
export function productGet(req: types.ProductGetRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/get`, req);
}

/**
 * @description
 * @param req
 */
export function productDelete(req: types.ProductGetRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/delete`, req);
}

/**
 * @description
 * @param req
 */
export function productUpdate(req: types.ProductUpdateRequest) {
  return service.post<types.GenericResponse<types.ProductResponse>>(`/v1/product/update`, req);
}

/**
 * @description
 * @param req
 */
export function productList(req: types.ProductListRequest) {
  return service.post<types.GenericResponse<types.ProductListResponse>>(`/v1/product/list`, req);
}

/**
 * @description
 * @param params
 */
export function Hello(params: types.HelloRequestParams, name: string) {
  return service.get<types.GenericResponse<types.HelloResponse>>(`/v1/hello/hello/${name}`, params);
}

//
// /**
//  * @description
//  * @param req
//  * @param headers
//  */
// export function add(req: types.FieldConfigRequest, headers: types.FieldConfigRequestHeaders) {
// 	return service.post<types.CommonConfigResponse>(`/v1/field/add`, req, headers)
// }
//
// /**
//  * @description
//  * @param req
//  * @param headers
//  */
// export function update(req: types.FieldConfigRequest, headers: types.FieldConfigRequestHeaders) {
// 	return service.post<types.CommonConfigResponse>(`/v1/field/update`, req, headers)
// }

/**
 * @description
 * @param req
 */
export function personAdd(req: types.PersonAddRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/add`, req);
}

/**
 * @description
 * @param req
 */
export function personGet(req: types.PersonGetRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/get`, req);
}

/**
 * @description
 * @param req
 */
export function personList(req: types.PersonListRequest) {
  return service.post<types.GenericResponse<types.PersonListResponse>>(`/v1/person/list`, req);
}

/**
 * @description
 * @param req
 */
/**
 * @description
 * @param req
 */
export function personAddPersonStage(req: types.AddPersonStageRequest) {
  return service.post<types.GenericResponse<types.PersonStage>>(`/v1/person/stage/add`, req);
}

/**
 * @description
 * @param req
 */
export function personDeletePersonStage(req: types.DeletePersonStageRequest) {
  return service.post<types.GenericResponse<types.PersonStage>>(`/v1/person/stage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function personGetPersonStage(req: types.GetPersonStageRequest) {
  return service.post<types.GenericResponse<types.PersonStage>>(`/v1/person/stage/get`, req);
}

/**
 * @description
 * @param req
 */
export function personUpdatePersonStage(req: types.UpdatePersonStageRequest) {
  return service.post<types.GenericResponse<types.PersonStage>>(`/v1/person/stage/update`, req);
}

export function scheduleList(req: types.ScheduleListRequest) {
  return service.post<types.GenericResponse<types.ScheduleListResponse>>(
    `/v1/schedule/get-work-schedule`,
    req,
  );
}

export function schedulesList(req: types.ScheduleListRequest) {
  return service.post<types.GenericResponse<types.SchedulesResponse>>(`/v1/schedule/list`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleupdate(req: types.ScheduleUpdateRequest) {
  return service.post<types.GenericResponse<types.Schedules>>(`/v1/schedule/update`, req);
}

// appointment
export function createAppointment(req: types.CreateAppointmentRequest) {
  return service.post<types.GenericResponse<types.CreateAppointmentsResponse>>(
    `/v1/appointment/add`,
    req,
  );
}

export function deleteAppointment(req: types.IdAppointmentRequest) {
  return service.post<types.GenericResponse<types.CreateAppointmentsResponse>>(
    `/v1/appointment/delete`,
    req,
  );
}

export function getDetailAppointment(req: types.IdAppointmentRequest) {
  return service.post<types.GenericResponse<types.CreateAppointmentsResponse>>(
    `/v1/appointment/get`,
    req,
  );
}

export function updateAppointment(req: types.UpdateAppointmentRequest) {
  return service.post<types.GenericResponse<types.ItemAppointmentList>>(
    `/v1/appointment/update`,
    req,
  );
}

export function getListAppointment(req: types.ListAppointmentRequest) {
  return service.post<types.GenericResponse<types.ListAppointmentResponse>>(
    `/v1/appointment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function appointmentGetLasted(req: types.AppointmentGetRequest) {
  return service.post<types.GenericResponse<types.ItemAppointmentList>>(
    `/v1/appointment/get/lasted`,
    req,
  );
}

export function getConstantType() {
  return service.post<types.GenericResponse<types.GetConstantResponse>>(`v1/constant`);
}

/**
 * @description
 * @param req
 */
export function personUpdate(req: types.PersonUpdateRequest) {
  return service.post<types.GenericResponse<types.PersonResponse>>(`/v1/person/update`, req);
}

/**
 * @description
 * @param req
 */
export function personExpectTask(req: types.PersonExpectTaskRequest) {
  return service.post<types.GenericResponse<{ expected_task: string }>>(
    `/v1/person/expect-task`,
    req,
  );
}

//
// /**
//  * @description
//  * @param req
//  */
// export function delete(req: types.PersonDeleteRequest) {
// 	return service.post<types.PersonResponse>(`/v1/person/delete`, req)
// }
export function bundleAdd(req: types.BundleAddRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/add`, req);
}

/**
 * @description
 * @param req
 */
export function bundleDelete(req: types.BundleGetRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/delete`, req);
}

/**
 * @description
 * @param req
 */
export function bundleUpdate(req: types.BundleUpdateRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/update`, req);
}

/**
 * @description
 * @param req
 */
export function bundleGet(req: types.BundleGetRequest) {
  return service.post<types.GenericResponse<types.BundleResponse>>(`/v1/bundle/get`, req);
}

/**
 * @description
 * @param req
 */
export function bundleList(req: types.BundleListRequest) {
  return service.post<types.GenericResponse<types.BundleListResponse>>(`/v1/bundle/list`, req);
}

//
// /**
//  * @description
//  * @param req
//  */
// export function termAdd(req: types.TermAddRequest) {
// 	return service.post<types.TermAddResponse>(`/v1/taxonomy/term-add`, req)
// }
//
// /**
//  * @description
//  * @param req
//  */
// export function termDelete(req: types.TermGetRequest) {
// 	return service.post<types.TermAddResponse>(`/v1/taxonomy/term-delete`, req)
// }
//
// /**
//  * @description
//  * @param req
//  */
// export function termUpdate(req: types.TermUpdateRequest) {
// 	return service.post<types.TermAddResponse>(`/v1/taxonomy/term-update`, req)
// }
//
// /**
//  * @description
//  * @param req
//  */
// export function termGet(req: types.TermGetRequest) {
// 	return service.post<types.TermGetResponse>(`/v1/taxonomy/term-get`, req)
// }
//
// /**
//  * @description
//  * @param req
//  */
export function termList(req: types.TermGetRequest) {
  return service.post<types.GenericResponse<types.TermGetResponse>>(`/v1/taxonomy/term-list`, req);
}

//
// /**
//  * @description
//  * @param req
//  */

export function addressGet() {
  return service.post<types.GenericResponse<types.LocationResponse>>(`/v1/location`);
}

/**
 * @description
 * @param req
 */
export function pipelineAdd(req: types.PipelineAddRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/add`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineDelete(req: types.PipelineDeleteRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/delete`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineGet(req: types.PipelineGetRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/get`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineList(req: types.PipelineListRequest) {
  return service.post<types.GenericResponse<types.PipelineListResponse>>(`/v1/pipeline/list`, req);
}

/**
 * @description
 * @param req
 */
export function pipelineUpdate(req: types.PipelineUpdateRequest) {
  return service.post<types.GenericResponse<types.PipelineResponse>>(`/v1/pipeline/update`, req);
}

/**
 * @description
 * @param req
 */
export function stageAdd(req: types.StageAddRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/add`, req);
}

/**
 * @description
 * @param req
 */
export function stageDelete(req: types.StageDeleteRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function stageGet(req: types.StageGetRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/get`, req);
}

/**
 * @description
 * @param req
 */
export function stageList(req: types.StageListRequest) {
  return service.post<types.GenericResponse<types.StageListResponse>>(`/v1/stage/list`, req);
}

/**
 * @description
 * @param req
 */
export function stageUpdate(req: types.StageUpdateRequest) {
  return service.post<types.GenericResponse<types.StageResponse>>(`/v1/stage/update`, req);
}

/**
 * @description
 * @param req
 */
export function dealAdd(req: types.DealAddRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/add`, req);
}

/**
 * @description
 * @param req
 */
export function dealDelete(req: types.DealDeleteRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/delete`, req);
}

/**
 * @description
 * @param req
 */
export function dealGet(req: types.DealGetRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/get`, req);
}

/**
 * @description
 * @param req
 */
export function dealList(req: types.DealListRequest) {
  return service.post<types.GenericResponse<types.DealListResponse>>(`/v1/deal/list`, req);
}

/**
 * @description
 * @param req
 */
export function dealListBig(req: types.DealListRequest) {
  return service.post<types.GenericResponse<types.DealListResponse>>(`/v1/deal/list-big`, req);
}

/**
 * @description
 * @param req
 */
export function dealUpdate(req: types.DealUpdateRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/update`, req);
}

/**
 * @description
 * @param req
 */
export function dealUpdateInstallment(req: types.DealUpdateInstallmentRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(
    `/v1/deal/update-installment`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function deal_userAdd(req: types.DealUserAddRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/add`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userDelete(req: types.DealUserDeleteRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/delete`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userList(req: types.DealUserListRequest) {
  return service.post<types.GenericResponse<types.DealUserListResponse>>(`/v1/deal_user/list`, req);
}

/**
 * @description
 * @param req
 */
export function deal_userUpdate(req: types.DealUserUpdateRequest) {
  return service.post<types.GenericResponse<types.DealUser>>(`/v1/deal_user/update`, req);
}

/**
 * @description
 * @param req
 */
export function installmentList(req: types.InstallmentListRequest) {
  return service.post<types.GenericResponse<types.InstallmentListResponse>>(
    `/v1/installment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function installmentAdd(req: types.InstallmentAddRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/add`, req);
}

/**
 * @description
 * @param req
 */
export function installmentDelete(req: types.InstallmentDeleteRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/delete`, req);
}

/**
 * @description
 * @param req
 */
export function installmentUpdate(req: types.InstallmentUpdateRequest) {
  return service.post<types.GenericResponse<types.Installment>>(`/v1/installment/update`, req);
}

/**
 * @description
 * @param req
 */
export function dealUpdatePayment(req: types.DealUpdatePaymentRequest) {
  return service.post<types.GenericResponse<types.DealResponse>>(`/v1/deal/update-payment`, req);
}

// upload file
/**
 * @description
 * @param req
 */
export function attachmentAdd(req: types.AttachmentAddRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(`/v1/attachment/add`, req);
}

/**
 * @description
 * @param req
 */
export function attachmentDelete(req: types.AttachmentDeleteRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(
    `/v1/attachment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentGet(req: types.AttachmentGetRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(`/v1/attachment/get`, req);
}

/**
 * @description
 * @param req
 */
export function attachmentList(req: types.AttachmentListRequest) {
  return service.post<types.GenericResponse<types.AttachmentListResponse>>(
    `/v1/attachment/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function attachmentUpdate(req: types.AttachmentUpdateRequest) {
  return service.post<types.GenericResponse<types.AttachmentResponse>>(
    `/v1/attachment/update`,
    req,
  );
}

export function treatmentLogAdd(req: types.TreatmentLogAddRequest) {
  return service.post<types.GenericResponse<types.TreatmentLog>>(`/v1/treatment_log/add`, req);
}

export function treatmentLogDelete(req: types.TreatmentLogDeleteRequest) {
  return service.post<types.GenericResponse<types.TreatmentLog>>(`/v1/treatment_log/delete`, req);
}

export function treatmentLogList(req: types.TreatmentLogListRequest) {
  return service.post<types.GenericResponse<types.TreatmentLogListResponse>>(
    `/v1/treatment_log/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function treatmentLogUpdate(req: types.TreatmentLogUpdateRequest) {
  return service.post<types.GenericResponse<types.TreatmentLog>>(`/v1/treatment_log/update`, req);
}

export function addFile(req: FormData) {
  return service.upload<types.GenericResponse<types.FileType>>("/v1/file/add", req);
}

export function getListFileUsage(req: types.FileUsageListRequest) {
  return service.post<types.GenericResponse<types.TypeListFileUsageResponse>>(
    `/v1/file_usage/list`,
    req,
  );
}

export function addFileUsage(req: types.CreateFileUsageRequest) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/add`, req);
}

export function deleteFileUsage(req: { id: number }) {
  return service.post<types.GenericResponse<types.FileUsageResponse>>(`/v1/file_usage/delete`, req);
}

/**
 * @description
 * @param req
 */
export function quoteAdd(req: types.QuoteAddRequest) {
  return service.post<types.GenericResponse<types.Quote>>(`/v1/quote/add`, req);
}

/**
 * @description
 * @param req
 */
export function quoteDelete(req: types.QuoteDeleteRequest) {
  return service.post<types.GenericResponse<types.Quote>>(`/v1/quote/delete`, req);
}

/**
 * @description
 * @param req
 */
export function quoteList(req: types.QuoteListRequest) {
  return service.post<types.GenericResponse<types.QuoteListResponse>>(`/v1/quote/list`, req);
}

/**
 * @description
 * @param req
 */
export function quoteUpdate(req: types.QuoteUpdateRequest) {
  return service.post<types.GenericResponse<types.Quote>>(`/v1/quote/update`, req);
}

/**
 * @description
 * @param req
 */
export function quote_itemAdd(req: types.QuoteItemAddRequest) {
  return service.post<types.GenericResponse<types.QuoteItem>>(`/v1/quote_item/add`, req);
}

/**
 * @description
 * @param req
 */
export function quote_itemDelete(req: types.QuoteItemDeleteRequest) {
  return service.post<types.GenericResponse<types.QuoteItem>>(`/v1/quote_item/delete`, req);
}

/**
 * @description
 * @param req
 */
export function quote_itemList(req: types.QuoteItemListRequest) {
  return service.post<types.GenericResponse<types.QuoteItemListResponse>>(
    `/v1/quote_item/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function quote_itemUpdate(req: types.QuoteItemUpdateRequest) {
  return service.post<types.GenericResponse<types.QuoteItem>>(`/v1/quote_item/update`, req);
}

/**
 * @description
 * @param req
 */
export function surveyAdd(req: types.SurveyAddRequest) {
  return service.post<types.GenericResponse<types.Survey>>(`/v1/survey/add`, req);
}

/**
 * @description
 * @param req
 */
export function surveyDelete(req: types.SurveyDeleteRequest) {
  return service.post<types.GenericResponse<types.Survey>>(`/v1/survey/delete`, req);
}

/**
 * @description
 * @param req
 */
export function surveyList(req: types.SurveyListRequest) {
  return service.post<types.GenericResponse<types.SurveyListResponse>>(`/v1/survey/list`, req);
}

/**
 * @description
 * @param req
 */
export function surveyUpdate(req: types.SurveyUpdateRequest) {
  return service.post<types.GenericResponse<types.Survey>>(`/v1/survey/update`, req);
}

/**
 * @description
 * @param req
 */
export function responseAdd(req: types.ResponseAddRequest) {
  return service.post<types.GenericResponse<types.Response>>(`/v1/response/add`, req);
}

/**
 * @description
 * @param req
 */
export function responseDelete(req: types.ResponseDeleteRequest) {
  return service.post<types.GenericResponse<types.Response>>(`/v1/response/delete`, req);
}

/**
 * @description
 * @param req
 */
export function responseList(req: types.ResponseListRequest) {
  return service.post<types.GenericResponse<types.ResponseListResponse>>(`/v1/response/list`, req);
}

/**
 * @description
 * @param req
 */
export function responseUpdate(req: types.ResponseUpdateRequest) {
  return service.post<types.GenericResponse<types.Response>>(`/v1/response/update`, req);
}

/**
 * @description
 * @param req
 */
export function questionAdd(req: types.QuestionAddRequest) {
  return service.post<types.GenericResponse<types.Question>>(`/v1/question/add`, req);
}

/**
 * @description
 * @param req
 */
export function questionDelete(req: types.QuestionDeleteRequest) {
  return service.post<types.GenericResponse<types.Question>>(`/v1/question/delete`, req);
}

/**
 * @description
 * @param req
 */
export function questionList(req: types.QuestionListRequest) {
  return service.post<types.GenericResponse<types.QuestionListResponse>>(`/v1/question/list`, req);
}

/**
 * @description
 * @param req
 */
export function questionUpdate(req: types.QuestionUpdateRequest) {
  return service.post<types.GenericResponse<types.Question>>(`/v1/question/update`, req);
}

/**
 * @description
 * @param req
 */
export function noteAdd(req: types.NoteAddRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/add`, req);
}

/**
 * @description
 * @param req
 */
export function noteDelete(req: types.NoteDeleteRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/delete`, req);
}

/**
 * @description
 * @param req
 */
export function noteList(req: types.NoteListRequest) {
  return service.post<types.GenericResponse<types.NoteListResponse>>(`/v1/note/list`, req);
}

/**
 * @description
 * @param req
 */
export function noteUpdate(req: types.NoteUpdateRequest) {
  return service.post<types.GenericResponse<types.Note>>(`/v1/note/update`, req);
}

/**
 * @description
 * @param req
 */
export function discountAdd(req: types.DiscountAddRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/add`, req);
}

/**
 * @description
 * @param req
 */
export function discountDelete(req: types.DiscountDeleteRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/delete`, req);
}

/**
 * @description
 * @param req
 */
export function discountGet(req: types.DiscountGetRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/get`, req);
}

/**
 * @description
 * @param req
 */
export function discountList(req: types.DiscountListRequest) {
  return service.post<types.GenericResponse<types.DiscountListResponse>>(`/v1/discount/list`, req);
}

/**
 * @description
 * @param req
 */
export function discountUpdate(req: types.DiscountUpdateRequest) {
  return service.post<types.GenericResponse<types.Discount>>(`/v1/discount/update`, req);
}

/**
 * @description
 * @param req
 */
export function discount_usageAdd(req: types.DiscountUsageAddRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageResponse>>(
    `/v1/discount_usage/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discount_usageDelete(req: types.DiscountUsageDeleteRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageResponse>>(
    `/v1/discount_usage/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discount_usageList(req: types.DiscountUsageListRequest) {
  return service.post<types.GenericResponse<types.DiscountUsageListResponse>>(
    `/v1/discount_usage/list`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function discountEligibleDiscount(req: types.EligibleAddRequest) {
  return service.post<types.GenericResponse<types.EligibleDiscountResponse>>(
    `/v1/discount/eligible`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function referralAdd(req: types.ReferralAddRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/add`, req);
}

/**
 * @description
 * @param req
 */
export function referralDelete(req: types.ReferralDeleteRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/delete`, req);
}

/**
 * @description
 * @param req
 */
export function referralSearchReferrer(req: types.SearchRequest) {
  return service.post<types.GenericResponse<types.SearchResponse>>(
    `/v1/referral/search-referrer`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function referralUpdate(req: types.ReferralUpdateRequest) {
  return service.post<types.GenericResponse<types.Referral>>(`/v1/referral/update`, req);
}

/**
 * @description
 * @param req
 */
export function billAdd(req: types.BillAddRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/add`, req);
}

/**
 * @description
 * @param req
 */
export function billDelete(req: types.BillDeleteRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/delete`, req);
}

/**
 * @description
 * @param req
 */
export function billGet(req: types.BillGetRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/get`, req);
}

/**
 * @description
 * @param req
 */
export function billList(req: types.BillListRequest) {
  return service.post<types.GenericResponse<types.BillListResponse>>(`/v1/bill/list`, req);
}

/**
 * @description
 * @param req
 */
export function billUpdate(req: types.BillUpdateRequest) {
  return service.post<types.GenericResponse<types.BillResponse>>(`/v1/bill/update`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemAdd(req: types.BillItemAddRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill_item/add`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemDelete(req: types.BillItemDeleteRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill_item/delete`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemGet(req: types.BillItemGetRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill_item/get`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemList(req: types.BillItemListRequest) {
  return service.post<types.GenericResponse<types.BillItemListResponse>>(`/v1/bill_item/list`, req);
}

/**
 * @description
 * @param req
 */
export function bill_itemUpdate(req: types.BillItemUpdateRequest) {
  return service.post<types.GenericResponse<types.BillItemResponse>>(`/v1/bill_item/update`, req);
}

/**
 * @description
 * @param req
 */
export function taskAdd(req: types.TaskAddRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/add`, req);
}

/**
 * @description
 * @param req
 */
export function taskDelete(req: types.TaskDeleteRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskGet(req: types.TaskGetRequest) {
  return service.post<types.GenericResponse<types.TaskResponse>>(`/v1/task/get`, req);
}

/**
 * @description
 * @param req
 */
export function taskList(req: types.TaskListRequest) {
  return service.post<types.GenericResponse<types.TaskListResponse>>(`/v1/task/list`, req);
}

/**
 * @description
 * @param req
 */
export function taskUpdate(req: types.TaskUpdateRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/update`, req);
}

/**
 * @description
 * @param req
 */
export function task_assignmentAdd(req: types.TaskAssignmentAddRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(`/v1/task_assignment/add`, req);
}

/**
 * @description
 * @param req
 */
export function task_assignmentDelete(req: types.TaskAssignmentDeleteRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function task_assignmentUpdateMultiple(req: types.TaskAssignmentUpdateMultipleRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/update-multiple`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function taskUpdateStatusMultiple(req: types.TaskUpdateMultipleRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/update-status-multiple`, req);
}

/**
 * @description
 * @param req
 */
export function taskDeleteMultiple(req: types.TaskDeleteMultipleRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/delete-multiple`, req);
}

/**
 * @description
 * @param req
 */
export function taskBulkDelete(req: types.BulkDeleteRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/bulk-delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskBulkUpdate(req: types.BulkUpdateTasksRequest) {
  return service.post<types.GenericResponse<types.Task>>(`/v1/task/bulk-update-status`, req);
}

/**
 * @description
 * @param req
 */
export function task_assignmentAssignTasks(req: types.AssignTasksRequest) {
  return service.post<types.GenericResponse<types.TaskAssignment>>(
    `/v1/task_assignment/assign-tasks`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personSendMessage(req: types.SendMessageRequest) {
  return service.post<types.GenericResponse<types.SendMessageResponse>>(
    `/v1/person/message/send`,
    req,
  );
}

export function casbinRoleAdd(req: types.Role) {
  return service.post<types.GenericResponse<types.Role>>(`/v1/casbin/role/add`, req);
}

/**
 * @description
 */
export function casbinRoleDelete() {
  return service.post<types.GenericResponse<boolean>>(`/v1/casbin/role/delete`);
}

/**
 * @description
 */
export function casbinRoleList() {
  return service.post<types.GenericResponse<types.Role[]>>(`/v1/casbin/role/list`);
}

/**
 * @description
 * @param req
 */
export function personMessageTemplate(req: types.MessageTemplateRequest) {
  return service.post<types.GenericResponse<types.MessageTemplateResponse>>(
    `/v1/person/message/template`,
    req,
  );
}

export function callAccept(req: types.CallUpdateRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/accept`, req);
}

/**
 * @description
 * @param req
 */
export function callAdd(req: types.CallAddRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/add`, req);
}

/**
 * @description
 * @param req
 */
export function callDelete(req: types.CallDeleteRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/delete`, req);
}

/**
 * @description
 * @param req
 */
export function callEnd(req: types.CallEndRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/end`, req);
}

/**
 * @description
 * @param req
 */
export function callGet(req: types.CallUpdateRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/get`, req);
}

/**
 * @description
 * @param req
 */
export function callList(req: types.CallListRequest) {
  return service.post<types.GenericResponse<types.CallListResponse>>(`/v1/call/list`, req);
}

/**
 * @description
 * @param req
 */
export function callUpdate(req: types.CallUpdateRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/update`, req);
}

/**
 * @description
 * @param req
 */
export function callQuery(req: types.CallDynamicQuery) {
  return service.post<types.GenericResponse<types.CallListResponse>>(`/v1/call/query`, req);
}

/**
 * @description
 */
export function casbinSync() {
  return service.post<types.GenericResponse<types.SyncResponse>>(`/v1/casbin/sync`);
}

/**
 * @description
 */
export function casbinPolicyUpdate(req: types.PolicyUpdateRequest) {
  return service.post<types.GenericResponse<types.PolicyUpdateResponse>>(
    `/v1/casbin/policy/update`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function personHistoryMessage(req: types.HistoryRequest) {
  return service.post<types.GenericResponse<types.HistoryResponse>>(
    `/v1/person/message/history`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentAdd(req: types.PersonAssignmentAddRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/add`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentDelete(req: types.PersonAssignmentDeleteRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/delete`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function person_assignmentUpdate(req: types.PersonAssignmentUpdateRequest) {
  return service.post<types.GenericResponse<types.PersonAssignment>>(
    `/v1/person_assignment/update`,
    req,
  );
}

export function settingList(req: types.SettingListRequest) {
  return service.post<types.GenericResponse<types.SettingListReponse>>(`/v1/setting/list`, req);
}

/**
 * @description
 * @param req
 */
export function settingAdd(req: types.SettingAddRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/add`, req);
}

/**
 * @description
 * @param req
 */
export function settingUpdate(req: types.SettingUpdateRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/update`, req);
}

/**
 * @description
 * @param req
 */
export function settingDelete(req: types.SettingDeleteRequest) {
  return service.post<types.GenericResponse<types.Setting>>(`/v1/setting/delete`, req);
}

/**
 * @description
 * @param req
 */
export function callUpdateFeedback(req: types.CallUpdateFeedbackRequest) {
  return service.post<types.GenericResponse<types.Call>>(`/v1/call/update/feedback`, req);
}

/**
 * @description
 * @param req
 */
export function taskAddNote(req: types.TaskNoteAddRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/add`, req);
}

/**
 * @description
 * @param req
 */
export function taskDeleteNote(req: types.TaskNoteDeleteRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/delete`, req);
}

/**
 * @description
 * @param req
 */
export function taskUpdateNote(req: types.TaskNoteUpdateRequest) {
  return service.post<types.GenericResponse<types.TaskNote>>(`/v1/task/note/update`, req);
}

/**
 * @description
 * @param req
 */
export function personIsPersonIn(req: types.PersonGetRequest) {
  return service.post<types.GenericResponse<boolean>>(`/v1/person/is-person-in`, req);
}

/**
 * @description
 * @param req
 */
export function adminLogs(req: types.LogsRequest) {
  return service.post<types.GenericResponse<types.LogsResponse>>(`/v1/admin/logs`, req);
}

/**
 * @description
 */
// export function adminMonitor() {
//   return service.post<types.GenericResponse<types.MonitorResponse>>(`/v1/admin/monitor`);
// }

/**
 * @description
 * @param req
 */
export function queryQuery(req: types.DynamicQuery) {
  return service.post<types.GenericResponse<types.QueryResult>>(`/v1/query/query`, req);
}

/**
 * @description
 * @param req
 */
export function appointmentQuery(req: types.AppointmentQueryRequest) {
  return service.post<types.GenericResponse<types.QueryResult>>(`/v1/appointment/query`, req);
}

/**
 * @description
 * @param req
 */
export function taskQuery(req: types.TaskDynamicQuery) {
  return service.post<types.GenericResponse<types.TaskListResponse>>(`/v1/task/query`, req);
}

/**
 * @description
 * @param req
 */
export function personQuery(req: types.PersonDynamicQuery) {
  return service.post<types.GenericResponse<types.PersonListResponse>>(`/v1/person/query`, req);
}

/**
 * @description
 * @param req
 */
export function discountCalculateDiscount(req: types.CalculateDiscountRequest) {
  return service.post<types.GenericResponse<types.CalculateDiscountResponse>>(
    `/v1/discount/calculate`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function scheduleAdd(req: types.ScheduleAddRequest) {
  return service.post<types.GenericResponse<types.ScheduleResponse>>(`/v1/schedule/add`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleDelete(req: types.ScheduleDeleteRequest) {
  return service.post<types.GenericResponse<types.ScheduleResponse>>(`/v1/schedule/delete`, req);
}

/**
 * @description
 * @param req
 */
export function scheduleGetWorkSchedule(req: types.ScheduleRequest) {
  return service.post<types.GenericResponse<types.WorkScheduleResponse>>(
    `/v1/schedule/get-work-schedule`,
    req,
  );
}

/**
 * @description
 * @param req
 */
export function activityList(req: types.ActivityListRequest) {
  return service.post<types.GenericResponse<types.ActivityListResponse>>(`/v1/activity/list`, req);
}
