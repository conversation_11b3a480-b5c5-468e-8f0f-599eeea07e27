import { EnumTypeAppointmenntStatus, TaskPriorityEnum, TaskStatusEnum } from "./bcare-enum";

export const mapEnumTypeAppointmennt: Record<EnumTypeAppointmenntStatus, string> = {
  [EnumTypeAppointmenntStatus.ORTHODONTIC]: "Chỉnh nha",
  [EnumTypeAppointmenntStatus.GENERALITY]: "Tổng quát",
  [EnumTypeAppointmenntStatus.IMPLANT]: "Implant",
  [EnumTypeAppointmenntStatus.MINOR_SURGERY]: "Tiểu phẫu",
};

export const mapTaskPriorityEnum: Record<TaskPriorityEnum, string> = {
  [TaskPriorityEnum.ALL]: "",
  [TaskPriorityEnum.HIGH]: "<PERSON>",
  [TaskPriorityEnum.MEDIUM]: "Trung bình",
  [TaskPriorityEnum.LOW]: "Thấp",
};

export const mapTaskStatusEnum: Record<TaskStatusEnum, string> = {
  [TaskStatusEnum.ALL]: "",
  [TaskStatusEnum.CLOSE]: "Hủy",
  [TaskStatusEnum.NEWS]: "Mới tạo",
  [TaskStatusEnum.PROCESSING]: "Đang thực hiện",
  [TaskStatusEnum.REVIEWER]: "Chờ duyệt",
  [TaskStatusEnum.DONE]: "Hoàn thành",
};
