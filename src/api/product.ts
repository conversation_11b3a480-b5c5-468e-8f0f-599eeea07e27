// constants/product.ts
export const PRODUCT_TYPES = {
  SERVICE: 'service',
  ITEM: 'item',
  GIFT: 'gift'
} as const;

export const PRODUCT_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0
} as const;

export const COLLECTION_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary'
} as const;

export type ProductType = (typeof PRODUCT_TYPES)[keyof typeof PRODUCT_TYPES];
export type ProductStatus = (typeof PRODUCT_STATUS)[keyof typeof PRODUCT_STATUS];
export type CollectionType = (typeof COLLECTION_TYPES)[keyof typeof COLLECTION_TYPES];

export const PRODUCT_TYPE_OPTIONS = [
  { name: 'Dịch vụ', value: PRODUCT_TYPES.SERVICE },
  { name: '<PERSON>ản phẩm', value: PRODUCT_TYPES.ITEM },
  { name: 'Quà tặng', value: PRODUCT_TYPES.GIFT },
] as const;

export interface ProductFilters {
  type?: ProductType;
  collection?: CollectionType;
  status?: ProductStatus;
}
