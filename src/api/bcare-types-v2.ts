// Code generated by goctl. DO NOT EDIT.
export interface GenericResponse<T> {
  code: number;
  message: string;
  data: T | null;
}

export interface ActiveTrackRequest {
  person_id: number;
}

export interface Activity {
  entity_id: number;
  kind: string;
  record: { [key: string]: any };
  person_id: number;
  created_at: string;
}

export interface ActivityBreakdown {
  appointments: Array<TimeSeriesData>; // Dữ liệu cuộc hẹn theo thời gian
  calls: Array<TimeSeriesData>; // Dữ liệu cuộc gọi theo thời gian
  notes: Array<TimeSeriesData>; // Dữ liệu ghi chú theo thời gian
  messages: Array<TimeSeriesData>; // Dữ liệu tin nhắn theo thời gian
  total: Array<TimeSeriesData>; // Dữ liệu tất cả theo thời gian
}

export interface ActivityDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
}

export interface ActivityRecord {
  type: string;
  data: any;
}

export interface Aggregation {
  field: string;
  function: "SUM" | "AVG" | "COUNT" | "MIN" | "MAX";
  alias: string;
  over?: WindowSpecification; // Optional window specification for aggregation
}

export interface AggregationV2 {
  field: string;
  function:
    | "COUNT"
    | "SUM"
    | "AVG"
    | "MIN"
    | "MAX"
    | "ARRAY_AGG"
    | "STRING_AGG"
    | "JSON_AGG"
    | "JSONB_AGG";
  alias: string;
  distinct?: boolean;
  over?: WindowSpecificationV2;
}

export interface AllocationAddRequest {
  deposit_id: number;
  attachment_id?: number;
  deal_id?: number;
  amount: number;
}

export interface Appointment {
  id: number;
  title: string;
  start_time: string;
  end_time: string;
  arrived_at: string;
  notes: string;
  extra_notes: string;
  type: number;
  person_id: number;
  doctor_id: number;
  creator_id: number;
  status: number;
  reminder_status: number;
  history: Array<HistoryEntry>;
  created_at: string;
  updated_at: string;
}

export interface AppointmentAddRequest {
  start_time: string;
  end_time: string;
  person_id: number;
  doctor_id?: number;
  status: number;
  type: number;
  notes?: string;
  extra_notes?: string;
}

export interface AppointmentDeleteRequest {
  id: number;
}

export interface AppointmentDetail {
  start_time: string;
  end_time: string;
  person_name: string;
  doctor_id: number;
}

export interface AppointmentDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  doctor?: string;
  person?: string;
  task?: string;
  note?: string;
  arrived?: "yes" | "no" | "both";
  has_doctor?: "yes" | "no" | "both";
  source_id?: number;
  export?: boolean;
}

export interface AppointmentFilter {
  person_id?: number;
  doctor_id?: number;
  status?: number;
  type?: number;
}

export interface AppointmentGetRequest {
  id?: number;
  person_id?: number;
}

export interface AppointmentListRequest {
  page_size?: number;
  page?: number;
  filter?: AppointmentFilter;
  from_date?: string;
  to_date?: string;
  has_doctor?: "yes" | "no" | "both";
  order_by?: string;
}

export interface AppointmentListResponse {
  appointments: Array<AppointmentResponse>;
  total: number;
  total_page: number;
}

export interface AppointmentResponse {
  id: number;
  title: string;
  start_time: string;
  end_time: string;
  arrived_at: string;
  notes: string;
  extra_notes: string;
  type: number;
  person_id: number;
  doctor_id: number;
  creator_id: number;
  status: number;
  reminder_status: number;
  history: Array<HistoryEntry>;
  created_at: string;
  updated_at: string;
  person: Person;
  doctor: UserShort;
}

export interface AppointmentSort {
  id: number;
  start_time: string;
  end_time: string;
  notes: string;
  extra_notes: string;
  doctor_id: number;
}

export interface AppointmentUpdateRequest {
  id: number;
  notes?: string;
  status?: number;
  type?: number;
  start_time?: string;
  end_time?: string;
  doctor_id?: number;
  reminder_status?: number;
  extra_notes?: string;
  modified?: Array<string>;
}

export interface AssignPersonsRequest {
  IdList: Array<number>;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
}

export interface AssignTasksRequest {
  id_list: Array<number>;
  users?: Array<TaskAssignmentAddRequest>;
}

export interface AttachmenForcetUpdateRequest {
  id: number;
  created_at?: string;
}

export interface Attachment {
  id: number;
  deal_id: number;
  person_id: number;
  product_id: number;
  plan_id: number;
  brand_id: number;
  quantity: number;
  price: number;
  discount: number;
  status: number;
  kind: string;
  note: string;
  title: string;
  parent_id: number;
  user_id: number;
  track_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface AttachmentAddRequest {
  deal_id?: number;
  person_id: number;
  product_id?: number;
  brand_id?: number;
  quantity?: number;
  price?: number;
  discount?: number;
  status?: number;
  kind: string;
  note?: string;
  title?: string;
  parent_id?: number;
  plan_id?: number;
  discounts?: Array<number>;
}

export interface AttachmentDataResponse {
  kind: string;
  data: { [key: string]: any };
  participant_id?: number;
}

export interface AttachmentDeleteRequest {
  id: number;
}

export interface AttachmentDetail {
  deal_id: number;
  product_id: number;
  brand_id: number;
  quantity: number;
  price: number;
  discount: number;
  note: string;
}

export interface AttachmentDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  export?: boolean;
}

export interface AttachmentFilter {
  deal_id?: number;
  product_id?: number;
  parent_id?: number;
  kind?: string;
  status?: number;
  plan_id?: number;
  person_id?: number;
}

export interface AttachmentGetRequest {
  id: number;
}

export interface AttachmentListRequest {
  page_size?: number;
  page?: number;
  filter?: AttachmentFilter;
  product_type?: string;
  product_category?: number;
  attachment_kind?: string;
  order_by?: string;
}

export interface AttachmentListResponse {
  attachments: Array<AttachmentResponse>;
  total: number;
  total_page: number;
}

export interface AttachmentOperationReportRecord {
  treatment_date: string;
  full_name: string;
  person_code: string;
  product_title: string;
  category_name: string;
  operation: string;
  price: number;
  treatment_sequence: number;
  doctor_name: string;
  created_at: string;
  buy_date: string;
  person: Person;
  user: UserShort;
}

export interface AttachmentResponse {
  id: number;
  deal_id: number;
  person_id: number;
  product_id: number;
  plan_id: number;
  brand_id: number;
  quantity: number;
  price: number;
  discount: number;
  status: number;
  kind: string;
  note: string;
  title: string;
  parent_id: number;
  user_id: number;
  track_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  creator: UserShort;
  payment: number;
  product: Product;
  parent: Attachment;
  children: Array<Attachment>;
  bill_item: BillItemResponse;
  eligible_discounts: Array<EligibleDiscount>;
  data: Array<AttachmentDataResponse>;
}

export interface AttachmentTreeResponse {
  id: number;
  deal_id: number;
  person_id: number;
  product_id: number;
  plan_id: number;
  brand_id: number;
  quantity: number;
  price: number;
  discount: number;
  status: number;
  kind: string;
  note: string;
  title: string;
  parent_id: number;
  user_id: number;
  track_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  creator: UserShort;
  payment: number;
  product: Product;
  parent: AttachmentResponse;
  children: Array<AttachmentResponse>;
  bill_item: BillItemResponse;
  eligible_discounts: Array<EligibleDiscount>;
  data: Array<AttachmentDataResponse>;
}

export interface AttachmentUpdateRequest {
  id: number;
  deal_id?: number;
  product_id?: number;
  brand_id?: number;
  quantity?: number;
  price?: number;
  discount?: number;
  note?: string;
  kind?: string;
  title?: string;
  parent_id?: number;
  plan_id?: number;
  status?: number;
  discounts?: Array<number>;
  modified?: Array<string>;
}

export interface Bill {
  id: number;
  deal_id: number;
  person_id: number;
  user_id: number;
  brand_id: number;
  status: number;
  state: string;
  discount: number;
  refund: number;
  debt_remaining: number;
  debt_payment: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface BillAddFromDealRequest {
  deal_id: number;
}

export interface BillAddRequest {
  deal_id?: number;
  person_id: number;
  brand_id?: number;
  state?: string;
  discount?: number;
  refund?: number;
  debt_remaining?: number;
  debt_payment?: number;
  bill_items: Array<BillItemAddRequest>;
  payment?: PaymentAddRequest;
}

export interface BillDataResponse {
  kind: string;
  data: { [key: string]: any };
}

export interface BillDeleteRequest {
  id: number;
}

export interface BillDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  person: string;
}

export interface BillFilter {
  deal_id?: number;
  user_id?: number;
  person_id?: number;
  brand_id?: number;
  status?: number;
  state?: string;
  has_installment_plan?: boolean;
}

export interface BillGetRequest {
  id: number;
}

export interface BillItem {
  id: number;
  bill_id: number;
  attachment_id: number;
  status: number;
  amount: number;
  note: string;
  user_id: number;
  track_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface BillItemAddFromAttachment {
  attachment_id: number;
  note?: string;
}

export interface BillItemAddRequest {
  bill_id?: number;
  attachment_id?: number;
  amount?: number;
  note?: string;
  payment_id?: number;
  allocate_amount?: number;
}

export interface BillItemDeleteRequest {
  id: number;
}

export interface BillItemDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
}

export interface BillItemFilter {
  bill_id?: number;
}

export interface BillItemGetPaidRequest {
  person_id: number;
  page_size?: number;
  page?: number;
}

export interface BillItemGetPartiallyPaidRequest {
  person_id: number;
}

export interface BillItemGetRequest {
  id: number;
}

export interface BillItemListRequest {
  page_size?: number;
  page?: number;
  filter?: BillItemFilter;
  order_by?: string;
}

export interface BillItemListResponse {
  bill_items: Array<BillItemResponse>;
  remaining_amount: number;
  total: number;
  total_page: number;
}

export interface BillItemResponse {
  id: number;
  bill_id: number;
  attachment_id: number;
  status: number;
  amount: number;
  note: string;
  user_id: number;
  track_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  paid_amount: number;
  attachment: AttachmentResponse;
  allocations: Array<PaymentAllocationResponse>;
  user: UserShort;
}

export interface BillItemUpdateRequest {
  id: number;
  status?: number;
  amount?: number;
  note: string;
  modified?: Array<string>;
}

export interface BillListRequest {
  page_size?: number;
  page?: number;
  filter?: BillFilter;
  person_id?: number;
  order_by?: string;
}

export interface BillListResponse {
  bills: Array<BillResponse>;
  total: number;
  total_page: number;
}

export interface BillResponse {
  id: number;
  deal_id: number;
  person_id: number;
  user_id: number;
  brand_id: number;
  status: number;
  state: string;
  discount: number;
  refund: number;
  debt_remaining: number;
  debt_payment: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  track: Track;
  items: Array<BillItemResponse>;
  payments: Array<PaymentResponse>;
  deal: Deal;
  total_value: number;
  doctor_name: string;
  data: Array<BillDataResponse>;
  installment_plans: Array<InstallmentPlanResponse>;
}

export interface BillUpdateRequest {
  id: number;
  deal_id?: number;
  status?: number;
  state?: string;
  brand_id?: number;
  debt_remaining?: number;
  debt_payment?: number;
  modified?: Array<string>;
}

export interface BulkDeleteRequest {
  id_list: Array<number>;
}

export interface BulkUpdateFormSubmissionResult {
  success_count: number;
  fail_count: number;
  errors: Array<FormSubmissionError>; // Danh sách các lỗi
}

export interface BulkUpdateResult {
  success_count: number; // Số task update thành công
  fail_count: number; // Số task update thất bại
  errors: Array<TaskUpdateError>; // Danh sách các lỗi
}

export interface BulkUpdateTasksRequest {
  id_list: Array<number>;
  state?: string;
  start_date?: string;
  end_date?: string;
  due_date?: string;
  modified?: Array<string>;
}

export interface Bundle {
  id: number;
  machine_name: string;
  name: string;
  type: string;
  description: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface BundleAddRequest {
  machine_name: string;
  name: string;
  type: string;
  description: string;
}

export interface BundleFilter {
  name?: string;
  type?: string;
  status?: number;
}

export interface BundleGetRequest {
  id: number;
}

export interface BundleListRequest {
  page_size?: number;
  page?: number;
  filter?: BundleFilter;
  order_by?: string;
}

export interface BundleListResponse {
  total: number;
  total_page: number;
  bundles: Array<Bundle>;
}

export interface BundleResponse {
  id: number;
  machine_name: string;
  name: string;
  type: string;
  description: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface BundleUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  modified?: Array<string>;
}

export interface CTEInfoV2 {
  name: string;
  columns?: Array<string>;
  query: DynamicQueryV2;
}

export interface CalculateDiscountAttachmentRequest {
  product_id: number;
  quantity: number;
  discount_ids: Array<number>;
}

export interface CalculateDiscountRequest {
  deal_id: number;
  discount_ids: Array<number>;
}

export interface CalculateDiscountResponse {
  total_discount_amount: number;
  results?: Array<DiscountResult>;
  discount_amounts: { [key: string]: number };
}

export interface Call {
  id: number;
  call_id: string;
  start_time: string;
  duration: number;
  direction: string;
  source: string;
  destination: string;
  recording_file: string;
  score: number;
  pdd: number;
  tta: number;
  feedback: string;
  kind: string;
  rating: number;
  call_status: string;
  status: number;
  person_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  state: string;
}

export interface CallAddRequest {
  call_id: string;
  person_id: number;
  direction: "inbound" | "outbound";
  phone?: string;
}

export interface CallDeleteRequest {
  id: number;
}

export interface CallDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  person?: string; // tên, sdt
  creator?: string; // tên, id
  state?: "inbound" | "outbound" | "miss_inbound" | "miss_outbound" | "all"; // state
}

export interface CallEndRequest {
  id: number;
  call_id: string;
  is_miss: boolean;
}

export interface CallFilter {
  direction?: string;
  call_status?: string;
  status?: number;
  person_id?: number;
  user_id?: number;
}

export interface CallGetRequest {
  id: number;
}

export interface CallListRequest {
  page_size?: number;
  page?: number;
  filter: CallFilter;
  order_by?: string;
}

export interface CallListResponse {
  calls: Array<Call>;
  total: number;
  total_inbound_miss: number;
  total_inbound: number;
  total_outbound: number;
  total_outbound_miss: number;
  total_page: number;
}

export interface CallResponse {
  id: number;
  call_id: string;
  start_time: string;
  duration: number;
  direction: string;
  source: string;
  destination: string;
  recording_file: string;
  score: number;
  pdd: number;
  tta: number;
  feedback: string;
  kind: string;
  rating: number;
  call_status: string;
  status: number;
  person_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  state: string;
  user: UserShort;
  person: Person;
}

export interface CallSetting {
  api_key: string;
  api_secret: string;
  api_url: string;
  uri: string;
  uri_ws: string;
  line_id: string;
  password: string;
}

export interface CallUpdateFeedbackRequest {
  id: number;
  feedback?: string;
  rating?: number;
  kind?: string;
  modified?: Array<string>;
}

export interface CallUpdateRequest {
  id: number;
  call_id: string;
  modified?: Array<string>;
}

export interface CaseConditionV2 {
  when: LogicalExpressionV2;
  then: any;
}

export interface CaseWhenV2 {
  conditions: Array<CaseConditionV2>;
  else?: any;
}

export interface ClearAttachmentDataRequest {
  attachment_id: number;
  kind?: string;
  key?: string;
  participant_id?: number;
}

export interface ClearBillDataRequest {
  bill_id: number;
  kind?: string;
  key?: string;
}

export interface ClearDataRequest {
  person_id: number;
  kind?: string;
  key?: string;
}

export interface ClearUserDataRequest {
  user_id: number;
  kind?: string;
  key?: string;
}

export interface CommonResponse {
  count: number;
}

export interface CompleteRequest {
  prompt: string;
}

export interface CompleteResponse {
  result: string;
}

export interface ConstantRequest {}

export interface ConstantResponse {
  appointment_status: { [key: string]: string };
  appointment_type: { [key: string]: string };
  appointment_reminder_status: { [key: string]: string };
  product_type: { [key: string]: string };
  deal_status: { [key: string]: string };
  deal_user_role: { [key: string]: string };
  note_type: { [key: string]: string };
  product_position: { [key: string]: string };
  attachment_type: { [key: string]: string };
  transaction_type: { [key: string]: string };
  task_priority: { [key: string]: string };
  task_type: { [key: string]: string };
  task_state: { [key: string]: string };
  task_assignment_state: { [key: string]: string };
  user_department_position: { [key: string]: string };
  call_setting: CallSetting;
}

export interface ContentCommandRequest {
  content: string;
  focus: string;
  command: string;
}

export interface ContentCommandResponse {
  options: Array<string>;
}

export interface ContentModificationRequest {
  content: string;
  focus: string;
}

export interface ContentModificationResponse {
  options: Array<string>;
}

export interface ConvertToPaymentRequest {
  deposit_id: number;
  payment_id: number;
  amount: number;
  note?: string;
}

export interface ConvertToPersonRequest {
  form_submission_ids: Array<number>;
}

export interface ConvertToPersonResponse {
  success: boolean;
  form_submission: FormSubmission;
}

export interface CsrfTokenResponse {}
export interface CsrfTokenResponseParams {}

export interface DailySchedule {
  date: string;
  day_of_week: string;
  work_schedules: Array<ScheduleResponse>;
  appointments: Array<AppointmentResponse>;
  total_appointments: number;
}

export interface DataResponse {
  data: { [key: string]: any };
}

export interface Deal {
  id: number;
  person_id: number;
  parent_deal_id: number;
  name: string;
  state: string;
  total_amount: number;
  total_plan_amount: number;
  paid_amount: number;
  discount_amount: number;
  paid_installment_count: number;
  refund_amount: number;
  deposit_amount: number;
  down_payment: number;
  down_payment_received_amount: number;
  stage_id: number;
  stage_history: Array<StageHistoryEntry>;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DealAddRequest {
  person_id: number;
  parent_deal_id?: number;
  name?: string;
  total_amount?: number;
  stage_id?: number;
  state?: string;
  status: number;
}

export interface DealCheckoutRequest {
  id: number;
}

export interface DealDeleteRequest {
  id: number;
}

export interface DealFilter {
  person_id?: number;
  stage_id?: number;
  status?: number;
  state?: string;
}

export interface DealGetRequest {
  id: number;
  include_relation?: boolean;
}

export interface DealListRequest {
  page_size?: number;
  page?: number;
  filter?: DealFilter;
  pipeline_id?: number;
  doctor_id?: number;
  order_by?: string;
  include_relation?: boolean;
}

export interface DealListResponse {
  deals: Array<DealResponse>;
  total: number;
  total_page: number;
}

export interface DealResponse {
  id: number;
  person_id: number;
  parent_deal_id: number;
  name: string;
  state: string;
  total_amount: number;
  total_plan_amount: number;
  paid_amount: number;
  discount_amount: number;
  paid_installment_count: number;
  refund_amount: number;
  deposit_amount: number;
  down_payment: number;
  down_payment_received_amount: number;
  stage_id: number;
  stage_history: Array<StageHistoryEntry>;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  tags: Array<TagInfo>;
  person: PersonResponse;
  deal_assignment: Array<DealUserResponse>;
  tracks: Array<TrackResponse>;
  attachments: Array<AttachmentResponse>;
  discount_usages: Array<DiscountUsageResponse>;
  eligible_discounts: Array<EligibleDiscount>;
}

export interface DealSort {
  id: number;
  person_id: number;
  name: string;
}

export interface DealStageHistory {
  id?: number;
  deal_id?: number;
  before?: number;
  after?: number;
  user_id?: number;
  changed_at?: string;
}

export interface DealStageHistoryDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  export?: boolean;
}

export interface DealStageHistoryFilter {
  deal_id?: number;
  before?: number;
  after?: number;
}

export interface DealStageHistoryListRequest {
  page_size?: number;
  page?: number;
  filter?: DealStageHistoryFilter;
  order_by?: string;
  include_relation?: boolean;
}

export interface DealStageHistoryListResponse {
  stage_histories: Array<DealStageHistory>;
  total: number;
  total_page: number;
}

export interface DealStageHistoryResponse {
  id?: number;
  deal_id?: number;
  before?: number;
  after?: number;
  user_id?: number;
  changed_at?: string;
  before_name?: string;
  after_name?: string;
  // thông tin khách
  person?: Person;
  person_source?: string;
  // thông tin người giới thiệu
  referrer?: Person;
  // thông tin sale
  sale_user?: UserShort;
  // thông tin deal
  deal?: DealResponse;
}

export interface DealStageHistoryUpdateRequest {
  id: number;
  changed_at?: string;
}

export interface DealUpdateRequest {
  id: number;
  parent_deal_id?: number;
  stage_id?: number;
  status?: number;
  name?: string;
  state?: string;
  total_amount?: number;
  modified?: Array<string>;
  discounts?: Array<number>;
}

export interface DealUser {
  id: number;
  deal_id: number;
  user_id: number;
  role: string;
  point: { [key: string]: any };
}

export interface DealUserAddRequest {
  deal_id: number;
  user_id: number;
  role: string;
}

export interface DealUserDeleteRequest {
  deal_id: number;
  user_id: number;
  role: string;
}

export interface DealUserInfo {
  id?: number;
  username: string;
  email?: string;
  name?: string;
  department_id?: number;
  role?: string;
  point?: { [key: string]: any };
}

export interface DealUserListRequest {
  deal_id: number;
}

export interface DealUserListResponse {
  deal_users: Array<DealUserResponse>;
}

export interface DealUserRating {
  id: number;
  deal_user_id: number;
  category: string;
  rating: number;
}

export interface DealUserRatingAddRequest {
  deal_user_id?: number;
  category?: string;
  rating?: number;
}

export interface DealUserRatingDeleteRequest {
  id: number;
}

export interface DealUserRatingUpdateRequest {
  id: number;
  category?: string;
  rating?: number;
}

export interface DealUserResponse {
  id: number;
  deal_id: number;
  user_id: number;
  role: string;
  point: { [key: string]: any };
  name: string;
  user: UserShort;
  ratings: Array<DealUserRating>;
}

export interface DealUserUpdateRequest {
  id: number;
  user_id?: number;
  point?: { [key: string]: any };
}

export interface DeleteRequest {
  id: number;
}

export interface Department {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface DepartmentAddRequest {
  name: string;
  description?: string;
}

export interface DepartmentDeleteRequest {
  id: number;
}

export interface DepartmentListRequest {
  name?: string;
}

export interface DepartmentListResponse {
  departments: Array<Department>;
}

export interface DepartmentResponse {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface DepartmentShort {
  id: number;
  name: string;
}

export interface DepartmentUpdateRequest {
  id: number;
  name?: string;
  description?: string;
}

export interface Deposit {
  id: number;
  deal_id?: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  state: string; // pending, active, converted, refund, cancelled
  description?: string;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
}

export interface DepositAddRequest {
  deal_id?: number;
  total_amount: number;
  description?: string;
  allocations?: Array<AllocationAddRequest>;
}

export interface DepositAllocation {
  id: number;
  deposit_id: number;
  attachment_id?: number;
  deal_id?: number;
  amount: number;
  state: string; // active, converted, refund, cancelled
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
}

export interface DepositFilter {
  deal_id?: number;
  state?: string;
  created_by?: number;
}

export interface DepositListRequest {
  page_size?: number;
  page?: number;
  filter?: DepositFilter;
  order_by?: string;
}

export interface DepositListResponse {
  deposits: Array<DepositResponse>;
  total: number;
  total_page: number;
}

export interface DepositPayment {
  id: number;
  deposit_id: number;
  payment_id: number;
  amount: number;
  conversion_date: string;
  state: string; // active, completed, cancelled
  note?: string;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
}

export interface DepositResponse {
  id: number;
  deal_id?: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  state: string; // pending, active, converted, refund, cancelled
  description?: string;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
  allocations: Array<DepositAllocation>;
  payments: Array<DepositPayment>;
  deal?: Deal;
}

export interface Discount {
  id: number;
  name: string;
  type: string;
  value: number;
  scope: string;
  condition: string;
  usage_type: string;
  description: string;
  meta: { [key: string]: any };
  start: string;
  end: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DiscountAddRequest {
  name: string;
  type: string;
  value: number;
  scope: string;
  condition: string;
  usage_type: string;
  description: string;
  meta: string;
  start: string;
  end: string;
}

export interface DiscountDeleteRequest {
  id: number;
}

export interface DiscountDetail {
  amount: number;
  applied_to: string; // "deal" hoặc "attachment"
  attachment_id?: number;
}

export interface DiscountFilter {
  name?: string;
  type?: string;
  scope?: string;
  usage_type?: string;
  status?: number;
}

export interface DiscountGetRequest {
  id: number;
}

export interface DiscountListRequest {
  page_size?: number;
  page?: number;
  filter?: DiscountFilter;
  order_by?: string;
}

export interface DiscountListResponse {
  discounts: Array<Discount>;
  total: number;
  total_page: number;
}

export interface DiscountResponse {
  discount_usage_id: number;
  id: number;
  name: string;
  value: number;
  scope: string;
  description: string;
}

export interface DiscountResult {
  discount_id: number;
  total_amount: number;
  details: Array<DiscountDetail>;
}

export interface DiscountUpdateRequest {
  id: number;
  name?: string;
  type?: string;
  value?: number;
  scope?: string;
  condition?: string;
  usage_type?: string;
  description?: string;
  meta?: string;
  start?: string;
  end?: string;
  status?: number;
  modified?: Array<string>;
}

export interface DiscountUsage {
  id: number;
  discount_id: number;
  user_id?: number;
  person_id?: number;
  attachment_id?: number;
  deal_id?: number;
  value: number;
  usage_count: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DiscountUsageAddRequest {
  discount_id: number;
  user_id?: number;
  person_id?: number;
  attachment_id?: number;
  deal_id?: number;
  value: number;
}

export interface DiscountUsageDeleteRequest {
  id: number;
}

export interface DiscountUsageFilter {
  search?: string;
  discount_id?: number;
  user_id?: number;
  person_id?: number;
  attachment_id?: number;
  deal_id?: number;
}

export interface DiscountUsageListRequest {
  page_size?: number;
  page?: number;
  filter?: DiscountUsageFilter;
  order_by?: string;
}

export interface DiscountUsageListResponse {
  discount_usages: Array<DiscountUsageResponse>;
  total: number;
  total_page: number;
}

export interface DiscountUsageResponse {
  id: number;
  discount_id: number;
  user_id?: number;
  person_id?: number;
  attachment_id?: number;
  deal_id?: number;
  usage_count: number;
  value: number;
  discount: Discount;
  created_at: string;
}

export interface DownloadFileRequest {}
export interface DownloadFileRequestParams {
  path: string; // Thay đổi từ path thành form
}

export interface DownloadJobRequest {
  job_id: string; // Thêm validate nếu muốn
  user_id: number; // Thêm validate nếu muốn
}

export interface DynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
}

export interface DynamicQueryResponse {
  result: { [key: string]: any };
}

export interface DynamicQueryV2 {
  table: string;
  alias?: string;
  distinct?: boolean;
  distinct_on?: Array<string>;
  where_logic?: LogicalExpressionV2;
  having_logic?: LogicalExpressionV2;
  group_by: Array<string>;
  aggregations: Array<AggregationV2>;
  sort: Array<SortCriteriaV2>;
  limit: number;
  offset: number;
  selects: Array<SelectFieldV2>;
  joins: Array<JoinInfoV2>;
  ctes?: Array<CTEInfoV2>;
  lock?: LockInfoV2;
  unions?: Array<UnionInfoV2>;
}

export interface DynamicQueryV2Response {
  result: { [key: string]: any };
}

export interface EligibleDiscount {
  discount: Discount;
  discount_value: number;
  discount_amount: number;
  entity_type: string;
  entity_id: number;
}

export interface EligibleDiscountRequest {
  person_id: number;
  deal_id: number;
  product_ids: Array<number>;
}

export interface EligibleDiscountResponse {
  eligible_discounts: Array<EligibleDiscount>;
}

export interface ExcelExportTaskPayload {
  job_id: number; // ID của record ExportJob trong DB
  headers: Array<HeaderConfig>;
  sql: string;
  parameters: Array<Parameter>;
  user_id: number; // Thêm UserID nếu worker cần kiểm tra quyền hoặc log
}

export interface ExpectedTasksResponse {
  expected_task: string;
}

export interface ExportJobData {
  internal_id: string; // ID duy nhất trong mảng JSON
  state: string;
  estimated_records?: number;
  actual_records?: number;
  file_path?: string;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  expires_at?: string;
  progress?: number;
}

export interface ExportJobResponse {
  job_id?: string;
  state: string;
  estimated_records?: number;
  message: string;
}

export interface ExportJobStatus {
  job_id: number; // Job ID kiểu int64
  state: string; // Trạng thái (pending, processing, completed, failed)
  progress: number; // Tiến độ (0.0 - 100.0)
  message?: string; // Thông báo tiến trình
  download_url?: string; // Chỉ có khi state='completed'
  file_name?: string; // Chỉ có khi state='completed'
  error?: string; // Chỉ có khi state='failed'
}

export interface ExportProgressMessage {
  type: string; // e.g., "export_progress"
  data: ExportJobStatus;
}

export interface ExportRequest {
  sql: string;
  parameters?: Array<Parameter>;
  headers: Array<HeaderConfig>;
  total_record: number;
}

export interface ExpressionV2 {
  type: "ARITHMETIC" | "FUNCTION" | "COLUMN" | "LITERAL";
  literal_type?: "STRING" | "NUMBER" | "RAW" | "BOOLEAN" | "NULL";
  operator?: "+" | "-" | "*" | "/";
  function?: string;
  arguments?: Array<ExpressionV2>;
  column_ref?: string;
  value?: any;
}

export interface FieldChange {
  field: string;
  old_value: any;
  new_value: any;
}

export interface File {
  id: number;
  name: string;
  kind: string;
  type: string;
  size: number;
  path: string;
  user_id: number;
  storage: string;
  meta: { [key: string]: any };
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface FileAddRequest {}
export interface FileAddRequestParams {
  kind: string;
  storage: string;
}

export interface FileDeleteRequest {
  id: number;
  name: string;
}

export interface FileFilter {
  search?: string;
  name?: string;
  kind?: string;
  type?: string;
  user_id?: number;
  storage?: string;
}

export interface FileGetRequest {
  id: number;
  name: string;
}

export interface FileListRequest {
  page_size?: number;
  page?: number;
  filters?: FileFilter;
  order_by?: string;
}

export interface FileListResponse {
  files: Array<FileResponse>;
  total: number;
  total_page: number;
}

export interface FileResponse {
  id: number;
  name: string;
  kind: string;
  type: string;
  size: number;
  path: string;
  user_id: number;
  storage: string;
  meta: { [key: string]: any };
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface FileUpdateRequest {
  id: number;
  name?: string;
  kind?: string;
  type?: string;
  size?: number;
  path?: string;
  user_id?: number;
  storage?: string;
  meta?: { [key: string]: any };
  modified?: Array<string>;
}

export interface FileUsage {
  id: number;
  file_id: number;
  entity_type: string;
  entity_id: number;
  usage_type: string;
  usage_meta: { [key: string]: any };
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface FileUsageAddRequest {
  file_id: number;
  entity_type: string;
  entity_id: number;
  usage_type: string;
  usage_meta: { [key: string]: any };
  track_id?: number;
}

export interface FileUsageDeleteRequest {
  id: number;
}

export interface FileUsageFilter {
  search?: string;
  file_id?: number;
  entity_type?: string;
  entity_id?: number;
  usage_type?: string;
  track_id?: number;
}

export interface FileUsageGetRequest {
  id: number;
}

export interface FileUsageListRequest {
  page_size?: number;
  page?: number;
  filter?: FileUsageFilter;
  order_by?: string;
}

export interface FileUsageListResponse {
  file_usages: Array<FileUsageResponse>;
  total: number;
  total_page: number;
}

export interface FileUsageResponse {
  id: number;
  file_id: number;
  entity_type: string;
  entity_id: number;
  usage_type: string;
  usage_meta: { [key: string]: any };
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  file: File;
  track: Track;
}

export interface FileUsageUpdateRequest {
  id: number;
  file_id?: number;
  entity_type?: string;
  entity_id?: number;
  usage_type?: string;
  usage_meta?: { [key: string]: any };
  track_id?: number;
  modified?: Array<string>;
}

export interface Filter {
  field?: string;
  operator?:
    | "EQ"
    | "NEQ"
    | "GT"
    | "GTE"
    | "LT"
    | "LTE"
    | "IN"
    | "NOTIN"
    | "LIKE"
    | "NOTLIKE"
    | "BETWEEN"
    | "ISNULL"
    | "ISNOTNULL"
    | "TODAY"
    | "CONTAINS"
    | "JSON_CONTAINS_ANY";
  value?: string;
  logic?: "AND" | "OR";
  conditions?: Array<Filter>;
  function?: "TO_CHAR" | "DATE" | "YEAR" | "MONTH" | "DAY" | "LOWER" | "UPPER" | "UNACCENT";
}

export interface FilterConditionV2 {
  field?: string;
  operator?:
    | "EQ"
    | "NEQ"
    | "GT"
    | "GTE"
    | "LT"
    | "LTE"
    | "IN"
    | "NOTIN"
    | "LIKE"
    | "NOTLIKE"
    | "BETWEEN"
    | "ISNULL"
    | "ISNOTNULL"
    | "TODAY"
    | "CONTAINS"
    | "JSON_CONTAINS_ANY";
  value?: any;
  function?: string;
  grouped_in?: Array<Array<any>>;
  // Tách riêng field cho SQL phức tạp ở vế phải
  right_field?: string;
  right_expr_raw?: string;
  right_expr?: ExpressionV2;
  subquery?: DynamicQueryV2;
  // Tương tự cho left side
  left_expr_raw?: string;
  left_expr?: ExpressionV2;
}

export interface FormAddRequest {
  full_name: string;
  phone: string;
  email?: string;
  data?: { [key: string]: any };
  source_url?: string;
  form_name?: string;
  referrer_url?: string;
  source_id?: number;
}

export interface FormResponse {
  phone_view_history: Array<PhoneViewerInfo>;
  tags: Array<TagInfo>;
  stage_name: string;
  referred_by: ReferredData;
  form_submissions: Array<FormSubmission>;
  creator: UserShort;
  sale: UserShort;
  issues: Array<Issue>;
  assignment: Array<PersonAssignmentResponse>;
}

export interface FormSubmission {
  id: number;
  full_name: string;
  phone: string;
  email: string;
  data: { [key: string]: any };
  source_url: string;
  referrer_url: string;
  source_id: number;
  form_name: string;
  state: string; // pending, approved, rejected, processing
  processed_at?: string;
  history?: Array<RecordHistoryEntry>;
  person_id?: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface FormSubmissionDeleteRequest {
  ids: Array<number>;
}

export interface FormSubmissionDeleteResponse {
  success: boolean;
}

export interface FormSubmissionDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  person: string;
}

export interface FormSubmissionError {
  form_submission_id: number;
  error: string;
}

export interface FormSubmissionListResponse {
  total: number;
  total_page: number;
  form_submissions: Array<FormSubmissionResponse>;
}

export interface FormSubmissionResponse {
  id: number;
  full_name: string;
  phone: string;
  email: string;
  data: { [key: string]: any };
  source_url: string;
  referrer_url: string;
  source_id: number;
  form_name: string;
  state: string; // pending, approved, rejected, processing
  processed_at?: string;
  history?: Array<RecordHistoryEntry>;
  person_id?: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  person: Person;
}

export interface FormSubmissionsFilter {
  person_id?: number;
}

export interface FormSubmissionsListRequest {
  page_size?: number;
  page?: number;
  filter?: FormSubmissionsFilter;
  order_by?: string;
}

export interface GetAttachmentDataRequest {
  attachment_id: number;
  kind?: string;
  key?: string;
  participant_id?: number;
}

export interface GetBillDataRequest {
  bill_id: number;
  kind?: string;
  key?: string;
}

export interface GetCronRequest {
  prompt: string;
}

export interface GetCronResponse {
  expression: string;
}

export interface GetDataRequest {
  person_id: number;
  kind?: string;
  key?: string;
}

export interface GetRequest {
  id: number;
}

export interface GetUserDataRequest {
  user_id: number;
  kind?: string;
  key?: string;
}

export interface HeaderConfig {
  field: string;
  display_name: string;
  data_type: "string" | "int" | "float" | "bool" | "date" | "datetime";
  formatter?: string; // vd: "date:02/01/2006"
  width: number;
}

export interface HistoryEntry {
  user_id: number;
  changes: Array<FieldChange>;
  timestamp: string;
}

export interface HistoryFilter {
  entity_type?: string;
  entity_id?: number;
  status?: number;
}

export interface HistoryListRequest {
  page_size?: number;
  page?: number;
  filter?: HistoryFilter;
  order_by?: string;
}

export interface HistoryListResponse {
  histories: Array<HistoryRecord>;
  total: number;
  total_page: number;
}

export interface HistoryRecord {
  id: number;
  entity_type: string;
  entity_id: number;
  operation: string;
  changed_at: string;
  user_id?: number;
  before?: { [key: string]: any };
  after?: { [key: string]: any };
  related_id?: number;
  related?: string;
  created_at: string;
  updated_at: string;
  source_table: string;
}

export interface HistoryRequest {
  person_id: number;
}

export interface HistoryResponse {
  message_histories: Array<MessageHistory>;
  total: number;
  total_page: number;
}

export interface Installment {
  id: number;
  plan_id: number;
  installment_number: number;
  amount: number;
  name: string;
  note: string;
  person_id: number;
  user_id: number;
  kind: string;
  transaction_type: number;
  state: string;
  status: number;
  paid_at: string;
  created_at: string;
  updated_at: string;
  version: number;
}

export interface InstallmentAddRequest {
  plan_id: number;
  installment_number?: number;
  amount: number;
  person_id: number;
  note?: string;
  kind: string;
  transaction_type: number;
  status?: number;
}

export interface InstallmentDeleteRequest {
  id: number;
}

export interface InstallmentFilter {
  plan_id?: number;
  person_id?: number;
  installment_number?: number;
  status?: number;
}

export interface InstallmentGetPartiallyPaidRequest {
  person_id: number;
}

export interface InstallmentGetRequest {
  id: number;
}

export interface InstallmentListRequest {
  page_size?: number;
  page?: number;
  filter?: InstallmentFilter;
  order_by?: string;
}

export interface InstallmentListResponse {
  installments: Array<InstallmentResponse>;
  total: number;
  total_page: number;
}

export interface InstallmentPlan {
  id: number;
  person_id: number;
  deal_id: number;
  user_id: number;
  bill_id: number; // Thêm trường bill_id
  name: string;
  total_amount: number;
  down_payment: number;
  paid_amount: number;
  total_installments: number;
  discount_amount: number;
  paid_installment_count: number;
  refund_amount: number;
  state: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface InstallmentPlanAddRequest {
  person_id: number;
  deal_id?: number;
  user_id?: number;
  bill_id?: number; // Thêm trường bill_id
  name?: string;
  state?: string;
  total_amount?: number;
  down_payment?: number;
  total_installments?: number;
}

export interface InstallmentPlanDeleteRequest {
  id: number;
}

export interface InstallmentPlanFilter {
  person_id?: number;
  deal_id?: number;
  user_id?: number;
  bill_id?: number; // Thêm trường bill_id
}

export interface InstallmentPlanGetRequest {
  id: number;
}

export interface InstallmentPlanListRequest {
  page_size?: number;
  page?: number;
  filter?: InstallmentPlanFilter;
  order_by?: string;
}

export interface InstallmentPlanListResponse {
  installment_plans: Array<InstallmentPlanResponse>;
  total: number;
  total_page: number;
}

export interface InstallmentPlanRefundableListRequest {
  person_id?: number;
}

export interface InstallmentPlanResponse {
  id: number;
  person_id: number;
  deal_id: number;
  user_id: number;
  bill_id: number; // Thêm trường bill_id
  name: string;
  total_amount: number;
  down_payment: number;
  paid_amount: number;
  total_installments: number;
  discount_amount: number;
  paid_installment_count: number;
  refund_amount: number;
  state: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  person: Person;
  deal: Deal;
  creator: UserShort;
  bill: Bill; // Thêm trường bill
  installments: Array<InstallmentResponse>;
}

export interface InstallmentPlanUpdateRequest {
  id: number;
  name?: string;
  state?: string;
  total_amount?: number;
  down_payment?: number;
  paid_amount?: number;
  total_installments?: number;
  paid_installments?: number;
  bill_id?: number; // Thêm trường bill_id
  modified?: Array<string>;
}

export interface InstallmentResponse {
  id: number;
  plan_id: number;
  installment_number: number;
  amount: number;
  name: string;
  note: string;
  person_id: number;
  user_id: number;
  kind: string;
  transaction_type: number;
  state: string;
  status: number;
  paid_at: string;
  created_at: string;
  updated_at: string;
  version: number;
  creator: UserShort;
  paid_amount: number;
  allocations: Array<PaymentAllocationResponse>;
}

export interface InstallmentUpdateRequest {
  id: number;
  amount?: number;
  note?: string;
  person_id?: number;
  kind?: string;
  transaction_type?: number;
  status?: number;
  modified?: Array<string>;
}

export interface Issue {
  id: number;
  title: string;
  description?: string;
  type: string;
  progress: string;
  priority: string;
  person_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
}

export interface IssueAddRequest {
  title: string;
  description?: string;
  type: string;
  priority?: string;
  person_id: number;
}

export interface IssueCloseRequest {
  id: number;
}

export interface IssueGetRequest {
  id: number;
}

export interface IssueListRequest {
  person_id?: number;
  type?: string;
  progress?: string;
  priority?: string;
}

export interface IssueListResponse {
  issues: Array<Issue>;
}

export interface IssueUpdateRequest {
  id: number;
  title?: string;
  description?: string;
  type?: string;
  progress?: string;
  priority?: string;
  modified?: Array<string>;
}

export interface JoinInfo {
  table: string;
  alias?: string;
  c1?: string;
  c2?: string;
  type?: "LEFT" | "INNER" | "RIGHT" | "FULL";
}

export interface JoinInfoV2 {
  table: string;
  alias?: string;
  type: "INNER" | "LEFT" | "RIGHT" | "FULL" | "CROSS";
  on_logic?: LogicalExpressionV2;
  using?: Array<string>;
  subquery?: DynamicQueryV2;
}

export interface ListRequest {
  person_id: number;
}

export interface ListResponse {
  activities: Array<ActivityRecord>;
}

export interface LocalDistrict {
  id: number;
  name: string;
  prefix: string;
  province_id: number;
}

export interface LocalProvince {
  id: number;
  name: string;
  code: string;
}

export interface LocalWard {
  id: number;
  name: string;
  prefix: string;
  province_id: number;
  district_id: number;
}

export interface LocationRequest {}

export interface LocationResponse {
  provinces: Array<LocalProvince>;
  districts: Array<LocalDistrict>;
  wards: Array<LocalWard>;
}

export interface LockInfoV2 {
  type: "UPDATE" | "SHARE";
  of?: Array<string>;
  wait_mode: "WAIT" | "NOWAIT" | "SKIP_LOCKED";
}

export interface LogicalExpressionV2 {
  type: "AND" | "OR" | "CONDITION";
  condition?: FilterConditionV2;
  expressions?: Array<LogicalExpressionV2>;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: UserResponse;
  status: string;
  access_token: string;
  refresh_token: string;
  access_expire: number;
  refresh_expire: number;
  refresh_after: number;
  message: string; // "OTP sent successfully"
  sent_to: string; // Masked email/phone
  expires_in: number; // OTP expiration in seconds
}

export interface LogsRequest {
  type: string;
  page_size: number;
  page: number;
}

export interface LogsResponse {
  logs: string;
  total: number;
  total_page: number;
}

export interface MessageHistory {
  id: number;
  person_id: number;
  user_id: number;
  message_id: string;
  phone: string;
  type: string; // sms or zns
  content: string;
  zns_data: string;
  error_code: string;
  message_status: string; // sent, failed, delivered
  delivered_at: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  person: Person;
}

export interface MessageTemplateRequest {
  person_id: number;
  deal_id: number;
  appointment_id: number;
}

export interface MessageTemplateResponse {
  sms: string;
  zns: string;
  email: string;
}

export interface MonitorRequest {}

export interface MonitorResponse {}

export interface NewTrackReportResponse {
  // Track fields
  id: number;
  weight?: number;
  person_source?: string;
  deal_stage_name?: string;
  begin?: string;
  end?: string;
  status: number;
  pipeline_id?: number;
  deal_id?: number;
  person_id?: number;
  stage_id?: number;
  state?: string;
  kind?: string;
  refund_reason?: string;
  deal_stage_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
  meta?: { [key: string]: any };
  user_id?: number;
  // thông tin khách
  person?: Person;
  // thông tin người giới thiệu
  referrer?: Person;
  // thông tin sale
  sale_user?: UserShort;
  // thông tin deal
  deal?: DealResponse;
}

export interface Note {
  id: number;
  body: string;
  type: number;
  person_id: number;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  history: Array<HistoryEntry>;
  version: number;
}

export interface NoteAddRequest {
  body: string;
  type: number;
  person_id: number;
}

export interface NoteDeleteRequest {
  id: number;
}

export interface NoteFilter {
  person_id?: number;
  user_id?: number;
  type?: number;
}

export interface NoteListRequest {
  page_size?: number;
  page?: number;
  filter?: NoteFilter;
  department_id?: number;
  order_by?: string;
}

export interface NoteListResponse {
  notes: Array<NoteResponese>;
  total: number;
  total_page: number;
}

export interface NoteResponese {
  id: number;
  body: string;
  type: number;
  person_id: number;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  history: Array<HistoryEntry>;
  version: number;
  creator: UserShort;
  person: Person;
}

export interface NoteUpdateRequest {
  id: number;
  body?: string;
  type?: number;
  person_id?: number;
  status?: number;
  modified?: Array<string>;
}

export interface Operation {
  id: number;
  name: string;
  group: Array<string>;
  duration: number;
}

export interface OperationAllResponse {
  operations: Array<OperationResponse>;
}

export interface OperationResponse {
  id: number;
  name: string;
  group: Array<string>;
  duration: number;
  product_operation: Array<ProductOperation>;
}

export interface Overview {
  today_appointments: number;
  upcoming_appointments: number;
}

export interface Parameter {
  type:
    | "string"
    | "int"
    | "float"
    | "bool"
    | "null"
    | "date"
    | "time"
    | "datetime"
    | "timestamp"
    | "array"
    | "json"
    | "uuid"
    | "bytea"; // - bytea: dữ liệu nhị phân
  value: string; // Giá trị dưới dạng string, sẽ được chuyển đổi theo Type
  arrayElementType?: "string" | "int" | "float" | "bool"; // Chỉ dùng khi Type là "array"
}

export interface Payment {
  id: number;
  total_amount: number;
  cash: number;
  credit_card: number;
  mpos: number;
  bank: number;
  momo: number;
  state: string;
  status: number;
  payment_date: string;
  bill_id: number;
  person_id: number;
  deal_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface PaymentAddRequest {
  total_amount: number;
  cash?: number;
  credit_card?: number;
  mpos?: number;
  bank?: number;
  momo?: number;
  state?: string;
  payment_date?: string;
  bill_id: number;
  person_id: number;
  deal_id?: number;
  send_sms?: number;
  allocations?: Array<PaymentAllocationAddRequest>;
}

export interface PaymentAllocation {
  id: number;
  payment_id: number;
  bill_item_id: number;
  installment_id: number;
  amount: number;
  note: string;
  state: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface PaymentAllocationAddRequest {
  payment_id?: number;
  bill_item_id?: number;
  installment_id?: number;
  amount: number;
  note?: string;
}

export interface PaymentAllocationDeleteRequest {
  id: number;
}

export interface PaymentAllocationDetail {
  product_name: string;
  category: string;
  price: number;
  creator?: PaymentUserInfo;
  payer?: PaymentUserInfo;
  paid_amount: number;
}

export interface PaymentAllocationResponse {
  id: number;
  payment_id: number;
  bill_item_id: number;
  installment_id: number;
  amount: number;
  note: string;
  state: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  bill_item: BillItemResponse;
  installment: InstallmentResponse;
}

export interface PaymentAllocationSum {
  bill_item_id: number;
  sum: number;
}

export interface PaymentAllocationUpdateRequest {
  id: number;
  amount?: number;
  note?: string;
  bill_item_id?: number;
  installment_id?: number;
  modified?: Array<string>;
}

export interface PaymentDeleteRequest {
  id: number;
}

export interface PaymentFilter {
  bill_id?: number;
  status?: number;
  state?: string;
  person_id?: number;
  deal_id?: number;
}

export interface PaymentGetRequest {
  id: number;
}

export interface PaymentListRequest {
  page_size?: number;
  page?: number;
  filter?: PaymentFilter;
  kind?: "refund" | "normal" | "both";
  order_by?: string;
}

export interface PaymentListResponse {
  payments: Array<PaymentResponse>;
  total: number;
  total_page: number;
}

export interface PaymentMessageTemplates {
  with_schedule: PaymentTemplateGroup;
  without_schedule: PaymentTemplateGroup;
}

export interface PaymentReportDetailResponse {
  created_at: string;
  id: number;
  person?: { [key: string]: any };
  person_code: string;
  full_name: string;
  phone: string;
  province?: string;
  district?: string;
  ward?: string;
  address_number: string;
  product_names?: string;
  group_names?: string;
  total_amount: number;
  payment_allocations?: Array<PaymentAllocationDetail>;
}

export interface PaymentReportDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  export?: boolean;
}

export interface PaymentReportRecord {
  created_at: string;
  id: number;
  person_id: number;
  person_code: number;
  full_name: string;
  phone: string;
  product_names?: string;
  group_names?: string;
  income: number;
  expense: number;
  total_amount: number;
  cash?: number;
  bank?: number;
  momo?: number;
  mpos?: number;
  credit_card?: number;
  product_amount?: number; // Total amount of products
  general_service_amount?: number; // Total amount of general services
  gxtn_amount?: number; // Total amount of general services
  veneer_amount?: number; // Total amount of general services
  orthodontic_amount?: number; // Total amount of orthodontic services
  implant_amount?: number; // Total amount of implant services
  other_amount?: number; // Total amount of other categories
  doctor_name?: string; // tên bác sĩ phụ trách
  person: Person;
  user: UserShort;
}

export interface PaymentReportSummary {
  total_records: number;
  total_income: number;
  total_expense: number;
  total_revenue: number;
  // Payment Methods Summary
  total_cash: number;
  total_bank: number;
  total_momo?: number;
  total_mpos: number;
  total_credit_card: number;
  // Service Amounts Summary
  total_product_amount: number;
  total_gxtn_amount: number;
  total_veneer_amount: number;
  total_general_service_amount: number;
  total_orthodontic_amount: number;
  total_implant_amount: number;
  total_other_amount: number;
}

export interface PaymentResponse {
  id: number;
  total_amount: number;
  cash: number;
  credit_card: number;
  mpos: number;
  bank: number;
  momo: number;
  state: string;
  status: number;
  payment_date: string;
  bill_id: number;
  person_id: number;
  deal_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  allocations: Array<PaymentAllocationResponse>;
  bill: Bill;
}

export interface PaymentTemplate {
  content: string;
  name: string;
  params: { [key: string]: string };
  zns_id?: string;
}

export interface PaymentTemplateGroup {
  zns: PaymentTemplate;
  sms: PaymentTemplate;
}

export interface PaymentUpdateRequest {
  id: number;
  total_amount?: number;
  cash?: number;
  credit_card?: number;
  mpos?: number;
  bank?: number;
  momo?: number;
  state?: string;
  payment_date?: string;
  allocations?: Array<PaymentAllocationUpdateRequest>;
  modified?: Array<string>;
}

export interface PaymentUserInfo {
  id: number;
  username: string;
  name: string;
}

export interface PerformanceOverview {
  appointment_count: number; // Số lượng cuộc hẹn
  call_count: number; // Số lượng cuộc gọi
  note_count: number; // Số lượng ghi chú
  message_count: number; // Số lượng tin nhắn
  daily_average: number; // Trung bình hoạt động mỗi ngày
}

export interface PerformanceStatResponse {
  // 1. Tổng quan hiệu suất
  overview: PerformanceOverview;
  // 2. Phân tích theo thời gian
  time_series: ActivityBreakdown;
}

export interface Person {
  id: number;
  full_name: string;
  date_of_birth: string;
  gender: string;
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  confirm_phone: boolean;
  confirm_mail: boolean;
  job_id: number;
  source_id: number;
  status: number;
  version: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  person_field: PersonMeta;
}

export interface PersonAddRequest {
  full_name: string;
  date_of_birth?: string;
  gender: "male" | "female" | "unknown";
  province_id?: number;
  district_id?: number;
  ward_id?: number;
  address_number?: string;
  phone: string;
  email?: string;
  job_id?: number;
  source_id?: number;
  status?: number;
  person_field?: PersonMeta;
  referred_by?: ReferredData;
  is_system_created?: boolean;
}

export interface PersonAssignment {
  id: number;
  person_id: number;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
  created_at: string;
  updated_at: string;
}

export interface PersonAssignmentAddRequest {
  person_id: number;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
}

export interface PersonAssignmentDeleteRequest {
  id: number;
}

export interface PersonAssignmentResponse {
  id: number;
  person_id: number;
  user_id: number;
  role: string;
  user: UserShort;
}

export interface PersonAssignmentUpdateRequest {
  id: number;
  person_id?: number;
  user_id?: number;
  role?: "doctor" | "counselor" | "sale" | "customer_care";
  modified?: Array<string>;
}

export interface PersonDeleteRequest {
  id: number;
  full_name: string;
}

export interface PersonDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  creator: string; // tên, id
  sale: string; // tên, id
  doctor_id: string;
  search: string;
  export?: boolean;
}

export interface PersonFieldFilter {}

export interface PersonFilter {
  gender?: string;
  phone?: string;
  email?: string;
  job_id?: number;
  source_id?: number;
  status?: number;
}

export interface PersonGetRequest {
  id: number;
  full_name?: string;
  phone?: string;
  email?: string;
  include_relation?: boolean;
}

export interface PersonListRequest {
  page_size?: number;
  page?: number;
  filter?: PersonFilter;
  user_id?: number;
  search?: string;
  order_by?: string;
}

export interface PersonListResponse {
  persons: Array<PersonResponse>;
  total: number;
  total_page: number;
}

export interface PersonMeta {
  treatment_id?: number;
  treatment_status_id?: number;
  description?: string;
  has_zalo: "yes" | "no" | "unknown";
  secondary_phone?: string;
  code?: string;
  medical_condition?: string;
  special_note?: string;
  account_id?: string;
  pancake_link?: string;
  source_channel?: string; // Nguồn từ kênh (Facebook, Website, Zalo, Youtube, ...)
  form_source?: string; // Nguồn Form (Form Báo Giá, Form Tư vấn, ...)
  landing_page_url?: string; // URL Landing Page/Website
  form_position?: string; // Vị trí Form trên Website
  bank_account_name?: string;
  bank_account_number?: string;
  bank?: string; // Tên ngân hàng
  bank_branch?: string;
}

export interface PersonOverview {
  total_persons: number; // Tổng số người
  new_persons: number; // Số người mới (30 ngày gần nhất)
}

export interface PersonResponse {
  id: number;
  full_name: string;
  date_of_birth: string;
  gender: string;
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  confirm_phone: boolean;
  confirm_mail: boolean;
  job_id: number;
  source_id: number;
  status: number;
  version: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  person_field: PersonMeta;
  phone_view_history: Array<PhoneViewerInfo>;
  tags: Array<TagInfo>;
  stage_name: string;
  referred_by: ReferredData;
  form_submissions: Array<FormSubmission>;
  creator: UserShort;
  sale: UserShort;
  issues: Array<Issue>;
  assignment: Array<PersonAssignmentResponse>;
}

export interface PersonSort {
  id: number;
  full_name: string;
  phone: string;
  gender: string;
}

export interface PersonSortResponse {
  id: number;
  full_name: string;
  phone: string;
  gender: string;
  person_field: PersonMeta;
  issues: Array<Issue>;
}

export interface PersonStatResponse {
  // 1. Tổng quan
  overview: PersonOverview;
  // 2. Danh sách người được phân công gần đây (30 ngày)
  recent_persons: Array<PersonResponse>;
}

export interface PersonTimeline {
  person_id: number;
  entity_id: number;
  kind: string;
  data: { [key: string]: any };
  created_at: string;
}

export interface PersonTimelineRequest {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
}

export interface PersonUpdateRequest {
  id: number;
  full_name?: string;
  date_of_birth?: string;
  gender?: "male" | "female" | "unknown";
  province_id?: number;
  district_id?: number;
  ward_id?: number;
  address_number?: string;
  phone?: string;
  email?: string;
  job_id?: number;
  source_id?: number;
  status?: number;
  person_field?: PersonMeta;
  referred_by?: ReferredData;
  modified?: Array<string>;
}

export interface PhoneViewerInfo {
  user_id: number;
  user_name: string;
  viewed_at: string;
}

export interface Pipeline {
  id: number;
  name: string;
  description: string;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface PipelineAddRequest {
  name: string;
  description: string;
  user_id: number;
  status: number;
}

export interface PipelineDeleteRequest {
  id: number;
  name: string;
}

export interface PipelineFilter {
  search: string;
  name: string;
  description: string;
  user_id: number;
  status: number;
}

export interface PipelineGetRequest {
  id: number;
  name: string;
}

export interface PipelineListRequest {
  page_size?: number;
  page?: number;
  order_by?: string;
}

export interface PipelineListResponse {
  pipelines: Array<PipelineResponse>;
  total: number;
  total_page: number;
}

export interface PipelineResponse {
  id: number;
  name: string;
  description: string;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  stages: Array<StageResponse>;
}

export interface PipelineUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  user_id?: number;
  status?: number;
  modified?: Array<string>;
}

export interface Product {
  id: number;
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  collection: Array<string>;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface ProductAddRequest {
  name: string;
  code: string;
  description?: string;
  price?: number;
  type: string;
  status: number;
  quantity?: number;
  sku?: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  collection?: Array<string>;
  operations?: Array<ProductOperationAddRequest>;
}

export interface ProductDeleteRequest {
  id: number;
}

export interface ProductFilter {
  name?: string;
  code?: string;
  type?: string;
  status?: number;
  sku?: string;
  unit_id?: number;
  group_id?: number;
  category_id?: number;
}

export interface ProductGetRequest {
  id: number;
  name?: string;
  code?: string;
}

export interface ProductImage {
  media_id: number;
  title: string;
  description: string;
}

export interface ProductListRequest {
  page_size?: number;
  page?: number;
  filter?: ProductFilter;
  search?: string;
  user_created?: number;
  user_managed?: Array<number>;
  order_by?: string;
}

export interface ProductListResponse {
  products: Array<ProductResponse>;
  total: number;
  total_page: number;
}

export interface ProductOperation {
  id: number;
  product_id: number;
  operation_id: number;
  order_sequence: number;
  operation: Operation;
}

export interface ProductOperationAddRequest {
  product_id?: number;
  operation_id: number;
}

export interface ProductOperationDeleteRequest {
  product_id: number;
  operation_id: number;
}

export interface ProductResponse {
  id: number;
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  collection: Array<string>;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  category: TermShort;
  operations: Array<Operation>;
}

export interface ProductUpdateRequest {
  id: number;
  name?: string;
  code?: string;
  description?: string;
  price?: number;
  type?: string;
  status?: number;
  quantity?: number;
  sku?: string;
  unit_id?: number;
  group_id?: number;
  category_id?: number;
  collection?: Array<string>;
  operations?: Array<ProductOperationAddRequest>;
  modified?: Array<string>;
}

export interface QueryError {
  code: string;
  message: string;
  detail?: string;
}

export interface RawQueryRequest {
  sql: string; // Câu truy vấn SQL với placeholders $1, $2, ...
  parameters?: Array<Parameter>; // Danh sách parameters có kiểu
  timeout?: number; // Thời gian timeout cho query (giây)
}

export interface RawQueryResponse {
  results: Array<{ [key: string]: any }>;
  rowCount?: number;
  executionTime?: number; // Thời gian thực thi truy vấn (ms)
  columnTypes?: { [key: string]: string }; // Thông tin kiểu dữ liệu của các cột
}

export interface RecordFieldChange {
  field: string;
  old_value: any;
  new_value: any;
}

export interface RecordHistoryEntry {
  user_id: number;
  changes: Array<RecordFieldChange>;
  timestamp: string;
}

export interface Referral {
  id: number;
  referred_person_id: number;
  notes: string;
  entity_type: string;
  entity_id: number;
  referrer_relationship: string;
  created_at: string;
}

export interface ReferralAddRequest {
  referred_person_id: number;
  notes: string;
  entity_type: string;
  entity_id: number;
  referrer_relationship: string;
}

export interface ReferralDeleteRequest {
  id: number;
}

export interface ReferralUpdateRequest {
  referred_person_id: number;
  notes?: string;
  entity_type?: string;
  entity_id?: number;
  referrer_relationship?: string;
}

export interface ReferredData {
  referrer_id?: number;
  relationship?: string;
  note?: string;
}

export interface Referrer {
  id: number;
  full_name: string;
  phone: string;
  email: string;
  entity_type: string;
  referrer_relationship: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
  id: number;
}

export interface RefreshTokenResponse {
  id: number;
  access_token: string;
  refresh_token: string;
  access_expire: number;
  refresh_expire: number;
  refresh_after: number;
}

export interface Role {
  name: string;
  description: string;
  parent: string;
}

export interface Schedule {
  id: number;
  user_id: number;
  status: number;
  start_time: string;
  end_time: string;
  stage: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface ScheduleAddRequest {
  user_id: number;
  start_time: string;
  end_time: string;
}

export interface ScheduleDeleteRequest {
  id: number;
}

export interface ScheduleListRequest {
  user_id?: number;
  start_time?: string;
  end_time?: string;
}

export interface ScheduleListResponse {
  schedules: Array<ScheduleResponse>;
}

export interface ScheduleRequest {
  from: string;
  to: string;
  user_id?: number;
}

export interface ScheduleResponse {
  id: number;
  user_id: number;
  status: number;
  start_time: string;
  end_time: string;
  stage: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  user: UserShort;
}

export interface ScheduleStatResponse {
  // 1. Thông tin tổng quan
  overview: Overview;
  // 2. Danh sách cuộc hẹn sắp tới
  upcoming_appointments: Array<AppointmentResponse>;
  // 3. Lịch trình theo ngày
  daily_schedules: Array<DailySchedule>;
}

export interface ScheduleUpdateRequest {
  id: number;
  start_time?: string;
  end_time?: string;
  stage?: number;
  modified?: Array<string>;
}

export interface SearchRequest {
  search: string;
}

export interface SearchResponse {
  referrers: Array<Referrer>;
}

export interface SelectFieldV2 {
  field: string;
  function?:
    | "COUNT"
    | "SUM"
    | "AVG"
    | "MIN"
    | "MAX"
    | "ARRAY_AGG"
    | "STRING_AGG"
    | "JSON_AGG"
    | "JSONB_AGG";
  alias?: string;
  format?: string;
  case?: CaseWhenV2;
  raw_sql?: string;
}

export interface SendMessageRequest {
  person_id: number;
  phone: string;
  email: string;
  email_content: string;
  sms_content: string;
  zns_template_id: string;
  zns_params: string;
  zns_content: string;
  fallback_sms: boolean;
}

export interface SendMessageResponse {}

export interface SetAttachmentDataRequest {
  attachment_id: number;
  kind: string;
  key?: string;
  value?: string;
  data?: { [key: string]: any };
  participant_id?: number;
}

export interface SetBillDataRequest {
  bill_id: number;
  kind: string;
  key: string;
  value: string;
  data?: { [key: string]: any };
  user_id: number;
}

export interface SetDataRequest {
  person_id: number;
  kind: string;
  key: string;
  value: string;
  data?: { [key: string]: any };
}

export interface SetUserDataRequest {
  user_id: number;
  kind: string;
  key: string;
  value: string;
  data?: { [key: string]: any };
}

export interface Setting {
  id: number;
  category: string;
  name: string;
  value: { [key: string]: any };
  description: string;
}

export interface SettingAddRequest {
  category: string;
  name: string;
  value: { [key: string]: any };
  description: string;
}

export interface SettingDeleteRequest {
  id: number;
}

export interface SettingListRequest {
  category?: string;
  name?: string;
}

export interface SettingListResponse {
  settings: Array<Setting>;
}

export interface SettingSyncRequest {
  category: string;
  name: string;
  value: { [key: string]: any };
  description?: string;
}

export interface SettingUpdateRequest {
  id: number;
  category?: string;
  name?: string;
  value?: { [key: string]: any };
  description?: string;
  modified?: Array<string>;
}

export interface SmsSetting {
  sms_api_url: string;
  sms_username: string;
  sms_password: string;
  sms_brand_name: string;
}

export interface SortCriteria {
  field: string;
  order: "ASC" | "DESC";
}

export interface SortCriteriaV2 {
  field: string;
  order: "ASC" | "DESC";
  collate?: string;
}

export interface Stage {
  id: number;
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
  meta: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface StageAddRequest {
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
}

export interface StageDeleteRequest {
  id: number;
}

export interface StageFilter {
  pipeline_id?: number;
  name?: string;
  status?: number;
}

export interface StageGetRequest {
  id: number;
}

export interface StageHistoryEntry {
  stage_id: number;
  entered_at: string;
  exited_at?: string;
  user_id?: number;
  reason?: string;
}

export interface StageListRequest {
  page_size?: number;
  page?: number;
  filter?: StageFilter;
  order_by?: string;
}

export interface StageListResponse {
  stages: Array<StageResponse>;
  total: number;
  total_page: number;
}

export interface StageResponse {
  id: number;
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
  meta: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  children: Array<Stage>;
}

export interface StageUpdateMetaRequest {
  id: number;
  meta_field_name: string;
  meta_value: string;
}

export interface StageUpdateRequest {
  id: number;
  name?: string;
  pipeline_id?: number;
  order_number?: number;
  parent_stage_id?: number;
  meta?: string;
  status?: number;
  modified?: Array<string>;
}

export interface StatRequest {
  user_id: number;
  start_date?: string;
  end_date?: string;
}

export interface StatusCount {
  status: string;
  count: number;
}

export interface SwitchDealRequest {
  old_id: number;
  new_id?: number;
  track_id: number;
  pipeline_id?: number;
  stage_id?: number;
}

export interface SwitchDealResponse {
  appointments: Array<Appointment>;
  person: PersonResponse;
  deal: DealResponse;
  attachments: Array<AttachmentResponse>;
  bill_items: Array<BillItemResponse>;
}

export interface SyncDataRequest {
  from_id: number;
  to_id: number;
}

export interface SyncResponse {
  perm: string;
}

export interface Tag {
  id: number;
  name: string;
  category: string;
  description?: string;
  user_id?: number;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface TagAddRequest {
  name: string;
  category: string;
  description?: string;
}

export interface TagDeal {
  id: number;
  deleted_at?: string;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  assigned_by?: number;
  removed_by?: number;
  deal_id: number;
  tag_id: number;
}

export interface TagDealAddRequest {
  deal_id: number;
  tag_id: number;
  assigned_by?: string;
}

export interface TagDealDeleteRequest {
  tag_id: number;
  deal_id: number;
}

export interface TagDealUpdateRequest {
  id: number;
  deal_id?: number;
  tag_id?: number;
}

export interface TagDeleteRequest {
  id: number;
  name?: string;
}

export interface TagFilter {
  ids?: Array<number>;
  name?: string;
  category?: string;
  status?: number;
  user_id?: number;
}

export interface TagGetRequest {
  id: number;
  name?: string;
  category?: string;
}

export interface TagInfo {
  id: number;
  name: string;
  category: string;
}

export interface TagListRequest {
  page_size?: number;
  page?: number;
  filter?: TagFilter;
  search?: string;
  order_by?: string;
}

export interface TagListResponse {
  tags: Array<TagResponse>;
  total: number;
  total_page: number;
}

export interface TagPerson {
  id: number;
  deleted_at?: string;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  assigned_by?: number;
  removed_by?: number;
  person_id: number;
  tag_id: number;
}

export interface TagPersonAddRequest {
  person_id: number;
  tag_id: number;
  assigned_by?: string;
}

export interface TagPersonDeleteRequest {
  tag_id: number;
  person_id: number;
}

export interface TagPersonUpdateRequest {
  id: number;
  person_id?: number;
  tag_id?: number;
}

export interface TagResponse {
  id: number;
  name: string;
  category: string;
  description?: string;
  user_id?: number;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface TagShort {
  id: number;
  name: string;
  category: string;
}

export interface TagUpdateRequest {
  id: number;
  name?: string;
  category?: string;
  description?: string;
  status?: number;
  modified?: Array<string>;
}

export interface Task {
  id: number;
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  end_date: string;
  type: string;
  status: number;
  state: string;
  priority: number;
  parent_id: number;
  person_id: number;
  deal_id: number;
  appointment_id: number;
  department_id: number;
  creator_id: number;
  serial: number;
  current_serial: number;
  task_recurring_id: number;
  history: Array<HistoryEntry>;
  complete_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TaskAddRequest {
  title: string;
  note: string;
  start_date?: string;
  due_date?: string;
  end_date?: string;
  type?: string;
  state?: string;
  priority: number;
  parent_id?: number;
  person_id: number;
  deal_id?: number;
  department_id?: number;
  appointment_id?: number;
  users?: Array<TaskAssignmentAddRequest>;
  departments?: Array<TaskDepartmentAddRequest>;
  cron_expression?: string;
}

export interface TaskAssignment {
  id: number;
  task_id: number;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
  state: "pending" | "completed" | "overdue";
  status: number;
  due_at: string;
  stated_at: string;
  completed_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TaskAssignmentAddRequest {
  task_id?: number;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
  stated_at?: string;
}

export interface TaskAssignmentDeleteRequest {
  id: number;
}

export interface TaskAssignmentDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  priority?: number; // độ ưu tiên trong task
}

export interface TaskAssignmentResponse {
  id: number;
  task_id: number;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
  state: "pending" | "completed" | "overdue";
  status: number;
  due_at: string;
  stated_at: string;
  completed_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  user: UserShort;
}

export interface TaskAssignmentUpdateRequest {
  id: number;
  state?: "pending" | "completed" | "overdue";
  user_id?: number;
  role?: "primary" | "contributor" | "reviewer";
  stated_at?: string;
}

export interface TaskDeleteRequest {
  id: number;
}

export interface TaskDepartment {
  id: number;
  task_id: number;
  department_id: number;
  complete_by: number;
  role: "primary" | "contributor" | "reviewer";
  state: "pending" | "completed" | "overdue";
  status: number;
  due_at: string;
  stated_at: string;
  completed_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TaskDepartmentAddRequest {
  task_id?: number;
  department_id: number;
  role?: "primary" | "contributor" | "reviewer";
  stated_at?: string;
}

export interface TaskDepartmentDeleteRequest {
  id: number;
}

export interface TaskDepartmentDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  priority?: number; // độ ưu tiên trong task
}

export interface TaskDepartmentResponse {
  id: number;
  task_id: number;
  department_id: number;
  complete_by: number;
  role: "primary" | "contributor" | "reviewer";
  state: "pending" | "completed" | "overdue";
  status: number;
  due_at: string;
  stated_at: string;
  completed_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  department: DepartmentShort;
  compeleter: UserShort;
}

export interface TaskDepartmentUpdateRequest {
  id: number;
  state?: "pending" | "ongoing" | "completed";
  role?: "primary" | "contributor" | "reviewer";
  department_id?: number;
  complete_by?: number;
  stated_at?: string;
}

export interface TaskDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  user_id?: number;
}

export interface TaskFilter {
  person_id?: number;
  start_date?: string;
  due_date?: string;
  status?: number;
  type?: string;
  priority?: number;
  creator_id?: number;
}

export interface TaskGetRequest {
  id: number;
  serial?: number;
}

export interface TaskListRequest {
  page_size?: number;
  page?: number;
  filter?: TaskFilter;
  primary_id?: number;
  contributor_id?: number;
  slow_process?: boolean;
  order_by?: string;
}

export interface TaskListResponse {
  tasks: Array<TaskResponse>;
  total: number;
  total_page: number;
}

export interface TaskNote {
  id: number;
  task_id: number;
  user_id: number;
  body: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
}

export interface TaskNoteAddRequest {
  task_id: number;
  body: string;
}

export interface TaskNoteDeleteRequest {
  id: number;
}

export interface TaskNoteFilter {
  task_id: number;
}

export interface TaskNoteListRequest {
  page_size?: number;
  page?: number;
  filter?: TaskNoteFilter;
  order_by?: string;
}

export interface TaskNoteListResponse {
  notes: Array<TaskNoteResponse>;
  total: number;
  total_page: number;
}

export interface TaskNoteResponse {
  id: number;
  task_id: number;
  user_id: number;
  body: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
  creator: UserShort;
}

export interface TaskNoteUpdateRequest {
  id: number;
  body: string;
}

export interface TaskOverview {
  total_tasks: number; // Tổng số công việc
  completed_tasks: number; // Số công việc hoàn thành
  completion_rate: number; // Tỷ lệ hoàn thành (%)
  remaining_tasks: number; // Số công việc còn lại
}

export interface TaskRecurring {
  id: number;
  cron_expression: string;
  next_occurrence: string;
  last_occurrence: string;
}

export interface TaskResponse {
  id: number;
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  end_date: string;
  type: string;
  status: number;
  state: string;
  priority: number;
  parent_id: number;
  person_id: number;
  deal_id: number;
  appointment_id: number;
  department_id: number;
  creator_id: number;
  serial: number;
  current_serial: number;
  task_recurring_id: number;
  history: Array<HistoryEntry>;
  complete_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  parent: Task;
  notes: Array<TaskNoteResponse>;
  children: Array<Task>;
  creator: UserShort;
  assignments: Array<TaskAssignmentResponse>;
  department_assignments: Array<TaskDepartmentResponse>;
  person: Person;
}

export interface TaskStatResponse {
  // 1. Tổng quan
  overview: TaskOverview;
  // 2. Phân tích theo trạng thái
  status_breakdown: Array<StatusCount>;
  // 3. Công việc sắp đến hạn
  upcoming_deadlines: Array<TaskResponse>;
}

export interface TaskUpdateError {
  task_id: number;
  error: string;
}

export interface TaskUpdateRequest {
  id: number;
  title?: string;
  note?: string;
  parent_id?: number;
  type?: string;
  state?: string;
  priority?: number;
  start_date?: string;
  end_date?: string;
  due_date?: string;
  person_id?: number;
  deal_id?: number;
  appointment_id?: number;
  department_id?: number;
  users?: Array<TaskAssignmentAddRequest>;
  departments?: Array<TaskDepartmentAddRequest>;
  modified?: Array<string>;
}

export interface Term {
  id: number;
  name: string;
  bundle: string;
  description: string;
  body: string;
  weight: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface TermAddRequest {
  name: string;
  machine_name: string;
  bundle: string;
  description: string;
  format: string;
  weight: number;
  status: number;
  parent_id: number;
}

export interface TermAddResponse {
  id: number;
  name: string;
  bundle: string;
  description: string;
  body: string;
  weight: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface TermGetRequest {
  id?: number;
  bundle?: string;
}

export interface TermGetResponse {
  term: Term;
  parent: Term;
  children: Array<Term>;
}

export interface TermListReponse {
  terms: Array<Term>;
}

export interface TermShort {
  id: number;
  name: string;
}

export interface TermUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  format?: string;
  weight?: number;
  status?: number;
  parent_id?: number;
  modified?: Array<string>;
}

export interface TimeSeriesData {
  date: string;
  count: number;
}

export interface Track {
  id: number;
  weight: number;
  begin: string;
  end: string;
  status: number;
  pipeline_id: number;
  deal_id: number;
  person_id: number;
  stage_id: number;
  stage_history: Array<StageHistoryEntry>;
  state: string;
  kind: string;
  refund_reason: string;
  deal_stage_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TrackAddRequest {
  pipeline_id?: number;
  weight?: number;
  deal_id?: number;
  person_id?: number;
  begin?: string;
  end?: string;
  stage_id?: number;
  state?: string;
}

export interface TrackCheckoutRequest {
  id: number;
}

export interface TrackDeleteRequest {
  id: number;
}

export interface TrackDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  joins?: Array<JoinInfo>;
  export?: boolean;
}

export interface TrackFilter {
  pipeline_id?: number;
  deal_id?: number;
  person_id?: number;
  stage_id?: number;
  status?: number;
}

export interface TrackListRequest {
  page_size?: number;
  page?: number;
  filter?: TrackFilter;
  order_by?: string;
  include_relation?: boolean;
}

export interface TrackListResponse {
  tracks: Array<TrackSortResponse>;
  total: number;
  total_page: number;
}

export interface TrackResponse {
  id: number;
  weight: number;
  begin: string;
  end: string;
  status: number;
  pipeline_id: number;
  deal_id: number;
  person_id: number;
  stage_id: number;
  stage_history: Array<StageHistoryEntry>;
  state: string;
  kind: string;
  refund_reason: string;
  deal_stage_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  appointments: Array<Appointment>;
  person: PersonResponse;
  deal: DealResponse;
  attachments: Array<AttachmentResponse>;
  bill_items: Array<BillItemResponse>;
}

export interface TrackSort {
  id: number;
  begin: string;
  end: string;
  pipeline_id: number;
  deal_id: number;
  person_id: number;
  stage_id: number;
  created_at: string;
}

export interface TrackSortResponse {
  id: number;
  begin: string;
  end: string;
  pipeline_id: number;
  deal_id: number;
  person_id: number;
  stage_id: number;
  created_at: string;
  appointments: Array<AppointmentSort>;
  person: PersonSortResponse;
  deal: DealSort;
}

export interface TrackUpdateRequest {
  id: number;
  weight?: number;
  pipeline_id?: number;
  deal_id?: number;
  begin?: string;
  end?: string;
  stage_id?: number;
  status?: number;
  state?: string;
  kind?: string;
  refund_reason?: string;
  deal_stage_id?: number;
  modified?: Array<string>;
}

export interface UnionInfoV2 {
  type: "UNION" | "UNION_ALL" | "INTERSECT" | "EXCEPT";
  query: DynamicQueryV2;
}

export interface UpdateDepositRequest {
  id: number;
  total_amount?: number;
  state?: string;
  description?: string;
  allocations?: Array<AllocationAddRequest>;
  modified?: Array<string>;
}

export interface User {
  id: number;
  username: string;
  phone: string;
  email: string;
  email_confirmed: boolean;
  name: string;
  gender: string;
  department_id: number;
  department_position: string;
  profile_image: string;
  state: string;
  status: number;
  version: number;
  created_at: string;
  suspended_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface UserAddRequest {
  username: string;
  password: string;
  name: string;
  email?: string;
  gender: "male" | "female" | "unknown";
  phone?: string;
  state?: string;
  department_id?: number;
  department_position?: string;
  profile_image?: string;
  roles: Array<string>;
}

export interface UserDataExportJobs {
  jobs: Array<ExportJobData>;
}

export interface UserDataResponse {
  kind: string;
  data: { [key: string]: any };
}

export interface UserDeleteRequest {
  id: number;
  username?: string;
}

export interface UserFilter {
  ids?: Array<number>;
  username?: string;
  name?: string;
  email?: string;
  gender?: string;
  phone?: string;
  status?: number;
  state?: string;
  department_id?: number;
}

export interface UserGetRequest {
  id: number;
  username?: string;
  phone?: string;
  email?: string;
}

export interface UserListRequest {
  page_size?: number;
  page?: number;
  filter?: UserFilter;
  role?: string;
  search?: string;
  order_by?: string;
}

export interface UserListResponse {
  users: Array<UserResponse>;
  total: number;
  total_page: number;
}

export interface UserResponse {
  id: number;
  username: string;
  phone: string;
  email: string;
  email_confirmed: boolean;
  name: string;
  gender: string;
  department_id: number;
  department_position: string;
  profile_image: string;
  state: string;
  status: number;
  version: number;
  created_at: string;
  suspended_at: string;
  updated_at: string;
  deleted_at: string;
  roles: Array<string>;
  data: Array<UserDataResponse>;
}

export interface UserShort {
  id: number;
  username: string;
  name: string;
  profile_image: string;
  department_id: number;
}

export interface UserShortInformation {
  id: number;
  username: string;
}

export interface UserUpdateRequest {
  id: number;
  username?: string;
  password?: string;
  name?: string;
  email?: string;
  gender?: "male" | "female" | "unknown";
  phone?: string;
  department_id?: number;
  department_position?: string;
  profile_image?: string;
  status?: number;
  state?: string;
  roles?: Array<string>;
  modified?: Array<string>;
}

export interface VerifyRequest {
  username: string;
  otp: string;
}

export interface VerifyResponse {
  user: UserResponse;
  status: string;
  access_token: string;
  refresh_token: string;
  access_expire: number;
  refresh_expire: number;
  refresh_after: number;
}

export interface WindowSpecification {
  partition_by?: Array<string>; // Fields for partitioning
  order_by?: Array<SortCriteria>; // Fields for ordering within the window
}

export interface WindowSpecificationV2 {
  name?: string;
  partition_by: Array<string>;
  order_by: Array<SortCriteriaV2>;
  based_on?: string;
}

export interface ZnsSetting {
  secret_key: string;
  app_id: string;
  url_template: string;
  url_refresh_token: string;
  access_token: string;
  refresh_token: string;
}
