export enum EnumTypeAppointmenntStatus {
  ORTHODONTIC = 1,
  GENERALITY = 2,
  IMPLANT = 3,
  MINOR_SURGERY = 4,
}

export const enum EnumFileUsageType {
  EXAMINATION_IMAGE = "hinh_tham_kham",
  X_RAY_IMAGE = "hinh_x_quang",
  FILE_IMAGE = "hinh_tap_tin",
  DOCUMENT_FILE_IMAGE = "tap_tin_tu_lieu",
  PROFILE_FILE_IMAGE = "tap_tin_ho_so",
}

export const enum EnumEntityType {
  DEAL = "deal",
  PERSON = "person",
}
export const enum AnswerType {
  BOOLEAN = "bool",
  SINGLE_CHOICE = "single_choice",
  MULTI_CHOICE = "multiple_choice",
  TABLE_CHOICE = "multiple_choice_table",
  INTEGER = "int",
  STRING = "string",
  TEXT = "text",
  LIST = "list",
}

export const enum TaskStatusEnum {
  ALL = 0,
  CLOSE = 1,
  NEWS = 2,
  PROCESSING = 3,
  DONE = 4,
  REVIEWER = 5,
}

export const enum TaskPriorityEnum {
  ALL = 0,
  HIGH = 3,
  MEDIUM = 2,
  LOW = 1,
}

export const enum TaskRoleAssignmentEnum {
  PRIMARY = "primary",
  REVIEWER = "reviewer",
  CONTRIBUTOR = "contributor",
}
export const enum GenderPerson {
  MALE = "male",
  FEMALE = "female",
  UNKNOWN = "unknown",
}
export const enum LogEnum {
  APP = "app",
  INFO = "info",
  ERROR = "error",
  SLOW = "slow",
  STAT = "stat",
  SEVERE = "severe",
}

export const enum CommonStatus {
  DELETED = -1,
  TEMP = -2,
  INACTIVE = 1,
  ACTIVE = 2, //normal
}

export const enum DealStatus {
  REFRESH = 13,
}

export const enum AttachmentStatus {
  DELETED = CommonStatus.DELETED,
  INACTIVE = CommonStatus.INACTIVE,
  ACTIVE = CommonStatus.ACTIVE,
  TEMP = CommonStatus.TEMP,
  UNPAID = 22,
}

export enum FilterOperator {
  EQ = "EQ",
  NEQ = "NEQ",
  GT = "GT",
  GTE = "GTE",
  LT = "LT",
  LTE = "LTE",
  IN = "IN",
  LIKE = "LIKE",
  NOTIN = "NOTIN",
  NOTLIKE = "NOTLIKE",
  BETWEEN = "BETWEEN",
  ISNULL = "ISNULL",
  ISNOTNULL = "ISNOTNULL",
  TODAY = "TODAY",
  CONTAINS = "CONTAINS",
  JSON_CONTAINS_ANY = "JSON_CONTAINS_ANY",
}

export enum FilterSqlFunction {
  TO_CHAR = "TO_CHAR",
  DATE = "DATE",
  YEAR = "YEAR",
  MONTH = "MONTH",
  DAY = "DAY",
  LOWER = "LOWER",
  UPPER = "UPPER",
}

export enum FilterAggregationFunction {
  SUM = "SUM",
  AVG = "AVG",
  COUNT = "COUNT",
  MIN = "MIN",
  MAX = "MAX",
}

export enum FilterJoinType {
  LEFT = "LEFT",
  INNER = "INNER",
  RIGHT = "RIGHT",
  FULL = "FULL",
}

export const enum TaskStateEnum {
  NEW_TASK = "new_task",
  IN_PROGRESS = "in_progress",
  AWAITING_APPROVAL = "awaiting_approval",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  COMPLETED_EARLY = "completed_early",
}

export const enum NoteType {
  COMPLAIN = 4,
  OTHER = 1,
}

export enum AppointmentReminderStatus {
  REMINDED = 1,
  NOT_REMINDED = 0,
  OTHER = -1,
}

export enum DepartmentAssignment {
  DOCTOR = 1,
  CS = 2,
  ASSISTANT = 5,
  TELESALES = 10,
}

export enum DoctorType {
  ORTHODONTIC = 12,
  GENERALITY = 13,
  IMPLANT = 14,
  MINOR_SURGERY = 15,
}

const CALL_STATES = {
  INBOUND_ANSWERED: "inbound_answered",
  INBOUND_MISSED: "inbound_missed",
  OUTBOUND_ANSWERED: "outbound_answered",
  OUTBOUND_MISSED: "outbound_missed",
  UNDEFINED: "undefined",
} as const;
export type CallState = (typeof CALL_STATES)[keyof typeof CALL_STATES];

export const USER_DATA_KIND = {
  CALL_CENTER: "call_center",
  // Add future kinds here
} as const;

export type UserDataKind = (typeof USER_DATA_KIND)[keyof typeof USER_DATA_KIND];

export const enum DealState {
  DRAFT = "draft",
  WON = "won",
  LOST = "lost",
  PAYING = "paying",
  CANCELLED = "cancelled",
  ACTIVE = "active",
}

export const CallKind = {
  PROCESSED: {
    value: "PROCESSED",
    label: "Đã xử lý",
  },
  UNHEARD: {
    value: "UNHEARD",
    label: "Không nghe máy",
  },
  RENTAL: {
    value: "RENTAL",
    label: "Thuê bao",
  },
  TEST: {
    value: "TEST",
    label: "Test tổng đài",
  },
  UNDEFINED: {
    value: "UNDEFINED",
    label: "Chưa xử lý",
  },
} as const;

export type CallKindType = keyof typeof CallKind;
