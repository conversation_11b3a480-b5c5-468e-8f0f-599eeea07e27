/* eslint-disable @typescript-eslint/no-explicit-any */
import { EnumFileUsageType, TaskPriorityEnum, TaskStatusEnum } from "./bcare-enum";

// Code generated by goctl. DO NOT EDIT.
export interface GenericResponse<T> {
  code: number;
  message: string;
  data: T | null;
}

export interface User {
  id: number;
  username: string;
  phone: string;
  email: string;
  email_confirmed: boolean;
  name: string;
  gender: string;
  status: number;
  created_at: string;
  suspended_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface UserAddRequest {
  username: string;
  password: string;
  name: string;
  email: string;
  gender: "male" | "female" | "unknown";
  line_call_password: string;
  phone: string;
  department_id: number;
  department_position: string;
  status: number;
  roles: Array<string>;
}

export interface UserResponse {
  id: number;
  username: string;
  phone: string;
  email: string;
  email_confirmed: boolean;
  name: string;
  gender: string;
  expertise_id: number;
  line_call_password: string;
  department_id: number;
  department_position: string;
  status: number;
  version: number;
  created_at: string;
  suspended_at: string;
  updated_at: string;
  deleted_at: string;
  roles: Array<string>;
}

export interface UserUpdateRequest {
  id: number;
  username: string;
  password: string;
  name: string;
  email: string;
  department_id: number;
  department_position: string;
  gender: "male" | "female" | "unknown";
  phone: string;
  status: number;
  roles: Array<string>;
}

export interface UserGetRequest {
  id: number;
  username: string;
  phone: string;
  email: string;
}

export interface UserDeleteRequest {
  id: number;
  username: string;
}

export interface UserListRequest {
  page_size: number;
  page: number;
  filter: UserFilter;
  order_by: string;
  search: string;
}

export interface UserFilter {
  username: string;
  name: string;
  email: string;
  department_id: number;
  gender: string;
  phone: string;
  status: number;
}

export interface UserListResponse {
  users: Array<UserResponse>;
  total: number;
  total_page: number;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  id: number;
  username: string;
  status: string;
  access_token: string;
  refresh_token: string;
  access_expire: number;
  refresh_after: number;
  refresh_expire: number;
}

export interface RefreshTokenRequest {
  refresh_token: string;
  id: number;
}

export interface RefreshTokenResponse {
  id: number;
  access_token: string;
  refresh_token: string;
  access_expire: number;
  refresh_after: number;
}

export interface Product {
  id: number;
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  display_position_id: number;
  tooth_position: string;
  work_performed: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface ProductAddRequest {
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  display_position_id: number;
  tooth_position: string;
  work_performed: string;
}

export interface ProductFilter {
  name: string;
  code: string;
  type: string;
  status: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  display_position_id: number;
}

export interface ProductGetRequest {
  id: number;
  name: string;
  code: string;
}

export interface ProductImage {
  media_id: number;
  title: string;
  description: string;
}

export interface ProductListRequest {
  page_size: number;
  page: number;
  filter: ProductFilter;
  search: string;
  user_created: number;
  user_managed: Array<number>;
  order_by: string;
}

export interface ProductListResponse {
  products: Array<ProductResponse>;
  total: number;
  total_page: number;
}

export interface ProductResponse {
  id: number;
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  display_position_id: number;
  tooth_position: string;
  work_performed: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  category: Term;
}

export interface Term {
  id: number;
  name: string;
  bundle: string;
  description: string;
  body: string;
  weight: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface ProductUpdateRequest {
  id: number;
  name: string;
  code: string;
  description: string;
  price: number;
  type: string;
  status: number;
  quantity: number;
  sku: string;
  unit_id: number;
  group_id: number;
  category_id: number;
  display_position_id: number;
  tooth_position: string;
  work_performed: string;
}

export interface Appointments {
  person_name: string;
  start_time: string;
  end_time: string;
  doctor_id: number;
  status?: number;
  person_id: number;
}

export interface Schedules {
  user_id: number;
  schedule_id: number;
  doctor_name: string;
  type: number;
  start_time: string;
  end_time: string;
  businessHours?: { schedule_id: number; startTime: string; endTime: string }[];
  stage: number;
  id: number;
  user: UserShort;
}

export interface LocalProvince {
  id: number;
  name: string;
  code: string;
}

export interface LocalDistrict {
  id: number;
  name: string;
  prefix: string;
  province_id: number;
}

export interface LocalWard {
  id: number;
  name: string;
  prefix: string;
  province_id: number;
  district_id: number;
}

export interface LocationRequest {}

export interface LocationResponse {
  provinces: Array<LocalProvince>;
  districts: Array<LocalDistrict>;
  wards: Array<LocalWard>;
}

export interface ScheduleListResponse {
  schedules: Array<Schedules>;
  total: number;
  total_page: number;
}

export interface SchedulesResponse {
  schedules: Array<ScheduleResponse>;
}

export interface ScheduleResponse {
  id: number;
  user_id: number;
  status: number;
  start_time: string;
  end_time: string;
  stage: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  user: UserShort;
}

export interface ScheduleAddRequest {
  user_id: number;
  status: number;
  start_time: string;
  end_time: string;
}

export interface CreateAppointmentsResponse {
  appointment: {
    id: number;
    title: string;
    start_time: string;
    end_time: string;
    notes: string;
    type: number;
    person_id: number;
    doctor_id: number;
    status: number;
    created_at: string;
    updated_at: string;
  };
  person: {
    id: number;
    full_name: string;
    date_of_birth: string;
    gender: string;
    province_id: number;
    district_id: number;
    ward_id: number;
    address_number: string;
    phone: string;
    email: string;
    confirm_phone: "<boolean>";
    confirm_mail: "<boolean>";
    status: number;
    version: number;
    created_at: string;
    updated_at: string;
    deleted_at: string;
  };
  doctor: {
    id: number;
    username: string;
    phone: string;
    email: string;
    email_confirmed: "<boolean>";
    name: string;
    gender: string;
    status: number;
    version: number;
    created_at: string;
    suspended_at: string;
    updated_at: string;
    deleted_at: string;
  };
}

export interface PersonExpectTaskRequest {
  id: number;
  full_name?: string;
  phone?: string;
  email?: string;
}

export interface ScheduleListRequest {
  from: string;
  to?: string;
  user_id?: number;
}

export interface ScheduleDeleteRequest {
  id: number;
}

export interface ScheduleRequest {
  from: string;
  to: string;
  user_id?: number;
}

export interface WorkScheduleResponse {
  schedules: Array<ScheduleDailyDetail>;
}

export interface ScheduleDailyDetail {
  schedule_id: number;
  doctor_id: number;
  doctor_name: string;
  start_time: string;
  end_time: string;
  type: number;
  stage: number;
}

export interface ScheduleUpdateRequest {
  id: number;
  start_time: string;
  end_time: string;
  stage: number;
}

export interface IdAppointmentRequest {
  id: number;
}

export interface UpdateAppointmentRequest {
  id: number;
  notes: string;
  extra_notes: string;
  status: number;
  start_time: string;
  end_time: string;
  doctor_id: number;
  reminder_status: number;
}

export interface CreateAppointmentRequest {
  start_time: string;
  end_time: string;
  person_id: number;
  doctor_id: number;
  status: number;
  notes: string;
  extra_notes: string;
}

export interface ListAppointmentRequest {
  page_size: number;
  page: number;
  filter: {
    person_id: number;
    doctor_id: number;
    status: number;
    type: number;
  };
  from_date: string;
  to_date: string;
  order_by: string;
}

export interface ListAppointmentResponse {
  appointments: Array<ItemAppointmentList>;
  total: number;
  total_page: number;
}

export interface ItemAppointmentList {
  start_time: string;
  end_time: string;
  id: number;
  notes: string;
  arrived_at: string;
  extra_notes: string;
  type: number;
  status: number;
  reminder_status: number;
  created_at: string;
  person: Person;
  doctor: UserShort;
}

export interface Bundle {
  id: number;
  machine_name: string;
  name: string;
  type: string;
  description: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface BundleAddRequest {
  machine_name: string;
  name: string;
  type: string;
  description: string;
}

export interface BundleResponse {
  id: number;
  machine_name: string;
  name: string;
  type: string;
  description: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface BundleGetRequest {
  id: number;
}

export interface BundleUpdateRequest {
  id: number;
  name: string;
  description: string;
}

export interface BundleListRequest {
  page_size: number;
  page: number;
  filter: BundleFilter;
  order_by: string;
}

export interface BundleListResponse {
  total: number;
  total_page: number;
  bundles: Array<Bundle>;
}

export interface BundleFilter {
  name: string;
  type: string;
  status: number;
}

export interface TaxonomyTermData {
  id: number;
  name: string;
  machine_name: string;
  description: string;
  format: string;
  weight: number;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface TaxonomyAddRequest {
  name: string;
  machineName: string;
  description: string;
  format: string;
  weight: number;
  status: number;
  parent_id: number;
}

export interface TaxonomyResponse {
  taxonomy_term_data: TaxonomyTermData;
  parent_taxonomy: TaxonomyTermData;
}

export interface TaxonomyGetRequest {
  id: number;
}

export interface TaxonomyUpdateRequest {
  id: number;
  name: string;
  machine_name: string;
  description: string;
  format: string;
  weight: number;
  status: number;
  parent_id: number;
}

export interface TaxonomyGetResponse {
  taxonomy_term_data: TaxonomyTermData;
  parent_taxonomy: TaxonomyTermData;
  children_taxonomy: Array<TaxonomyTermData>;
}

export interface TaxonomyListRequest {}

export interface TaxonomyListResponse {
  page_size: number;
  page: number;
  filter: TaxonomyFilter;
}

export interface TaxonomyFilter {
  name: string;
  machineName: string;
  description: string;
  format: string;
  weight: number;
  status: number;
}

export interface CustomField {
  field_name: string;
  field_value: string;
}

export interface HelloRequest {}

export interface HelloRequestParams {}

export interface HelloResponse {
  hello: string;
}

export interface TermGetRequest {
  id: number;
  bundle: string;
}

export interface TermGetResponse {
  terms: Term[];
}

export interface TermGetResponseData {
  terms: [];
}

export interface RecordPerson {
  id: number;
  key: string;
  name: string;
  isCheck: boolean;
}

export interface GetConstantResponse {
  appointment_status: Record<number, string>;
  appointment_reminder_status: Record<number, string>;
  appointment_type: Record<number, string>;
  product_type: string;
  note_type: Record<number, string>;
  product_position: Record<number, string>;
  task_status: Record<string, string>;
  user_department_position: Record<string, string>;
  call_setting: {
    api_key: string;
    api_secret: string;
    api_url: string;
    uri: string;
    uri_ws: string;
    line_id: string;
    password: string;
  };
}

export interface Pipeline {
  id: number;
  name: string;
  description: string;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface PipelineAddRequest {
  name: string;
  description: string;
  user_id: number;
  status: number;
}

export interface PipelineDeleteRequest {
  id: number;
  name: string;
}

export interface PipelineFilter {
  search: string;
  name: string;
  description: string;
  user_id: number;
  status: number;
}

export interface PipelineGetRequest {
  id: number;
  name: string;
}

export interface PipelineListRequest {
  page_size: number;
  page: number;
  filter: PipelineFilter;
  order_by: string;
}

export interface PipelineListResponse {
  pipelines: Array<PipelineResponse>;
  total: number;
  total_page: number;
}

export interface PipelineResponse {
  id: number;
  name: string;
  description: string;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface PipelineUpdateRequest {
  id: number;
  name: string;
  description: string;
  user_id: number;
  status: number;
}

export interface Stage {
  id: number;
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface StageAddRequest {
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
}

export interface StageDeleteRequest {
  id: number;
}

export interface StageFilter {
  pipeline_id: number;
  name: string;
  status: number;
}

export interface StageGetRequest {
  id: number;
}

export interface StageListRequest {
  page_size: number;
  page: number;
  filter: StageFilter;
  order_by: string;
}

export interface StageListResponse {
  stages: Array<StageResponse>;
  total: number;
  total_page: number;
}

export interface StageResponse {
  id: number;
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  child_stages: Array<Stage>;
}

export interface StageUpdateRequest {
  id: number;
  name: string;
  pipeline_id: number;
  order_number: number;
  parent_stage_id: number;
  status: number;
}

export interface Deal {
  id: number;
  person_id: number;
  parent_deal_id: number;
  name: string;
  total_value: number;
  deposit_amount: number;
  total_payment: number;
  total_discount: number;
  total_installments: number;
  payment_installments: number;
  stage_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DealAddRequest {
  person_id: number;
  parent_deal_id: number;
  name: string;
  total_value: number;
  deposit_amount: number;
  total_installments: number;
  stage_id: number;
  status: number;
}

export interface DealDeleteRequest {
  id: number;
}

export interface DealFilter {
  person_id: number;
  stage_id: number;
  status: number;
}

export interface DealGetRequest {
  id: number;
}

export interface DealListRequest {
  page_size: number;
  page: number;
  filter: DealFilter;
  doctor_id: number;
  pipeline_id: number;
  order_by: string;
}

export interface DealListResponse {
  deals: Array<DealResponse>;
  total: number;
  total_page: number;
}

export interface DealResponse {
  id: number;
  person_id: number;
  parent_deal_id: number;
  name: string;
  total_value: number;
  deposit_amount: number;
  total_payment: number;
  total_discount: number;
  total_installments: number;
  payment_installments: number;
  stage_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  person: Person;
  deal_assignment: Array<DealUserResponse>;
  installment: Array<Installment>;
  tracks: Array<TrackResponse>;
  eligible_discounts: Array<EligibleDiscount>;
}

export interface Track {
  id: number;
  begin: string;
  end: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DealResponseDb {
  id: number;
  person_id: number;
  person_name: string;
  name: string;
  parent_deal_id: number;
  total_value: number;
  discount: number;
  deposit_amount: number;
  payment_total: number;
  paid_note: string;
  paid: number;
  unpaid: number;
  payment_cash: number;
  payment_bank: number;
  payment_mpos: number;
  payment_credit: number;
  total_installments: number;
  stage_id: number;
  pipeline_id: number;
  status: number;
  arrived_at: string;
  created_at: string;
  treatment_created_at: string;
  treatment_started_at: string;
  treatment_ended_at: string;
  note: string;
  doctor_id: number;
  doctor_name: string;
}

export interface DealUpdateInstallmentRequest {
  id: number;
  deposit_amount: number;
  total_installments: number;
  total_value: number;
  name: string;
}

export interface DealUpdateRequest {
  id: number;
  parent_deal_id?: number;
  stage_id?: number;
  status?: number;
  name?: string;
}

export interface DealUser {
  id: number;
  deal_id: number;
  user_id: number;
  role: string;
}

export interface DealUserAddRequest {
  deal_id: number;
  user_id: number;
  role: string;
}

export interface DealUserDeleteRequest {
  deal_id: number;
  user_id: number;
  role: string;
}

export interface DealUserListRequest {
  deal_id: number;
}

export interface DealUserListResponse {
  deal_users: Array<DealUserResponse>;
}

export interface DealUserResponse {
  id: number;
  deal_id: number;
  user_id: number;
  role: string;
  user: UserShort;
}

export interface DealUserUpdateRequest {
  id: number;
  user_id: number;
}

export interface Installment {
  id: number;
  deal_id: number;
  installment_number: number;
  amount: number;
  name: string;
  notes: string;
  child_deal_id: number;
  transaction_type: number;
  status: number;
  created_at: string;
  updated_at: string;
  version: number;
}

export interface InstallmentAddRequest {
  deal_id: number;
  installment_number: number;
  amount: number;
  child_deal_id: number;
  notes: string;
  transaction_type: number;
  status: number;
}

export interface InstallmentDeleteRequest {
  id: number;
}

export interface InstallmentFilter {
  deal_id: number;
  child_deal_id: number;
  installment_number: number;
  status: number;
}

export interface InstallmentGetRequest {
  id: number;
}

export interface InstallmentListRequest {
  page_size: number;
  page: number;
  filter: InstallmentFilter;
  order_by: string;
}

export interface InstallmentListResponse {
  installments: Array<InstallmentResponse>;
  total: number;
  total_page: number;
}

export interface InstallmentResponse {
  id: number;
  deal_id: number;
  installment_number: number;
  amount: number;
  name: string;
  notes: string;
  child_deal_id: number;
  transaction_type: number;
  status: number;
  created_at: string;
  updated_at: string;
  version: number;
  creator: UserShort;
  bill_item: BillItem;
}

export interface InstallmentUpdateRequest {
  id: number;
  amount: number;
  notes: string;
  child_deal_id: number;
  transaction_type: number;
  status: number;
}

export interface DealUpdatePaymentRequest {
  id: number;
  paid_note: string;
  paid: number;
  unpaid: number;
  payment_bank: number;
  payment_mpos: number;
  payment_credit: number;
}

export interface Attachment {
  id: number;
  deal_id: number;
  product_id: number;
  quantity: number;
  price: number;
  discount: number;
  status: number;
  extra_notes: string;
  type: number;
  note: string;
  service_task: string;
  expected_task: string;
  doctor_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface AttachmentAddRequest {
  deal_id: number;
  product_id: number;
  quantity: number;
  price: number;
  discount: number;
  status: number;
  note: string;
  type: number;
  service_task: string;
  expected_task: string;
  doctor_id: number;
  attachment_field: AttachmentField;
}

export interface AttachmentDeleteRequest {
  id: number;
}

export interface AttachmentDetail {
  deal_id: number;
  product_id: number;
  quantity: number;
  price: number;
  discount: number;
  service_task: string;
  expected_task: string;
  doctor_id: number;
}

export interface AttachmentField {
  coordinator_id: number;
  technician_id: number;
  assistant_id: number;
  description: string;
  tooth_position: string;
  tooth_position_desc: string;
  instruction: string;
}

export interface AttachmentFilter {
  deal_id: number;
  product_id: number;
  type: number;
  status: number;
  doctor_id: number;
}

export interface AttachmentGetRequest {
  id: number;
}

export interface AttachmentListRequest {
  page_size: number;
  page: number;
  filter: AttachmentFilter;
  person_id: number;
  product_type: string;
  is_treatment: number;
  order_by: string;
}

export interface AttachmentListResponse {
  attachments: Array<AttachmentResponse>;
  total: number;
  total_page: number;
}

export interface AttachmentResponse {
  id: number;
  deal_id: number;
  product_id: number;
  quantity: number;
  price: number;
  type: number;
  note: string;
  status: number;
  discount: number;
  service_task: string;
  expected_task: string;
  doctor_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  creator: UserShort;
  product: Product;
  bill_item: BillItem;
  attachment_field: AttachmentField;
  eligible_discounts: Array<EligibleDiscount>;
}

export interface AttachmentResponseDb {
  id: number;
  deal_id: number;
  product_id: number;
  product_name: string;
  product_type: string;
  bill_item_id: number;
  quantity: number;
  price: number;
  discount: number;
  type: number;
  service_task: string;
  expected_task: string;
  doctor_id: number;
  field_dieu_phoi: string;
  field_toa_thuoc: string;
  field_ky_thuat_vien: string;
  field_phu_ta: string;
  field_mo_ta: string;
  field_mo_ta_vi_tri_rang: string;
  field_vi_tri_rang: string;
}

export interface AttachmentUpdateRequest {
  id: number;
  quantity: number;
  price: number;
  discount: number;
  service_task: string;
  expected_task: string;
  doctor_id: number;
  type: number;
  note: string;
  attachment_field: AttachmentField;
}

export interface TreatmentLog {
  id: number;
  deal_id: number;
  created_at: string;
  start_time: string;
  end_time: string;
  note: string;
}

export interface TreatmentLogAddRequest {
  deal_id: number;
  start_time: string;
  end_time: string;
  note: string;
}

export interface TreatmentLogDeleteRequest {
  id: number;
}

export interface TreatmentLogListRequest {
  deal_id: number;
}

export interface TreatmentLogListResponse {
  treatment_logs: Array<TreatmentLog>;
}

export interface TreatmentLogUpdateRequest {
  id: number;
  start_time: string;
  end_time: string;
  note: string;
}

export interface ScheduleRequest {
  from: string;
  to: string;
  user_id?: number;
}

export interface ScheduleUpdateRequest {
  id: number;
  start_time: string;
  end_time: string;
  stage: number;
}

export interface ScheduleResponse {
  id: number;
  user_id: number;
  status: number;
  start_time: string;
  end_time: string;
  stage: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  user: UserShort;
}

export interface TimerItem {
  id: string;
  isRunning: boolean;
  elapsedTime: number;
  dealId: number | null;
  startTime: number | null;
}

export interface CreateFileUsageRequest {
  file_id: number;
  entity_type: string;
  entity_id: number;
  usage_type: string;
  usage_meta: string;
}

export interface FileUsageResponse {
  id: number;
  file_id: number;
  entity_type: string;
  entity_id: number;
  usage_type: EnumFileUsageType;
  usage_meta: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  file: File;
  track: Track;
}

export interface File {
  id: number;
  name: string;
  kind: string;
  type: string;
  size: number;
  path: string;
  user_id: number;
  storage: string;
  meta: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TypeListFileUsageResponse {
  file_usages: FileUsageResponse[];
  total: number;
  total_page: number;
}

export interface Quote {
  id: number;
  deal_id: number;
  created_by: number;
  status: number;
  creation_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
}

export interface QuoteAddRequest {
  deal_id: number;
}

export interface QuoteDeleteRequest {
  id: number;
}

export interface QuoteItem {
  id: number;
  quote_id: number;
  deal_id: number;
  product_id: number;
  product_name: string;
  description: string;
  quantity: number;
  min_price: number;
  max_price: number;
  discount: number;
}

export interface QuoteItemAddRequest {
  quote_id: number;
  product_id: number;
  product_name: string;
  description: string;
  quantity: number;
  min_price: number;
  max_price: number;
  discount: number;
}

export interface QuoteItemDeleteRequest {
  id: number;
}

export interface QuoteItemListRequest {
  quote_id: number;
  person_id: number;
  deal_id?: number;
}

export interface QuoteItemListResponse {
  quote_items: Array<QuoteItem>;
}

export interface QuoteItemUpdateRequest {
  id: number;
  product_name?: string;
  description?: string;
  quantity?: number;
  min_price?: number;
  max_price?: number;
  discount?: number;
}

export interface QuoteListRequest {
  deal_id?: number;
  person_id?: number;
  status?: number;
}

export interface QuoteListResponse {
  quotes: Array<Quote>;
}

export interface QuoteUpdateRequest {
  id: number;
  status?: number;
  version?: number;
}

export interface FileUsageListRequest {
  page_size: number;
  page: number;
  filter: {
    search: string;
    file_id: number;
    entity_type: string;
    entity_id: number;
    usage_type: string;
  };
  order_by: string;
}

export interface FileType {
  id: number;
  name: string;
  kind: string;
  type: string;
  size: number;
  path: string;
  user_id: number;
  storage: string;
  meta: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: null;
  version: number;
}

export interface Survey {
  id: number;
  name: string;
  description: string;
  category: string;
  entity_type: string;
}

export interface SurveyAddRequest {
  name: string;
  description: string;
  category: string;
  entity_type: string;
}

export interface SurveyDeleteRequest {
  id: number;
}

export interface SurveyFilter {
  category: string;
  entity_type: string;
}

export interface SurveyListRequest {
  filter: SurveyFilter;
}

export interface SurveyListResponse {
  surveys: Array<Survey>;
}

export interface SurveyUpdateRequest {
  id: number;
  name?: string;
  description?: string;
  category?: string;
  entity_type?: string;
}

export interface Question {
  id: number;
  survey_id: number;
  question_text: string;
  answer_type: string;
  options: string;
  titleCols: string;
}

export interface QuestionAddRequest {
  survey_id: number;
  question_text: string;
  answer_type: string;
  options: string;
}

export interface QuestionDeleteRequest {
  id: number;
}

export interface QuestionListRequest {
  survey_id: number;
}

export interface QuestionListResponse {
  questions: Array<Question>;
}

export interface QuestionUpdateRequest {
  id: number;
  question_text?: string;
  answer_type?: string;
  options?: string;
}

export interface Response {
  id: number;
  entity_id: number;
  question_id: number;
  answer_value: string;
  created_at: string;
}

export interface ResponseAddRequest {
  entity_id: number;
  question_id: number;
  answer_value: string;
}

export interface ResponseDeleteRequest {
  id: number;
}

export interface ResponseListRequest {
  entity_id: number;
  question_id: number;
  survey_id: number;
}

export interface ResponseListResponse {
  responses: Array<Response>;
}

export interface ResponseUpdateRequest {
  id: number;
  answer_value: string;
}

export interface Note {
  id: number;
  body: string;
  type: number;
  person_id: number;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface NoteAddRequest {
  body: string;
  type: number;
  person_id: number;
}

export interface NoteDeleteRequest {
  id: number;
}

export interface NoteFilter {
  person_id: number;
  user_id: number;
  type: number;
}

export interface NoteListRequest {
  page_size: number;
  page: number;
  filter: NoteFilter;
  order_by: string;
}

export interface NoteListResponse {
  notes: Array<NoteResponese>;
  total: number;
  total_page: number;
}

export interface NoteResponese {
  id: number;
  body: string;
  type: number;
  person_id: number;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  creator: UserShort;
  person: Person;
}

export interface NoteUpdateRequest {
  id: number;
  body?: string;
  type: number;
  person_id?: number;
  status?: number;
}

export interface NoteResponse {
  id: number;
  body: string;
  type: number;
  person_id: number;
  user_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  creator: UserShort;
  person: Person;
}

export interface Discount {
  id: number;
  name: string;
  type: string;
  value: number;
  scope: string;
  condition: string;
  usage_type: string;
  description: string;
  meta: string;
  start: string;
  end: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DiscountAddRequest {
  name: string;
  type: string;
  value: number;
  scope: string;
  condition: string;
  usage_type: string;
  description: string;
  meta: string;
  start: string;
  end: string;
}

export interface DiscountDeleteRequest {
  id: number;
}

export interface DiscountFilter {
  name: string;
  type: string;
  scope: string;
  usage_type: string;
  status: number;
}

export interface DiscountGetRequest {
  id: number;
}

export interface DiscountListRequest {
  page_size: number;
  page: number;
  filter: DiscountFilter;
  order_by: string;
}

export interface DiscountListResponse {
  discounts: Array<Discount>;
  total: number;
  total_page: number;
}

export interface DiscountUpdateRequest {
  id: number;
  name: string;
  type: string;
  value: number;
  scope: string;
  condition: string;
  usage_type: string;
  description: string;
  meta: string;
  start: string;
  end: string;
  status: number;
}

export interface DiscountUsage {
  id: number;
  discount_id: number;
  entity_type: string;
  entity_id: number;
  status: number;
  usage_count: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DiscountUsageAddRequest {
  discount_id: number;
  entity_type: string;
  entity_id: number;
  value: number;
}

export interface DiscountUsageDeleteRequest {
  id: number;
}

export interface DiscountUsageFilter {
  search: string;
  discount_id: number;
  entity_type: string;
  entity_id: number;
}

export interface DiscountUsageListRequest {
  page_size: number;
  page: number;
  filter: DiscountUsageFilter;
  order_by: string;
}

export interface DiscountUsageListResponse {
  discount_usages: Array<DiscountResponse>;
  total: number;
  total_page: number;
}

export interface DiscountUsageResponse {
  discount_usage_id: number;
  discount_id: number;
  entity_type: string;
  entity_id: number;
  usage_count: number;
  created_at: string;
}

export interface EligibleAddRequest {
  person_id: number;
  deal_id: number;
  product_id: number;
}

export interface EligibleDiscount {
  discount: Discount;
  discount_value: number;
  discount_amount: number;
  entity_type: string;
  entity_id: number;
}

export interface EligibleDiscountResponse {
  eligible_discounts: Array<EligibleDiscount>;
}

export interface DiscountResponse {
  discount_usage_id: number;
  id: number;
  name: string;
  value: number;
  scope: string;
  description: string;
}

export interface Referral {
  id: number;
  referred_person_id: number;
  notes: string;
  entity_type: string;
  entity_id: number;
  referrer_relationship: string;
  created_at: string;
}

export interface ReferralAddRequest {
  referred_person_id: number;
  notes: string;
  entity_type: string;
  entity_id: number;
  referrer_relationship: string;
}

export interface ReferralDeleteRequest {
  id: number;
}

export interface ReferralUpdateRequest {
  referred_person_id: number;
  notes: string;
  entity_type: string;
  entity_id: number;
  referrer_relationship: string;
}

export interface Referrer {
  id: number;
  full_name: string;
  phone: string;
  email: string;
  entity_type: string;
  referrer_relationship: string;
}

export interface SearchRequest {
  search: string;
}

export interface SearchResponse {
  referrers: Array<Referrer>;
}

export interface Bill {
  id: number;
  deal_id: number;
  user_id: number;
  branch_id: number;
  status: number;
  paid_note: string;
  paid: number;
  unpaid: number;
  payment_cash: number;
  payment_credit_card: number;
  payment_mpos: number;
  payment_bank: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface BillAddRequest {
  deal_id: number;
  user_id: number;
  branch_id: number;
  paid_note: string;
  paid: number;
  unpaid: number;
  unpaid_note: string;
  payment_cash: number;
  payment_credit_card: number;
  payment_mpos: number;
  payment_bank: number;
  bill_items: Array<BillItemAddRequest>;
}

export interface BillDeleteRequest {
  id: number;
}

export interface BillFilter {
  deal_id: number;
  user_id: number;
  branch_id: number;
  status: number;
  from_date: string;
  to_date: string;
}

export interface BillGetRequest {
  id: number;
}

export interface BillItem {
  id: number;
  bill_id: number;
  attachment_id: number;
  installment_id: number;
  status: number;
  quantity: number;
  price: number;
  discount: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface BillItemAddRequest {
  bill_id: number;
  attachment_id: number;
  installment_id: number;
  quantity: number;
  price: number;
  discount: number;
}

export interface BillItemDeleteRequest {
  id: number;
}

export interface BillItemFilter {
  bill_id: number;
  attachment_id: number;
  installment_id: number;
  status: number;
  from_date: string;
  to_date: string;
}

export interface BillItemGetRequest {
  id: number;
}

export interface BillItemListRequest {
  page_size: number;
  page: number;
  filter: BillItemFilter;
  order_by: string;
}

export interface BillItemListResponse {
  bill_items: Array<BillItemResponse>;
  total: number;
  total_page: number;
}

export interface BillItemResponse {
  id: number;
  bill_id: number;
  attachment_id: number;
  installment_id: number;
  status: number;
  quantity: number;
  price: number;
  discount: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  attachment: AttachmentResponse;
  installment: Installment;
}

export interface BillItemUpdateRequest {
  id: number;
  status: number;
  quantity: number;
  price: number;
}

export interface BillListRequest {
  page_size: number;
  page: number;
  filter: BillFilter;
  person_id: number;
  order_by: string;
}

export interface BillListResponse {
  bills: Array<BillResponse>;
  total: number;
  total_page: number;
}

export interface BillResponse {
  id: number;
  deal_id: number;
  user_id: number;
  branch_id: number;
  status: number;
  paid_note: string;
  unpaid_note: string;
  paid: number;
  unpaid: number;
  payment_cash: number;
  payment_credit_card: number;
  payment_mpos: number;
  payment_bank: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  track: Track;
  total_value: number;
  doctor_name: string;
}

export interface TrackResponse {
  id: number;
  begin: string;
  end: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  appointments: Array<Appointment>;
}

export interface Appointment {
  id: number;
  start_time: string;
  end_time: string;
  arrived_at: string;
  notes: string;
  extra_notes: string;
  type: number;
  status: number;
  reminder_status: number;
  created_at: string;
}

export interface AppointmentResponse {
  start_time: string;
  doctor_id: number;
  end_time: string;
  arrived_at: string;
  id: number;
  notes: string;
  extra_notes: string;
  type: number;
  status: number;
  reminder_status: number;
  created_at: string;
  person: Person;
  doctor: UserShort;
}

export interface AppointmentGetRequest {
  id: number;
  person_id: number;
}

export interface BillUpdateRequest {
  id: number;
  status: number;
  branch_id: number;
  payment_cash: number;
  payment_credit_card: number;
  payment_mpos: number;
  payment_bank: number;
}

export interface Task {
  id: number;
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  end_date: string;
  type: number;
  status: number;
  priority: number;
  parent_id: number;
  person_id: number;
  deal_id: number;
  appointment_id: number;
  creator_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface TaskAddRequest {
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  type: number;
  primary_user_id: number;
  priority: TaskPriorityEnum;
  parent_id: number;
  person_id: number;
  deal_id: number;
  department_id: number;
  appointment_id: number;
}

export interface TaskAssignment {
  id: number;
  task_id: number;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
  created_at: string;
  updated_at: string;
}

export interface TaskAssignmentAddRequest {
  task_id: number;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
}

export interface TaskAssignmentDeleteRequest {
  id: number;
}

export interface TaskAssignmentResponse {
  id: number;
  task_id: number;
  user_id: number;
  role: string;
  user: UserShort;
}

export interface TaskDeleteRequest {
  id: number;
}

export interface TaskFilter {
  person_id: number;
  start_date: string;
  due_date: string;
  status: number;
  type: number;
  creator_id: number;
  priority: TaskPriorityEnum;
}

export interface TaskGetRequest {
  id: number;
}

export interface TaskListRequest {
  page_size: number;
  page: number;
  filter: TaskFilter;
  primary_id: number;
  contributor_id: number;
  slow_process: boolean;
  order_by: string;
}

export interface TaskListResponse {
  tasks: Array<TaskResponse>;
  total: number;
  total_page: number;
}

//todo fix typo
export interface TaskResponse {
  id: number;
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  end_date: string;
  type: number;
  status: TaskStatusEnum;
  priority: TaskPriorityEnum;
  notes: Array<TaskNoteResponse>;
  parent_id: number;
  person_id: number;
  deal_id: number;
  appointment_id: number;
  department_id: number;
  creator_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  parent: Task;
  childrent: Array<Task>;
  creator: UserShort;
  assignment: Array<TaskAssignmentResponse>;
  person: Person;
}

export interface TaskUpdateRequest {
  id: number;
  title: string;
  note: string;
  start_date: string;
  due_date: string;
  end_date: string;
  parent_id: number;
  type: number;
  status: TaskStatusEnum;
  priority: TaskPriorityEnum;
  person_id: number;
  deal_id: number;
  appointment_id: number;
  department_id: number;
}

export interface UserShort {
  id: number;
  username: string;
  name: string;
}

export interface Assignment {
  id: number;
  task_id: number;
  user_id: number;
  role: string;
  name: string;
}

export interface TaskAssignmentUpdateMultipleRequest {
  tasks: Array<TaskId>;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
}

export interface TaskDeleteMultipleRequest {
  tasks: Array<TaskId>;
}

export interface TaskId {
  id: number;
}

export interface TaskUpdateMultipleRequest {
  tasks: Array<TaskId>;
  status: number;
}

export interface BulkDeleteRequest {
  id_list: Array<number>;
}

export interface BulkUpdateTasksRequest {
  id_list: Array<number>;
  status: number;
}

export interface AssignTasksRequest {
  IdList: Array<number>;
  user_id: number;
  role: "primary" | "contributor" | "reviewer";
}

export interface MessageTemplateRequest {
  person_id: number;
  deal_id: number;
  appointment_id: number;
}

export interface paramsZnsTemplate {
  customer_name: string;
  customer_ID: string;
  schedule_time: string;
  price_need_to_paid: string;
}

export interface SmsTemplate {
  name: string;
  content: string;
}

export interface ZnsTemplate {
  name: string;
  zns_id: string;
  content: string;
  params: paramsZnsTemplate;
}

export interface MessageTemplateResponse {
  sms: string;
  zns: string;
  email: string;
}

export interface SendMessageRequest {
  person_id: number;
  phone: string;
  email: string;
  sms_content: string;
  zns_template_id: string;
  zns_content: string;
  zns_params: string;
  email_content: string;
  fallback_sms: boolean;
}

export interface SendMessageResponse {}

export interface SyncResponse {
  perm: string;
}

export interface Role {
  name: string;
  description: string;
  parent: string;
}

export interface Call {
  id: number;
  call_id: string;
  start_time: string;
  duration: string;
  direction: string;
  source: string;
  destination: string;
  recording_file: string;
  score: number;
  pdd: number;
  tta: number;
  feedback: string;
  rating: number;
  call_status: string;
  status: number;
  person_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  state: string;
}

export interface CallResponse {
  id: number;
  call_id: string;
  start_time: string;
  duration: string;
  direction: string;
  source: string;
  destination: string;
  recording_file: string;
  score: number;
  person: Person;
  user: UserResponse;
  pdd: number;
  tta: number;
  feedback: string;
  rating: number;
  call_status: string;
  status: number;
  person_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  state: string;
}

export interface CallAddRequest {
  call_id: string;
  person_id: number;
  direction: string;
}

export interface CallDeleteRequest {
  id: number;
}

export interface CallEndRequest {
  id: number;
  call_id: string;
  is_miss: boolean;
}

export interface CallFilter {
  direction: string;
  call_status: string;
  status: number;
  person_id: number;
  user_id: number;
}

export interface CallGetRequest {
  id: number;
}

export interface CallListRequest {
  page_size: number;
  page: number;
  filter: CallFilter;
  order_by: string;
}

export interface CallListResponse {
  calls: Array<Call>;
  total: number;
  total_inbound_miss: number;
  total_inbound: number;
  total_outbound: number;
  total_outbound_miss: number;
  total_page: number;
}

export interface CallUpdateFeedbackRequest {
  id: number;
  feedback: string;
  rating: number;
}

export interface CallSetting {
  api_key: string;
  api_secret: string;
  api_url: string;
}

export interface CallUpdateRequest {
  id: number;
  call_id: string;
}

export interface CallDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  person: string; // tên, sdt
  creator: string; // tên, id
  state: "inbound" | "outbound" | "miss_inbound" | "miss_outbound" | "all"; // state
}

export interface PolicyUpdateRequest {
  type: string;
  name: string;
  operation: string;
  expr: string;
}

export interface PolicyUpdateResponse {
  data: string[];
}

export interface HistoryRequest {
  person_id: number;
}

export interface HistoryResponse {
  message_histories: Array<MessageHistory>;
  total: number;
  total_page: number;
}

export interface MessageHistory {
  id: number;
  person_id: number;
  user_id: number;
  message_id: string;
  phone: string;
  type: string; // sms or zns
  content: string;
  zns_data: string;
  error_code: string;
  message_status: string; // sent, failed, delivered
  delivered_at: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  person: Person;
}

export interface Person {
  id: number;
  full_name: string;
  date_of_birth: string;
  gender: string;
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  confirm_phone: boolean;
  confirm_mail: boolean;
  job_id: number;
  source_id: number;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  issues: Array<Issue>;
}

export interface PersonAddRequest {
  full_name: string;
  date_of_birth: string;
  gender: "male" | "female" | "unknown";
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  job_id: number;
  source_id: number;
  status: number;
  person_field: PersonField;
}

export interface PersonAssignment {
  id: number;
  person_id: number;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
  created_at: string;
  updated_at: string;
}

export interface PersonAssignmentAddRequest {
  person_id: number;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
}

export interface PersonAssignmentDeleteRequest {
  id: number;
}

export interface PersonAssignmentResponse {
  id: number;
  person_id: number;
  user_id: number;
  role: string;
  user: UserShort;
}

export interface PersonDeleteRequest {
  id: number;
  full_name: string;
}

export interface PersonField {
  treatment_id: number;
  treatment_status_id: number;
  description: string;
  has_zalo?: "unknown" | "yes" | "no";
  secondary_phone: string;
  code: string;
  medical_condition: string;
  special_note: string;
}

export interface PersonFieldFilter {}

export interface PersonFilter {
  //        Search string `json:"search"`//        FieldNguon int `json:"field_nguon"`//        FieldTrangThaiDieuTri int `json:"field_trang_thai_dieu_tri"`//        FieldLoaiDieuTri int `json:"field_loai_dieu_tri"`//        FullName string `json:"full_name"`//        DateOfBirth string `json:"date_of_birth"`
  gender: string;
  phone: string;
  email: string;
  job_id: number;
  source_id: number;
  status: number;
}

export interface PersonGetRequest {
  id: number;
  full_name: string;
  phone: string;
  email: string;
}

export interface PersonListRequest {
  page_size: number;
  page: number;
  filter: PersonFilter;
  treatment_id: number;
  treatment_status_id: number;
  search: string;
  doctor_id: number;
  order_by: string;
}

export interface PersonListResponse {
  persons: Array<PersonResponse>;
  total: number;
  total_page: number;
}

export interface PersonResponse {
  id: number;
  full_name: string;
  date_of_birth: string;
  gender: string;
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  confirm_phone: boolean;
  confirm_mail: boolean;
  job_id: number;
  source_id: number;
  status: number;
  version: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  issues: Array<Issue>;
  referrer: Referrer;
  person_field: PersonField;
  assignment: Array<PersonAssignmentResponse>;
  person_stages: Array<PersonStageResponse>;
}

export interface PersonStageResponse {
  id: number;
  person_id: number;
  stage_id: number;
  user_id: number;
  expected_end_date: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
  person: Person;
  stage: Stage;
  user: UserShort;
}

export interface Issue {
  id: number;
  title: string;
  description?: string;
  type: string;
  progress: string;
  priority: string;
  person_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
}

export interface PersonUpdateRequest {
  id: number;
  full_name: string;
  date_of_birth: string;
  gender: "male" | "female" | "unknown";
  province_id: number;
  district_id: number;
  ward_id: number;
  address_number: string;
  phone: string;
  email: string;
  job_id: number;
  source_id: number;
  status: number;
  person_field: PersonField;
}

export interface PersonAssignmentUpdateRequest {
  id: number;
  person_id: number;
  user_id: number;
  role: "doctor" | "counselor" | "sale" | "customer_care";
}

export interface AddPersonStageRequest {
  person_id: number;
  stage_id: number;
}

export interface PersonStage {
  id: number;
  person_id: number;
  stage_id: number;
  user_id: number;
  expected_end_date: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  version: number;
}

export interface DeletePersonStageRequest {
  id: number;
}

export interface GetPersonStageRequest {
  id: number;
}

export interface UpdatePersonStageRequest {
  id: number;
  person_id: number;
  stage_id: number;
  status: number;
}

export interface SettingListRequest {
  category: string;
  name: string;
}

export interface Setting {
  id: number;
  category: string;
  name: string;
  value: string;
  description: string;
}

export interface SettingListReponse {
  settings: Setting[];
}

export interface SettingAddRequest {
  category: string;
  name: string;
  value: string;
  description: string;
}

export interface SettingUpdateRequest {
  id: number;
  category: string;
  name: string;
  value: string;
  description: string;
}

export interface SettingDeleteRequest {
  id: number;
}

export interface TaskNote {
  id: number;
  task_id: number;
  user_id: number;
  body: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
}

export interface TaskNoteAddRequest {
  task_id: number;
  body: string;
}

export interface TaskNoteDeleteRequest {
  id: number;
}

export interface TaskNoteResponse {
  id: number;
  task_id: number;
  user_id: number;
  body: string;
  status: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  version: number;
  creator: UserShort;
}

export interface TaskNoteUpdateRequest {
  id: number;
  body: string;
}

export interface LogsRequest {
  type: string;
  page_size: number;
  page: number;
}

export interface LogsResponse {
  logs: string;
  total: number;
  total_page: number;
}

export interface DynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
}

export interface Aggregation {
  field: string;
  function: "SUM" | "AVG" | "COUNT" | "MIN" | "MAX";
  alias: string;
}

export interface SortCriteria {
  field: string;
  order?: "ASC" | "DESC";
}

export interface QueryResult {
  count: string;
}

export interface AppointmentQueryResult {
  appointments: Array<ItemAppointmentList>;
  total: number;
  total_page: number;
}

export interface AppointmentQueryRequest {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
}

export interface AppointmentDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  doctor?: string;
  person?: string;
  task?: string;
  note?: string;
  arrived?: "yes" | "no" | "both";
}

export interface TaskDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  creator?: string; // người tạo vừa id, vừa name dc
  primary?: string; // người thực hiện vừa id, vừa name dc
  contributor?: string; // người liên quan vừa id, vừa name dc
  person?: string; // khách hàng tên, sdt
}

export interface PersonDynamicQuery {
  table: string;
  selects?: Array<string>;
  filters?: Array<Filter>;
  group_by?: Array<string>;
  aggregations?: Array<Aggregation>;
  sort?: Array<SortCriteria>;
  limit?: number;
  offset?: number;
  creator: string; // tên, id
  sale: string; // tên, id
  doctor_id: string;
  search: string; // tên, sdt, id
}

export interface SortCriteria {
  field: string;
  order?: "ASC" | "DESC";
}

export interface Aggregation {
  field: string;
  function: "SUM" | "AVG" | "COUNT" | "MIN" | "MAX";
  alias: string;
}

export interface Filter {
  field: string;
  operator: "EQ" | "NEQ" | "GT" | "GTE" | "LT" | "LTE" | "IN" | "LIKE" | "NOTIN" | "NOTLIKE";
  value: any;
  logic?: "AND" | "OR";
  conditions?: Array<Filter>;
}

export interface CalculateDiscountRequest {
  discount_id: number;
  product_ids: Array<number>;
}

export interface CalculateDiscountResponse {
  discount_amount: number;
}

export interface ActivityListRequest {
  person_id: number;
}

export interface BaseActivity {
  type: string;
  data: {
    id: number;
    deleted_at: string | null;
    status: number;
    version: number;
    created_at: string;
    updated_at: string;
  };
}

export interface TaskActivity extends BaseActivity {
  type: "Task";
  data: BaseActivity["data"] & TaskListResponse;
}

export interface NoteActivity extends BaseActivity {
  type: "Note";
  data: BaseActivity["data"] & NoteListResponse;
}

export interface CallActivity extends BaseActivity {
  type: "Call";
  data: BaseActivity["data"] & CallListResponse;
}

export interface MessageActivity extends BaseActivity {
  type: "MessageHistory";
  data: BaseActivity["data"] & MessageHistory[];
}

export interface ActivityListResponse {
  activities: Array<TaskActivity | NoteActivity | CallActivity | MessageActivity>;
}
