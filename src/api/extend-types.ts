import { AttachmentAddRequest, AttachmentResponse } from "@/api/bcare-types-v2";

interface AttachmentWithChildren {
  self: AttachmentResponse;
  children: AttachmentResponse[];
}

export interface AttachmentMetaPriceRange {
  price: {
    min: number | undefined;
    max: number | undefined;
    discount?: number | undefined;
  };
}

export interface ExtendedAttachmentAddRequest extends AttachmentAddRequest {
  meta?: AttachmentMetaPriceRange;
  temp_id?: string;
  temp_parent_id?: string;
}

export interface ExtendedAttachmentResponse extends AttachmentResponse {
  meta?: AttachmentMetaPriceRange;
}

export interface UniversalSetting {
  type: "toggle_button" | "select" | "group" | "checkbox" | "multi-number" | "text" | "toggle_switch" | "text_input"; //add more supported type here
  label?: string;
  field_name: string;
  value?: any;
  options?: { label: string; value: any; field_name?: string }[];
  children?: UniversalSetting[];
  validation?: (value: any) => boolean;
}

// Medication
export interface Medication {
  ten: string;
  so_luong: string;
  don_vi: string;
  cach_dung: string;
  sang: string;
  trua: string;
  chieu: string;
  toi: string;
}

export interface MedicationData {
  chuan_doan: string;
  loi_dan: string[];
  ten: string;
  tieu_de: string;
  thuoc: Medication[];
}

export type StateType = "draft" | "active" | "won" | "lost" | "cancelled" | "new" | "pending";

export interface AttachmentWithParent {
  self: AttachmentResponse;
  parent: AttachmentResponse;
}
