import { defineStore } from "pinia";
import { onMounted, reactive } from "vue";

import { getConstantType } from "@/api/bcare";
import { GetConstantResponse } from "@/api/bcare-types";

interface State {
  settingConfigs?: GetConstantResponse;
  error?: number;
  loading?: boolean;
  invalidate: boolean;
}

export const useSettingConstantsStore = defineStore("settingConstantsStore", () => {
  const state = reactive<State>({
    settingConfigs: undefined,
    invalidate: false,
    error: 0,
    loading: false,
  });

  const getSettingConstants = async () => {
    try {
      state.loading = true;
      if (!state.invalidate) state.invalidate = true;
      const response = await getConstantType();
      if (response.code === 0) {
        if (response.data) state.settingConfigs = response.data;
      } else state.error = response.code;
    } catch (error) {
      state.error = error as number;
    } finally {
      state.loading = false;
    }
  };

  onMounted(() => {
    if (!state.invalidate) getSettingConstants();
  });

  return { state, getSettingConstants };
});
