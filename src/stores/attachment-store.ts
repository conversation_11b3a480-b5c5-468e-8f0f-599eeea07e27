import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  AttachmenForcetUpdateRequest,
  AttachmentAddRequest,
  AttachmentDeleteRequest,
  AttachmentDynamicQuery,
  AttachmentGetRequest,
  AttachmentListRequest,
  AttachmentResponse,
  AttachmentUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  attachmentAdd,
  attachmentDelete,
  attachmentForceUpdateCreatedAt,
  attachmentGet,
  attachmentList,
  attachmentQuery,
  attachmentUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useQuery } from "@/hooks/useQuery-v3";

export const useAttachmentStore = defineStore("attachment", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const attachments = shallowRef<AttachmentResponse[]>([]);
  const currentAttachment = shallowRef<AttachmentResponse | null>(null);

  // Query
  const attachmentQueryResult = useQuery<AttachmentDynamicQuery, AttachmentResponse>({
    queryFn: attachmentQuery,
    initialQuery: {
      // Thêm các trường initial query nếu cần
    },
  });

  // Getters
  const getAttachmentCount = computed(() => attachments.value.length);
  const getAttachmentById = computed(
    () => (id: number) => attachments.value.find((attachment) => attachment.id === id),
  );

  // Actions
  function addAttachment(req: AttachmentAddRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentAdd(req);
      if (response.data) {
        attachments.value.push(response.data);
      }
      return response.data;
    });
  }

  function deleteAttachment(req: AttachmentDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentDelete(req);
      if (response.data) {
        attachments.value = attachments.value.filter((attachment) => attachment.id !== req.id);
      }
      return response.data;
    });
  }

  function getAttachment(req: AttachmentGetRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentGet(req);
      currentAttachment.value = response.data ?? null;
      return response.data;
    });
  }

  function listAttachments(req: AttachmentListRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentList(req);
      attachments.value = response.data?.attachments ?? [];
      return response.data;
    });
  }

  function updateAttachment(req: AttachmentUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentUpdate(req);
      if (response.data) {
        const index = attachments.value.findIndex((attachment) => attachment.id === req.id);
        if (index !== -1) {
          attachments.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  function forceUpdateCreatedAt(req: AttachmenForcetUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await attachmentForceUpdateCreatedAt(req);
      if (response.data) {
        const index = attachments.value.findIndex((attachment) => attachment.id === req.id);
        if (index !== -1) {
          attachments.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  async function queryAttachments(
    req: Partial<AttachmentDynamicQuery> = {},
    getCount: boolean = true,
  ) {
    try {
      await attachmentQueryResult.fetchQuery(req, getCount);
      attachments.value = [...attachmentQueryResult.items.value];
    } catch (error) {
      attachments.value = [];
    }
  }

  return {
    // State
    attachments,
    currentAttachment,
    isLoading,
    error,
    // Getters
    getAttachmentCount,
    getAttachmentById,
    // Actions
    addAttachment,
    deleteAttachment,
    getAttachment,
    listAttachments,
    updateAttachment,
    forceUpdateCreatedAt,
    // Query
    queryAttachments,
    attachmentQueryResult,
  };
});
