import { defineStore } from "pinia";
import { shallowRef } from "vue";

import type { UserDataResponse } from "@/api/bcare-types-v2";
import { user_dataClearData,user_dataGetData, user_dataSetData } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useUserDataStore = defineStore("userData", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const userData = shallowRef<{ [key: string]: UserDataResponse }>({});

  // Actions
  const setUserData = async (
    userId: number,
    kind: string,
    key: string,
    value: string,
    data?: { [key: string]: any },
  ) => {
    return performAsyncAction(async () => {
      const response = await user_dataSetData({ user_id: userId, kind, key, value, data });
      if (response.data) {
        // Update local state
        if (!userData.value[userId]) {
          userData.value[userId] = { kind, data: {} };
        }
        userData.value[userId].data = { ...userData.value[userId].data, [key]: value };
      }
      return response.data;
    });
  };

  const getUserData = async (userId: number, kind?: string, key?: string) => {
    return performAsyncAction(async () => {
      const response = await user_dataGetData({ user_id: userId, kind, key });
      if (response.data) {
        userData.value[userId] = response.data;
      }
      return response.data;
    });
  };

  const clearUserData = async (userId: number, kind?: string, key?: string) => {
    return performAsyncAction(async () => {
      const response = await user_dataClearData({ user_id: userId, kind, key });
      if (response.data) {
        if (key && userData.value[userId]?.data) {
          delete userData.value[userId].data[key];
        } else {
          delete userData.value[userId];
        }
      }
      return response.data;
    });
  };

  return {
    // State
    userData,
    isLoading,
    error,
    // Actions
    setUserData,
    getUserData,
    clearUserData,
  };
});
