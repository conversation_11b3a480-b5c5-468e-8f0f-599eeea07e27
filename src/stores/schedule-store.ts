import { defineStore } from "pinia";
import { ref } from "vue";

import type {
  ScheduleAddRequest,
  ScheduleDeleteRequest,
  ScheduleRequest,
  ScheduleResponse,
  ScheduleUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  scheduleAdd,
  scheduleDelete,
  schedulegetWorkSchedule,
  scheduleList,
  scheduleUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useScheduleStore = defineStore("schedule", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // state
  const schedules = ref<ScheduleResponse[]>([]);

  // actions
  const fetchWorkSchedules = (req: ScheduleRequest) => {
    return performAsyncAction(async () => {
      const res = await schedulegetWorkSchedule(req);
      schedules.value = res.data?.schedules || [];
    });
  };

  const fetchSchedules = (req: ScheduleRequest) => {
    return performAsyncAction(async () => {
      const res = await scheduleList(req);
      schedules.value = res.data?.schedules || [];
    });
  };

  const addSchedule = (req: ScheduleAddRequest) => {
    return performAsyncAction(async () => {
      const res = await scheduleAdd(req);
      if (res.data) {
        schedules.value.push(res.data);
      }
    });
  };

  const updateSchedule = (req: ScheduleUpdateRequest) => {
    return performAsyncAction(async () => {
      const res = await scheduleUpdate(req);
      if (res.data) {
        const index = schedules.value.findIndex((s) => s.id === res.data?.id);
        if (index !== -1) {
          schedules.value[index] = res.data as ScheduleResponse;
        }
      }
    });
  };

  const deleteSchedule = (req: ScheduleDeleteRequest) => {
    return performAsyncAction(async () => {
      await scheduleDelete(req);
      const index = schedules.value.findIndex((s) => s.id === req.id);
      if (index !== -1) {
        schedules.value.splice(index, 1);
      }
    });
  };

  return {
    // state
    schedules,
    isLoading,
    error,

    // actions
    fetchWorkSchedules,
    fetchSchedules,
    addSchedule,
    updateSchedule,
    deleteSchedule,
  };
});
