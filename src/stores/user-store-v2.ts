import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  UserAddRequest,
  UserDeleteRequest,
  UserGetRequest,
  UserListRequest,
  UserResponse,
  UserUpdateRequest,
} from "@/api/bcare-types-v2";
import { userAdd, userDelete, userGet, userList, userUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  users: UserResponse[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useUserStore = defineStore("userv2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const users = shallowRef<UserResponse[]>([]);
  const currentUser = shallowRef<UserResponse | null>(null);
  const filteredUsers = shallowRef<UserResponse[]>([]);
  const paginatedUsers = shallowRef<UserResponse[]>([]);

  const cachedData = useStorage<CacheData>("userStoreCache", { users: [], expireTime: 0 });

  // Getters
  const getUserCount = computed(() => users.value.length);
  const getUserById = computed(() => (id: number) => users.value.find((user) => user.id === id));
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() && cachedData.value.users.length > 0;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      users.value = cachedData.value.users;
    }
  }

  function updateCache() {
    cachedData.value = {
      users: users.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllUsers() {
    return performAsyncAction(async () => {
      const response = await userList({ page: 1, page_size: 1000 });
      if (response.data?.users) {
        users.value = response.data.users;
        updateCache();
      }
      return response.data;
    });
  }

  function addUser(req: UserAddRequest) {
    return performAsyncAction(async () => {
      const response = await userAdd(req);
      if (response.data) {
        users.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteUser(req: UserDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await userDelete(req);
      if (response.data) {
        users.value = users.value.filter((user) => user.id !== req.id);
        updateCache();
      }
      return response.data;
    });
  }

  function getUser(req: UserGetRequest) {
    return performAsyncAction(async () => {
      const response = await userGet(req);
      currentUser.value = response.data ?? null;
      return response.data;
    });
  }

  function listUsers(req: UserListRequest) {
    return performAsyncAction(async () => {
      const response = await userList(req);
      if (response.data?.users) {
        filteredUsers.value = response.data.users;
        paginatedUsers.value = response.data.users;
      }
      return response.data;
    });
  }

  function updateUser(req: UserUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await userUpdate(req);
      if (response.data) {
        const index = users.value.findIndex((user) => user.id === req.id);
        if (index !== -1) {
          users.value = [
            ...users.value.slice(0, index),
            response.data,
            ...users.value.slice(index + 1),
          ];
          updateCache();
        }
      }
      return response.data;
    });
  }

  function updateFullUserList() {
    return performAsyncAction(async () => {
      const response = await userList({ page: 1, page_size: 1000 });
      if (response.data?.users) {
        users.value = response.data.users;
        updateCache();
      }
      return response.data;
    });
  }

  return {
    // State
    users,
    currentUser,
    filteredUsers,
    paginatedUsers,
    isLoading,
    error,
    // Getters
    getUserCount,
    getUserById,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllUsers,
    addUser,
    deleteUser,
    getUser,
    listUsers,
    updateUser,
    updateFullUserList,
  };
});
