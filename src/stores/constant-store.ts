// src/store/constant.ts
import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, ref } from "vue";

import { ConstantResponse } from "@/api/bcare-types-v2";
import { constantGet } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  constants: ConstantResponse | null;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

export const useConstantStore = defineStore("constant-v2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const constants = ref<ConstantResponse | null>(null);

  const cachedData = useStorage<CacheData>("constantStoreCache", { constants: null, expireTime: 0 });

  const isCacheValid = computed(() => {
    return cachedData.value.expireTime > Date.now();
  });

  function initializeFromCache() {
    if (isCacheValid.value) {
      constants.value = cachedData.value.constants;
      return true;
    }
    return false;
  }

  function updateCache() {
    cachedData.value = {
      constants: constants.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  async function fetchConstants() {
    return performAsyncAction(async () => {
      const response = await constantGet();
      if (response.code === 0 && response.data) {
        constants.value = response.data;
        updateCache();
        return constants.value;
      }
      throw new Error(`Failed to fetch constants: ${response.code}`);
    });
  }

  // Getters
  const getConstants = computed(() => constants.value);

  return {
    constants,
    isLoading,
    error,
    initializeFromCache,
    fetchConstants,
    getConstants,
    isCacheValid,
  };
});

