import dayjs from "dayjs";
import { groupBy } from "lodash";
import { defineStore } from "pinia";
import { computed, ref } from "vue";

import { EnumFileUsageType } from "@/api/bcare-enum";
import {
  FileResponse,
  FileUsageAddRequest,
  FileUsageDeleteRequest,
  FileUsageListRequest,
  FileUsageResponse,
  GenericResponse,
} from "@/api/bcare-types-v2";
import { file_usageAdd, file_usageDelete, file_usageList } from "@/api/bcare-v2";
import service from "@/api/http";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const mediaRequest: FileUsageListRequest = {
  page: 1,
  page_size: 0,
  filter: {
    search: "",
    file_id: 0,
    entity_id: 0,
    entity_type: "person",
    usage_type: EnumFileUsageType.EXAMINATION_IMAGE,
  },
};

export const useMediaStore = defineStore("media", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();
  // state
  const media = ref<FileUsageResponse[]>([]);
  //getters
  const getDateOptions = computed(() => {
    const sortedDates = Object.keys(getGroupedMedia.value);
    return sortedDates.map((date) => ({ name: date, code: date }));
  });

  const getGroupedMedia = computed(() => {
    const grouped = groupBy(media.value, (item) => {
      const date = item.track?.created_at || item.created_at;
      return dayjs(date).format("DD/MM/YYYY");
    });

    return Object.entries(grouped)
      .sort(([dateA], [dateB]) => {
        return dayjs(dateB, "DD/MM/YYYY").diff(dayjs(dateA, "DD/MM/YYYY"));
      })
      .reduce(
        (acc, [key, items]) => {
          acc[key] = items.sort((a, b) => dayjs(a.file.created_at).diff(dayjs(b.file.created_at)));
          return acc;
        },
        {} as Record<string, typeof media.value>,
      );
  });
  // actions
  function fetchMedia(req: FileUsageListRequest) {
    return performAsyncAction(async () => {
      const res = await file_usageList(req);
      media.value = res.data?.file_usages || [];
    });
  }

  function addFile(req: File) {
    return performAsyncAction(async () => {
      const formData = new FormData();
      formData.append("file_upload", req);
      formData.append("storage", "local");
      formData.append("kind", "image");
      return await fileAdd(formData);
    });
  }

  function addFileUsage(req: FileUsageAddRequest) {
    return performAsyncAction(async () => {
      return await file_usageAdd(req);
    });
  }

  function deleteFile(req: FileUsageDeleteRequest) {
    return performAsyncAction(async () => {
      return await file_usageDelete(req);
    });
  }

  function deleteFiles(ids: number[]) {
    return performAsyncAction(async () => {
      const deletePromises = ids.map((id) => deleteFile({ id }));
      return await Promise.all(deletePromises);
    });
  }

  function fileAdd(params: FormData | File) {
    return service.upload<GenericResponse<FileResponse>>("/v1/file/add", params);
  }

  function resetStore() {
    media.value = [];
  }

  function cleanup() {
    media.value = [];
    // Reset các state khác nếu cần
  }

  return {
    // state
    isLoading,
    error,
    media,
    // actions
    fetchMedia,
    addFile,
    addFileUsage,
    deleteFile,
    deleteFiles,
    resetStore,
    cleanup,
    // getters
    getGroupedMedia,
    getDateOptions,
  };
});
