import { defineStore } from "pinia";
import { computed, readonly, ref, watch } from "vue";

import { AttachmentStatus } from "@/api/bcare-enum";
import type { AttachmentResponse, Product } from "@/api/bcare-types-v2";
import { MedicationData } from "@/api/extend-types";
import useAttachment from "@/hooks/useAttachment";
import useAttachmentData from "@/hooks/useAttachmentData";
import useAttachmentMeta from "@/hooks/useAttachmentDataMeta";
import useBill from "@/hooks/useBill";

interface AttachmentWithParent {
  self: AttachmentResponse;
  parent: AttachmentResponse;
}

export const useTreatmentFormStore = defineStore("attachmentStore", () => {
  // State
  const personId = ref(0);

  function setPersonId(id: number) {
    personId.value = id;
  }

  const operationFocused = ref<"today" | "next">("today");

  const participant = ref<Record<string, number>>({
    bac_si: 0,
    phu_ta: 0,
    dieu_phoi: 0,
    x_quang: 0,
  });

  //const selectedTeeth = ref<Record<string, boolean>>({});
  //const selectedTeethNext = ref<Record<string, boolean>>({});

  const pendingAttachments = ref<Record<number, AttachmentWithParent>>({});
  const editingAttachment = ref<AttachmentWithParent | null>(null);

  const selectedMedications = ref<MedicationData[]>([]);
  const nextOperations = ref<{ id: number | string; name: string }[]>([]);
  const selectedProduct = ref<Product | null>(null);
  const selectedParentAttachment = ref<AttachmentResponse | null>(null);

  // New temporary state
  const tempSelections = ref({
    teeth: {} as Record<string, boolean>,
    nextTeeth: {} as Record<string, boolean>,
    participants: {} as Record<string, number>,
    operations: [] as { id: number | string; name: string }[],
    nextOperations: [] as { id: number | string; name: string }[],
    medications: [] as MedicationData[],
  });

  // Hooks
  const { addAttachment, updateAttachment, getAttachment } = useAttachment();
  const { setParticipantByRole, setMedications } = useAttachmentData();
  const { getMetaValues, syncMetaData, parseNextOperations } = useAttachmentMeta({
    operation: {},
    teeth: {},
    next_operation: {},
    next_teeth: {},
  });
  const { addBillItemFromAttachment, updateBillItemFromAttachment } = useBill({ useStore: false });

  // Computed
  const hasPendingAttachments = computed(() => Object.keys(pendingAttachments.value).length > 0);

  const productId = computed(() => editingAttachment.value?.parent.product_id ?? 0);
  const productIds = computed(() =>
    Object.values(pendingAttachments.value).map((attachment) => attachment.parent.product_id),
  );
  const editingAttachmentId = computed(() =>
    editingAttachment.value ? editingAttachment.value.self.id : 0,
  );

  const toggleAttachmentSearch = (attachment: AttachmentWithParent | null) => {
    if (attachment) {
      editingAttachment.value = attachment;
      selectedParentAttachment.value = attachment.parent;
    } else {
      editingAttachment.value = null;
      selectedParentAttachment.value = null;
    }
  };

  const toggleProductSearch = (attachment: AttachmentWithParent | null) => {
    editingAttachment.value = attachment;
    selectedProduct.value = attachment ? { ...attachment.self.product } : null;
  };

  const toggleNextOperationPop = () => {
    // The component should handle the actual popover toggle
  };

  const applyTempSelectionsToAttachment = (attachmentId: number) => {
    const metaValues = getMetaValues(attachmentId);

    // Sync Teeth if there is data in tempSelections.teeth
    if (Object.keys(tempSelections.value.teeth).length > 0) {
      metaValues.teeth = { ...tempSelections.value.teeth };
      syncMetaData(attachmentId, "teeth");
    }

    // Sync Next Teeth if there is data in tempSelections.nextTeeth
    if (Object.keys(tempSelections.value.nextTeeth).length > 0) {
      metaValues.next_teeth = { ...tempSelections.value.nextTeeth };
      syncMetaData(attachmentId, "next_teeth");
    }

    // Sync Operations if there are operations selected in tempSelections.operations
    if (tempSelections.value.operations.length > 0) {
      metaValues.operation = Object.fromEntries(
        tempSelections.value.operations.map((op) => [op.id, op.name]),
      );
      syncMetaData(attachmentId, "operation");
    }

    // Sync Next Operations if there are operations selected in tempSelections.nextOperations
    if (tempSelections.value.nextOperations.length > 0) {
      metaValues.next_operation = Object.fromEntries(
        tempSelections.value.nextOperations.map((op) => [op.id, op.name]),
      );
      syncMetaData(attachmentId, "next_operation");
    }

    // Apply Participants only if there are participants selected in tempSelections.participants
    if (Object.keys(tempSelections.value.participants).length > 0) {
      Object.entries(tempSelections.value.participants).forEach(([role, value]) => {
        setParticipantByRole(attachmentId, role, value);
      });
    }

    // Sync Medications if there are medications selected in tempSelections.medications
    if (tempSelections.value.medications.length > 0) {
      setMedications(attachmentId, tempSelections.value.medications);
    }
  };

  const selectParentAttachment = async (selectedParent: AttachmentResponse) => {
    if (selectedParent) {
      if (editingAttachment.value !== null) {
        // Update existing attachment
        const updatedAttachment = {
          //...editingAttachment.value.self,
          id: editingAttachment.value.self.id,
          parent_id: selectedParent.id,
        };

        const updated = await updateAttachment(updatedAttachment);

        if (updated) {
          // Update local state
          pendingAttachments.value[updatedAttachment.id] = {
            self: updated,
            parent: selectedParent,
          };
          applyTempSelectionsToAttachment(updatedAttachment.id);
        }
      } else {
        // Add new attachment
        const newAttachment = {
          person_id: personId.value,
          kind: "operation",
          parent_id: selectedParent.id,
          status: AttachmentStatus.TEMP,
        };

        const added = await addAttachment(newAttachment);
        if (added) {
          pendingAttachments.value[added.id] = {
            self: added,
            parent: selectedParent,
          };
          applyTempSelectionsToAttachment(added.id);
        }
      }

      // Reset state
      editingAttachment.value = null;
      selectedParentAttachment.value = null;
    }
  };

  const selectProduct = async () => {
    if (selectedProduct.value) {
      // Create a parent attachment of kind "product"
      const parentAttachment = {
        person_id: personId.value,
        kind: "product",
        product_id: selectedProduct.value.id,
        status: AttachmentStatus.TEMP,
        product: { ...selectedProduct.value },
        quantity: 1,
      };

      const addedParentAttachment = await addAttachment(parentAttachment);

      if (addedParentAttachment) {
        // Create the operation attachment with the new parent
        const operationAttachment = {
          person_id: personId.value,
          kind: "operation",
          parent_id: addedParentAttachment.id,
          status: AttachmentStatus.TEMP,
        };

        let addedOperationAttachment: AttachmentResponse | null = null;

        if (editingAttachment.value) {
          // Update existing attachment
          const updatedAttachment = {
            id: editingAttachment.value.self.id,
            ...operationAttachment,
          };

          addedOperationAttachment = await updateAttachment(updatedAttachment);
        } else {
          // Add new attachment
          addedOperationAttachment = await addAttachment(operationAttachment);
        }

        if (addedOperationAttachment) {
          pendingAttachments.value[addedOperationAttachment.id] = {
            self: addedOperationAttachment,
            parent: addedParentAttachment,
          };

          applyTempSelectionsToAttachment(addedOperationAttachment.id);
        }

        // Reset state
        selectedProduct.value = null;
        editingAttachment.value = null;
      }
    }
  };

  const isEditing = ref(false);

  const setEditMode = (value: boolean) => {
    isEditing.value = value;
  };

  const loadExistingAttachment = async (attachmentId: number) => {
    try {
      // Load attachment chính và parent của nó
      const attachment = await getAttachment({ id: attachmentId });
      if (attachment) {
        // Khởi tạo pendingAttachments với attachment hiện có
        pendingAttachments.value[attachment.id] = {
          self: attachment,
          parent: attachment.parent,
        };

        // Set person ID
        setPersonId(attachment.person_id);
        setEditMode(true);
        // Xử lý attachment.data trực tiếp
        if (Array.isArray(attachment.data)) {
          attachment.data.forEach((data) => {
            switch (data.kind) {
              case "bac_si":
              case "phu_ta":
              case "dieu_phoi":
              case "x_quang":
                participant.value[data.kind] = data.participant_id || 0;
                break;
              case "medication":
                if (data.data) {
                  selectedMedications.value = Object.values(data.data);
                }
                break;
              case "meta":
                // Parse next_operation data
                if (data.data?.next_operation) {
                  // Sử dụng hook useAttachmentMeta để parse
                  nextOperations.value = parseNextOperations(data.data.next_operation);
                }
                break;
            }
          });
        }
      }
    } catch (error) {
      console.error("Error loading attachment:", error);
    }
  };

  const handleRemoveOperation = (id: number | string) => {
    nextOperations.value = nextOperations.value.filter((op) => op.id !== id);
  };

  const updateAttachmentWithParent = async (
    attachment: AttachmentWithParent,
    newStatus: AttachmentStatus,
  ): Promise<AttachmentWithParent | null> => {
    const updateSingleAttachment = async (attachmentToUpdate: AttachmentResponse) => {
      const updatedAttachment = {
        id: attachmentToUpdate.id,
        status: newStatus,
      };
      try {
        const updated = await updateAttachment(updatedAttachment);
        if (updated) {
          console.log(`Attachment ${updatedAttachment.id} updated to status: ${newStatus}`);
          return updated;
        }
      } catch (error) {
        console.error(`Error updating attachment ${updatedAttachment.id}:`, error);
      }
      return null;
    };

    const updatePromises = [updateSingleAttachment(attachment.self)];

    if (attachment.parent && attachment.parent.status === AttachmentStatus.TEMP) {
      updatePromises.push(updateSingleAttachment(attachment.parent));
    }

    const [updated, updatedParent] = await Promise.all(updatePromises);

    if (updated) {
      return {
        self: updated,
        parent: updatedParent || attachment.parent,
      };
    }
    return null;
  };

  const removeAttachment = async (id: number) => {
    delete pendingAttachments.value[id];
  };

  const canSave = computed(() => {
    // Kiểm tra có attachment nào không
    if (Object.keys(pendingAttachments.value).length === 0) {
      return false;
    }

    return true;
  });

  const saveAll = async (status: AttachmentStatus): Promise<AttachmentWithParent[]> => {
    try {
      if (!status) {
        throw new Error("Status is required");
      }

      const successfulUpdates: AttachmentWithParent[] = [];

      // Xử lý attachments
      for (const attachment of Object.values(pendingAttachments.value || {})) {
        try {
          const updatedAttachment = await updateAttachmentWithParent(attachment, status);
          if (updatedAttachment) {
            successfulUpdates.push(updatedAttachment);
          }
        } catch (error) {
          console.error(`Failed to update attachment: ${error}`);
          // Có thể thêm logic retry hoặc rollback
        }
      }

      if (successfulUpdates.length > 0) {
        try {
          const parentIds = successfulUpdates
            .filter((item) => item.parent?.product?.price)
            .map((item) => item.parent.id);

          if (!isEditing.value) {
            // Create new bill items
            const note = "";
            await Promise.all(
              parentIds.map((id) =>
                addBillItemFromAttachment({ attachment_id: id, note }).catch((error) => {
                  console.error(`Failed to add bill item for attachment ${id}:`, error);
                }),
              ),
            );
          } else {
            // Update existing bill items
            await Promise.all(
              parentIds.map((id) =>
                updateBillItemFromAttachment({ attachment_id: id }).catch((error) => {
                  console.error(`Failed to update bill item for attachment ${id}:`, error);
                }),
              ),
            );
          }

          await clearAll();
        } catch (error) {
          console.error("Failed to process bill items:", error);
          throw error;
        }
      } else {
        console.warn("No attachments were processed successfully");
      }

      return successfulUpdates;
    } catch (error) {
      console.error("SaveAll failed:", error);
      throw error; // Re-throw để caller có thể xử lý
    }
  };

  const saveParticipant = async (role: string, value: number) => {
    if (Object.keys(pendingAttachments.value).length === 0) {
      // If no attachments, save to temporary state
      tempSelections.value.participants[role] = value;
    } else {
      // If attachments exist, process as before
      for (const [, attachment] of Object.entries(pendingAttachments.value)) {
        const { self } = attachment;

        // Update cho attachment hiện tại
        await setParticipantByRole(self.id, role, value);

        // Update cho parent nếu có
        // if (parent) {
        //   await setParticipantByRole(parent.id, role, value);
        // }

        console.log(`Updated ${role} for attachment ${self.id}`);
      }
    }
  };

  const clearAll = () => {
    setEditMode(false);
    // personId.value = 0;
    operationFocused.value = "today";

    participant.value = {
      bac_si: 0,
      phu_ta: 0,
      dieu_phoi: 0,
      x_quang: 0,
    };

    pendingAttachments.value = {};
    editingAttachment.value = null;
    selectedMedications.value = [];
    nextOperations.value = [];

    selectedProduct.value = null;
    selectedParentAttachment.value = null;

    tempSelections.value = {
      teeth: {},
      nextTeeth: {},
      participants: {},
      operations: [],
      nextOperations: [],
      medications: [],
    };
  };

  watch(nextOperations, (newNextOperations) => {
    if (Object.keys(pendingAttachments.value).length === 0) {
      // If no attachments, save to temporary state
      tempSelections.value.nextOperations = [...newNextOperations];
    } else {
      // If attachments exist, process as before
      Object.values(pendingAttachments.value).forEach((attachment) => {
        const metaValues = getMetaValues(attachment.self.id);
        metaValues.next_operation = Object.fromEntries(
          newNextOperations.map((op) => [op.id, op.name]),
        );
        syncMetaData(attachment.self.id, "next_operation");
      });
    }
  });

  watch(
    selectedMedications,
    (medications) => {
      if (Object.keys(pendingAttachments.value).length === 0) {
        // If no attachments, save to temporary state
        tempSelections.value.medications = medications;
      } else {
        // If attachments exist, process medications for each attachment and its parent
        Object.values(pendingAttachments.value).forEach((attachment) => {
          // Update cho attachment hiện tại
          setMedications(attachment.self.id, medications);

          // Update cho parent nếu có
          if (attachment.parent) {
            setMedications(attachment.parent.id, medications);
          }
        });
      }
    },
    { deep: true },
  );

  return {
    // State
    setPersonId,

    operationFocused,
    participant,
    pendingAttachments,
    editingAttachment,
    nextOperations,
    selectedProduct,
    selectedParentAttachment,
    tempSelections,
    selectedMedications,

    // Computed
    hasPendingAttachments,
    productId,
    productIds,
    editingAttachmentId,
    canSave,

    // Actions
    toggleAttachmentSearch,
    toggleProductSearch,
    toggleNextOperationPop,
    selectParentAttachment,
    selectProduct,
    handleRemoveOperation,
    removeAttachment,
    saveAll,
    clearAll,
    saveParticipant,
    loadExistingAttachment,
    applyTempSelectionsToAttachment,
    isEditing: readonly(isEditing),
  };
});
