import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  Product,
  ProductAddRequest,
  ProductDeleteRequest,
  ProductGetRequest,
  ProductListRequest,
  ProductUpdateRequest} from "@/api/bcare-types-v2";
import { productAdd, productDelete,productGet, productList, productUpdate } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  products: Product[];
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useProductStore = defineStore("product", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const products = shallowRef<Product[]>([]);
  const currentProduct = shallowRef<Product | null>(null);
  const filteredProducts = shallowRef<Product[]>([]);

  const cachedData = useStorage<CacheData>("productStoreCache", { products: [], expireTime: 0 });

  // Getters
  const getProductCount = computed(() => products.value.length);
  const getProductById = computed(() => (id: number) => products.value.find((product) => product.id === id));
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() && cachedData.value.products.length > 0;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      products.value = cachedData.value.products;
    }
  }

  function updateCache() {
    cachedData.value = {
      products: products.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllProducts() {
    return performAsyncAction(async () => {
      const response = await productList({ page: 1, page_size: 1000 });
      if (response.data?.products) {
        products.value = response.data.products;
        updateCache();
      }
      return response.data;
    });
  }

  function addProduct(req: ProductAddRequest) {
    return performAsyncAction(async () => {
      const response = await productAdd(req);
      if (response.data) {
        products.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteProduct(req: ProductDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await productDelete(req);
      if (response.data) {
        products.value = products.value.filter((product) => product.id !== req.id);
        updateCache();
      }
      return response.data;
    });
  }

  function getProduct(req: ProductGetRequest) {
    return performAsyncAction(async () => {
      const response = await productGet(req);
      currentProduct.value = response.data ?? null;
      return response.data;
    });
  }

  function listProducts(req: ProductListRequest) {
    return performAsyncAction(async () => {
      const response = await productList(req);
      if (response.data?.products) {
        filteredProducts.value = response.data.products;
      }
      return response.data;
    });
  }

  function updateProduct(req: ProductUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await productUpdate(req);
      if (response.data) {
        const index = products.value.findIndex((product) => product.id === req.id);
        if (index !== -1) {
          products.value[index] = response.data;
          updateCache();
        }
      }
      return response.data;
    });
  }

  function updateFullProductList() {
    return performAsyncAction(async () => {
      const response = await productList({ page: 1, page_size: 1000 });
      if (response.data?.products) {
        products.value = response.data.products;
        updateCache();
      }
      return response.data;
    });
  }

  return {
    // State
    products,
    currentProduct,
    filteredProducts,
    isLoading,
    error,
    // Getters
    getProductCount,
    getProductById,
    hasCachedData,
    // Actions
    initializeFromCache,
    fetchAllProducts,
    addProduct,
    deleteProduct,
    getProduct,
    listProducts,
    updateProduct,
    updateFullProductList,
  };
});
