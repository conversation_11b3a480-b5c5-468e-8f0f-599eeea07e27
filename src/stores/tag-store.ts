import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, ref } from "vue";

import type {
  TagAddRequest,
  TagDeleteRequest,
  TagGetRequest,
  TagListRequest,
  TagResponse,
  TagUpdateRequest,
  TagPersonAddRequest,
  TagPersonUpdateRequest,
  TagPersonDeleteRequest,
  TagDealAddRequest,
  TagDealUpdateRequest,
  TagDealDeleteRequest,
  TagPerson,
  TagDeal,
} from "@/api/bcare-types-v2";
import {
  tagAdd,
  tagDelete,
  tagGet,
  tagList,
  tagUpdate,
  tag_personAdd,
  tag_personUpdate,
  tag_personDelete,
  tag_dealAdd,
  tag_dealUpdate,
  tag_dealDelete,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  tags: TagResponse[];
  expireTime: number;
}

// Interface for the grouped tags structure
export interface GroupedTagOption {
  category: string;
  items: TagResponse[];
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 hours

export const useTagStore = defineStore("tag", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const tags = ref<TagResponse[]>([]);
  const currentTag = ref<TagResponse | null>(null);
  const filteredTags = ref<TagResponse[]>([]);
  const paginatedTags = ref<TagResponse[]>([]);

  const cachedData = useStorage<CacheData>("tagStoreCache", { tags: [], expireTime: 0 });

  // Additional state for tag relationships
  const tagPersonRelations = ref<TagPerson[]>([]);
  const tagDealRelations = ref<TagDeal[]>([]);

  // Getters
  const getTagCount = computed(() => tags.value.length);
  const getTagById = computed(() => (id: number) => tags.value.find((tag) => tag.id === id));
  const getTagByName = computed(
    () => (name: string) => tags.value.find((tag) => tag.name.toLowerCase() === name.toLowerCase()),
  );
  const hasCachedData = computed(() => {
    return cachedData.value.expireTime > Date.now() && cachedData.value.tags.length > 0;
  });

  // Getter to group tags by category
  const groupedTags = computed((): GroupedTagOption[] => {
    const grouped: GroupedTagOption[] = [];
    const uncategorizedTags: TagResponse[] = [];

    tags.value.forEach((tag) => {
      if (tag.category) {
        let group = grouped.find((g) => g.category === tag.category);
        if (!group) {
          group = { category: tag.category, items: [] };
          grouped.push(group);
        }
        group.items.push(tag);
      } else {
        uncategorizedTags.push(tag);
      }
    });

    // Sort groups by category name
    grouped.sort((a, b) => a.category.localeCompare(b.category));

    // Sort items within each group by tag name
    grouped.forEach((group) => {
      group.items.sort((a, b) => a.name.localeCompare(b.name));
    });

    // Add sorted uncategorized tags as the last group if any exist
    if (uncategorizedTags.length > 0) {
      uncategorizedTags.sort((a, b) => a.name.localeCompare(b.name));
      grouped.push({ category: "Uncategorized", items: uncategorizedTags });
    }

    return grouped;
  });

  // Cache functions
  function initializeFromCache() {
    if (hasCachedData.value) {
      tags.value = cachedData.value.tags;
    }
  }

  function updateCache() {
    cachedData.value = {
      tags: tags.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Actions
  function fetchAllTags() {
    return performAsyncAction(async () => {
      const response = await tagList({ page: 1, page_size: 1000 });
      if (response.data?.tags) {
        tags.value = response.data.tags;
        updateCache();
      }
      return response.data;
    });
  }

  function addTag(req: TagAddRequest) {
    return performAsyncAction(async () => {
      const response = await tagAdd(req);
      if (response.data) {
        tags.value.push(response.data);
        updateCache();
      }
      return response.data;
    });
  }

  function deleteTag(req: TagDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await tagDelete(req);
      if (response.data) {
        tags.value = tags.value.filter((tag) => tag.id !== req.id);
        filteredTags.value = filteredTags.value.filter((tag) => tag.id !== req.id);
        paginatedTags.value = paginatedTags.value.filter((tag) => tag.id !== req.id);
        if (currentTag.value?.id === req.id) {
          currentTag.value = null;
        }
        updateCache();
      }
      return response.data;
    });
  }

  function getTag(req: TagGetRequest) {
    return performAsyncAction(async () => {
      const response = await tagGet(req);
      currentTag.value = response.data ?? null;
      return response.data;
    });
  }

  function listTags(req: TagListRequest) {
    return performAsyncAction(async () => {
      const response = await tagList(req);
      if (response.data?.tags) {
        filteredTags.value = response.data.tags;
        paginatedTags.value = response.data.tags;
      }
      return response.data;
    });
  }

  function updateTag(req: TagUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await tagUpdate(req);
      if (response.data) {
        const index = tags.value.findIndex((tag) => tag.id === req.id);
        if (index !== -1) {
          tags.value = [
            ...tags.value.slice(0, index),
            response.data,
            ...tags.value.slice(index + 1),
          ];
        }

        const filteredIndex = filteredTags.value.findIndex((tag) => tag.id === req.id);
        if (filteredIndex !== -1) {
          filteredTags.value = [
            ...filteredTags.value.slice(0, filteredIndex),
            response.data,
            ...filteredTags.value.slice(filteredIndex + 1),
          ];
        }

        const paginatedIndex = paginatedTags.value.findIndex((tag) => tag.id === req.id);
        if (paginatedIndex !== -1) {
          paginatedTags.value = [
            ...paginatedTags.value.slice(0, paginatedIndex),
            response.data,
            ...paginatedTags.value.slice(paginatedIndex + 1),
          ];
        }

        if (currentTag.value?.id === req.id) {
          currentTag.value = response.data;
        }

        updateCache();
      }
      return response.data;
    });
  }

  function updateFullTagList() {
    return performAsyncAction(async () => {
      const response = await tagList({ page: 1, page_size: 1000 });
      if (response.data?.tags) {
        tags.value = response.data.tags;
        updateCache();
      }
      return response.data;
    });
  }

  // Tag-Person relationship actions
  function addTagToPerson(req: TagPersonAddRequest) {
    return performAsyncAction(async () => {
      const response = await tag_personAdd(req);
      if (response.data) {
        tagPersonRelations.value.push(response.data);
      }
      return response.data;
    });
  }

  function updateTagPerson(req: TagPersonUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await tag_personUpdate(req);
      if (response.data) {
        const index = tagPersonRelations.value.findIndex((relation) => relation.id === req.id);
        if (index !== -1) {
          tagPersonRelations.value = [
            ...tagPersonRelations.value.slice(0, index),
            response.data,
            ...tagPersonRelations.value.slice(index + 1),
          ];
        }
      }
      return response.data;
    });
  }

  function deleteTagFromPerson(req: TagPersonDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await tag_personDelete(req);
      if (response.data) {
        tagPersonRelations.value = tagPersonRelations.value.filter(
          (relation) => !(relation.tag_id === req.tag_id && relation.person_id === req.person_id),
        );
      }
      return response.data;
    });
  }

  // Tag-Deal relationship actions
  function addTagToDeal(req: TagDealAddRequest) {
    return performAsyncAction(async () => {
      const response = await tag_dealAdd(req);
      if (response.data) {
        tagDealRelations.value.push(response.data);
      }
      return response.data;
    });
  }

  function updateTagDeal(req: TagDealUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await tag_dealUpdate(req);
      if (response.data) {
        const index = tagDealRelations.value.findIndex((relation) => relation.id === req.id);
        if (index !== -1) {
          tagDealRelations.value = [
            ...tagDealRelations.value.slice(0, index),
            response.data,
            ...tagDealRelations.value.slice(index + 1),
          ];
        }
      }
      return response.data;
    });
  }

  function deleteTagFromDeal(req: TagDealDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await tag_dealDelete(req);
      if (response.data) {
        tagDealRelations.value = tagDealRelations.value.filter(
          (relation) => !(relation.tag_id === req.tag_id && relation.deal_id === req.deal_id),
        );
      }
      return response.data;
    });
  }

  return {
    // State
    tags,
    currentTag,
    filteredTags,
    paginatedTags,
    isLoading,
    error,
    // Getters
    getTagCount,
    getTagById,
    getTagByName,
    hasCachedData,
    groupedTags,
    // Actions
    initializeFromCache,
    fetchAllTags,
    addTag,
    deleteTag,
    getTag,
    listTags,
    updateTag,
    updateFullTagList,
    // Tag-Person states and actions
    tagPersonRelations,
    addTagToPerson,
    updateTagPerson,
    deleteTagFromPerson,
    // Tag-Deal states and actions
    tagDealRelations,
    addTagToDeal,
    updateTagDeal,
    deleteTagFromDeal,
  };
});
