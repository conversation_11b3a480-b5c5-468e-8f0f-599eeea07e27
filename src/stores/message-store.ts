import { defineStore } from "pinia";

import { personMessageTemplate } from "@/api/bcare";
import { MessageTemplateRequest, MessageTemplateResponse } from "@/api/bcare-types";

interface State {
  messages: MessageTemplateResponse | null;
  error?: number;
}

export const useMessageStore = defineStore("messageStore", {
  state: (): State => ({ messages: null }),
  actions: {
    async getMessageTemplate(request: MessageTemplateRequest) {
      try {
        const response = await personMessageTemplate(request);
        if (response.code === 0) {
          this.messages = response.data ?? null;
        } else this.error = response.code;
      } catch (error) {
        this.error = error as number;
      }
    },
  },
  getters: {},
});
