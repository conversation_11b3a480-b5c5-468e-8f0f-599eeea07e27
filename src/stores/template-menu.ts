import { defineStore } from "pinia";

import { Icon } from "@/base-components/Lucide/Lucide.vue";

export interface Menu {
  icon: Icon;
  title: string;
  pageName?: string;
  subMenu?: Menu[];
  ignore?: boolean;
}

export interface TemplateMenuState {
  menu: Array<Menu>;
}

export const useTemplateMenuStore = defineStore("templateMenu", {
  state: (): TemplateMenuState => ({
    menu: [
      {
        icon: "Home",
        pageName: "template-menu-dashboard",
        title: "Phòng khám",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-dashboard-updental",
            title: "Updental",
          },
          {
            icon: "Activity",
            pageName: "template-menu-assistant",
            title: "Phòng điều trị",
          },
          {
            icon: "Activity",
            pageName: "template-menu-consulting",
            title: "Phòng tư vấn",
          },
          {
            icon: "Activity",
            pageName: "template-menu-upload",
            title: "Phòng X-Quang",
          },
          {
            icon: "Activity",
            pageName: "template-menu-pipeline",
            title: "Quy trình",
          },
        ],
      },
      {
        icon: "Box",
        pageName: "template-menu-menu-layout",
        title: "Menu Layout",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-dashboard-overview-1",
            title: "Side Menu",
            ignore: true,
          },
          {
            icon: "Activity",
            pageName: "template-menu-dashboard-overview-1",
            title: "Simple Menu",
            ignore: true,
          },
          {
            icon: "Activity",
            pageName: "template-menu-dashboard-overview-1",
            title: "Top Menu",
            ignore: true,
          },
        ],
      },
      {
        icon: "Activity",
        pageName: "template-menu-apps",
        title: "Apps",
        subMenu: [
          {
            icon: "Users",
            pageName: "template-menu-users",
            title: "Users",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-user-list",
                title: "Danh sách",
              },
              {
                icon: "Zap",
                pageName: "template-menu-doctor-schedule",
                title: "Lịch bác sĩ",
              },
              {
                icon: "Zap",
                pageName: "template-menu-users-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-users-layout-2",
                title: "Layout 2",
              },
              {
                icon: "Zap",
                pageName: "template-menu-users-layout-3",
                title: "Layout 3",
              },
            ],
          },
          {
            icon: "Users",
            pageName: "template-menu-customers",
            title: "Khách hàng",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-customer-list",
                title: "Danh sách",
              },
            ],
          },
          {
            icon: "Headphones",
            pageName: "template-menu-customer-care",
            title: "CSKH",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-customer-care",
                title: "Công việc",
              },
            ],
          },
          {
            icon: "File",
            pageName: "template-menu-accounting",
            title: "Kế toán",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-accounting",
                title: "Báo cáo SP",
              },
              {
                icon: "Zap",
                pageName: "template-menu-consumables",
                title: "Vật tư tiêu hao",
              },
            ],
          },
          {
            icon: "Trello",
            pageName: "template-menu-statistics",
            title: "Thống kê",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-supplies",
                title: "Vật tư",
              },
            ],
          },
          {
            icon: "ShoppingBag",
            pageName: "template-menu-products",
            title: "Sản phẩm",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-product-list",
                title: "Danh sách",
              },
            ],
          },
          {
            icon: "Trello",
            pageName: "template-menu-bundle",
            title: "Phân loại",
            subMenu: [
              {
                icon: "Activity",
                pageName: "template-menu-bundle",
                title: "Danh sách nhóm",
              },
              {
                icon: "Activity",
                pageName: "template-menu-bundle-edit",
                title: "Chỉnh sửa nhóm",
              },
              {
                icon: "Activity",
                pageName: "template-menu-field-create",
                title: "Chỉnh sửa field",
              },
            ],
          },
          {
            icon: "User",
            pageName: "template-menu-assistant",
            title: "Phụ tá",
            subMenu: [
              {
                icon: "Activity",
                pageName: "template-menu-assistant",
                title: "Phòng điều trị",
              },
            ],
          },
          {
            icon: "Users",
            pageName: "template-menu-upload",
            title: "Bác sĩ",
            subMenu: [
              {
                icon: "Activity",
                pageName: "template-menu-upload",
                title: "Đăng hình",
              },
            ],
          },
          {
            icon: "ClipboardCheck",
            pageName: "template-menu-task",
            title: "Công việc",
          },
          {
            icon: "Trello",
            pageName: "template-menu-profile",
            title: "Profile",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-profile-overview-1",
                title: "Overview 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-profile-overview-2",
                title: "Overview 2",
              },
              {
                icon: "Zap",
                pageName: "template-menu-profile-overview-3",
                title: "Overview 3",
              },
            ],
          },
          {
            icon: "ShoppingBag",
            pageName: "template-menu-ecommerce",
            title: "E-Commerce",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-categories",
                title: "Categories",
              },
              {
                icon: "Zap",
                pageName: "template-menu-add-product",
                title: "Add Product",
              },
              {
                icon: "Zap",
                pageName: "template-menu-products-list",
                title: "Product List",
              },
              {
                icon: "Zap",
                pageName: "template-menu-product-grid",
                title: "Product Grid",
              },
              {
                icon: "Zap",
                pageName: "template-menu-transaction-list",
                title: "Transaction List",
              },
              {
                icon: "Zap",
                pageName: "template-menu-transaction-detail",
                title: "Transaction Detail",
              },
              {
                icon: "Zap",
                pageName: "template-menu-seller-list",
                title: "Seller List",
              },
              {
                icon: "Zap",
                pageName: "template-menu-seller-detail",
                title: "Seller Detail",
              },
              {
                icon: "Zap",
                pageName: "template-menu-reviews",
                title: "Reviews",
              },
            ],
          },
          {
            icon: "Inbox",
            pageName: "template-menu-inbox",
            title: "Inbox",
          },
          {
            icon: "Folder",
            pageName: "template-menu-file-manager",
            title: "File Manager",
          },
          {
            icon: "CreditCard",
            pageName: "template-menu-point-of-sale",
            title: "Point of Sale",
          },
          {
            icon: "MessageSquare",
            pageName: "template-menu-chat",
            title: "Chat",
          },
          {
            icon: "FileText",
            pageName: "template-menu-post",
            title: "Post",
          },
          {
            icon: "Calendar",
            pageName: "template-menu-calendar",
            title: "Calendar",
          },
          {
            icon: "Edit",
            pageName: "template-menu-crud",
            title: "Crud",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-crud-data-list",
                title: "Data List",
              },
              {
                icon: "Zap",
                pageName: "template-menu-crud-form",
                title: "Form",
              },
            ],
          },
        ],
      },
      {
        icon: "Layout",
        pageName: "template-menu-layout",
        title: "Pages",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-wizards",
            title: "Wizards",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-wizard-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-wizard-layout-2",
                title: "Layout 2",
              },
              {
                icon: "Zap",
                pageName: "template-menu-wizard-layout-3",
                title: "Layout 3",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-blog",
            title: "Blog",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-blog-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-blog-layout-2",
                title: "Layout 2",
              },
              {
                icon: "Zap",
                pageName: "template-menu-blog-layout-3",
                title: "Layout 3",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-pricing",
            title: "Pricing",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-pricing-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-pricing-layout-2",
                title: "Layout 2",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-invoice",
            title: "Invoice",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-invoice-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-invoice-layout-2",
                title: "Layout 2",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-faq",
            title: "FAQ",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-faq-layout-1",
                title: "Layout 1",
              },
              {
                icon: "Zap",
                pageName: "template-menu-faq-layout-2",
                title: "Layout 2",
              },
              {
                icon: "Zap",
                pageName: "template-menu-faq-layout-3",
                title: "Layout 3",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "login",
            title: "Login",
          },
          {
            icon: "Activity",
            pageName: "register",
            title: "Register",
          },
          {
            icon: "Activity",
            pageName: "error-page",
            title: "Error Page",
          },
          {
            icon: "Activity",
            pageName: "template-menu-update-profile",
            title: "Update profile",
          },
          {
            icon: "Activity",
            pageName: "template-menu-change-password",
            title: "Change Password",
          },
        ],
      },
      {
        icon: "Inbox",
        pageName: "template-menu-components",
        title: "Components",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-table",
            title: "Table",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-regular-table",
                title: "Regular Table",
              },
              {
                icon: "Zap",
                pageName: "template-menu-tabulator",
                title: "Tabulator",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-overlay",
            title: "Overlay",
            subMenu: [
              {
                icon: "Zap",
                pageName: "template-menu-modal",
                title: "Modal",
              },
              {
                icon: "Zap",
                pageName: "template-menu-slide-over",
                title: "Slide Over",
              },
              {
                icon: "Zap",
                pageName: "template-menu-notification",
                title: "Notification",
              },
            ],
          },
          {
            icon: "Activity",
            pageName: "template-menu-tab",
            title: "Tab",
          },
          {
            icon: "Activity",
            pageName: "template-menu-accordion",
            title: "Accordion",
          },
          {
            icon: "Activity",
            pageName: "template-menu-button",
            title: "Button",
          },
          {
            icon: "Activity",
            pageName: "template-menu-alert",
            title: "Alert",
          },
          {
            icon: "Activity",
            pageName: "template-menu-progress-bar",
            title: "Progress Bar",
          },
          {
            icon: "Activity",
            pageName: "template-menu-tooltip",
            title: "Tooltip",
          },
          {
            icon: "Activity",
            pageName: "template-menu-dropdown",
            title: "Dropdown",
          },
          {
            icon: "Activity",
            pageName: "template-menu-typography",
            title: "Typography",
          },
          {
            icon: "Activity",
            pageName: "template-menu-icon",
            title: "",
          },
          {
            icon: "Activity",
            pageName: "template-menu-loading-icon",
            title: "Loading ",
          },
        ],
      },
      {
        icon: "Sidebar",
        pageName: "template-menu-forms",
        title: "Forms",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-regular-form",
            title: "Regular Form",
          },
          {
            icon: "Activity",
            pageName: "template-menu-datepicker",
            title: "Datepicker",
          },
          {
            icon: "Activity",
            pageName: "template-menu-tom-select",
            title: "Tom Select",
          },
          {
            icon: "Activity",
            pageName: "template-menu-file-upload",
            title: "File Upload",
          },
          {
            icon: "Activity",
            pageName: "template-menu-wysiwyg-editor",
            title: "Wysiwyg Editor",
          },
          {
            icon: "Activity",
            pageName: "template-menu-validation",
            title: "Validation",
          },
        ],
      },
      {
        icon: "HardDrive",
        pageName: "template-menu-widgets",
        title: "Widgets",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-chart",
            title: "Chart",
          },
          {
            icon: "Activity",
            pageName: "template-menu-slider",
            title: "Slider",
          },
          {
            icon: "Activity",
            pageName: "template-menu-image-zoom",
            title: "Image Zoom",
          },
        ],
      },
      {
        icon: "Settings",
        pageName: "template-menu-settings",
        title: "Thiết lập",
        subMenu: [
          {
            icon: "Activity",
            pageName: "template-menu-message",
            title: "Tin nhắn",
          },
        ],
      },
    ],
  }),
});
