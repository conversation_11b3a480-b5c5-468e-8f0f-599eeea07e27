// stores/fullscreenStore.ts
import { defineStore } from "pinia";
import { ref } from "vue";

export const useFullscreenStore = defineStore("fullscreen", () => {
  const isFullScreen = ref(false);
  const isBrowserFullScreen = ref(false);

  function setFullScreen(value: boolean) {
    isFullScreen.value = value;
  }

  function setBrowserFullScreen(value: boolean) {
    isBrowserFullScreen.value = value;
  }

  function toggleFullScreen() {
    isFullScreen.value = !isFullScreen.value;
  }

  function toggleBrowserFullScreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }

  return {
    isFullScreen,
    isBrowserFullScreen,
    setFullScreen,
    setBrowserFullScreen,
    toggleFullScreen,
    toggleBrowserFullScreen,
  };
});
