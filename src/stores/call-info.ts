import { defineStore } from "pinia";
import { ref } from "vue";

import type { PersonResponse } from "@/api/bcare-types-v2";

interface CallInfo {
  personId: number;
  name: string;
  phone: string;
  address: string;
}

export const useCallInfoStore = defineStore("callInfo", () => {
  const callInfo = ref<CallInfo | null>(null);
  const isVisible = ref(false);

  const showWithPerson = (person: PersonResponse) => {
    callInfo.value = {
      personId: person.id ?? 0,
      name: person.full_name ?? "",
      phone: person.phone ?? "",
      address: person.address_number ?? "",
    };
    isVisible.value = true;
  };

  const clear = () => {
    callInfo.value = null;
    isVisible.value = false;
  };

  return {
    callInfo,
    isVisible,
    showWithPerson,
    clear,
  };
});
