import { defineStore } from "pinia";
import { shallowRef } from "vue";

import { addressGet } from "@/api/bcare";
import { LocalDistrict, LocalWard, LocationResponse } from "@/api/bcare-types";

export const useLocationStore = defineStore("location", () => {
  const location = shallowRef<any>({
    provinces: [],
    districts: [],
    wards: [],
  });

  function setLocation(newLocation: LocationResponse) {
    location.value = newLocation;
    const locationString = JSON.stringify(newLocation);
    localStorage.setItem("location", locationString);
  }

  async function fetchLocation() {
    try {
      const response = await addressGet();

      if (response.code === 0 && response.data != null) {
        setLocation(response.data);
      }
    } catch (error) {
      //TODO
      throw error;
    }
  }

  const getLocation = async () => {
    if (location.value.provinces.length === 0) {
      // State chưa có location data
      // Load location từ localStorage
      const locationData = JSON.parse(localStorage.getItem("location") || "null");

      if (locationData) {
        // có dữ liệu trong storage, set vào state
        location.value = locationData;
      } else {
        // không có dữ liệu, gọi API
        await fetchLocation();
      }
    }

    return {
      provinces: location.value.provinces,
      districts: location.value.districts,
      wards: location.value.wards,
    };
  };
  const getProvinces = async () => {
    return (await getLocation()).provinces;
  };
  const getDistrictAll = async () => {
    return (await getLocation()).districts;
  };
  const getWardAll = async () => {
    return (await getLocation()).wards;
  };
  const getDistricts = async (provinceId: number) => {
    // Lấy tất cả districts hiện tại
    const { districts } = await getLocation();
    // Lọc ra các district theo provinceId
    return districts.filter((d: LocalDistrict) => d.province_id == provinceId);
  };
  const getWards = async (districtId: number) => {
    // Lấy tất cả wards hiện tại
    const { wards } = await getLocation();

    // Lọc ra các wards theo district
    return wards.filter((d: LocalWard) => d.district_id == districtId);
  };
  return {
    location,
    getProvinces,
    getDistricts,
    getWards,
    getLocation,
    getDistrictAll,
    getWardAll,
  };
});
