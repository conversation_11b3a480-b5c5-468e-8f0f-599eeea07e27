import { defineStore } from "pinia";
import { computed, reactive } from "vue";

import {
  Setting,
  SettingAddRequest,
  SettingDeleteRequest,
  SettingListRequest,
  SettingSyncRequest,
  SettingUpdateRequest,
} from "@/api/bcare-types-v2";
import { settingAdd, settingDelete, settingList, settingSync, settingUpdate } from "@/api/bcare-v2";

export const useConfigurationsStore = defineStore("ConfigurationsStore", () => {
  const state = reactive({
    configurations: [] as Setting[],
    categoryMap: {} as Record<string, Setting[]>,
    error: 0,
    loading: false,
    isInitialized: false,
  });

  // Track the promise for fetching settings to prevent duplicate requests
  let settingsPromise: Promise<void> | null = null;

  const getSettingByKey = computed(() => (category: string, name: string) => {
    return state.categoryMap[category]?.find((setting) => setting.name === name);
  });

  const getSettings = async (request: SettingListRequest) => {
    try {
      state.loading = true;
      const response = await settingList(request);

      if (response.code === 0) {
        const settings = response.data?.settings || [];
        state.configurations = settings;
        state.categoryMap = settings.reduce(
          (result, setting) => {
            const { category } = setting;
            if (category) {
              if (!result[category]) {
                result[category] = [];
              }
              result[category].push(setting);
            }
            return result;
          },
          {} as Record<string, Setting[]>,
        );
        state.isInitialized = true;
      } else {
        state.error = response.code;
      }
    } catch (error) {
      state.error = (error as any).code || -1;
    } finally {
      state.loading = false;
    }
  };

  const deleteSetting = async (request: SettingDeleteRequest) => {
    try {
      state.loading = true;
      const response = await settingDelete(request);
      if (response.code === 0) {
        await init({ category: "", name: "" }); // Refresh settings after deletion
      }
      return response.code === 0;
    } catch (error) {
      state.error = (error as any).code || -1;
      return false;
    } finally {
      state.loading = false;
    }
  };

  const updateSetting = async (request: SettingUpdateRequest) => {
    try {
      state.loading = true;
      const response = await settingUpdate(request);
      if (response.code === 0) {
        await init({ category: "", name: "" }); // Refresh settings after update
      }
      return response.code === 0;
    } catch (error) {
      state.error = (error as any).code || -1;
      return false;
    } finally {
      state.loading = false;
    }
  };

  const addSetting = async (request: SettingAddRequest) => {
    try {
      state.loading = true;
      const response = await settingAdd(request);
      if (response.code === 0) {
        await init({ category: "", name: "" }); // Refresh settings after adding
      }
      return response.code === 0;
    } catch (error) {
      state.error = (error as any).code || -1;
      return false;
    } finally {
      state.loading = false;
    }
  };

  const syncSetting = async (category: string, name: string, value: any, description?: string) => {
    try {
      state.loading = true;
      const request: SettingSyncRequest = {
        category,
        name,
        value: value,
        description,
      };

      const response = await settingSync(request);

      if (response.code === 0 && response.data) {
        const newSetting = response.data;

        // Update configurations array
        const existingIndex = state.configurations.findIndex(
          (s) => s.category === category && s.name === name,
        );

        if (existingIndex !== -1) {
          state.configurations[existingIndex] = newSetting;
        } else {
          state.configurations.push(newSetting);
        }

        // Update categoryMap
        if (!state.categoryMap[category]) {
          state.categoryMap[category] = [];
        }

        const categoryIndex = state.categoryMap[category].findIndex((s) => s.name === name);

        if (categoryIndex !== -1) {
          state.categoryMap[category][categoryIndex] = newSetting;
        } else {
          state.categoryMap[category].push(newSetting);
        }
      }

      // Refresh settings after sync
      if (response.code === 0) {
        await init({ category: "", name: "" });
      }

      return response.code === 0;
    } catch (error) {
      state.error = (error as any).code || -1;
      return false;
    } finally {
      state.loading = false;
    }
  };

  // Initialization method with singleton pattern
  const init = (request: SettingListRequest): Promise<void> => {
    // 1. If already initialized, return a resolved promise immediately
    if (state.isInitialized) {
      return Promise.resolve();
    }

    // 2. If a fetch promise already exists (another init call is in progress), return it
    if (settingsPromise) {
      return settingsPromise;
    }

    // 3. Otherwise, create and assign the promise *before* starting the async operation
    settingsPromise = (async () => {
      try {
        // Call the actual fetch function
        await getSettings(request);
      } finally {
        // Reset the promise only when *this specific* init call completes
        // It's okay to reset even if initialized, allows forced refresh if needed later
        // although current logic uses the flag primarily.
        settingsPromise = null;
      }
    })();

    // Return the newly created promise
    return settingsPromise;
  };

  return {
    state,
    getSettings,
    getSettingByKey,
    syncSetting,
    deleteSetting,
    updateSetting,
    addSetting,
    init,
  };
});
