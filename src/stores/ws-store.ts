import { defineStore } from "pinia";
import { useConfirm } from "primevue/useconfirm";
import { computed, ref } from "vue";

import { config } from "@/config";
import Realtime from "@/realtime";
import { WsMessage } from "@/realtime/websocket";

interface EventCallback {
  event: string;
  callback: (msg: WsMessage) => void;
}

export const useWsStore = defineStore("websocket", () => {
  const conn = ref<Realtime | null>(null);
  const events = ref<EventCallback[]>([]);
  const isReady = ref(false);
  const confirm = useConfirm();

  function connect(onMessageCallback?: (msg: WsMessage) => void, onData?: (data: string) => void) {
    if (!conn.value) {
      conn.value = new Realtime(config.wsUrl, onMessageCallback, onData);
      conn.value.getWs().addEvent("ready", () => {
        isReady.value = true;
        handleReady();
      });
      // conn.value.getWs().addEvent("close", handleDisconnect);
    }
  }

  function disconnect() {
    conn.value?.getWs().disconnect();
    conn.value = null;
    events.value = [];
    isReady.value = false;
  }

  function addEvent(event: string, callback: (msg: WsMessage) => void) {
    events.value.push({ event, callback });
    if (isReady.value && conn.value) {
      conn.value.on(event, callback);
    }
  }

  function removeEvent(event: string, callback: (msg: WsMessage) => void) {
    const index = events.value.findIndex((e) => e.event === event && e.callback === callback);
    if (index !== -1) {
      events.value.splice(index, 1);
      conn.value?.off(event, callback);
    }
  }

  function handleReady() {
    if (!conn.value) return;
    events.value.forEach(({ event, callback }) => {
      conn.value!.on(event, callback);
    });
  }

  function handleDisconnect() {
    confirm.require({
      message: "Vui lòng tải lại trang để kết nối lại.",
      header: "Mất kết nối",
      group: "global",
      icon: "pi pi-exclamation-triangle",
      blockScroll: true,
      modal: true,
      accept: () => {
        window.location.reload();
      },
      acceptLabel: "Tải lại trang",
    });
  }

  const realtime = computed(() => {
    if (!conn.value) {
      throw new Error("WebSocket connection not initialized");
    }
    return conn.value;
  });

  return {
    realtime,
    connect,
    disconnect,
    addEvent,
    removeEvent,
    isReady,
  };
});

export interface WsStore extends ReturnType<typeof useWsStore> {}
