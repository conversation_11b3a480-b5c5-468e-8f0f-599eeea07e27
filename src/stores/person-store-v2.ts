import { defineStore } from "pinia";
import { computed, ref, shallowRef } from "vue";

import type {
  BulkUpdateFormSubmissionResult,
  ConvertToPersonRequest,
  FormSubmissionDeleteRequest,
  FormSubmissionDeleteResponse,
  PersonAddRequest,
  PersonAssignmentAddRequest,
  PersonAssignmentDeleteRequest,
  PersonAssignmentUpdateRequest,
  PersonDeleteRequest,
  PersonGetRequest,
  PersonListRequest,
  PersonResponse,
  PersonUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  form_submissionConvertToPerson,
  form_submissionDeleteFormSubmission,
  person_assignmentAdd,
  person_assignmentDelete,
  person_assignmentUpdate,
  personAdd,
  personDelete,
  personGet,
  personIsPersonIn,
  personList,
  personUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const usePersonStore = defineStore("person", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const persons = shallowRef<PersonResponse[]>([]);
  const currentPerson = shallowRef<PersonResponse | null>(null);
  const totalRecords = ref<number | null>(null);

  // Getters
  const getPersonCount = computed(() => persons.value.length);
  const getPersonById = computed(
    () => (id: number) => persons.value.find((person) => person.id === id),
  );

  // Actions
  function addPerson(req: PersonAddRequest) {
    return performAsyncAction(async () => {
      const response = await personAdd(req);
      if (response.data) {
        persons.value.push(response.data);
      }
      return response.data;
    });
  }

  function deletePerson(req: PersonDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await personDelete(req);
      if (response.data) {
        persons.value = persons.value.filter((person) => person.id !== req.id);
      }
      return response.data;
    });
  }

  function getPerson(req: PersonGetRequest, isUpdateCurrentPerson = true) {
    return performAsyncAction(async () => {
      const response = await personGet(req);
      if (isUpdateCurrentPerson) {
        currentPerson.value = response.data ?? null;
      }
      return response.data;
    });
  }

  function listPersons(req: PersonListRequest) {
    return performAsyncAction(async () => {
      const response = await personList(req);
      persons.value = response.data?.persons ?? [];
      totalRecords.value = response.data?.total ?? null;
      return response.data;
    });
  }

  function updatePerson(req: PersonUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await personUpdate(req);
      if (response.data) {
        const index = persons.value.findIndex((person) => person.id === req.id);
        if (index !== -1) {
          persons.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  function addPersonAssignment(req: PersonAssignmentAddRequest) {
    return performAsyncAction(async () => {
      const response = await person_assignmentAdd(req);
      return response.code;
    });
  }

  function updatePersonAssignment(req: PersonAssignmentUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await person_assignmentUpdate(req);
      return response.code;
    });
  }

  function deletePersonAssignment(req: PersonAssignmentDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await person_assignmentDelete(req);
      return response.code;
    });
  }

  // Functions for form submission operations
  function convertFormSubmissionsToPerson(formSubmissionIds: number[]) {
    return performAsyncAction(async () => {
      const request: ConvertToPersonRequest = {
        form_submission_ids: formSubmissionIds,
      };
      const response = await form_submissionConvertToPerson(request);
      return response.data;
    });
  }

  function deleteFormSubmissions(formSubmissionIds: number[]) {
    return performAsyncAction(async () => {
      const request: FormSubmissionDeleteRequest = {
        ids: formSubmissionIds,
      };
      const response = await form_submissionDeleteFormSubmission(request);
      return response.data;
    });
  }

  function checkPersonIn(req: PersonGetRequest) {
    return performAsyncAction(async () => {
      const response = await personIsPersonIn(req);
      return response.data;
    });
  }

  return {
    // State
    persons,
    currentPerson,
    isLoading,
    error,
    totalRecords,
    // Getters
    getPersonCount,
    getPersonById,
    // Actions
    addPerson,
    deletePerson,
    getPerson,
    listPersons,
    updatePerson,
    addPersonAssignment,
    updatePersonAssignment,
    deletePersonAssignment,
    // New actions
    convertFormSubmissionsToPerson,
    deleteFormSubmissions,
    checkPersonIn,
  };
});
