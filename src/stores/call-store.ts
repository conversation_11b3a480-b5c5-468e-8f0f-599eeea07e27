import JsSI<PERSON> from "jssip";
import { RTCSession, SessionDirection } from "jssip/lib/RTCSession";
import { CallOptions } from "jssip/lib/UA";
import { defineStore } from "pinia";
import { toRaw } from "vue";

import { useCallBroadcast } from "@/composables/useCallBroadcast";
import { addLeadingZero } from "@/utils/helper";

export type RTCSessionApp = RTCSession & {
  roomId?: number;
  localMuted?: boolean;
  audioTag?: HTMLAudioElement;
  _automaticHold?: boolean;
  call_id?: string;
};

export interface PersonInfoCallType {
  name: string;
  address: string;
  phone: string;
  personId: number;
}

interface TimeType {
  callId: string;
  minutes: number;
  seconds: number;
  formatted: string;
}

interface RoomType {
  roomId: number;
  incomingInProgress: boolean;
}

interface CallStatusType {
  isMoving: false;
  isTransferring: false;
  isMerging: false;
}

interface OptionInitJSSIPType {
  configuration: {
    session_timers?: boolean;
    uri: string;
    password: string;
  };
  socketInterfaces: string[];
}

export const CONSTRAINTS = {
  CALL_DIRECTION_OUTGOING: "outgoing",
  CALL_DIRECTION_INCOMING: "incoming",
  CALL_STATUS_UNANSWERED: 0,
  CALL_STATUS_TERMINATED: 8,
};

const STORAGE_KEYS = {
  SELECTED_INPUT_DEVICE: "selectedInputDevice",
  SELECTED_OUTPUT_DEVICE: "selectedOutputDevice",
};

const AUDIO_INCOMING_RINGTONE = "/audio/incoming-ringtone.mp3";

type IntervalType = ReturnType<typeof setInterval>;

function processAudioVolume(stream: MediaStream, volume: number) {
  const audioContext = new AudioContext();
  const audioSource = audioContext.createMediaStreamSource(stream);
  const audioDestination = audioContext.createMediaStreamDestination();
  const gainNode = audioContext.createGain();
  audioSource.connect(gainNode);
  gainNode.connect(audioDestination);
  gainNode.gain.value = volume;

  return audioDestination.stream;
}

export const setupTime = (time: TimeType) => {
  let minutes = time.minutes || 0;
  let seconds = time.seconds || 0;

  seconds++;
  if (seconds === 60) {
    seconds = 0;
    minutes++;
  }
  const formatted = `${addLeadingZero(minutes)}:${addLeadingZero(seconds)}`;
  return {
    seconds,
    minutes,
    formatted,
  };
};

function getNewRoomId(activeRooms: Record<string, RoomType>) {
  const roomIdList = Object.keys(activeRooms);

  if (roomIdList.length === 0) {
    return 1;
  }

  return parseInt(roomIdList.sort()[roomIdList.length - 1]) + 1;
}

function syncStream(
  event: { stream: MediaStream },
  call: RTCSessionApp,
  outputDevice: string,
  volume: number,
) {
  const audio = document.createElement("audio") as HTMLAudioElement & {
    setSinkId: (deviceId: string) => Promise<void>;
  };

  audio.id = call.id;
  audio.className = "audioTag";
  audio.srcObject = event.stream;
  audio.setSinkId(outputDevice);
  audio.volume = volume;
  audio.play();
  call.audioTag = audio;
}

let streamLocal: MediaStream;

interface State {
  infoStartCall?: PersonInfoCallType;
  uaInit: boolean;
  speakerVolume: number; // from 0 to 1
  isMuted: boolean;
  time: Record<string, TimeType>;
  timeIntervals: Record<string, IntervalType>;
  activeCalls: Record<string, RTCSessionApp>;
  activeRooms: Record<string, RoomType>;
  currentActiveRoomId?: number;
  microphoneInputLevel: number; // from 0 to 2
  originalStream?: MediaStream;
  muteWhenJoin: boolean;
  listeners: Record<string, Array<(session: RTCSessionApp) => void>>;
  callAddingInProgress?: string;
  callStatus: Record<string, CallStatusType>;
  selectedMediaDevices: {
    input: string;
    output: string;
  };
  incomingRingtone?: HTMLAudioElement;
  hasUserInteracted: boolean;
  lineId?: string;
  activeIncomingCalls: Set<string>; // Track active incoming call IDs
  tabId: string; // Thêm ID để identify tab
}

let UA: JsSIP.UA;

export const useCallStore = defineStore("call", {
  state: (): State => ({
    infoStartCall: { address: "", name: "", personId: 0, phone: "" },
    uaInit: false,
    isMuted: false,
    microphoneInputLevel: 2,
    speakerVolume: 1,
    time: {},
    muteWhenJoin: false,
    activeCalls: {},
    timeIntervals: {},
    activeRooms: {},
    listeners: {},
    callStatus: {},
    selectedMediaDevices: {
      input: localStorage.getItem(STORAGE_KEYS.SELECTED_INPUT_DEVICE) || "default",
      output: localStorage.getItem(STORAGE_KEYS.SELECTED_OUTPUT_DEVICE) || "default",
    },
    incomingRingtone: undefined,
    hasUserInteracted: false,
    lineId: undefined,
    activeIncomingCalls: new Set(), // Initialize activeIncomingCalls
    tabId: crypto.randomUUID(), // Thêm ID để identify tab
  }),
  actions: {
    async _addCall(session: RTCSessionApp) {
      if (this.activeCalls[session.id]) {
        console.warn(`Call with id ${session.id} already exists`);
        return;
      }

      const roomId = getNewRoomId(this.getActiveRooms);
      const newRoomInfo = {
        started: new Date(),
        incomingInProgress: false,
        roomId,
      };

      if (session.direction === CONSTRAINTS.CALL_DIRECTION_INCOMING) {
        newRoomInfo.incomingInProgress = true;
        this.subscribe({
          type: "confirmed",
          callback: (callSession) => {
            if (session.id === callSession.id) {
              this._stopIncomingRingtone();
              this._update_room({
                incomingInProgress: false,
                roomId,
              });
              this._startCallTimer(session.id);
            }
          },
        });

        this.subscribe({
          type: "failed",
          callback: (call) => {
            if (session.id === call.id) {
              this._stopIncomingRingtone();
              this._update_room({
                incomingInProgress: false,
                roomId,
              });
            }
          },
        });
      }

      session.roomId = roomId;
      session.localMuted = false;
      this._mutationAddCall(session);
      this._addCallStatus(session.id);
      this._addRoom(newRoomInfo);
      this._cleanupListeners();
    },
    _update_room(value: RoomType) {
      const room = this.activeRooms[value.roomId];
      this.activeRooms = {
        ...this.activeRooms,
        [value.roomId]: {
          ...room,
          ...value,
        },
      };
    },
    _removeCall(id: string) {
      const stateActiveCallsCopy = { ...this.activeCalls };
      delete stateActiveCallsCopy[id];
      this.activeCalls = stateActiveCallsCopy;
    },
    _mutationAddCall(session: RTCSessionApp) {
      this.activeCalls = {
        ...this.activeCalls,
        [session.id]: session,
      };
    },
    _mutationUpdateCall(session: RTCSessionApp) {
      this.activeCalls = {
        ...this.activeCalls,
        [session.id]: session,
      };
    },
    _mutationRemoveCall(callId: string) {
      const stateActiveCallsCopy = { ...this.activeCalls };
      delete stateActiveCallsCopy[callId];
      this.activeCalls = stateActiveCallsCopy;
    },
    _activeCallListRemove(id: string) {
      const callRoomIdToConfigure = this.activeCalls[id]?.roomId;
      this._mutationRemoveCall(id);
      this._roomReconfigure(callRoomIdToConfigure);
    },
    _setCallTime(timeData: TimeType) {
      this.time = {
        ...this.time,
        [timeData.callId]: timeData,
      };
    },
    _removeCallTime(callId: string) {
      const callTimeCopy = { ...this.time };
      delete callTimeCopy[callId];

      this.time = {
        ...callTimeCopy,
      };
    },
    _setTimeInterval(callId: string, interval: IntervalType) {
      this.timeIntervals = {
        ...this.timeIntervals,
        [callId]: interval,
      };
    },
    _removeTimeInterval(callId: string) {
      const timeIntervalsCopy = { ...this.timeIntervals };
      clearInterval(timeIntervalsCopy[callId]);
      delete timeIntervalsCopy[callId];

      this.timeIntervals = {
        ...timeIntervalsCopy,
      };
    },
    _startCallTimer(callId: string) {
      this._removeTimeInterval(callId);
      const timeData = {
        callId,
        minutes: 0,
        seconds: 0,
        formatted: "",
      };
      this._setCallTime(timeData);

      const interval = setInterval(() => {
        const callTime = { ...this.time[callId] };
        const updatedTime = setupTime(callTime);
        this._setCallTime({ callId, ...updatedTime });
      }, 1000);

      this._setTimeInterval(callId, interval);
    },
    _stopCallTimer(callId: string) {
      this._removeCallTime(callId);
      this._removeTimeInterval(callId);
      this._cleanupListeners();
    },
    _addCallStatus(callId: string) {
      this.callStatus = {
        ...this.callStatus,
        [callId]: {
          isMoving: false,
          isTransferring: false,
          isMerging: false,
        },
      };
    },
    _removeCallStatus(callId: string) {
      const callStatusCopy = { ...this.callStatus };
      delete callStatusCopy[callId];

      this.callStatus = {
        ...callStatusCopy,
      };
    },
    _addRoom(value: RoomType) {
      this.activeRooms = {
        ...this.activeRooms,
        [value.roomId]: value,
      };
    },
    _removeRoom(roomId: number) {
      const activeRoomsCopy = { ...this.activeRooms };
      delete activeRoomsCopy[roomId];

      this.activeRooms = {
        ...activeRoomsCopy,
      };
    },
    _deleteRoomIfEmpty(roomId: number) {
      if (Object.values(this.activeCalls).filter((call) => call.roomId === roomId).length === 0) {
        this._removeRoom(roomId);

        if (this.currentActiveRoomId === roomId) {
          this.currentActiveRoomId = roomId;
        }
      }
    },
    _muteReconfigure(call: RTCSessionApp) {
      if (this.isMuted) {
        call.mute({ audio: true });
      } else {
        call.unmute({ audio: true });
      }
    },
    async _doConference(sessions: RTCSessionApp[]) {
      sessions.forEach((call) => {
        if (call.isOnHold()) {
          this.doCallHold({ callId: call.id, toHold: false });
        }
      });

      // Take all received tracks from the sessions you want to merge
      const receivedTracks: MediaStreamTrack[] = [];

      sessions.forEach((session) => {
        if (session !== null && session !== undefined) {
          session.connection.getReceivers().forEach((receiver) => {
            receivedTracks.push(receiver.track);
          });
        }
      });

      // Use the Web Audio API to mix the received tracks
      const audioContext = new AudioContext();
      const allReceivedMediaStreams = new MediaStream();

      // For each call we will build dedicated mix for all other calls
      await sessions.forEach(async (session) => {
        if (session === null || session === undefined) {
          return;
        }

        const mixedOutput = audioContext.createMediaStreamDestination();

        session.connection.getReceivers().forEach((receiver) => {
          receivedTracks.forEach((track) => {
            allReceivedMediaStreams.addTrack(receiver.track);

            if (receiver.track.id !== track.id) {
              const sourceStream = audioContext.createMediaStreamSource(new MediaStream([track]));

              sourceStream.connect(mixedOutput);
            }
          });
        });

        if (sessions[0].roomId === this.currentActiveRoomId) {
          // Mixing your voice with all the received audio
          const stream = await navigator.mediaDevices.getUserMedia(this.getUserMediaConstraints);
          streamLocal = stream;
          const processedStream = processAudioVolume(stream, this.microphoneInputLevel);
          processedStream.getTracks().forEach((track) => (track.enabled = !this.isMuted));
          this.originalStream = processedStream;
          const sourceStream = audioContext.createMediaStreamSource(processedStream);

          // stream.getTracks().forEach(track => track.enabled = !getters.isMuted) // TODO: Fix this

          sourceStream.connect(mixedOutput);
        }

        if (session.connection.getSenders()[0]) {
          //mixedOutput.stream.getTracks().forEach(track => track.enabled = !getters.isMuted) // Uncomment to mute all callers on mute
          await session.connection.getSenders()[0].replaceTrack(mixedOutput.stream.getTracks()[0]);
          this._muteReconfigure(session);
        }
      });
    },
    cancelAllOutgoingUnanswered() {
      Object.values(this.activeCalls)
        .filter((call) => {
          return (
            call.direction === CONSTRAINTS.CALL_DIRECTION_OUTGOING &&
            call.status === CONSTRAINTS.CALL_STATUS_UNANSWERED
          );
        })
        .forEach((call) => this.callTerminate(call.id));
    },
    doCallHold({
      callId,
      toHold,
      automatic,
    }: {
      callId: string;
      toHold: boolean;
      automatic?: boolean;
    }) {
      const call = this.activeCalls[callId];
      call._automaticHold = automatic || false;

      if (toHold) {
        call.hold();
      } else {
        call.unhold();
      }

      this._mutationUpdateCall(call);
    },

    _triggerListener({
      listenerType,
      session,
    }: {
      listenerType: "new_call" | "ended" | "progress" | "failed" | "confirmed";
      session: RTCSessionApp;
    }) {
      // Tránh lặp qua mảng rỗng
      const listeners = this.listeners[listenerType];
      if (!listeners?.length) return;
      // Sử dụng for...of thay vì forEach để có thể break khi cần
      for (const listener of listeners) {
        try {
          listener(session);
        } catch (error) {
          console.error(`Error in ${listenerType} listener:`, error);
        }
      }
    },

    subscribe({
      type,
      callback,
    }: {
      type: "new_call" | "ended" | "progress" | "failed" | "confirmed";
      callback: (session: RTCSessionApp) => void;
    }): () => void {
      if (!this.listeners[type]) {
        this.listeners[type] = [];
      }
      this.listeners[type].push(callback);

      return () => {
        const index = this.listeners[type].indexOf(callback);
        if (index > -1) {
          this.listeners[type].splice(index, 1);
        }
      };
    },

    async setCurrentActiveRoom(roomId?: number) {
      const oldRoomId = this.currentActiveRoomId;
      if (roomId === oldRoomId) {
        return;
      }
      this.currentActiveRoomId = roomId;
      this._roomReconfigure(oldRoomId);
      this._roomReconfigure(roomId);
    },

    async _roomReconfigure(roomId?: number) {
      if (!roomId) {
        return;
      }
      const callsInRoom = Object.values(this.activeCalls).filter((call) => call.roomId === roomId);
      // Lets take care on the audio output first and check if passed room is our selected room
      if (this.currentActiveRoomId === roomId) {
        callsInRoom.forEach((call) => {
          if (call.audioTag) {
            this._muteReconfigure(call);
            call.audioTag.muted = false;
            this._mutationUpdateCall(call);
          }
        });
      } else {
        callsInRoom.forEach((call) => {
          if (call.audioTag) {
            call.audioTag.muted = true;
            this._mutationUpdateCall(call);
          }
        });
      }
      // Now lets configure the sound we are sending for each active call on this room
      if (callsInRoom.length === 0) {
        this._deleteRoomIfEmpty(roomId);
      } else if (callsInRoom.length === 1 && this.currentActiveRoomId !== roomId) {
        if (!callsInRoom[0].isOnHold()) {
          this.doCallHold({ callId: callsInRoom[0].id, toHold: true, automatic: true });
        }
      } else if (callsInRoom.length === 1 && this.currentActiveRoomId === roomId) {
        if (callsInRoom[0].isOnHold() && callsInRoom[0]._automaticHold) {
          this.doCallHold({ callId: callsInRoom[0].id, toHold: false });
        }

        let stream: MediaStream | undefined;

        try {
          stream = await navigator.mediaDevices.getUserMedia(this.getUserMediaConstraints);
          streamLocal = stream;
        } catch (err) {
          console.error(err);
        }
        if (callsInRoom[0].connection && callsInRoom[0].connection.getSenders()[0] && stream) {
          const processedStream = processAudioVolume(stream, this.microphoneInputLevel);
          processedStream.getTracks().forEach((track) => (track.enabled = !this.isMuted));
          this.originalStream = processedStream;
          await callsInRoom[0].connection
            .getSenders()[0]
            .replaceTrack(processedStream.getTracks()[0]);
          this._muteReconfigure(callsInRoom[0]);
        }
      } else if (callsInRoom.length > 1) {
        this._doConference(callsInRoom);
      }
    },
    async _triggerAddStream({
      event,
      call,
    }: {
      event: { stream: MediaStream };
      call: RTCSessionApp;
    }) {
      this.isMuted = this.muteWhenJoin;

      const stream = await navigator.mediaDevices.getUserMedia(this.getUserMediaConstraints);
      streamLocal = stream;
      const processedStream = processAudioVolume(stream, this.microphoneInputLevel);
      const muteMicro = this.isMuted || this.muteWhenJoin;

      processedStream.getTracks().forEach((track) => (track.enabled = !muteMicro));
      this.originalStream = processedStream;
      await call.connection.getSenders()[0].replaceTrack(processedStream.getTracks()[0]);

      syncStream(event, call, this.selectedMediaDevices.output, this.speakerVolume);
      this._mutationUpdateCall(call);
    },

    callTerminate(callId: string) {
      const call = toRaw(this.activeCalls[callId]);

      if (!call) return;

      try {
        if (call.status !== CONSTRAINTS.CALL_STATUS_TERMINATED) {
          if (call.isEstablished()) {
            call.terminate();
          } else if (
            call.direction === CONSTRAINTS.CALL_DIRECTION_OUTGOING &&
            call.status === CONSTRAINTS.CALL_STATUS_UNANSWERED
          ) {
            call.terminate({
              status_code: 487,
              reason_phrase: "Call Canceled",
            });
          } else {
            call.terminate({
              status_code: 487,
              reason_phrase: "Call Canceled",
            });
          }
        }
        this._stopAllMediaTracks(call);
        this._removeCall(callId);
        this._cleanupListeners();
      } catch (error) {
        console.error(`Error terminating call ${callId}:`, error);
        this._stopAllMediaTracks(call);
        this._removeCall(callId);
        this._cleanupListeners();
      }
    },

    doMute(muted: boolean) {
      const activeRoomId = this.currentActiveRoomId;
      this.isMuted = muted;
      this._roomReconfigure(activeRoomId);
    },

    muteCaller({ callId, value }: { callId: string; value: boolean }) {
      const call = this.activeCalls[callId];

      if (call && call.connection.getReceivers().length) {
        call.localMuted = value;
        call.connection.getReceivers().forEach((receiver) => {
          receiver.track.enabled = !value;
        });
        this._mutationUpdateCall(call);
        this._roomReconfigure(call.roomId);
      }
    },

    _stopAllMediaTracks(call: RTCSessionApp) {
      // Dừng tất cả các tracks gửi đi
      call.connection?.getSenders()?.forEach((sender) => {
        if (sender.track) {
          sender.track.stop();
        }
      });

      // Dừng tất cả các tracks nhận được
      call.connection?.getReceivers()?.forEach((receiver) => {
        if (receiver.track) {
          receiver.track.stop();
        }
      });

      // Dừng local streams
      if (this.originalStream) {
        this.originalStream.getTracks().forEach((track) => {
          track.stop();
        });
        this.originalStream = undefined;
      }

      // Dừng streamLocal
      if (streamLocal) {
        streamLocal.getTracks().forEach((track) => {
          track.stop();
        });
      }

      // Dừng và xóa audio element nếu có
      if (call.audioTag) {
        call.audioTag.pause();
        call.audioTag.srcObject = null;
        call.audioTag.remove();
      }

      // Force release microphone
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          stream.getTracks().forEach((track) => track.stop());
        })
        .catch(console.error);
    },

    callAnswer({ callId, options = {} }: { callId: string; options?: CallOptions }) {
      const call = this.activeCalls[callId];

      const optionsDefault = {
        mediaConstraints: { audio: true, video: false },
        sessionTimersExpires: 120,
      };

      this.cancelAllOutgoingUnanswered();
      call.answer(Object.assign(optionsDefault, options));
      this._mutationUpdateCall(call);
      this.currentActiveRoomId = call.roomId;

      call.connection.addEventListener("addstream", async (event) => {
        this._triggerAddStream({ event: event as unknown as { stream: MediaStream }, call });
      });
      this._startCallTimer(callId);
    },

    doCall({
      target,
      options = {},
      // addToCurrentRoom = false,
    }: {
      target: string;
      addToCurrentRoom?: boolean;
      options?: CallOptions;
    }) {
      // const activeRoomId = this.currentActiveRoomId;

      const optionsDefault = {
        mediaConstraints: { audio: true, video: false },
        sessionTimersExpires: 120,
      };

      if (!this.uaInit) {
        return console.error("Run init action first");
      }

      if (target.toString().length === 0) {
        return console.error("Target must be passed");
      }

      const call = UA.call(target, Object.assign(optionsDefault, options));
      this.callAddingInProgress = call.id;

      call.connection.addEventListener("addstream", async (event) => {
        this._triggerAddStream({ event: event as unknown as { stream: MediaStream }, call });
      });
    },

    startCall(data: PersonInfoCallType) {
      this.infoStartCall = data;
    },

    clearInfoStartCall() {
      this.infoStartCall = undefined;
    },

    _setupSessionHandlers(session: RTCSessionApp, request: any) {
      // Return early if this session already exists or is terminated
      if (this.activeCalls[session.id] || session.status === CONSTRAINTS.CALL_STATUS_TERMINATED) {
        return;
      }

      // Check for existing active confirmed calls when receiving a new incoming call
      if (session.direction === CONSTRAINTS.CALL_DIRECTION_INCOMING) {
        const hasActiveOrPendingCall = Object.values(this.activeCalls).some((call) => {
          console.log("call.status", call.status);
          console.log("call.direction", call.direction);
          console.log("call.isEstablished()", call.isEstablished());
          return (
            (call.isEstablished() && call.status !== CONSTRAINTS.CALL_STATUS_TERMINATED) ||
            (call.direction === CONSTRAINTS.CALL_DIRECTION_OUTGOING && !call.isEstablished())
          );
        });
        console.log("hasActiveOrPendingCall", hasActiveOrPendingCall);
        if (hasActiveOrPendingCall) {
          console.log("Has active or pending call, rejecting incoming call");
          // session.terminate({
          //   status_code: 486,
          //   reason_phrase: "Busy Here",
          // });
          return;
        }
      }

      session.call_id =
        session.direction === CONSTRAINTS.CALL_DIRECTION_INCOMING && request.hasHeader("X-IVR-EXT")
          ? request.getHeader("X-IVR-EXT")
          : request.call_id;

      session.on("ended", () => {
        this._stopIncomingRingtone();
        this._triggerListener({ listenerType: "ended", session });
        this._activeCallListRemove(session.id);
        this._stopCallTimer(session.id);
        this._removeCallStatus(session.id);
        this._stopAllMediaTracks(session);
        navigator.mediaDevices.getUserMedia(this.getUserMediaConstraints).then((stream) => {
          stream.getTracks().forEach((track) => track.stop());
        });
        if (!Object.keys(this.activeCalls).length) {
          this.isMuted = false;
        }
        this.activeIncomingCalls.delete(session.id);
      });

      session.on("progress", () => {
        this._triggerListener({ listenerType: "progress", session });
      });

      session.on("failed", () => {
        this._stopIncomingRingtone();
        this._triggerListener({ listenerType: "failed", session });
        if (session.id === this.callAddingInProgress) {
          this.callAddingInProgress = undefined;
        }
        this._activeCallListRemove(session.id);
        this._stopCallTimer(session.id);
        this._removeCallStatus(session.id);
        this._stopAllMediaTracks(session);
        navigator.mediaDevices.getUserMedia(this.getUserMediaConstraints).then((stream) => {
          stream.getTracks().forEach((track) => track.stop());
        });
        if (!Object.keys(this.activeCalls).length) {
          this.isMuted = false;
        }
        this.activeIncomingCalls.delete(session.id);
      });

      session.on("confirmed", () => {
        this._stopIncomingRingtone();
        this._triggerListener({ listenerType: "confirmed", session });
        this._mutationUpdateCall(session);
        if (session.id === this.callAddingInProgress) {
          this.callAddingInProgress = undefined;
        }
        this._startCallTimer(session.id);
      });

      this._triggerListener({ listenerType: "new_call", session });
      this._addCall(session);

      if (session.direction === CONSTRAINTS.CALL_DIRECTION_OUTGOING) {
        this.setCurrentActiveRoom(session.roomId);
      }

      if (session.direction === CONSTRAINTS.CALL_DIRECTION_INCOMING) {
        if (session.status === CONSTRAINTS.CALL_STATUS_TERMINATED) return;

        // Tab 3 (activeCallTab) sẽ handle cuộc gọi
        if (
          localStorage.getItem("activeCallTab") === this.tabId &&
          !this.activeIncomingCalls.has(session.id) &&
          session.isInProgress()
        ) {
          this.activeIncomingCalls.add(session.id);
          this._playIncomingRingtone();
        }
      }
    },

    // Initialize JsSIP
    init({ configuration, socketInterfaces }: OptionInitJSSIPType) {
      try {
        const sockets = socketInterfaces.map((sock) => new JsSIP.WebSocketInterface(sock));
        UA = new JsSIP.UA({ ...configuration, sockets });

        UA.on("newRTCSession", ({ session, request }: { session: RTCSessionApp; request: any }) => {
          this._setupSessionHandlers(session, request);
        });

        UA.start();
        this.uaInit = true;

        this.tabId = crypto.randomUUID();

        // Khi khởi tạo, nếu chưa có tab active nào thì set tab này là active
        if (!localStorage.getItem("activeCallTab")) {
          localStorage.setItem("activeCallTab", this.tabId);
        }

        // Listen visibility change
        document.addEventListener("visibilitychange", () => {
          if (document.visibilityState === "visible") {
            // Khi tab được focus, set nó là active tab
            localStorage.setItem("activeCallTab", this.tabId);
          }
        });

        // Cleanup khi đóng tab
        window.addEventListener("beforeunload", () => {
          if (localStorage.getItem("activeCallTab") === this.tabId) {
            localStorage.removeItem("activeCallTab");
          }
        });
      } catch (error) {
        console.error("Failed to initialize JsSIP:", error);
        throw error;
      }
    },

    async _playIncomingRingtone() {
      this._stopIncomingRingtone();

      const audio = new Audio(AUDIO_INCOMING_RINGTONE);
      audio.volume = 0.05;
      let playCount = 0;

      audio.addEventListener("ended", () => {
        if (playCount < 2) {
          audio.play();
          playCount++;
        } else {
          this._stopIncomingRingtone();
        }
      });

      try {
        await audio.load();
        await audio.play();
        this.incomingRingtone = audio;
      } catch (err) {
        console.error("Failed to play ringtone:", err);
        this._stopIncomingRingtone();
        audio.src = "";
        audio.load();
      }
    },

    _stopIncomingRingtone() {
      try {
        if (this.incomingRingtone) {
          // Stop trước khi pause để chắc chắn dừng loop
          this.incomingRingtone.loop = false;
          this.incomingRingtone.pause();
          this.incomingRingtone.currentTime = 0;

          // Remove listeners
          this.incomingRingtone.onended = null;
          this.incomingRingtone.onerror = null;

          // Release resources
          this.incomingRingtone.src = "";
          this.incomingRingtone.load();
          this.incomingRingtone = undefined;
        }
      } catch (err) {
        console.error("Error stopping ringtone:", err);
        if (this.incomingRingtone) {
          // Force stop trong mọi trường hợp
          this.incomingRingtone.loop = false;
          this.incomingRingtone.pause();
          this.incomingRingtone = undefined;
        }
      }
    },

    skipCall(session: RTCSessionApp) {
      session.terminate({
        status_code: 480,
        reason_phrase: "Temporarily Unavailable",
      });
      if (session.roomId) {
        this._removeRoom(session.roomId);
      }
    },

    handleBroadcastCall(callData: {
      id: string;
      call_id: string;
      direction: string;
      remote_identity: any;
      status: number;
    }) {
      if (this.activeCalls[callData.id]) return;

      // Create pseudo RTCSession from broadcast data
      const pseudoSession = {
        id: callData.id,
        call_id: callData.call_id,
        direction: callData.direction,
        remote_identity: callData.remote_identity,
        status: callData.status,
      } as RTCSessionApp;

      // Use existing logic to handle incoming call
      this._setupSessionHandlers(pseudoSession, {
        call_id: callData.call_id,
        hasHeader: () => false,
        getHeader: () => null,
      });
    },

    setLineId(id: string) {
      this.lineId = id;
    },

    async _cleanupCall(callId: string) {
      const call = this.activeCalls[callId];
      if (call) {
        this._stopAllMediaTracks(call);
        this._removeCall(callId);
        this._stopCallTimer(callId);
        this._removeCallStatus(callId);
      }
    },

    _cleanupListeners() {
      for (const type in this.listeners) {
        this.listeners[type] = this.listeners[type].filter((listener) => {
          try {
            // Kiểm tra listener còn valid
            return typeof listener === "function";
          } catch {
            return false;
          }
        });
      }
    },
  },
  getters: {
    getActiveRooms: (state) => state.activeRooms,
    getActiveCalls: (state) => state.activeCalls,
    getValueActiveCalls: (state) => Object.values(state.activeCalls),
    getUserMediaConstraints: (state) => {
      return {
        audio: {
          deviceId: {
            exact: state.selectedMediaDevices.input,
          },
        },
        video: false,
      };
    },
  },
});
