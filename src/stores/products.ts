import { defineStore } from "pinia";

import { productList, ProductResponse } from "@/api/bcare";
import { ProductListRequest } from "@/api/bcare-types";

interface State {
  products: ProductResponse[];
  total?: number;
  error?: number;
  loading?: boolean;
}

export const useProductsStore = defineStore("productsStore", {
  state: (): State => ({ products: [] }),
  actions: {
    async getProductList(request: ProductListRequest) {
      try {
        this.loading = true;
        const response = await productList(request);
        if (response.code === 0) {
          this.products = response.data?.products ?? [];
          this.total = response.data?.total ?? 0;
        } else this.error = response.code;
      } catch (error) {
        this.error = error as number;
      } finally {
        this.loading = false;
      }
    },
  },
  getters: {
    totalItem: (state) => state.total ?? 0,
  },
});
