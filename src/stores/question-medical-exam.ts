import { defineStore } from "pinia";

import { questionList, responseList, surveyList } from "@/api/bcare";
import { Question, Response, ResponseListRequest } from "@/api/bcare-types";

export const CATEGORY_PLAN = "khám";
export const surveyPayloadDefault = {
  filter: {
    category: CATEGORY_PLAN,
    entity_type: "",
  },
};
export const responseParamsDefault = {
  entity_id: 0,
  question_id: 0,
  survey_id: 0,
};

export type QuestionsEachStep = { [key: string | number]: Question[] };

interface State {
  questions: Record<number, Question & { listOptions: string[] }>;
  responses: Record<number, Response>;
  total?: number;
  error?: number;
  loading?: boolean;
  questionsEachStep: QuestionsEachStep;
}

export const useQuestionMedicalExamStore = defineStore("questionMedicalExamStore", {
  state: (): State => ({ questions: {}, responses: {}, questionsEachStep: {} }),
  actions: {
    async getQuestions() {
      try {
        this.loading = true;
        const response = await surveyList(surveyPayloadDefault);

        if (response.code === 0) {
          const surveys = response.data?.surveys.reverse() ?? [];
          if (surveys) {
            for (const survey of surveys) {
              const responseQuestion = await questionList({ survey_id: survey.id });
              if (responseQuestion.code === 0 && responseQuestion.data) {
                this.questionsEachStep = {
                  ...this.questionsEachStep,
                  [survey.id - 1]: responseQuestion.data.questions,
                };
                responseQuestion.data.questions.forEach((question) => {
                  this.questions[question.id] = {
                    ...question,
                    listOptions: question.options.split("|"),
                  };
                });
              }
            }
          }
        } else this.error = response.code;
      } catch (error) {
        this.error = error as number;
      } finally {
        this.loading = false;
      }
    },
    async getResponsesOfQuestions(request: Partial<ResponseListRequest>) {
      try {
        this.loading = true;
        const response = await responseList(Object.assign(responseParamsDefault, request));
        if (response.code === 0 && response.data) {
          response.data?.responses.forEach((item) => {
            this.responses[item.question_id] = item;
          });
        } else this.error = response.code;
      } catch (error) {
        this.error = error as number;
      } finally {
        this.loading = false;
      }
    },
    clearResponses() {
      this.responses = {};
    },
  },
  getters: {
    totalItem: (state) => state.total ?? 0,
  },
});
