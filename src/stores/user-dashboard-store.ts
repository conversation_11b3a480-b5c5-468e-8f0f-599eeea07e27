import { defineS<PERSON> } from "pinia";
import { ref } from "vue";

import type {
  PerformanceStatResponse,
  ScheduleStatResponse,
  TaskStatResponse,
} from "@/api/bcare-types-v2";
import { user_statPerformance, user_statSchedule, user_statTaskStat } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useUserDashboardStore = defineStore("userDashboard", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const taskStats = ref<TaskStatResponse | null>(null);
  const scheduleStats = ref<ScheduleStatResponse | null>(null);
  const performanceStats = ref<PerformanceStatResponse | null>(null);

  // Actions
  function fetchTaskStats(userId: number, startDate?: string, endDate?: string) {
    return performAsyncAction(async () => {
      const response = await user_statTaskStat({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        taskStats.value = response.data;
      }
      return response.data;
    });
  }

  function fetchScheduleStats(userId: number, startDate?: string, endDate?: string) {
    return performAsyncAction(async () => {
      const response = await user_statSchedule({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        scheduleStats.value = response.data;
      }
      return response.data;
    });
  }

  function fetchPerformanceStats(userId: number, startDate?: string, endDate?: string) {
    return performAsyncAction(async () => {
      const response = await user_statPerformance({
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
      });
      if (response.code === 0 && response.data) {
        performanceStats.value = response.data;
      }
      return response.data;
    });
  }

  return {
    // State
    taskStats,
    scheduleStats,
    performanceStats,
    isLoading,
    error,
    // Actions
    fetchTaskStats,
    fetchScheduleStats,
    fetchPerformanceStats,
  };
});
