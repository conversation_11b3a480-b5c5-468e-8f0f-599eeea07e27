import dayjs from "dayjs";
import { defineStore } from "pinia";
import { reactive, onUnmounted } from "vue";

import { DEAL_COUNT_QUERY, DEAL_QUERY } from "@/constants/sql/deal-queries";
import { ParamConfig, useRawQuery } from "@/hooks/useRawQuery";
import type { UserShort } from "@/api/bcare-types-v2";
import {
  DEAL_FILTER_PRESETS,
  type DealFilterPreset,
} from "@/pages/customer/components/DealsTab/constants";
import { useNotiStore } from "@/stores/notification";

interface DealFilters {
  dealName: string | null;
  personInfo: string | null;
  createdDateRange: {
    start: Date | string | null;
    end: Date | string | null;
  };
  updatedDateRange: {
    start: Date | string | null;
    end: Date | string | null;
  };
  totalValueRange: {
    min: number | null;
    max: number | null;
  };
  depositAmountRange: {
    min: number | null;
    max: number | null;
  };
  stages: number[] | null;
  state: string | null;
  persons: number[] | null;
  tags: number[] | null;
  relatedUsers: UserShort[] | null;
}

// --- Define Assumed Stage IDs ---
const WON_STAGE_ID = [18, 23]; // Adjust if necessary
const LOST_STAGE_ID = [22, 16]; // Adjust if necessary

// --- Default Sorting ---
// Define default clauses as constants
const defaultOrderClause1 = "d.created_at DESC";
const defaultOrderClause2 = "dd.created_at DESC";

export const useDealDatatableStore = defineStore("dealDatatable", () => {
  const notiStore = useNotiStore();

  const dealQuery = DEAL_QUERY;
  const countQuery = DEAL_COUNT_QUERY;

  const paramConfig: Record<string, ParamConfig> = {
    dealName: { type: "string", placeholder: "$1", default: null },
    personInfo: { type: "string", placeholder: "$2", default: null },
    createdDateFrom: { type: "date", placeholder: "$3", default: null },
    createdDateTo: { type: "date", placeholder: "$4", default: null },
    updatedDateFrom: { type: "date", placeholder: "$5", default: null },
    updatedDateTo: { type: "date", placeholder: "$6", default: null },
    totalValueMin: { type: "float", placeholder: "$7", default: null },
    totalValueMax: { type: "float", placeholder: "$8", default: null },
    depositAmountMin: { type: "float", placeholder: "$9", default: null },
    depositAmountMax: { type: "float", placeholder: "$10", default: null },
    stages: { type: "array", placeholder: "$11", arrayElementType: "int", default: null },
    state: { type: "string", placeholder: "$12", default: null },
    persons: { type: "array", placeholder: "$13", arrayElementType: "int", default: null },
    historyState: { type: "string", placeholder: "$14", default: null },
    historyTotalValue: { type: "float", placeholder: "$15", default: null },
    historyName: { type: "string", placeholder: "$16", default: null },
    historyTotalAmount: { type: "float", placeholder: "$17", default: null },
    historyDepositAmount: { type: "float", placeholder: "$18", default: null },
    historyOperation: { type: "string", placeholder: "$19", default: null },
    stageHistoryBefore: {
      type: "array",
      placeholder: "$20",
      arrayElementType: "int",
      default: null,
    },
    stageHistoryAfter: {
      type: "array",
      placeholder: "$21",
      arrayElementType: "int",
      default: null,
    },
    historyUserId: { type: "int", placeholder: "$22", default: null },
    stageHistoryUserId: { type: "int", placeholder: "$23", default: null },
    historyChangedFrom: { type: "datetime", placeholder: "$24", default: null },
    historyChangedTo: { type: "datetime", placeholder: "$25", default: null },
    stageHistoryChangedFrom: { type: "datetime", placeholder: "$26", default: null },
    stageHistoryChangedTo: { type: "datetime", placeholder: "$27", default: null },
    tags: { type: "array", placeholder: "$28", arrayElementType: "int", default: null },
    relatedUsers: { type: "array", placeholder: "$29", arrayElementType: "int", default: null },
    limit: { type: "int", placeholder: "$30", isLimit: true, default: 50 },
    offset: { type: "int", placeholder: "$31", isOffset: true, default: 0 },
    orderClause1: {
      type: "string",
      template: true,
      default: defaultOrderClause1,
    },
    orderClause2: {
      type: "string",
      template: true,
      default: defaultOrderClause2,
    },
  };

  const {
    data: deals,
    loading,
    error,
    rowCount: totalDeals,
    executionTime,
    params,
    execute,
    refresh,
    resetParams: resetRawQueryParams,
    pagination,
    executeCount,
  } = useRawQuery(dealQuery, paramConfig, {
    pageSize: 50,
    timeout: 60000,
    countQuery: countQuery,
    onSuccess: (data) => {
      console.log("Query succeeded:", data);
    },
    onError: (err) => {
      console.error("Error executing deal query:", err);
      notiStore.error({ message: "Lấy danh sách deal thất bại." });
    },
  });

  const filters = reactive<DealFilters>(_getDefaultFilterState());

  const offsetParamKey = Object.entries(paramConfig).find(([_, config]) => config.isOffset)?.[0];

  function _applyFiltersToParams() {
    params.dealName = filters.dealName;
    params.personInfo = filters.personInfo;

    params.createdDateFrom = filters.createdDateRange?.start
      ? dayjs(filters.createdDateRange.start).toDate()
      : null;
    params.createdDateTo = filters.createdDateRange?.start
      ? dayjs(filters.createdDateRange.end ?? filters.createdDateRange.start)
          .add(1, "day")
          .toDate()
      : null;

    params.updatedDateFrom = filters.updatedDateRange?.start
      ? dayjs(filters.updatedDateRange.start).toDate()
      : null;
    params.updatedDateTo = filters.updatedDateRange?.start
      ? dayjs(filters.updatedDateRange.end ?? filters.updatedDateRange.start)
          .add(1, "day")
          .toDate()
      : null;

    params.totalValueMin = filters.totalValueRange.min;
    params.totalValueMax = filters.totalValueRange.max;
    params.depositAmountMin = filters.depositAmountRange.min;
    params.depositAmountMax = filters.depositAmountRange.max;

    params.stages = filters.stages && filters.stages.length > 0 ? filters.stages : null;
    params.persons = filters.persons && filters.persons.length > 0 ? filters.persons : null;
    params.tags = filters.tags && filters.tags.length > 0 ? filters.tags : null;

    params.relatedUsers =
      filters.relatedUsers && filters.relatedUsers.length > 0
        ? filters.relatedUsers.map((user) => (typeof user === "number" ? user : user.id))
        : null;

    params.state = filters.state;

    if (offsetParamKey && pagination.page !== 1) {
      pagination.page = 1;
      params[offsetParamKey] = 0;
    } else if (offsetParamKey && params[offsetParamKey] !== 0) {
      params[offsetParamKey] = 0;
    }
  }

  function _getDefaultFilterState(): DealFilters {
    return {
      dealName: null,
      personInfo: null,
      createdDateRange: { start: null, end: null },
      updatedDateRange: { start: null, end: null },
      totalValueRange: { min: null, max: null },
      depositAmountRange: { min: null, max: null },
      stages: null,
      state: null,
      persons: null,
      tags: null,
      relatedUsers: null,
    };
  }

  async function applyFilters() {
    _applyFiltersToParams();
    return await execute();
  }

  async function resetFilters() {
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    _applyFiltersToParams();
    params.orderClause1 = defaultOrderClause1;
    params.orderClause2 = defaultOrderClause2;
    return await execute();
  }

  async function clearFilter(filterKey: keyof DealFilters) {
    const defaultState = _getDefaultFilterState();
    const currentFilterValue = filters[filterKey];
    const defaultFilterValue = defaultState[filterKey];

    if (JSON.stringify(currentFilterValue) === JSON.stringify(defaultFilterValue)) {
      console.log(`Filter ${filterKey} is already in default state.`);
      return;
    }

    switch (filterKey) {
      case "dealName":
        filters.dealName = defaultState.dealName;
        break;
      case "personInfo":
        filters.personInfo = defaultState.personInfo;
        break;
      case "createdDateRange":
        filters.createdDateRange = defaultState.createdDateRange;
        break;
      case "updatedDateRange":
        filters.updatedDateRange = defaultState.updatedDateRange;
        break;
      case "totalValueRange":
        filters.totalValueRange = defaultState.totalValueRange;
        break;
      case "depositAmountRange":
        filters.depositAmountRange = defaultState.depositAmountRange;
        break;
      case "stages":
        filters.stages = defaultState.stages;
        break;
      case "state":
        filters.state = defaultState.state;
        break;
      case "persons":
        filters.persons = defaultState.persons;
        break;
      case "tags":
        filters.tags = defaultState.tags;
        break;
      case "relatedUsers":
        filters.relatedUsers = defaultState.relatedUsers;
        break;
      default:
        const exhaustiveCheck: never = filterKey;
        console.warn(`Attempted to clear unhandled filter key: ${String(exhaustiveCheck)}`);
        return;
    }

    await applyFilters();
  }

  const filterKeyMap: Record<string, keyof DealFilters> = {
    name: "dealName",
    person_id: "personInfo",
    created_at: "createdDateRange",
    updated_at: "updatedDateRange",
    stage_id: "stages",
    tags: "tags",
    related_users: "relatedUsers",
    state: "state",
  };

  async function clearFilterByKey(datatableFilterKey: string) {
    const storeFilterKey = filterKeyMap[datatableFilterKey];
    if (storeFilterKey) {
      await clearFilter(storeFilterKey);
    } else {
      console.warn(`No store mapping found for DataTable filter key: ${datatableFilterKey}`);
    }
  }

  async function loadInitialData() {
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    _applyFiltersToParams();
    params.orderClause1 = defaultOrderClause1;
    params.orderClause2 = defaultOrderClause2;
    return await execute();
  }

  function resetStoreState() {
    deals.value = [];
    totalDeals.value = 0;
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    params.orderClause1 = defaultOrderClause1;
    params.orderClause2 = defaultOrderClause2;
    error.value = null;
  }

  onUnmounted(() => {
    resetStoreState();
  });

  function $reset() {
    resetStoreState();
  }

  function $dispose() {
    $reset();
  }

  const applyFilterPreset = async (filterType: DealFilterPreset, userId: number) => {
    Object.assign(filters, _getDefaultFilterState());
    params.stageHistoryAfter = null;
    params.stageHistoryChangedFrom = null;
    params.stageHistoryChangedTo = null;
    params.stageHistoryBefore = null;

    let presetFilters: Partial<DealFilters> = {};
    let sortOrder1 = defaultOrderClause1;
    let sortOrder2 = defaultOrderClause2;

    const todayStart = dayjs().startOf("day").toDate();
    const todayEnd = dayjs().endOf("day").toDate();
    const todayStartFormatted = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
    const todayEndFormatted = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");

    switch (filterType) {
      case DEAL_FILTER_PRESETS.ALL:
        break;

      case DEAL_FILTER_PRESETS.MY_DEALS:
      case DEAL_FILTER_PRESETS.RELATED_DEALS:
        presetFilters.relatedUsers = [
          { id: userId, username: "", name: "", profile_image: "", department_id: 0 },
        ];
        break;

      case DEAL_FILTER_PRESETS.CREATED_TODAY:
        presetFilters.createdDateRange = { start: todayStart, end: todayEnd };
        break;

      case DEAL_FILTER_PRESETS.ACTIVE_DEALS:
        presetFilters.state = "active";
        // sortOrder1 = "d.updated_at DESC";
        // sortOrder2 = "dd.updated_at DESC";
        break;

      case DEAL_FILTER_PRESETS.WON_TODAY:
        params.stageHistoryAfter = Array.isArray(WON_STAGE_ID) ? WON_STAGE_ID : [WON_STAGE_ID];
        params.stageHistoryChangedFrom = todayStartFormatted;
        params.stageHistoryChangedTo = todayEndFormatted;
        // sortOrder1 = "d.updated_at DESC";
        // sortOrder2 = "dd.updated_at DESC";
        break;

      case DEAL_FILTER_PRESETS.LOST_TODAY:
        params.stageHistoryAfter = Array.isArray(LOST_STAGE_ID) ? LOST_STAGE_ID : [LOST_STAGE_ID];
        params.stageHistoryChangedFrom = todayStartFormatted;
        params.stageHistoryChangedTo = todayEndFormatted;
        // sortOrder1 = "d.updated_at DESC";
        // sortOrder2 = "dd.updated_at DESC";
        break;

      default:
        const exhaustiveCheck: never = filterType;
        console.warn("Unknown filter preset type:", exhaustiveCheck);
        break;
    }

    Object.assign(filters, presetFilters);

    _applyFiltersToParams();

    params.orderClause1 = sortOrder1;
    params.orderClause2 = sortOrder2;

    return execute();
  };

  return {
    deals,
    loading,
    error,
    totalDeals,
    executionTime,
    pagination,
    filters,
    applyFilters,
    resetFilters,
    clearFilterByKey,
    applyFilterPreset,
    execute,
    refresh,
    loadInitialData,
    executeCount,
    $reset,
    $dispose,
  };
});
