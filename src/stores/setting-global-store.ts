import { defineStore } from "pinia";
import { reactive, watch } from "vue";

import { UniversalSetting } from "@/api/extend-types";

export const ALL_SETTINGS: Record<string, UniversalSetting[]> = {
  dashboard: [
    {
      type: "group",
      label: "Bộ lọc",
      field_name: "display",
      value: null,
      children: [
        {
          type: "toggle",
          label: "Ẩn bác sĩ",
          field_name: "hide_doctor",
          value: false,
        },
        {
          type: "toggle",
          label: "Ẩn bộ lọc",
          field_name: "hide_filter",
          value: false,
        },
      ],
    },
    {
      type: "select",
      label: "Chế độ hiển thị",
      field_name: "display_mode",
      options: [
        { label: "Danh sách", value: "list" },
        { label: "Lưới", value: "grid" },
      ],
      value: "list",
    },
  ],
  person: [
    {
      type: "group",
      label: "Hiển thị cột",
      field_name: "display",
      value: null,
      children: [
        {
          type: "checkbox",
          label: "<PERSON><PERSON><PERSON> thị cột",
          field_name: "display_columns",
          options: [
            { label: "STT", value: true, field_name: "id" },
            { label: "Thông tin KH", value: true, field_name: "full_name" },
            { label: "Email", value: false, field_name: "email" },
            { label: "Ngày sinh", value: false, field_name: "date_of_birth" },
            { label: "Nghề nghiệp", value: false, field_name: "job_id" },
            {
              label: "Mô tả",
              value: false,
              field_name: "person_field.description",
            },
            { label: "Địa chỉ", value: false, field_name: "address_number" },
            { label: "Nguồn khách hàng", value: true, field_name: "source_id" },
            {
              label: "Loại điều trị",
              value: true,
              field_name: "person_field.treatment_id",
            },
            {
              label: "Trạng thái điều trị",
              value: true,
              field_name: "person_field.treatment_status_id",
            },
            {
              label: "Sale",
              value: true,
              field_name: "sale.name",
            },
            {
              label: "Stage",
              value: true,
              field_name: "stage_name",
            },
            {
              label: "Ngày tạo",
              value: true,
              field_name: "created_at",
            },
            {
              label: "Thông tin form",
              value: false,
              field_name: "person_field.form_source",
            },
            {
              label: "Nguồn & URL",
              value: false,
              field_name: "person_field.source_channel",
            },
          ],
        },
      ],
    },
  ],
};

export const useSettingGlobalStore = defineStore("settingGlobalStore", () => {
  const state = reactive<Record<string, UniversalSetting[]>>(ALL_SETTINGS);

  Object.keys(ALL_SETTINGS).forEach((key) => {
    const localStorageValue = localStorage.getItem(key);
    if (localStorageValue) state[key] = JSON.parse(localStorageValue);

    watch(
      () => state[key],
      (newValue) => {
        localStorage.setItem(key, JSON.stringify(newValue));
      },
      { deep: true },
    );
  });

  const getSettingByKey = (settingKey: string) => {
    return state[settingKey];
  };

  return { state, getSettingByKey };
});
