import { defineStore } from "pinia";
import { computed, onMounted, reactive } from "vue";

import { stageList } from "@/api/bcare";
import { StageResponse } from "@/api/bcare-types";

interface State {
  data: Record<string, StageResponse>;
  stageMapPipelineId: Record<string, StageResponse[]>;
  error?: number;
  loading: boolean;
}

export const useStageStore = defineStore("stageStore", () => {
  const state = reactive<State>({
    data: {},
    stageMapPipelineId: {},
    error: undefined,
    loading: false,
  });

  const fetchData = async () => {
    try {
      state.loading = true;
      const response = await stageList({
        page_size: 0,
        page: 1,
        filter: {
          pipeline_id: 0,
          name: "",
          status: 0,
        },
        order_by: "order_number",
      });
      if (response.code === 0) {
        if (response.data) {
          const dataMap: Record<string, StageResponse[]> = {};
          state.data = response.data.stages.reduce(
            (result, stage) => {
              if (dataMap[stage.pipeline_id]) dataMap[stage.pipeline_id].push(stage);
              else dataMap[stage.pipeline_id] = [stage];

              if (stage.id) result[stage.id] = stage;
              return result;
            },
            {} as Record<string, StageResponse>,
          );
          state.stageMapPipelineId = { ...dataMap };
        }
      } else {
        state.error = response.code;
      }
    } catch (error) {
      state.error = error as number;
    } finally {
      state.loading = false;
    }
  };

  //TODO refactor this fking stupid one,
  onMounted(async () => {
    await fetchData();
  });

  const getPipelines = computed(() => {
    return Object.keys(state.stageMapPipelineId);
  });

  return {
    state,
    fetchData,
    getPipelines,
  };
});
