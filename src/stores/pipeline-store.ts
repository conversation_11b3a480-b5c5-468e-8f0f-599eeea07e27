// src/store/pipeline.ts
import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, ref, shallowRef } from "vue";

import { PipelineResponse, StageResponse } from "@/api/bcare-types-v2";
import { pipelineList } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  pipelines: PipelineResponse[];
  totalPipelines: number;
  totalPages: number;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const usePipelineStore = defineStore("pipelineStore", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const pipelines = shallowRef<PipelineResponse[]>([]);
  const totalPipelines = ref(0);
  const totalPages = ref(0);

  const cachedData = useStorage<CacheData>("pipelineStoreCache", {
    pipelines: [],
    totalPipelines: 0,
    totalPages: 0,
    expireTime: 0,
  });

  function initializeFromCache() {
    if (cachedData.value.expireTime > Date.now()) {
      pipelines.value = cachedData.value.pipelines;
      totalPipelines.value = cachedData.value.totalPipelines;
      totalPages.value = cachedData.value.totalPages;
    }
  }

  function updateCache() {
    cachedData.value = {
      pipelines: pipelines.value,
      totalPipelines: totalPipelines.value,
      totalPages: totalPages.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  // Getters
  const getPipelines = computed(() => pipelines.value);
  const getTotalPipelines = computed(() => totalPipelines.value);
  const getTotalPages = computed(() => totalPages.value);

  // Actions
  async function fetchPipelines() {
    return performAsyncAction(async () => {
      const response = await pipelineList({
        page_size: 100,
        page: 1,
        order_by: "created_at desc",
      });

      if (response.code === 0) {
        const pipelineData = response.data;
        pipelines.value = pipelineData?.pipelines || [];
        totalPipelines.value = pipelineData?.total || 0;
        totalPages.value = pipelineData?.total_page || 0;
        updateCache();
      } else {
        throw new Error("Error fetching pipelines: " + response.data);
      }
    });
  }

  // Method to get pipeline by ID
  function getPipelineById(id: number) {
    return pipelines.value.find((pipeline) => pipeline.id === id);
  }

  // Method to get stages by pipeline ID
  function getStagesByPipelineID(pipelineID: number): StageResponse[] | undefined {
    const pipeline = getPipelineById(pipelineID);
    return pipeline?.stages;
  }

  // Method to get top-level stages by pipeline ID (stages without parent_stage_id)
  function getTopLevelStagesByPipelineID(pipelineID: number): StageResponse[] | undefined {
    const stages = getStagesByPipelineID(pipelineID);
    return stages?.filter((stage) => !stage.parent_stage_id);
  }

  return {
    pipelines,
    totalPipelines,
    totalPages,
    isLoading,
    error,
    getPipelines,
    getTotalPipelines,
    getTotalPages,
    initializeFromCache,
    fetchPipelines,
    getPipelineById,
    getStagesByPipelineID,
    getTopLevelStagesByPipelineID,
  };
});
