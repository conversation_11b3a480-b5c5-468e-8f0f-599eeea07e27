// src/stores/bill-store-v2.ts
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import type {
  Bill,
  BillAddFromDealRequest,
  BillAddRequest,
  BillDeleteRequest,
  BillDynamicQuery,
  BillGetRequest,
  BillItem,
  BillItemAddFromAttachment,
  BillItemAddRequest,
  BillItemDeleteRequest,
  BillItemDynamicQuery,
  BillItemGetPaidRequest,
  BillItemGetPartiallyPaidRequest,
  BillItemGetRequest,
  BillItemListRequest,
  BillItemListResponse,
  BillItemResponse,
  BillItemUpdateRequest,
  BillListRequest,
  BillListResponse,
  BillResponse,
  BillUpdateRequest,
} from "@/api/bcare-types-v2";
import {
  bill_itemAdd,
  bill_itemAddFromAttachment,
  bill_itemDelete,
  bill_itemGet,
  bill_itemGetPaid,
  bill_itemGetPartiallyPaid,
  bill_itemList,
  bill_itemQuery,
  bill_itemUpdate,
  bill_itemUpdateFromAttachment,
  billAdd,
  billAddFromDeal,
  billDelete,
  billGet,
  billList,
  billQuery,
  billUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { useQuery } from "@/hooks/useQuery-v3";

export const useBillStore = defineStore("bill", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const bills = shallowRef<BillResponse[]>([]);
  const currentBill = shallowRef<BillResponse | null>(null);
  const billItems = shallowRef<BillItemResponse[]>([]);
  const currentBillItem = shallowRef<BillItemResponse | null>(null);
  const partiallyPaidItems = shallowRef<BillItemResponse[]>([]);

  // Queries
  const billQueryResult = useQuery<BillDynamicQuery, BillResponse>({
    queryFn: billQuery,
    initialQuery: {},
  });

  const billItemQueryResult = useQuery<BillItemDynamicQuery, BillItemResponse>({
    queryFn: bill_itemQuery,
    initialQuery: {},
  });

  // Getters
  const getBillCount = computed(() => bills.value.length);
  const getBillById = computed(() => (id: number) => bills.value.find((bill) => bill.id === id));
  const getBillItemCount = computed(() => billItems.value.length);
  const getBillItemById = computed(
    () => (id: number) => billItems.value.find((item) => item.id === id),
  );

  // Actions for Bills
  function addBill(req: BillAddRequest) {
    return performAsyncAction(async () => {
      const response = await billAdd(req);
      if (response.data) {
        bills.value.push(response.data);
      }
      return response.data;
    });
  }

  function addBillFromDeal(req: BillAddFromDealRequest) {
    return performAsyncAction(async () => {
      const response = await billAddFromDeal(req);
      if (response.data) {
        bills.value.push(response.data);
      }
      return response.data;
    });
  }

  function deleteBill(req: BillDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await billDelete(req);
      if (response.data) {
        bills.value = bills.value.filter((bill) => bill.id !== req.id);
      }
      return response.data;
    });
  }

  function getBill(req: BillGetRequest) {
    return performAsyncAction(async () => {
      const response = await billGet(req);
      currentBill.value = response.data ?? null;
      return response.data;
    });
  }

  function listBills(req: BillListRequest) {
    return performAsyncAction(async () => {
      const response = await billList(req);
      bills.value = response.data?.bills ?? [];
      return response.data;
    });
  }

  function updateBill(req: BillUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await billUpdate(req);
      if (response.data) {
        const index = bills.value.findIndex((bill) => bill.id === req.id);
        if (index !== -1) {
          bills.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  async function queryBills(req: Partial<BillDynamicQuery> = {}, getCount: boolean = true) {
    try {
      await billQueryResult.fetchQuery(req, getCount);
      bills.value = [...billQueryResult.items.value];
    } catch (error) {
      bills.value = [];
    }
  }

  // Actions for Bill Items
  function addBillItem(req: BillItemAddRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemAdd(req);
      if (response.data) {
        billItems.value.push(response.data);
      }
      return response.data;
    });
  }

  function addBillItemFromAttachment(req: BillItemAddFromAttachment) {
    return performAsyncAction(async () => {
      const response = await bill_itemAddFromAttachment(req);
      if (response.data) {
        billItems.value.push(response.data);
      }
      return response.data;
    });
  }

  function deleteBillItem(req: BillItemDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemDelete(req);
      if (response.data) {
        billItems.value = billItems.value.filter((item) => item.id !== req.id);
      }
      return response.data;
    });
  }

  function getBillItem(req: BillItemGetRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemGet(req);
      currentBillItem.value = response.data ?? null;
      return response.data;
    });
  }

  function listBillItems(req: BillItemListRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemList(req);
      billItems.value = response.data?.bill_items ?? [];
      return response.data;
    });
  }

  function updateBillItem(req: BillItemUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemUpdate(req);
      if (response.data) {
        const index = billItems.value.findIndex((item) => item.id === req.id);
        if (index !== -1) {
          billItems.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  async function queryBillItems(req: Partial<BillItemDynamicQuery> = {}, getCount: boolean = true) {
    try {
      await billItemQueryResult.fetchQuery(req, getCount);
      billItems.value = [...billItemQueryResult.items.value];
    } catch (error) {
      billItems.value = [];
    }
  }

  function getPartiallyPaidItems(req: BillItemGetPartiallyPaidRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemGetPartiallyPaid(req);
      if (response.data) {
        partiallyPaidItems.value = response.data.bill_items ?? [];
      }
      return response.data;
    });
  }

  function getPaidItems(req: BillItemGetPaidRequest) {
    return performAsyncAction(async () => {
      const response = await bill_itemGetPaid(req);
      if (response.data) {
        billItems.value = response.data.bill_items ?? [];
      }
      return response.data;
    });
  }

  function updateBillItemFromAttachment(req: BillItemAddFromAttachment) {
    return performAsyncAction(async () => {
      const response = await bill_itemUpdateFromAttachment(req);
      if (response.data) {
        const index = billItems.value.findIndex(
          (item) => item.attachment_id === req.attachment_id
        );
        if (index !== -1) {
          billItems.value[index] = response.data;
        }
      }
      return response.data;
    });
  }

  return {
    // State
    bills,
    currentBill,
    billItems,
    currentBillItem,
    partiallyPaidItems,
    isLoading,
    error,

    // Getters
    getBillCount,
    getBillById,
    getBillItemCount,
    getBillItemById,

    // Actions for Bills
    addBill,
    addBillFromDeal,
    deleteBill,
    getBill,
    listBills,
    updateBill,
    queryBills,
    billQueryResult,

    // Actions for Bill Items
    addBillItem,
    addBillItemFromAttachment,
    deleteBillItem,
    getBillItem,
    listBillItems,
    updateBillItem,
    queryBillItems,
    billItemQueryResult,
    getPartiallyPaidItems,
    getPaidItems,
    updateBillItemFromAttachment,
  };
});
