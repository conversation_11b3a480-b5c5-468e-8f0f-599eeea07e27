import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { useRoute, useRouter, type RouteLocationNormalizedLoaded } from "vue-router";
import { usePermissions } from "@/composables/usePermissions";
import { TOP_MENU_ITEMS, type MenuItem } from "../config/menu-definitions";

// --- TYPES ---
export interface FormattedMenuItem extends MenuItem {
  active?: boolean;
  activeDropdown?: boolean;
  subMenu?: FormattedMenuItem[];
}

export interface AppRoute extends RouteLocationNormalizedLoaded {}

// --- STORE DEFINITION ---
export const useMenuStore = defineStore("menu", () => {
  const route = useRoute() as AppRoute;
  const router = useRouter();
  const { filterMenuByPermission } = usePermissions();

  // --- STATE ---
  const rawMenu = ref<MenuItem[]>(TOP_MENU_ITEMS);
  const _internalForceActivePageName = ref<string | undefined>(undefined);

  const _isMenuItemActiveRecursive = (
    menuItems: MenuItem[],
    currentRoute: AppRoute,
    forcedPageName?: string,
  ): boolean => {
    let match = false;
    for (const item of menuItems) {
      const targetPageNameToMatch = forcedPageName ?? currentRoute.name;
      if (item.pageName === targetPageNameToMatch && !item.ignore) {
        match = true;
        break;
      }
      if (!match && item.subMenu) {
        match = _isMenuItemActiveRecursive(item.subMenu, currentRoute, forcedPageName);
        if (match) break;
      }
    }
    return match;
  };

  const _formatMenuItemsRecursive = (
    itemsToFormat: MenuItem[],
    currentRoute: AppRoute,
    forcedPageName?: string,
  ): FormattedMenuItem[] => {
    return itemsToFormat.map((item) => {
      const menuItem: FormattedMenuItem = {
        ...item,
        active: undefined,
        activeDropdown: undefined,
      };

      const pageNameToMatchAgainst = forcedPageName ?? currentRoute.name;

      menuItem.active =
        (menuItem.pageName === pageNameToMatchAgainst ||
          (menuItem.subMenu &&
            _isMenuItemActiveRecursive(menuItem.subMenu, currentRoute, forcedPageName))) &&
        !menuItem.ignore;

      if (menuItem.subMenu) {
        menuItem.activeDropdown = _isMenuItemActiveRecursive(
          menuItem.subMenu,
          currentRoute,
          forcedPageName,
        );
        menuItem.subMenu = _formatMenuItemsRecursive(
          menuItem.subMenu,
          currentRoute,
          forcedPageName,
        );
      }
      return menuItem;
    });
  };

  // --- GETTERS (Computed Properties) ---
  const permittedMenu = computed(() => {
    return filterMenuByPermission(rawMenu.value);
  });

  const formattedMenu = computed(() => {
    return _formatMenuItemsRecursive(
      permittedMenu.value,
      route,
      _internalForceActivePageName.value,
    );
  });

  // --- ACTIONS (Functions) ---
  const navigateTo = (menuItem: FormattedMenuItem) => {
    if (menuItem.pageName) {
      _internalForceActivePageName.value = undefined;
      router.push({
        name: menuItem.pageName,
      });
    }
  };

  const setForceActiveMenu = (pageName: string) => {
    _internalForceActivePageName.value = pageName;
  };

  const clearForceActiveMenu = () => {
    _internalForceActivePageName.value = undefined;
  };

  return {
    formattedMenu,
    navigateTo,
    setForceActiveMenu,
    clearForceActiveMenu,
  };
});

export type ProvideForceActiveMenuFunction = (pageName: string) => void;
