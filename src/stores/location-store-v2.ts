import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import { LocalDistrict, LocalWard, LocationResponse } from "@/api/bcare-types-v2";
import { locationGet } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface CacheData {
  data: LocationResponse;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useLocationStore = defineStore("location-v2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const locationData = shallowRef<LocationResponse>({
    provinces: [],
    districts: [],
    wards: [],
  });

  const cachedData = useStorage<CacheData>("locationStoreCache", {
    data: { provinces: [], districts: [], wards: [] },
    expireTime: 0,
  });

  function initializeFromCache() {
    if (cachedData.value.expireTime > Date.now()) {
      locationData.value = cachedData.value.data;
    }
  }

  function updateCache() {
    cachedData.value = {
      data: locationData.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  function fetchLocation() {
    return performAsyncAction(async () => {
      const response = await locationGet();

      if (response.code === 0 && response.data != null) {
        locationData.value = response.data;
        updateCache();
        return response.data;
      }
      throw new Error("Invalid response from server");
    });
  }

  // Getters
  const getLocation = computed(() => locationData.value);
  const getProvinces = computed(() => locationData.value.provinces);
  const getDistrictAll = computed(() => locationData.value.districts);
  const getWardAll = computed(() => locationData.value.wards);
  const getDistricts = computed(
    () => (provinceId: number) =>
      locationData.value.districts.filter((d: LocalDistrict) => d.province_id == provinceId),
  );
  const getWards = computed(
    () => (districtId: number) =>
      locationData.value.wards.filter((w: LocalWard) => w.district_id == districtId),
  );

  return {
    locationData,
    isLoading,
    error,
    initializeFromCache,
    fetchLocation,
    getLocation,
    getProvinces,
    getDistrictAll,
    getWardAll,
    getDistricts,
    getWards,
  };
});
