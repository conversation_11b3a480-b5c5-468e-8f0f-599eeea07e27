import { defineStore } from "pinia";
import { reactive } from "vue";

import { AttachmentResponse, DealUpdateInstallmentRequest } from "@/api/bcare-types";

interface State {
  invoice?: {
    payment_credit_card: number;
    payment_mpos: number;
    payment_bank: number;
    unpaid: number;
    unpaid_note: string;
    paid: number;
    paid_note: string;
  };
  service?: Record<
    string,
    {
      dealId?: number;
      selectedQuantities?: Record<number, number>;
      selectedMainService?: AttachmentResponse[];
      selectedPrices?: AttachmentResponse[];
      selectedProductQuantities?: Record<number, number>;
      installment: DealUpdateInstallmentRequest;
      getCategoryName: string;
    }
  >;
}

export const useCacheDataStore = defineStore("cacheDataStore", () => {
  const state = reactive<State>({
    invoice: undefined,
    service: undefined,
  });

  const updateData = async <T extends keyof State>(key: T, value: State[T]) => {
    state[key] = value;
  };

  const clearData = async <T extends keyof State>(key: T) => {
    state[key] = undefined;
  };

  return { state, updateData, clearData };
});
