import { defineStore } from "pinia";

import { Icon } from "../base-components/Lucide/Lucide.vue";

export interface Menu {
  icon: Icon;
  title: string;
  pageName?: string;
  subMenu?: Menu[];
  ignore?: boolean;
}

export interface SideMenuState {
  menu: Array<Menu | "divider">;
}

export const useSideMenuStore = defineStore("sideMenu", {
  state: (): SideMenuState => ({
    menu: [
      {
        icon: "Home",
        pageName: "top-menu-dashboard-updental",
        title: "Phòng khám",
      },
      {
        icon: "ListChecks",
        pageName: "top-menu-task",
        title: "Công việc",
      },
      {
        icon: "Users",
        pageName: "top-menu-customer-list",
        title: "Kh<PERSON>ch hàng",
      },
      {
        icon: "Headphones",
        pageName: "top-menu-call-center",
        title: "Tổng đài",
      },
      {
        icon: "CalendarClock",
        pageName: "top-menu-appointments-doctor",
        title: "<PERSON>ịch hẹn",
      },
      {
        icon: "CalendarClock",
        pageName: "top-menu-appointments-offline",
        title: "Lịch hẹn lý thuyết",
      },
      {
        icon: "CalendarClock",
        pageName: "top-menu-appointment-of-doctor",
        title: "Lịch bác sĩ",
      },
      {
        icon: "File",
        pageName: "top-menu-accounting",
        title: "Kế toán",
        subMenu: [
          {
            icon: "Zap",
            pageName: "top-menu-accounting",
            title: "Báo cáo SP",
          },
          {
            icon: "Zap",
            pageName: "top-menu-consumables",
            title: "Vật tư tiêu hao",
          },
        ],
      },
      {
        icon: "Trello",
        pageName: "top-menu-statistics",
        title: "Thống kê",
        subMenu: [
          {
            icon: "Zap",
            pageName: "top-menu-revenue",
            title: "Doanh thu",
          },
          {
            icon: "Zap",
            pageName: "top-menu-revenue-attachment",
            title: "Dịch vụ điều trị",
          },
        ],
      },
      {
        icon: "Settings",
        pageName: "top-menu-settings",
        title: "Thiết lập",
        subMenu: [
          {
            icon: "Users",
            pageName: "top-menu-users",
            title: "Users",
            subMenu: [
              {
                icon: "Zap",
                pageName: "top-menu-user-list",
                title: "Danh sách",
              },
              {
                icon: "CalendarClock",
                pageName: "top-menu-schedule",
                title: "Lịch làm việc",
              },
            ],
          },
          {
            icon: "ShoppingBag",
            pageName: "top-menu-products",
            title: "Sản phẩm",
            subMenu: [
              {
                icon: "Zap",
                pageName: "top-menu-product-list",
                title: "Danh sách",
              },
            ],
          },
          {
            icon: "Trello",
            pageName: "top-menu-bundle",
            title: "Phân loại",
            subMenu: [
              {
                icon: "Activity",
                pageName: "top-menu-bundle",
                title: "Danh sách nhóm",
              },
              {
                icon: "Activity",
                pageName: "top-menu-bundle-edit",
                title: "Chỉnh sửa nhóm",
              },
              {
                icon: "Activity",
                pageName: "top-menu-field-create",
                title: "Chỉnh sửa field",
              },
            ],
          },

          {
            icon: "Terminal",
            pageName: "top-menu-admin",
            title: "Admin",
            subMenu: [
              {
                icon: "Sliders",
                pageName: "top-menu-configuration",
                title: "Cấu hình",
              },
              {
                icon: "List",
                pageName: "top-menu-logs",
                title: "Logs",
              },
            ],
          },
        ],
      },
    ],
  }),
});
