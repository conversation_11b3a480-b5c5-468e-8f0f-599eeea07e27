import { defineStore } from "pinia";
import { computed, ref, watch } from "vue";

import { AttachmentStatus, CommonStatus, DealState } from "@/api/bcare-enum";
import type {
  AttachmentResponse,
  CalculateDiscountResponse,
  DealResponse,
  Discount,
  Product,
} from "@/api/bcare-types-v2";
import useAttachment from "@/hooks/useAttachment";
import useAttachmentData from "@/hooks/useAttachmentData";
import useDeal from "@/hooks/useDeal";
import useDiscount from "@/hooks/useDiscount";
import { formatShortDate } from "@/utils/time-helper";

interface AttachmentWithChildren {
  self: AttachmentResponse;
  children: AttachmentResponse[];
}

export const useDealFormStore = defineStore("dealFormStore", () => {
  // ------------------------------
  // State
  // ------------------------------

  // Identifier for the person associated with the deal
  const personId = ref(0);

  function setPersonId(id: number) {
    personId.value = id;
  }

  // Trigger for recalculations
  const recalculateTrigger = ref(0);

  // Store pending attachments with their children
  const pendingAttachments = ref<Record<number, AttachmentWithChildren>>({});

  // Currently editing attachment
  const editingAttachment = ref<AttachmentWithChildren | null>(null);

  // Selected parent attachment during attachment search
  const selectedParentAttachment = ref<AttachmentResponse | null>(null);

  // Selected product during product search
  const selectedProduct = ref<Product | null>(null);

  // Temporary selections for participants
  const tempSelections = ref({
    participants: {} as Record<string, number>,
  });

  // Current deal being managed (either new or existing)
  const currentDeal = ref<DealResponse | null>(null);

  // Selected discounts and their calculated results
  const selectedDiscounts = ref<Discount[]>([]);
  const calculatedDiscount = ref<CalculateDiscountResponse | null>(null);

  // Flag to determine if the store is in edit mode (updating existing deal)
  const isEditMode = ref(false);

  // Discounts for attachments
  const attachmentDiscounts = ref<Record<number, number>>({});

  const setAttachmentDiscount = (attachmentId: number, amount: number) => {
    attachmentDiscounts.value[attachmentId] = amount;
  };

  const clearAttachmentDiscounts = () => {
    attachmentDiscounts.value = {};
  };

  // Add this to the store state section
  const participants = ref<Record<string, number>>({
    treatment_doctor: 0,
    consultant_doctor: 0,
    consultant: 0,
    sale: 0,
    doctor_assistant: 0,
  });

  // ------------------------------
  // Hooks
  // ------------------------------

  const { addAttachment, updateAttachment, getAttachment, deleteAttachment } = useAttachment(); // Added deleteAttachment
  const { setParticipantByRole } = useAttachmentData();
  const dealHook = useDeal({ useStore: true });
  const { getDiscountById, calculateDiscount, discounts, listDiscounts } = useDiscount();

  // ------------------------------
  // Action Methods
  // ------------------------------

  /**
   * Clear all selected discounts and calculated discount.
   */
  const clearDiscounts = () => {
    selectedDiscounts.value = [];
    calculatedDiscount.value = null;
    //console.log('All discounts cleared.');
  };

  /**
   * Initialize the store for creating a new deal.
   * This creates a new deal and sets it as the current deal.
   */
  const initializeDeal = async () => {
    if (personId.value) {
      try {
        const newDeal = await dealHook.createDeal({
          person_id: personId.value,
          status: CommonStatus.INACTIVE,
          state: DealState.ACTIVE,
          name: "Deal " + formatShortDate(new Date().toISOString()),
        });
        if (newDeal) {
          currentDeal.value = newDeal;
          isEditMode.value = false;
        }
      } catch (error) {
        console.error("Error initializing new deal:", error);
      }
    } else {
      console.warn("Person ID is not set. Cannot initialize deal.");
    }
  };

  /**
   * Initialize the store for updating an existing deal.
   * Fetches the deal details and populates the store's state.
   * @param dealId - The ID of the deal to fetch and edit.
   */
  const initializeExistingDeal = async (dealId: number) => {
    try {
      const deal = await dealHook.fetchDealDetails({ id: dealId, include_relation: true });
      if (deal) {
        // Set isEditMode based on whether deal has attachments
        isEditMode.value = deal.attachments?.length > 0;
        currentDeal.value = deal;

        // Set person ID
        // setPersonId(deal.person_id);

        // Populate attachments and find top-level attachment
        if (deal.attachments?.length) {
          const attachments: Record<number, AttachmentWithChildren> = {};
          deal.attachments?.forEach((attachment) => {
            if (!attachment.parent_id) {
              attachments[attachment.id] = {
                self: attachment,
                children: deal.attachments.filter((child) => child.parent_id === attachment.id),
              };
            }
          });
          pendingAttachments.value = attachments;
        }

        // Set selected parent attachment to the top-level attachment
        // selectedParentAttachment.value = topLevelAttachment;

        // Reset editing attachment
        editingAttachment.value = null;

        // Reset selected product
        selectedProduct.value = null;

        // Populate discounts based on discount_usages
        selectedDiscounts.value = deal.discount_usages?.map((usage) => usage.discount);

        // Construct calculatedDiscount from discount_usages
        const totalDiscountAmount =
          deal.discount_usages?.reduce((sum, usage) => sum + usage.value, 0) ?? 0;

        // Construct discount_amounts object with discount_id as key and value as value
        const discountAmounts =
          deal.discount_usages?.reduce(
            (acc, usage) => {
              acc[usage.discount_id] = usage.value;
              return acc;
            },
            {} as { [key: string]: number },
          ) ?? {};

        calculatedDiscount.value = {
          total_discount_amount: totalDiscountAmount,
          discount_amounts: discountAmounts,
        };

        // Trigger recalculation
        recalculateTrigger.value++;

        // Initialize participants
        initializeParticipants();

        //console.log('Existing deal initialized:', deal);
      } else {
        console.warn(`Deal with ID ${dealId} not found.`);
      }
    } catch (error) {
      console.error("Error fetching deal details:", error);
    }
  };

  /**
   * Toggle the attachment search UI.
   * @param attachment - The attachment to edit or null to close the search.
   */
  const toggleAttachmentSearch = (attachment: AttachmentWithChildren | null) => {
    if (attachment) {
      editingAttachment.value = attachment;
      selectedParentAttachment.value = attachment.self;
    } else {
      editingAttachment.value = null;
      selectedParentAttachment.value = null;
    }
  };

  /**
   * Toggle the product search UI.
   * @param attachment - The attachment to associate the selected product with, or null.
   */
  const toggleProductSearch = (attachment: AttachmentWithChildren | null) => {
    editingAttachment.value = attachment;
    selectedProduct.value =
      attachment && attachment.self.product ? { ...attachment.self.product } : null;
  };

  /**
   * Select a parent attachment for the current editing attachment.
   * Updates the selected attachment with the new deal_id.
   * @param selectedParent - The selected parent attachment.
   */
  const selectParentAttachment = async (selectedParent: AttachmentResponse) => {
    if (selectedParent && currentDeal.value) {
      try {
        // Update the selected parent attachment with the new deal_id
        const updatedAttachment = {
          id: selectedParent.id,
          deal_id: currentDeal.value.id,
        };

        const updated = await updateAttachment(updatedAttachment);
        if (updated) {
          // Update local state
          const attachment = await getAttachment({ id: selectedParent.id });
          pendingAttachments.value[updatedAttachment.id] = {
            self: updated,
            children: attachment ? attachment.children : [],
          };
          //console.log(`Attachment ${updatedAttachment.id} updated with new deal_id: ${currentDeal.value.id}.`);
        }
      } catch (error) {
        console.error("Error updating attachment:", error);
      }

      // Reset state
      editingAttachment.value = null;
      selectedParentAttachment.value = null;
    }
  };

  /**
   * Select a product for the current editing attachment.
   * Updates or adds the attachment accordingly.
   */
  const selectProduct = async () => {
    if (selectedProduct.value && currentDeal.value) {
      if (editingAttachment.value) {
        // Update existing attachment with new product
        const updatedAttachment = {
          ...editingAttachment.value.self,
          product_id: selectedProduct.value.id,
          deal_id: currentDeal.value.id,
        };

        try {
          const updated = await updateAttachment(updatedAttachment);
          if (updated) {
            pendingAttachments.value[updatedAttachment.id] = {
              self: updated,
              children: [],
            };
            //console.log(`Attachment ${updatedAttachment.id} updated with new product.`);
          }
        } catch (error) {
          console.error("Error updating attachment with new product:", error);
        }
      } else {
        // Create a new parent attachment with the selected product
        const parentAttachment = {
          person_id: personId.value,
          kind: "product",
          product_id: selectedProduct.value.id,
          status: AttachmentStatus.TEMP,
          product: { ...selectedProduct.value },
          quantity: 1,
          deal_id: currentDeal.value.id,
        };

        try {
          const addedParentAttachment = await addAttachment(parentAttachment);
          if (addedParentAttachment) {
            pendingAttachments.value[addedParentAttachment.id] = {
              self: addedParentAttachment,
              children: [],
            };
            //console.log(`New parent attachment ${addedParentAttachment.id} added.`);
          }
        } catch (error) {
          console.error("Error adding new parent attachment:", error);
        }
      }

      // Reset state
      selectedProduct.value = null;
      editingAttachment.value = null;
    }
  };

  /**
   * Save or update a participant by role
   * @param role - The role to assign the user to
   * @param userId - The user ID to assign (0 to remove)
   */
  const saveParticipant = async (role: string, userId: number) => {
    if (!currentDeal.value?.id) {
      console.warn("No current deal found. Cannot save participant.");
      return;
    }
    try {
      console.log("saveParticipant", role, userId);
      const previousUserId = participants.value[role];
      console.log("previousUserId", previousUserId);
      // If removing a user (userId = 0)
      if (userId === 0) {
        await dealHook.removeUserFromDeal({
          deal_id: currentDeal.value.id,
          user_id: previousUserId,
          role,
        });
        participants.value[role] = 0;
        return;
      }
      // If previous user exists, remove them first
      if (previousUserId > 0) {
        await dealHook.removeUserFromDeal({
          deal_id: currentDeal.value.id,
          user_id: previousUserId,
          role,
        });
      }
      // Add new user
      if (userId > 0) {
        await dealHook.addUserToDeal({
          deal_id: currentDeal.value.id,
          user_id: userId,
          role,
        });
        participants.value[role] = userId;
      }
    } catch (error) {
      console.error(`Error saving participant for role ${role}:`, error);
    }
  };

  /**
   * Remove an attachment by deleting it and its children.
   * @param id - The ID of the attachment to remove.
   */
  const removeAttachment = async (id: number) => {
    const attachment = pendingAttachments.value[id];
    if (attachment) {
      try {
        const deleted = await removeAttachmentWithChildren(attachment);
        if (deleted) {
          delete pendingAttachments.value[id];
          //console.log(`Attachment ${id} and its children have been deleted.`);
        } else {
          console.warn(`Failed to delete attachment ${id}.`);
        }
      } catch (error) {
        console.error(`Error deleting attachment ${id}:`, error);
      }
    } else {
      console.warn(`Attachment with ID ${id} not found.`);
    }
  };

  /**
   * Remove an attachment and all its children using the delete API.
   * @param attachment - The attachment to delete.
   * @returns A boolean indicating if deletion was successful.
   */
  const removeAttachmentWithChildren = async (
    attachment: AttachmentWithChildren,
  ): Promise<boolean> => {
    const deleteSingleAttachment = async (attachmentToDelete: AttachmentResponse) => {
      try {
        await deleteAttachment({ id: attachmentToDelete.id });
        //console.log(`Attachment ${attachmentToDelete.id} deleted.`);
        return true;
      } catch (error) {
        console.error(`Error deleting attachment ${attachmentToDelete.id}:`, error);
        return false;
      }
    };

    // Delete the main attachment and all its children
    const deletePromises = [
      deleteSingleAttachment(attachment.self),
      ...attachment.children.map((child) => deleteSingleAttachment(child)),
    ];

    try {
      const results = await Promise.all(deletePromises);
      const allDeleted = results.every((result) => result === true);
      if (allDeleted) {
        return true;
      } else {
        console.warn("Some attachments failed to delete.");
        return false;
      }
    } catch (error) {
      console.error("Error deleting attachments with children:", error);
      return false;
    }
  };

  /**
   * Update an attachment and its children with a new status.
   * @param attachment - The attachment to update.
   * @param newStatus - The new status to set.
   * @param paymentMethod - The payment method to apply.
   * @returns The updated attachment with its children or null if failed.
   */
  const updateAttachmentWithChildren = async (
    attachment: AttachmentWithChildren,
    newStatus: AttachmentStatus | CommonStatus,
    paymentMethod: string = "full",
  ): Promise<AttachmentWithChildren | null> => {
    const updateSingleAttachment = async (attachmentToUpdate: AttachmentResponse) => {
      const updatedAttachment = {
        ...attachmentToUpdate,
        status: newStatus,
        discount: paymentMethod === "installment" ? 0 : attachmentToUpdate.discount,
      };

      try {
        const updated = await updateAttachment(updatedAttachment);
        if (updated) {
          return updated;
        }
      } catch (error) {
        console.error(`Error updating attachment ${updatedAttachment.id}:`, error);
      }
      return null;
    };

    // Update the main attachment and all its children
    const updatePromises = [
      updateSingleAttachment(attachment.self),
      ...attachment.children.map((child) => updateSingleAttachment(child)),
    ];

    try {
      const [updated, ...updatedChildren] = await Promise.all(updatePromises);
      if (updated) {
        return {
          self: updated,
          children: updatedChildren.filter((child) => child !== null) as AttachmentResponse[],
        };
      }
    } catch (error) {
      console.error("Error updating attachments with children:", error);
    }

    return null;
  };

  /**
   * Update all child attachments for a given parent attachment.
   * @param parentId - The ID of the parent attachment.
   * @param childAttachments - The array of child attachments to associate.
   */
  function updateAllChildAttachments(parentId: number, childAttachments: AttachmentResponse[]) {
    if (pendingAttachments.value[parentId]) {
      pendingAttachments.value[parentId].children = childAttachments;
      recalculateTrigger.value++;
      //console.log(`All child attachments updated for parent ID ${parentId}.`);
    } else {
      console.warn(`Parent attachment with ID ${parentId} not found.`);
    }
  }

  /**
   * Save all attachments and update the deal status simultaneously.
   * Handles both creation and update modes.
   * @param status - The new status to set for attachments.
   * @param paymentMethod - The payment method to apply.
   * @returns A boolean indicating success or failure.
   */
  const saveAll = async (
    status: AttachmentStatus,
    paymentMethod: string = "full",
  ): Promise<number | false> => {
    if (!currentDeal.value) {
      console.error("No current deal found.");
      return false;
    }

    if (status !== AttachmentStatus.TEMP && status !== AttachmentStatus.DELETED) {
      currentDeal.value.status = CommonStatus.ACTIVE;
    }

    try {
      // Update deal first
      const updatedDeal = await dealHook.updateDeal({
        ...currentDeal.value,
        status: currentDeal.value.state !== DealState.DRAFT ? CommonStatus.ACTIVE : status,
        discounts: selectedDiscounts.value?.map((discount) => discount.id),
      });

      if (!updatedDeal) return false;

      // Update attachments
      const attachmentStatus =
        updatedDeal.state !== DealState.DRAFT ? AttachmentStatus.ACTIVE : status;

      const attachmentUpdates = Object.values(pendingAttachments.value).map((attachment) =>
        updateAttachmentWithChildren(attachment, attachmentStatus, paymentMethod),
      );

      const attachmentResults = await Promise.all(attachmentUpdates);
      const successfulAttachmentUpdates = attachmentResults.filter((result) => result !== null);

      if (successfulAttachmentUpdates.length === Object.keys(pendingAttachments.value).length) {
        pendingAttachments.value = {};

        if (isEditMode.value && currentDeal.value) {
          await initializeExistingDeal(currentDeal.value.id);
        }

        return updatedDeal.id;
      } else {
        console.warn(
          `${successfulAttachmentUpdates.length} out of ${Object.keys(pendingAttachments.value).length} attachments updated successfully.`,
        );
        return false;
      }
    } catch (error) {
      console.error("Error saving all attachments and updating deal status:", error);
      return false;
    }
  };

  /**
   * Save all attachments and update the deal status with rollback on failure.
   * @param status - The new status to set for attachments.
   * @returns A boolean indicating success or failure.
   */
  const saveAllWithRollback = async (status: AttachmentStatus): Promise<boolean> => {
    if (!currentDeal.value) {
      console.error("No current deal found.");
      return false;
    }

    // Deep clone the original state for rollback
    const originalAttachments = JSON.parse(JSON.stringify(pendingAttachments.value));
    const originalDeal = { ...currentDeal.value };

    try {
      // If in edit mode, update the deal's status
      if (isEditMode.value) {
        await dealHook.updateDeal({
          ...currentDeal.value,
          status: CommonStatus.ACTIVE,
        });
        //console.log('Deal status updated.');
      }

      // Update all attachments with the new status
      const attachmentUpdates = Object.values(pendingAttachments.value).map((attachment) =>
        updateAttachmentWithChildren(attachment, status),
      );

      const attachmentResults = await Promise.all(attachmentUpdates);
      const successfulAttachmentUpdates = attachmentResults.filter((result) => result !== null);

      if (successfulAttachmentUpdates.length === Object.keys(pendingAttachments.value).length) {
        //console.log('All attachments and deal status updated successfully.');
        pendingAttachments.value = {};

        if (isEditMode.value && currentDeal.value) {
          // Optionally, refetch the deal to ensure consistency
          await initializeExistingDeal(currentDeal.value.id);
        }

        return true;
      } else {
        throw new Error("Not all updates were successful.");
      }
    } catch (error) {
      console.error("Error during saveAllWithRollback:", error);
      // Rollback to original state
      pendingAttachments.value = originalAttachments;
      currentDeal.value = originalDeal;
      return false;
    }
  };

  /**
   * Clear all data from the store, resetting it to its initial state.
   */
  const clearAll = () => {
    pendingAttachments.value = {};
    editingAttachment.value = null;
    selectedParentAttachment.value = null;
    selectedProduct.value = null;
    tempSelections.value.participants = {};
    currentDeal.value = null;
    selectedDiscounts.value = [];
    calculatedDiscount.value = null;
    isEditMode.value = false;
    participants.value = {
      treatment_doctor: 0,
      consultant_doctor: 0,
      consultant: 0,
      sale: 0,
      doctor_assistant: 0,
    };
    //console.log('All store data cleared.');
  };

  /**
   * Update the quantity of an attachment.
   * @param attachmentId - The ID of the attachment to update.
   * @param newQuantity - The new quantity value.
   * @param parentId - (Optional) The ID of the parent attachment if updating a child.
   */
  const updateQuantity = async (attachmentId: number, newQuantity: number, parentId?: number) => {
    let attachment: AttachmentResponse | undefined;

    if (parentId) {
      // This is a child attachment
      attachment = pendingAttachments.value[parentId]?.children.find(
        (child) => child.id === attachmentId,
      );
    } else {
      // This is a parent attachment
      attachment = pendingAttachments.value[attachmentId]?.self;
    }

    if (attachment) {
      const updatedAttachment = {
        ...attachment,
        quantity: newQuantity,
        deal_id: currentDeal.value?.id,
      };

      try {
        const updated = await updateAttachment(updatedAttachment);
        if (updated) {
          if (parentId) {
            const parentAttachment = pendingAttachments.value[parentId];
            const childIndex = parentAttachment.children.findIndex(
              (child) => child.id === attachmentId,
            );
            if (childIndex !== -1) {
              parentAttachment.children[childIndex] = updated;
              //console.log(`Child attachment ${attachmentId} quantity updated to ${newQuantity}.`);
            }
          } else {
            pendingAttachments.value[attachmentId].self = updated;
            //console.log(`Parent attachment ${attachmentId} quantity updated to ${newQuantity}.`);
          }
        }
      } catch (error) {
        console.error(`Error updating quantity for attachment ${attachmentId}:`, error);
      }
    } else {
      console.warn(`Attachment with ID ${attachmentId} not found.`);
    }

    recalculateTrigger.value++;
  };

  /**
   * Select or deselect a discount.
   * @param discount - The discount to toggle.
   */
  const selectDiscount = async (discount: Discount) => {
    const index = selectedDiscounts.value.findIndex((d) => d.id === discount.id);
    if (index === -1) {
      selectedDiscounts.value.push(discount);
      //console.log(`Discount ${discount.id} selected.`);
    } else {
      selectedDiscounts.value.splice(index, 1);
      //console.log(`Discount ${discount.id} deselected.`);
    }
    // Discounts will be recalculated via watcher
  };

  /**
   * Select multiple discounts at once.
   * @param discountArray - The array of discounts to select.
   */
  const selectDiscounts = (discountArray: Discount[]) => {
    selectedDiscounts.value = discountArray;
    //console.log(`Selected discounts: ${discountArray.map(d => d.id).join(', ')}`);
    // Discounts will be recalculated via watcher
  };

  /**
   * Calculate the total discount based on selected discounts and current deal.
   */
  const calculateDiscounts = async () => {
    if (currentDeal.value && selectedDiscounts.value?.length > 0) {
      try {
        calculatedDiscount.value = await calculateDiscount({
          deal_id: currentDeal.value.id,
          discount_ids: selectedDiscounts.value.map((d) => d.id),
        });
        //console.log('Discounts calculated:', calculatedDiscount.value);
      } catch (error) {
        console.error("Error calculating discounts:", error);
        calculatedDiscount.value = null;
      }
    } else {
      calculatedDiscount.value = null;
      //console.log('No discounts to calculate.');
    }
  };

  // Tạo một computed property để tính toán độ dài của pendingAttachments
  const pendingAttachmentsLength = computed(() => Object.keys(pendingAttachments.value).length);

  /**
   * Watcher to monitor changes in pendingAttachments.
   * Clears discounts and logs initialized attachments.
   * TODO: fix sau, hiện tại nếu để watch lại khi edit nó clear hết discount
   */
  /*watch(
    pendingAttachmentsLength,
    (newAttachments) => {
      clearDiscounts();
      Object.values(newAttachments).forEach((attachment) => {
      });
    },
    {
      immediate: true
    }
  );*/

  // Recalculate discounts whenever selectedDiscounts or pendingAttachments change
  watch(
    [selectedDiscounts, pendingAttachments],
    () => {
      calculateDiscounts();
    },
    { deep: true },
  );

  // ------------------------------
  // Computed Properties
  // ------------------------------

  // Check if there are any pending attachments
  const hasPendingAttachments = computed(() => Object.keys(pendingAttachments.value).length > 0);

  // Get the product ID of the currently editing attachment
  const productId = computed(() => editingAttachment.value?.self.product_id ?? 0);

  // Get all product IDs from pending attachments
  const productIds = computed(() =>
    Object.values(pendingAttachments.value).map((attachment) => attachment.self.product_id),
  );

  // Get the ID of the currently editing attachment
  const editingAttachmentId = computed(() =>
    editingAttachment.value ? editingAttachment.value.self.id : 0,
  );

  /**
   * Compute the total value of the deal, applying any discounts.
   * If attachment.self.price > 0, use attachment.self.price * quantity.
   * Else, use attachment.self.product.price * quantity.
   * Similarly for child attachments.
   */
  const totalValue = computed(() => {
    recalculateTrigger.value;
    let total = Object.values(pendingAttachments.value).reduce((total, attachment) => {
      const selfPrice =
        (attachment.self.price > 0 ? attachment.self.price : attachment.self.product?.price || 0) *
        (attachment.self.quantity || 1);
      const childrenPrice = attachment.children.reduce((childTotal, child) => {
        return (
          childTotal +
          (child.price > 0 ? child.price : child.product?.price || 0) * (child.quantity || 1)
        );
      }, 0);
      return total + selfPrice + childrenPrice;
    }, 0);

    // Apply calculated discount if available
    if (calculatedDiscount.value && calculatedDiscount.value.total_discount_amount > 0) {
      total -= calculatedDiscount.value.total_discount_amount;
    }
    return total;
  });

  /**
   * Update the discount of an attachment. // ONLY 'FULL' PAYMENT METHOD
   * @param attachmentId - The ID of the attachment to update.
   * @param newDiscount - The new discount value.
   * @param parentId - (Optional) The ID of the parent attachment if updating a child.
   */
  const updateDiscount = async (attachmentId: number, newDiscount: number, parentId?: number) => {
    let attachment: AttachmentResponse | undefined;

    if (parentId) {
      // This is a child attachment
      attachment = pendingAttachments.value[parentId]?.children.find(
        (child) => child.id === attachmentId,
      );
    } else {
      // This is a parent attachment
      attachment = pendingAttachments.value[attachmentId]?.self;
    }

    if (attachment) {
      const updatedAttachment = {
        ...attachment,
        discount: newDiscount,
        deal_id: currentDeal.value?.id,
      };

      try {
        const updated = await updateAttachment(updatedAttachment);
        if (updated) {
          if (parentId) {
            const parentAttachment = pendingAttachments.value[parentId];
            const childIndex = parentAttachment.children.findIndex(
              (child) => child.id === attachmentId,
            );
            if (childIndex !== -1) {
              parentAttachment.children[childIndex] = updated;
            }
          } else {
            pendingAttachments.value[attachmentId].self = updated;
          }
        }
      } catch (error) {
        console.error(`Error updating discount for attachment ${attachmentId}:`, error);
      }
    } else {
      console.warn(`Attachment with ID ${attachmentId} not found.`);
    }

    recalculateTrigger.value++;
  };

  // Tổng discount từ các attachments
  const totalAttachmentDiscounts = computed(() => {
    recalculateTrigger.value;
    return Object.values(pendingAttachments.value).reduce((total, attachment) => {
      // Parent discount
      const selfDiscount = attachment.self.discount || 0;

      // Children discounts
      const childrenDiscount = attachment.children.reduce(
        (acc, child) => acc + (child.discount || 0),
        0,
      );

      return total + selfDiscount + childrenDiscount;
    }, 0);
  });

  // Tổng giá trị sau khi trừ discount
  const finalValue = computed(() => {
    let total = totalValue.value;

    // Trừ discount từ attachments
    if (totalAttachmentDiscounts.value > 0) {
      total -= totalAttachmentDiscounts.value;
    }

    return total;
  });

  /**
   * Auto-select discounts with meta.auto_check = true when there are pending attachments
   */
  const autoSelectDiscounts = async () => {
    // Only proceed if we have pending attachments
    if (!hasPendingAttachments.value) {
      return;
    }

    // Only fetch if we haven't already
    if (discounts.value.length === 0) {
      await listDiscounts({});
    }

    // Find discounts with meta.auto_check = true
    const autoDiscounts = discounts.value.filter((discount) => discount.meta?.auto_check);

    if (autoDiscounts.length > 0) {
      // Filter out any already selected discounts to avoid duplicates
      const newAutoDiscounts = autoDiscounts.filter(
        (autoDiscount) =>
          !selectedDiscounts.value?.some?.((selected) => selected.id === autoDiscount.id),
      );

      if (newAutoDiscounts.length > 0) {
        // Add auto-selected discounts to the current selection
        selectDiscounts([...(selectedDiscounts.value ?? []), ...newAutoDiscounts]);
        //console.log("Auto-selected discounts:", newAutoDiscounts.map(d => d.name));
      }
    }
  };

  // Watch for changes to hasPendingAttachments
  watch(hasPendingAttachments, (hasAttachments, oldValue) => {
    // Only trigger when transitioning from false to true (no attachments -> has attachments)
    if (hasAttachments && !oldValue && !isEditMode.value) {
      // Extra safeguard: don't auto-select in edit mode
      autoSelectDiscounts();
    }
  });

  /**
   * Initialize participants from existing deal
   */
  const initializeParticipants = () => {
    if (!currentDeal.value?.deal_assignment) return;

    // Reset participants
    participants.value = {
      treatment_doctor: 0,
      consultant_doctor: 0,
      consultant: 0,
      sale: 0,
      doctor_assistant: 0,
    };

    // Map deal_assignment to participants
    currentDeal.value.deal_assignment.forEach((assignment) => {
      console.log("assignment", assignment);
      participants.value[assignment.role] = assignment.user_id;
    });
  };

  // ------------------------------
  // Return Object
  // ------------------------------

  return {
    // ------------------------------
    // State
    // ------------------------------
    setPersonId,
    initializeDeal,
    initializeExistingDeal,

    pendingAttachments,
    editingAttachment,
    selectedParentAttachment,
    selectedProduct,
    tempSelections,
    recalculateTrigger,

    // Current deal being managed
    currentDeal,

    // Discounts state
    selectedDiscounts,
    calculatedDiscount,

    // Edit mode flag
    isEditMode,

    // Discounts for attachments
    attachmentDiscounts,

    // Add this to the store state section
    participants,

    // ------------------------------
    // Computed Properties
    // ------------------------------
    hasPendingAttachments,
    productId,
    productIds,
    editingAttachmentId,
    totalValue,
    totalAttachmentDiscounts,
    finalValue,

    // ------------------------------
    // Actions
    // ------------------------------
    clearDiscounts,

    toggleAttachmentSearch,
    toggleProductSearch,
    selectParentAttachment,
    selectProduct,
    saveParticipant,
    removeAttachment, // Updated to use removeAttachmentWithChildren
    updateAllChildAttachments,
    saveAll,
    saveAllWithRollback,
    clearAll,
    updateQuantity,
    updateDiscount,
    calculateDiscounts,

    // Discount-related methods
    selectDiscount,
    selectDiscounts,

    // Attachment discount methods
    setAttachmentDiscount,
    clearAttachmentDiscounts,

    // New function
    autoSelectDiscounts,

    // Add these functions to the actions section
    initializeParticipants,
  };
});
