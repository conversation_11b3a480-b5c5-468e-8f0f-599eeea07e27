import { defineStore } from "pinia";
import { shallowRef } from "vue";

import type {
  AttachmentDataResponse,
  ClearAttachmentDataRequest,
  GetAttachmentDataRequest,
  SetAttachmentDataRequest,
} from "@/api/bcare-types-v2";
import {
  attachment_dataClear,
  attachment_dataGet,
  attachment_dataSet
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useAttachmentDataStore = defineStore("attachmentData", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const currentData = shallowRef<AttachmentDataResponse | null>(null);

  // Actions
  function clearAttachmentData(req: ClearAttachmentDataRequest) {
    return performAsyncAction(async () => {
      const response = await attachment_dataClear(req);
      return response.data;
    });
  }

  function getAttachmentData(req: GetAttachmentDataRequest) {
    return performAsyncAction(async () => {
      const response = await attachment_dataGet(req);
      currentData.value = response.data ?? null;
      return response.data;
    });
  }

  function setAttachmentData(req: SetAttachmentDataRequest) {
    return performAsyncAction(async () => {
      const response = await attachment_dataSet(req);
      return response.data;
    });
  }

  return {
    // State
    currentData,
    isLoading,
    error,
    // Actions
    clearAttachmentData,
    getAttachmentData,
    setAttachmentData,
  };
});
