import { defineStore } from "pinia";
import { ref } from "vue";

import { DepartmentListRequest, DepartmentResponse } from "@/api/bcare-types-v2";
import { departmentList } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useDepartmentStore = defineStore("department", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();
  // state
  const departments = ref<DepartmentResponse[]>([]);
  //getters

  // actions
  function fetchDepartments(req: DepartmentListRequest) {
    return performAsyncAction(async () => {
      const res = await departmentList(req);
      const data = res.data?.departments || [];
      departments.value = data;
    });
  }

  return {
    // state
    isLoading,
    error,
    departments,
    // actions
    fetchDepartments,
  };
});
