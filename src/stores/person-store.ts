import { defineStore } from "pinia";

import { PersonAddRequest } from "@/api/bcare-types-v2";
import { personAdd } from "@/api/bcare-v2";

export const usePersonStore = defineStore("user", () => {
  const addPerson = async (req: PersonAddRequest) => personAdd(req);
  // const getPersonList = async (req: PersonListRequest) => await personList(req);
  // const getPerson = async (req: PersonGetRequest) => await personGet(req);
  return {
    addPerson,
    // getPersonList,
    // getPerson
  };
});
