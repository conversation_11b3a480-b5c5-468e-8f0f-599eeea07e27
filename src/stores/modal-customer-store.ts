import { defineStore } from "pinia";
import { computed,ref } from "vue";

import type { PersonResponse } from "@/api/bcare-types-v2";

export const useModalCustomerStore = defineStore("modalCustomer", () => {
  const visible = ref(false);
  const personId = ref<string | null>(null);
  const person = ref<PersonResponse | null>(null);

  const isVisible = computed(() => visible.value);

  const openModal = (id: string) => {
    personId.value = id;
    visible.value = true;
  };

  const closeModal = () => {
    visible.value = false;
    personId.value = null;
    person.value = null;
  };

  const setPerson = (data: PersonResponse) => {
    person.value = data;
  };

  return {
    visible,
    personId,
    person,
    isVisible,
    openModal,
    closeModal,
    setPerson,
  };
});
