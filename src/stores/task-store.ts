import { isArray, mergeWith } from "lodash";
import { defineStore } from "pinia";
import { computed, reactive, ref, shallowRef } from "vue";

import { TaskPriorityEnum } from "@/api/bcare-enum";
import {
  BulkDeleteRequest,
  TaskAssignment,
  TaskAssignmentAddRequest,
  TaskAssignmentDeleteRequest,
  TaskListRequest,
  TaskNoteAddRequest,
  TaskNoteDeleteRequest,
  TaskNoteUpdateRequest,
  TaskResponse,
} from "@/api/bcare-types-v2";
import {
  AssignTasksRequest,
  BulkUpdateResult,
  BulkUpdateTasksRequest,
  TaskAddRequest,
  TaskAssignmentDynamicQuery,
  TaskAssignmentUpdateRequest,
  TaskDepartmentAddRequest,
  TaskDepartmentDeleteRequest,
  TaskDepartmentDynamicQuery,
  TaskDepartmentUpdateRequest,
  TaskDynamicQuery,
  TaskNoteListRequest,
  TaskUpdateRequest,
} from "@/api/bcare-types-v2";
import { taskList } from "@/api/bcare-v2";
import {
  task_assignmentAssignTasks,
  taskAdd,
  taskAddNote,
  taskBulkDelete,
  taskBulkUpdate,
  taskDeleteNote,
  taskListNote,
  taskQuery,
  taskUpdate,
  taskUpdateNote,
} from "@/api/bcare-v2";
import {
  task_assignmentAdd,
  task_assignmentDelete,
  task_assignmentQuery,
  task_assignmentUpdate,
} from "@/api/bcare-v2";
import {
  task_departmentAdd,
  task_departmentDelete,
  task_departmentQuery,
  task_departmentUpdate,
} from "@/api/bcare-v2";
import { taskGet } from "@/api/bcare-v2";

export const taskDynamicQuery: TaskDynamicQuery = {
  table: "task_serial_view",
  sort: [
    {
      field: "task_serial_view.start_date",
      order: "DESC",
    },
    {
      field: "task_serial_view.serial",
      order: "DESC",
    },
  ],
  limit: 30,
  offset: 0,
};

export const taskAddPayload: TaskAddRequest = {
  title: "",
  note: "",
  start_date: "",
  end_date: "",
  due_date: "",
  type: "",
  priority: TaskPriorityEnum.MEDIUM,
  parent_id: undefined,
  person_id: 0,
  deal_id: undefined,
  department_id: undefined,
  appointment_id: undefined,
  users: [],
  cron_expression: undefined,
};

export const useTaskStore = defineStore("task", () => {
  // State
  const tasks = shallowRef<TaskResponse[]>([]);
  const dynamicQuery = shallowRef<Partial<TaskDynamicQuery>>({ ...taskDynamicQuery });
  const savedQuery = reactive<Record<string, any>>({});
  const task = reactive<TaskResponse | {}>({});
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const total = ref<Number>(0);

  // Getters
  const getTasks = computed(() => tasks);

  // Helper function for actions
  const initValue = () => {
    isLoading.value = true;
    error.value = null;
  };

  async function performAsyncAction<T>(action: () => Promise<T>): Promise<T | null> {
    initValue();
    try {
      return await action();
    } catch (err) {
      error.value = (err as Error).message;
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  // Actions
  function fetchTasks(req: TaskListRequest) {
    return performAsyncAction(async () => {
      const response = await taskList(req);
      if (response.data) {
        tasks.value = response.data.tasks;
      }
      return response.data;
    });
  }

  function fetchQueryTasks(
    req: TaskDynamicQuery,
    isReset: boolean = false,
    isTableQuery: boolean = false,
  ) {
    let payload: TaskDynamicQuery;
    if (isReset) {
      payload = { ...taskDynamicQuery, ...req };
      savedQuery.value = payload; // Update savedQuery only when resetting
    } else if (isTableQuery) {
      // Custom merge function to handle arrays
      const customMerge = (objValue: any, srcValue: any) => {
        if (isArray(objValue)) {
          return srcValue;
        }
      };

      // Merge savedQuery and req, with req taking precedence
      payload = mergeWith({}, savedQuery.value, req, customMerge);

      // Handle filters separately
      if (req.filters) {
        const currentFilterFields = new Set(req.filters.map((f) => f.field));
        payload.filters = [
          ...req.filters,
          ...(savedQuery.value.filters || []).filter(
            (f: { field: string | undefined }) => !currentFilterFields.has(f.field),
          ),
        ];
      }
    } else {
      payload = { ...dynamicQuery.value, ...req };
    }

    dynamicQuery.value = payload;

    // Do not update savedQuery here to preserve it

    return performAsyncAction(async () => {
      const response = await taskQuery(payload);

      const countResponse = await taskQuery({
        ...payload,
        sort: [],
        limit: 1,
        offset: 0,
        aggregations: [
          {
            field: "id",
            function: "COUNT",
            alias: "count",
          },
        ],
      });
      if (countResponse.code === 0) {
        total.value = countResponse.data?.result?.count || 0;
      }

      if (response.code === 0 && typeof response.data === "string") {
        tasks.value = JSON.parse(response.data).rows || [];
      } else {
        tasks.value = [];
      }

      if (response.code === 0 && response.data) {
        tasks.value = response.data?.result?.rows || [];
      } else {
        tasks.value = [];
      }

      return response.data;
    });
  }

  async function deleteTasks(request: BulkDeleteRequest) {
    try {
      const response = await taskBulkDelete(request);
      if (response.code === 0) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  async function updateStatusTasks(request: BulkUpdateTasksRequest) {
    try {
      const response = await taskBulkUpdate(request);
      if (response.code === 0) {
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  function refetchTasks() {
    fetchQueryTasks(dynamicQuery.value as TaskDynamicQuery);
  }

  function addTask(request: TaskAddRequest) {
    return performAsyncAction(async () => {
      const response = await taskAdd(request);
      return response.data;
    });
  }

  function updateTask(request: TaskUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await taskUpdate(request);
      return response.data;
    });
  }

  async function addTaskAssignment(assignment: TaskAssignmentAddRequest) {
    return performAsyncAction(async () => {
      const response = await task_assignmentAdd(assignment);
      return response.data;
    });
  }

  async function updateTaskAssignment(assignment: TaskAssignmentUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await task_assignmentUpdate(assignment);
      return response.data;
    });
  }

  async function queryTaskAssignments(query: TaskAssignmentDynamicQuery) {
    return performAsyncAction(async () => {
      const response = await task_assignmentQuery(query);
      return response.data;
    });
  }

  async function deleteTaskAssignment(request: TaskAssignmentDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await task_assignmentDelete(request);
      return response.data;
    });
  }

  // Task Department Assignment Actions
  async function addTaskDepartmentAssignment(assignment: TaskDepartmentAddRequest) {
    return performAsyncAction(async () => {
      const response = await task_departmentAdd(assignment);
      return response.data;
    });
  }

  async function updateTaskDepartmentAssignment(assignment: TaskDepartmentUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await task_departmentUpdate(assignment);
      return response.data;
    });
  }

  async function queryTaskDepartmentAssignments(query: TaskDepartmentDynamicQuery) {
    return performAsyncAction(async () => {
      const response = await task_departmentQuery(query);
      return response.data;
    });
  }

  async function deleteTaskDepartmentAssignment(request: TaskDepartmentDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await task_departmentDelete(request);
      return response.data;
    });
  }

  async function bulkUpdateTasks(
    request: BulkUpdateTasksRequest,
  ): Promise<BulkUpdateResult | null> {
    return performAsyncAction(async () => {
      const response = await taskBulkUpdate(request);
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return null;
    });
  }

  async function addTaskNote(request: TaskNoteAddRequest) {
    return performAsyncAction(async () => {
      const response = await taskAddNote(request);
      return response.data;
    });
  }

  async function updateTaskNote(request: TaskNoteUpdateRequest) {
    return performAsyncAction(async () => {
      const response = await taskUpdateNote(request);
      return response.data;
    });
  }

  async function deleteTaskNote(request: TaskNoteDeleteRequest) {
    return performAsyncAction(async () => {
      const response = await taskDeleteNote(request);
      return response.data;
    });
  }

  async function listTaskNotes(request: TaskNoteListRequest) {
    return performAsyncAction(async () => {
      const response = await taskListNote(request);
      return response.data;
    });
  }

  async function bulkUpdateTaskAssignments(
    request: AssignTasksRequest,
  ): Promise<TaskAssignment | null> {
    return performAsyncAction(async () => {
      const response = await task_assignmentAssignTasks(request);
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return null;
    });
  }

  async function getTask(taskId: number) {
    return performAsyncAction(async () => {
      const response = await taskGet({ id: taskId });
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return null;
    });
  }

  return {
    // State
    tasks,
    task,
    isLoading,
    error,
    total,
    savedQuery,
    // Getters
    getTasks,
    // Actions
    fetchTasks,
    fetchQueryTasks,
    refetchTasks,
    deleteTasks,
    updateStatusTasks,
    addTask,
    updateTask,
    addTaskAssignment,
    updateTaskAssignment,
    queryTaskAssignments,
    deleteTaskAssignment,
    addTaskDepartmentAssignment,
    updateTaskDepartmentAssignment,
    queryTaskDepartmentAssignments,
    deleteTaskDepartmentAssignment,
    bulkUpdateTasks,
    bulkUpdateTaskAssignments,
    getTask,
    addTaskNote,
    updateTaskNote,
    deleteTaskNote,
    listTaskNotes,
  };
});
