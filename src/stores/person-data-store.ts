import { defineStore } from "pinia";
import { shallowRef } from "vue";

import type {
  ClearDataRequest,
  DataResponse,
  GetDataRequest,
  SetDataRequest,
} from "@/api/bcare-types-v2";
import { person_dataClearData, person_dataGetData, person_dataSetData } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const usePersonDataStore = defineStore("personData", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const currentData = shallowRef<DataResponse | null>(null);

  // Actions
  function clearData(req: ClearDataRequest) {
    return performAsyncAction(async () => {
      const response = await person_dataClearData(req);
      return response.data;
    });
  }

  function getData(req: GetDataRequest) {
    return performAsyncAction(async () => {
      const response = await person_dataGetData(req);
      currentData.value = response.data ?? null;
      return response.data;
    });
  }

  function setData(req: SetDataRequest) {
    return performAsyncAction(async () => {
      const response = await person_dataSetData(req);
      return response.data;
    });
  }

  return {
    // State
    currentData,
    isLoading,
    error,
    // Actions
    clearData,
    getData,
    setData,
  };
});
