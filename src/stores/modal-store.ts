import { defineStore } from "pinia";
import { computed, ref } from "vue";

type ModalName = string;

interface ModalState {
  activeModals: Map<ModalName, Record<string, any>>;
}

export const useModalStore = defineStore("modal", () => {
  const state = ref<ModalState>({
    activeModals: new Map(),
  });

  const showModal = (modalName: ModalName, props: Record<string, any> = {}): void => {
    state.value.activeModals.set(modalName, props);
  };

  const hideModal = (modalName: ModalName): void => {
    state.value.activeModals.delete(modalName);
  };

  const isModalVisible = computed(() => (modalName: ModalName): boolean => {
    return state.value.activeModals.has(modalName);
  });

  const getModalProps = computed(() => (modalName: ModalName): Record<string, any> => {
    return state.value.activeModals.get(modalName) || {};
  });

  return {
    showModal,
    hideModal,
    isModalVisible,
    getModalProps,
  };
});
