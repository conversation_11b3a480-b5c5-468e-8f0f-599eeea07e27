import dayjs from "dayjs";
import { defineStore } from "pinia";
import { reactive, onUnmounted, ref } from "vue";

import { PERSON_COUNT_QUERY, PERSON_QUERY } from "@/constants/sql/person-queries";
import { ParamConfig, useRawQuery } from "@/hooks/useRawQuery";
import type { UserShort } from "@/api/bcare-types-v2";
import { PERSON_EXPORT_HEADERS } from "@/constants/columns/person-columns";
import { useAuthStore } from "./auth-store";
import service from "@/api/http";

interface PersonFilters {
  searchString: string | null;
  tagIds: number[] | null;
  relatedUsers: UserShort[] | null;
  createdDateRange: { start: Date | string | null; end: Date | string | null };
  sourceId: number | null;
  stageId: number | null;
  dealName: string | null;

  presetFilterType: "new" | "returning" | "refunded" | null;
  presetDateRange: { start: Date | string | null; end: Date | string | null };
}

export const PERSON_FILTER_PRESETS = {
  ALL: "all",
  NEW: "new",
  RETURNING: "returning",
  REFUNDED: "refunded",
} as const;

export type PersonFilterPreset = (typeof PERSON_FILTER_PRESETS)[keyof typeof PERSON_FILTER_PRESETS];

// --- Default Sorting ---
const defaultOrderClause = "p.created_at DESC";
const presetOrderClause = "track_begin DESC";

export const usePersonDatatableStore = defineStore("personDatatable", () => {
  const authStore = useAuthStore();
  const personQuery = PERSON_QUERY;
  const countQuery = PERSON_COUNT_QUERY;
  const isExporting = ref(false);

  const paramConfig: Record<string, ParamConfig> = {
    presetDateFrom: { type: "timestamp", placeholder: "$1", default: null },
    presetDateTo: { type: "timestamp", placeholder: "$2", default: null },
    presetFilterType: { type: "string", placeholder: "$3", default: null },
    searchString: { type: "string", placeholder: "$4", default: null },
    tagIds: { type: "array", placeholder: "$5", arrayElementType: "int", default: null },
    relatedUserIds: { type: "array", placeholder: "$6", arrayElementType: "int", default: null },
    createdDateFrom: { type: "timestamp", placeholder: "$7", default: null },
    createdDateTo: { type: "timestamp", placeholder: "$8", default: null },
    sourceId: { type: "int", placeholder: "$9", default: null },
    stageId: { type: "int", placeholder: "$10", default: null },
    dealName: { type: "string", placeholder: "$11", default: null },
    limit: { type: "int", placeholder: "$12", isLimit: true, default: 30 },
    offset: { type: "int", placeholder: "$13", isOffset: true, default: 0 },
    orderClause1: {
      type: "string",
      template: true,
      default: defaultOrderClause,
    },
  };

  const {
    data: persons,
    loading,
    error,
    rowCount: totalPersons,
    executionTime,
    params,
    execute,
    executeExport,
    exportLoading,
    refresh,
    resetParams: resetRawQueryParams,
    pagination,
    executeCount,
  } = useRawQuery(personQuery, paramConfig, {
    pageSize: 30,
    timeout: 60000,
    countQuery: countQuery,
    onSuccess: (data) => {
      console.log("Person query succeeded:", data);
    },
    onError: (err) => {
      console.error("Error executing person query:", err);
    },
  });

  const filters = reactive<PersonFilters>(_getDefaultFilterState());

  const offsetParamKey = Object.entries(paramConfig).find(([_, config]) => config.isOffset)?.[0];

  function _applyFiltersToParams() {
    params.searchString = filters.searchString;
    params.dealName = filters.dealName;
    params.tagIds = filters.tagIds && filters.tagIds.length > 0 ? filters.tagIds : null;
    params.relatedUserIds =
      filters.relatedUsers && filters.relatedUsers.length > 0
        ? filters.relatedUsers.map((user) => user.id)
        : null;
    params.sourceId = filters.sourceId;
    params.stageId = filters.stageId;

    params.createdDateFrom = filters.createdDateRange?.start
      ? dayjs(filters.createdDateRange.start).startOf("day").add(1, "day").toDate()
      : null;
    params.createdDateTo = filters.createdDateRange?.start
      ? dayjs(filters.createdDateRange.end ?? filters.createdDateRange.start)
          .endOf("day")
          .toDate()
      : null;

    params.presetDateFrom = filters.presetDateRange?.start
      ? dayjs(filters.presetDateRange.start).startOf("day").add(1, "day").toDate()
      : null;
    params.presetDateTo = filters.presetDateRange?.start
      ? dayjs(filters.presetDateRange.end ?? filters.presetDateRange.start)
          .endOf("day")
          .toDate()
      : null;

    params.presetFilterType = filters.presetFilterType;

    if (offsetParamKey && pagination.page !== 1) {
      pagination.page = 1;
      params[offsetParamKey] = 0;
    } else if (offsetParamKey && params[offsetParamKey] !== 0) {
      params[offsetParamKey] = 0;
    }
  }

  function _getDefaultFilterState(): PersonFilters {
    return {
      searchString: null,
      tagIds: null,
      relatedUsers: null,
      createdDateRange: { start: null, end: null },
      sourceId: null,
      stageId: null,
      dealName: null,
      presetFilterType: null,
      presetDateRange: { start: null, end: null },
    };
  }

  async function applyFilters() {
    _applyFiltersToParams();
    if (!params.orderClause1) {
      params.orderClause1 = defaultOrderClause;
    }
    return await execute();
  }

  async function resetFilters() {
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    _applyFiltersToParams();
    params.orderClause1 = defaultOrderClause;
    return await execute();
  }

  async function clearFilter(filterKey: keyof PersonFilters) {
    const defaultState = _getDefaultFilterState();
    const currentFilterValue = filters[filterKey];
    const defaultFilterValue = defaultState[filterKey];

    if (JSON.stringify(currentFilterValue) === JSON.stringify(defaultFilterValue)) {
      console.log(`Filter ${filterKey} is already in default state.`);
      return;
    }

    (filters[filterKey] as any) = defaultFilterValue;
    await applyFilters();
  }

  const filterKeyMap: Record<string, keyof PersonFilters> = {
    created_at: "createdDateRange",
    track_begin: "presetDateRange",
    full_name: "searchString",
    person_source: "sourceId",
    stage_name: "stageId",
    tags: "tagIds",
    related_users: "relatedUsers",
    deal_name: "dealName",
  };

  async function clearFilterByKey(datatableFilterKey: string) {
    console.log("Attempting to clear filter for key:", datatableFilterKey);
    const storeFilterKey = filterKeyMap[datatableFilterKey];
    if (storeFilterKey) {
      console.log("Mapped to store filter key:", storeFilterKey);
      await clearFilter(storeFilterKey);
    } else {
      console.warn(`No store mapping found for DataTable filter key: ${datatableFilterKey}`);
    }
  }

  async function loadInitialData() {
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    _applyFiltersToParams();
    params.orderClause1 = defaultOrderClause;
    return await execute();
  }

  function resetStoreState() {
    persons.value = [];
    totalPersons.value = 0;
    Object.assign(filters, _getDefaultFilterState());
    resetRawQueryParams();
    error.value = null;
    if (offsetParamKey) params[offsetParamKey] = 0;
    pagination.page = 1;
    params.orderClause1 = defaultOrderClause;
  }

  onUnmounted(() => {
    resetStoreState();
  });

  function $reset() {
    resetStoreState();
  }

  function $dispose() {
    $reset();
  }

  async function applyFilterPreset(filterType: PersonFilterPreset /*, userId?: number */) {
    filters.presetFilterType = filterType === PERSON_FILTER_PRESETS.ALL ? null : filterType;

    _applyFiltersToParams();

    if (filterType === PERSON_FILTER_PRESETS.ALL) {
      params.orderClause1 = defaultOrderClause;
    } else {
      params.orderClause1 = presetOrderClause;
    }

    return execute();
  }

  async function triggerPersonExport() {
    if (isExporting.value) return;

    const currentUser = authStore.currentUser;
    isExporting.value = true;

    try {
      const response = await executeExport(PERSON_EXPORT_HEADERS);

      if (response?.data?.job_id && currentUser?.id) {
        const blob = await service.downloadBlob("/v1/export/download", {
          job_id: response.data.job_id,
          user_id: currentUser.id,
        });

        const today = dayjs().format("DD-MM-YYYY");
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `ds_khach_hang_${today}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }

      return response;
    } catch (err) {
      console.error("Error during export:", err);
      throw err;
    } finally {
      isExporting.value = false;
    }
  }

  return {
    persons,
    loading,
    exportLoading,
    error,
    totalPersons,
    executionTime,
    pagination,
    filters,

    applyFilters,
    resetFilters,
    clearFilterByKey,
    applyFilterPreset,
    execute,
    executeExport,
    refresh,
    loadInitialData,
    executeCount,
    triggerPersonExport,
    $reset,
    $dispose,
  };
});
