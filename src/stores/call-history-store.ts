import { defineStore } from "pinia";
import { reactive, toRefs } from "vue";

import { CallDynamicQuery, CallResponse } from "@/api/bcare-types-v2";
import { callQuery } from "@/api/bcare-v2";

interface State {
  totalCalls: {
    inbound: number;
    outbound: number;
    missInbound: number;
    missOutbound: number;
  };
  history: { data: CallResponse[]; total: number };
}

interface ListParams {
  pageNo: number;
  pageSize: number;
}

export const useCallHistoryStore = defineStore("callHistoryStore", () => {
  const state = reactive<State>({
    totalCalls: {
      inbound: 0,
      outbound: 0,
      missInbound: 0,
      missOutbound: 0,
    },
    history: {
      data: [],
      total: 0,
    },
  });

  const fetchCallList = async (callFilter: CallDynamicQuery, { pageNo, pageSize }: ListParams) => {
    const count = await callQuery({
      ...callFilter,
      sort: [],
      aggregations: [
        {
          field: "id",
          function: "COUNT",
          alias: "count",
        },
      ],
    });
    if (typeof count.data === "string") {
      state.history.total = Math.ceil(
        Number(JSON.parse(count.data).rows[0].count) / (callFilter.limit ?? 1),
      );
    }
    const res = await callQuery({
      ...callFilter,
      limit: pageSize,
      offset: (pageNo - 1) * pageSize,
    });
    if (typeof res.data === "string")
      try {
        state.history.data = JSON.parse(res.data).rows;
      } catch (error) {
        console.error("Lỗi khi parse chuỗi JSON:", error);
      }
  };

  const fetchTotalCall = async (countFilter: CallDynamicQuery) => {
    const promises = [
      callQuery({ ...countFilter, state: "inbound" }),
      callQuery({ ...countFilter, state: "outbound" }),
      callQuery({ ...countFilter, state: "miss_outbound" }),
      callQuery({ ...countFilter, state: "miss_inbound" }),
    ];

    const [countInbound, countOutbound, countMissOutbound, countMissInbound] =
      await Promise.all(promises);

    if (typeof countInbound.data === "string") {
      state.totalCalls.inbound = JSON.parse(countInbound.data).count;
    }

    if (typeof countOutbound.data === "string") {
      state.totalCalls.outbound = JSON.parse(countOutbound.data).count;
    }

    if (typeof countMissOutbound.data === "string") {
      state.totalCalls.missOutbound = JSON.parse(countMissOutbound.data).count;
    }

    if (typeof countMissInbound.data === "string") {
      state.totalCalls.missInbound = JSON.parse(countMissInbound.data).count;
    }
  };

  return { ...toRefs(state), fetchTotalCall, fetchCallList };
});
