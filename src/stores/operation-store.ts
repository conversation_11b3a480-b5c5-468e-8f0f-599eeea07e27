// src/store/operation.ts
import { useStorage } from "@vueuse/core";
import { defineStore } from "pinia";
import { computed, shallowRef } from "vue";

import { OperationResponse, ProductOperation } from "@/api/bcare-types-v2";
import { operationAll } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

interface OperationData {
  [id: number]: OperationResponse;
}

interface CacheData {
  data: OperationData;
  expireTime: number;
}

const CACHE_EXPIRATION = 24 * 60 * 60 * 1000; // 24 giờ

export const useOperationStore = defineStore("operation_v2", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  const operationData = shallowRef<OperationData>({});

  const cachedData = useStorage<CacheData>("operationStoreCache", { data: {}, expireTime: 0 });

  function initializeFromCache() {
    if (cachedData.value.expireTime > Date.now()) {
      operationData.value = cachedData.value.data;
    }
  }

  function updateCache() {
    cachedData.value = {
      data: operationData.value,
      expireTime: Date.now() + CACHE_EXPIRATION,
    };
  }

  function fetchAllOperations() {
    return performAsyncAction(async () => {
      const response = await operationAll();
      if (response.code === 0 && response.data) {
        const newOperationData: OperationData = {};
        response.data.operations.forEach((operation) => {
          if (operation.id) {
            newOperationData[operation.id] = operation;
          }
        });

        operationData.value = newOperationData;
        updateCache();
        return newOperationData;
      }
      throw new Error("Failed to fetch operations");
    });
  }

  // Getters
  const getOperationById = computed(
    () => (id: number): OperationResponse | undefined => operationData.value[id]
  );

  const getOperationNameById = computed(
    () => (id: number): string => operationData.value[id]?.name || "Unknown"
  );

  const getProductOperationsForOperation = computed(
    () => (operationId: number): ProductOperation[] => {
      const operation = operationData.value[operationId];
      return operation ? operation.product_operation : [];
    }
  );

  async function refreshOperations() {
    // Xóa cache
    cachedData.value = { data: {}, expireTime: 0 };
    operationData.value = {};

    // Tải lại dữ liệu
    return await fetchAllOperations();
  }

  return {
    operationData,
    isLoading,
    error,
    initializeFromCache,
    fetchAllOperations,
    getOperationById,
    getOperationNameById,
    getProductOperationsForOperation,

    refreshOperations,
  };
});
