import dayjs from "dayjs";
import { defineStore } from "pinia";
import { onUnmounted, reactive } from "vue";

import { TaskPriorityEnum, TaskStateEnum } from "@/api/bcare-enum";
import {
  AssignTasksRequest,
  BulkDeleteRequest,
  BulkUpdateResult,
  BulkUpdateTasksRequest,
  TaskAssignment,
  TaskAssignmentResponse,
  TaskResponse,
  TaskUpdateRequest,
  UserShort,
} from "@/api/bcare-types-v2";
import {
  task_assignmentAssignTasks,
  taskBulkDelete,
  taskBulkUpdate,
  taskGet,
  taskUpdate,
} from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";
import { usePermissions } from "@/composables/usePermissions";
import { TASK_COUNT_QUERY, TASK_QUERY } from "@/constants/sql/task-queries";
import { ParamConfig, useRawQuery } from "@/hooks/useRawQuery";
import { TASK_VIEW_TYPES, type TaskViewType } from "@/pages/task/constants";
import { useAuthStore } from "@/stores/auth-store";
import { useNotiStore } from "@/stores/notification";

export const useTaskStore = defineStore("tasks", () => {
  // Initialize composables once at the store level
  const notiStore = useNotiStore();
  const { hasAdministrativePrivileges } = usePermissions();
  const authStore = useAuthStore();

  const taskQuery = TASK_QUERY;
  const countQuery = TASK_COUNT_QUERY;
  // Định nghĩa cấu hình params
  const paramConfig: Record<string, ParamConfig> = {
    // Các tham số filter
    taskTitle: {
      type: "string",
      placeholder: "$1",
      default: null,
    },
    personName: {
      type: "string",
      placeholder: "$2",
      default: null,
    },
    startDateFrom: {
      type: "date",
      placeholder: "$3",
      default: null,
    },
    startDateTo: {
      type: "date",
      placeholder: "$4",
      default: null,
    },
    endDateFrom: {
      type: "date",
      placeholder: "$5",
      default: null,
    },
    endDateTo: {
      type: "date",
      placeholder: "$6",
      default: null,
    },
    primaryAssignees: {
      type: "array",
      placeholder: "$7",
      arrayElementType: "int",
      default: null,
    },
    creators: {
      type: "array",
      placeholder: "$8",
      arrayElementType: "int",
      default: null,
    },
    contributors: {
      type: "array",
      placeholder: "$9",
      arrayElementType: "int",
      default: null,
    },
    overdueBefore: {
      type: "date",
      placeholder: "$10",
      default: null,
    },
    completionStatus: {
      type: "string",
      placeholder: "$11",
      default: null,
    },
    priority: {
      type: "int",
      placeholder: "$12",
      default: null,
    },
    state: {
      type: "string",
      placeholder: "$13",
      default: null,
    },
    limit: {
      type: "int",
      placeholder: "$14",
      isLimit: true,
      default: 50,
    },
    offset: {
      type: "int",
      placeholder: "$15",
      isOffset: true,
      default: 0,
    },

    // Template variable cho order clause
    orderClause1: {
      type: "string",
      template: true,
      default: "t.start_date DESC, ta.serial DESC",
    },
    // Template variable cho order clause
    orderClause2: {
      type: "string",
      template: true,
      default: "td.start_date DESC, td.serial DESC",
    },
  };

  // Sử dụng useRawQuery với countQuery
  const {
    data: tasks,
    loading,
    error,
    rowCount: totalTasks,
    executionTime,
    params,
    execute,
    refresh,
    resetParams,
    setParams,
    pagination,
  } = useRawQuery(taskQuery, paramConfig, {
    pageSize: 50,
    timeout: 100,
    countQuery: countQuery, // Thêm câu query count
    onSuccess: (data) => {
      console.log("Query executed successfully:", data);
    },
    onError: (err) => {
      console.error("Error executing query:", err);
    },
  });

  // Các filter helpers
  const filters = reactive({
    current: {
      taskTitle: null as string | null,
      personName: null as string | null,
      dateRange: {
        start: null as Date | string | null,
        end: null as Date | string | null,
      },
      endDateRange: {
        start: null as Date | string | null,
        end: null as Date | string | null,
      },
      primaryAssignees: [] as number[],
      creators: [] as number[],
      contributors: [] as number[],
      overdueBefore: null as Date | string | null,
      completionStatus: null as string | null,
      priority: null as number | null,
      state: null as string | null,
    },

    // Các options cho filters
    priorityOptions: [
      { value: 0, label: "Thấp" },
      { value: 1, label: "Trung bình" },
      { value: 2, label: "Cao" },
      { value: 3, label: "Khẩn cấp" },
    ],

    completionStatusOptions: [
      { value: "completed", label: "Đã hoàn thành" },
      { value: "incomplete", label: "Chưa hoàn thành" },
    ],

    stateOptions: [
      { value: TaskStateEnum.NEW_TASK, label: "Mới tạo" },
      { value: TaskStateEnum.IN_PROGRESS, label: "Đang thực hiện" },
      { value: TaskStateEnum.AWAITING_APPROVAL, label: "Chờ phê duyệt" },
      { value: TaskStateEnum.COMPLETED, label: "Hoàn thành" },
      { value: TaskStateEnum.CANCELLED, label: "Đã hủy" },
      { value: TaskStateEnum.COMPLETED_EARLY, label: "Hoàn thành sớm" },
    ],

    // Hàm helper để áp dụng các giá trị filter vào params
    _applyToParams() {
      params.taskTitle = this.current.taskTitle || null;
      params.personName = this.current.personName || null;

      // Handle start date range with dayjs
      if (this.current.dateRange.start) {
        params.startDateFrom = this.current.dateRange.start;

        if (!this.current.dateRange.end) {
          // For single date selection, set end date to next day
          params.startDateTo = dayjs(this.current.dateRange.start).add(1, "day").toDate();
        } else {
          // For date range, add one day to end date to include full day
          params.startDateTo = dayjs(this.current.dateRange.end).add(1, "day").toDate();
        }
      } else {
        params.startDateFrom = null;
        params.startDateTo = null;
      }

      // Handle end date range with dayjs
      if (this.current.endDateRange.start) {
        params.endDateFrom = this.current.endDateRange.start;

        if (!this.current.endDateRange.end) {
          // For single date selection, set end date to next day
          params.endDateTo = dayjs(this.current.endDateRange.start).add(1, "day").toDate();
        } else {
          // For date range, add one day to end date to include full day
          params.endDateTo = dayjs(this.current.endDateRange.end).add(1, "day").toDate();
        }
      } else {
        params.endDateFrom = null;
        params.endDateTo = null;
      }

      // Handle overdueBefore date
      if (this.current.overdueBefore) {
        params.overdueBefore = this.current.overdueBefore;
      } else {
        params.overdueBefore = null;
      }

      params.primaryAssignees =
        this.current.primaryAssignees?.length > 0 ? this.current.primaryAssignees : null;
      params.creators = this.current.creators?.length > 0 ? this.current.creators : null;
      params.contributors =
        this.current.contributors?.length > 0 ? this.current.contributors : null;
      params.completionStatus = this.current.completionStatus || null;
      params.priority =
        this.current.priority !== null && this.current.priority !== undefined
          ? this.current.priority
          : null;
      params.state = this.current.state || null;

      // Reset pagination
      pagination.page = 1;
      params.offset = 0;
    },

    // Trạng thái mặc định của filters
    _getDefaultState() {
      return {
        taskTitle: null,
        personName: null,
        dateRange: { start: null, end: null },
        endDateRange: { start: null, end: null },
        primaryAssignees: [],
        creators: [],
        contributors: [],
        overdueBefore: null,
        completionStatus: null,
        priority: null,
        state: null,
      };
    },

    // Phương thức áp dụng filter
    apply() {
      this._applyToParams();
      return execute();
    },

    // Reset tất cả filters
    reset() {
      this.current = this._getDefaultState();
      resetParams();
      return this;
    },

    // Áp dụng filter mới
    set(newFilters = {}) {
      Object.assign(this.current, newFilters);
      return this;
    },

    // Hàm kết hợp: reset, set, và apply
    update(newFilters = {}) {
      this.current = this._getDefaultState();
      if (newFilters) {
        Object.assign(this.current, newFilters);
      }
      this._applyToParams();
      return execute();
    },
  });

  // Phương thức để thay đổi sắp xếp
  const updateSort = (orderClause1: string, orderClause2: string) => {
    setParams({
      orderClause1: orderClause1 || "t.start_date DESC, ta.serial DESC",
      orderClause2: orderClause2 || "td.start_date DESC, td.serial DESC",
    });
    return execute();
  };

  // Phương thức để tải dữ liệu ban đầu
  const loadInitialData = async () => {
    // Reset filters to defaults and set default sorting in one call
    setParams({
      orderClause1: "t.start_date ASC, ta.serial ASC",
      orderClause2: "td.start_date ASC, td.serial ASC",
      completionStatus: "un_completed", // Set completion status directly
    });

    // Reset pagination
    pagination.page = 1;
    params.offset = 0;

    // Execute query
    return await execute();
  };

  // Add a reset function to clean up state
  function resetState() {
    // Reset data
    tasks.value = [];
    totalTasks.value = 0;

    // Reset filters
    filters.reset();

    // Reset pagination
    pagination.page = 1;
    params.offset = 0;

    // Reset order clauses
    params.orderClause1 = "t.start_date DESC, ta.serial DESC";
    params.orderClause2 = "td.start_date DESC, td.serial DESC";
  }

  // Replace the current onUnmounted with proper Pinia lifecycle hooks

  // This runs when the store is no longer in use (all components using it are unmounted)
  function $dispose() {
    resetState();
  }

  onUnmounted(() => {
    resetState();
  });

  // This runs when the store is reset (useful for testing or manual resets)
  function $reset() {
    resetState();
  }

  // Refactored bulk operations using useAsyncAction
  const bulkDeleteTasks = async (request: BulkDeleteRequest): Promise<boolean> => {
    const { performAsyncAction } = useAsyncAction({
      onError: (error) => {
        console.error("Error deleting tasks:", error);
        notiStore.error({ message: "Xóa công việc thất bại" });
      },
      onSuccess: () => {
        notiStore.success({ message: "Xóa công việc thành công" });
      },
    });

    return await performAsyncAction(async () => {
      const response = await taskBulkDelete(request);
      if (response.code === 0) {
        return true;
      }
      throw new Error("Failed to delete tasks");
    });
  };

  const bulkUpdateTasks = async (
    request: BulkUpdateTasksRequest,
  ): Promise<BulkUpdateResult | null> => {
    const { performAsyncAction } = useAsyncAction({
      onError: (error) => {
        console.error("Error updating tasks:", error);
        notiStore.error({ message: "Cập nhật công việc thất bại" });
      },
      onSuccess: () => {
        notiStore.success({ message: "Cập nhật công việc thành công" });
      },
    });

    return await performAsyncAction(async () => {
      const response = await taskBulkUpdate(request);
      if (response.code === 0 && response.data) {
        return response.data;
      }
      throw new Error("Failed to update tasks");
    });
  };

  const bulkUpdateTaskAssignments = async (
    request: AssignTasksRequest,
  ): Promise<TaskAssignment | null> => {
    const { performAsyncAction } = useAsyncAction({
      onError: (error) => {
        console.error("Error assigning tasks:", error);
        notiStore.error({ message: "Gán công việc thất bại" });
      },
      onSuccess: () => {
        notiStore.success({ message: "Gán công việc thành công" });
      },
    });

    return await performAsyncAction(async () => {
      const response = await task_assignmentAssignTasks(request);
      if (response.code === 0 && response.data) {
        return response.data;
      }
      throw new Error("Failed to assign tasks");
    });
  };

  // Thêm phương thức để xử lý filter từ DataTable
  const applyDataTableFilters = (dataTableFilters: any) => {
    filters
      .set({
        taskTitle: dataTableFilters.title.value,
        personName: dataTableFilters.person_id.value,
        dateRange: {
          start: dataTableFilters.start_date.value ? dataTableFilters.start_date.value[0] : null,
          end: dataTableFilters.start_date.value ? dataTableFilters.start_date.value[1] : null,
        },
        endDateRange: {
          start: dataTableFilters.due_date.value ? dataTableFilters.due_date.value[0] : null,
          end: dataTableFilters.due_date.value ? dataTableFilters.due_date.value[1] : null,
        },
      })
      .apply();
  };

  // Thêm phương thức để xử lý filter theo loại view
  const applyFilterByViewType = async (filterType: TaskViewType, userId: number) => {
    // Instead of resetting all filters, keep existing DataTable filters
    // filters.reset();

    // Save existing DataTable filters we want to preserve
    const preservedFilters = {
      taskTitle: filters.current.taskTitle,
      personName: filters.current.personName,
      dateRange: filters.current.dateRange,
      endDateRange: filters.current.endDateRange,
      state: filters.current.state,
      // Other filters are controlled by view type selection
    };

    // Reset specific filters that should be controlled by view type
    filters.current.primaryAssignees = [];
    filters.current.creators = [];
    filters.current.contributors = [];
    filters.current.overdueBefore = null;
    filters.current.completionStatus = null;
    filters.current.priority = null;

    // Apply new filters based on type
    switch (filterType) {
      case TASK_VIEW_TYPES.ALL:
        // All tasks: Show incomplete tasks, ordered by start date (recent to older)
        filters.current.completionStatus = "un_completed";
        setParams({
          orderClause1: "t.start_date ASC, ta.serial ASC",
          orderClause2: "td.start_date ASC, td.serial ASC",
        });
        break;

      case TASK_VIEW_TYPES.PRIMARY:
        // My tasks: All incomplete tasks where user is primary (recent to older)
        filters.current.primaryAssignees = [userId];
        filters.current.completionStatus = "un_completed";
        setParams({
          orderClause1: "t.start_date ASC, ta.serial ASC",
          orderClause2: "td.start_date ASC, td.serial ASC",
        });
        break;

      case TASK_VIEW_TYPES.CREATOR:
        // Assigned tasks: Tasks created by user (older to recent)
        filters.current.creators = [userId];
        setParams({
          orderClause1: "t.start_date ASC, ta.serial ASC",
          orderClause2: "td.start_date ASC, td.serial ASC",
        });
        break;

      case TASK_VIEW_TYPES.CONTRIBUTOR:
        // Keep existing order
        filters.current.contributors = [userId];
        filters.current.completionStatus = "un_completed";
        break;

      case TASK_VIEW_TYPES.SLOW_PROCESS:
        // Overdue tasks (recent to older)
        filters.current.overdueBefore = new Date();
        setParams({
          orderClause1: "t.start_date ASC, ta.serial ASC",
          orderClause2: "td.start_date ASC, td.serial ASC",
        });
        break;

      case TASK_VIEW_TYPES.COMPLETE:
        // Keep existing order
        filters.current.completionStatus = "completed";
        break;

      case TASK_VIEW_TYPES.PRIORITY_LOW:
        filters.current.priority = TaskPriorityEnum.LOW;
        filters.current.primaryAssignees = [userId];
        break;

      case TASK_VIEW_TYPES.PRIORITY_MEDIUM:
        filters.current.priority = TaskPriorityEnum.MEDIUM;
        filters.current.primaryAssignees = [userId];
        break;

      case TASK_VIEW_TYPES.PRIORITY_HIGH:
        filters.current.priority = TaskPriorityEnum.HIGH;
        filters.current.primaryAssignees = [userId];
        break;
    }

    // Restore preserved filters
    Object.entries(preservedFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        (filters.current as Record<string, any>)[key] = value;
      }
    });

    // Always reset to page 1 when applying filters
    pagination.page = 1;
    params.offset = 0;

    // Apply filters and execute query
    return await filters.apply();
  };

  // Thêm phương thức để xử lý filter assignments
  const applyAssignmentsFilter = (userIds: number[]) => {
    filters
      .set({
        primaryAssignees: userIds.length ? userIds : [],
      })
      .apply();
  };

  // Add a helper function to filter by state
  const applyStateFilter = (state: TaskStateEnum | null) => {
    filters
      .set({
        state: state,
      })
      .apply();
  };

  // Permission utilities

  /**
   * Get primary user IDs from a task's assignments
   */
  const getPrimaryUserIdsForTask = (task: TaskResponse): number[] => {
    if (!task?.assignments || !Array.isArray(task.assignments)) return [];
    return task.assignments.filter((a) => a.role === "primary" && a.user_id).map((a) => a.user_id);
  };

  /**
   * Get primary users from task assignments
   */
  const getPrimaryUsersFromTask = (assignments?: TaskAssignmentResponse[]): UserShort[] => {
    if (!assignments || !Array.isArray(assignments)) return [];
    return assignments
      .filter((item) => item?.role === "primary" && item?.user_id)
      .map((item) => item.user)
      .filter(Boolean);
  };

  /**
   * Check if current user can edit task status
   */
  const canUserEditTaskStatus = (task: TaskResponse): boolean => {
    // Admins and department managers can always edit
    if (hasAdministrativePrivileges()) return true;

    const currentUserId = authStore.currentUser?.id;
    if (!currentUserId) return false;

    // Check if user is creator
    if (task.creator_id === currentUserId) return true;

    // Check if user is primary assignee
    const primaryUserIds = getPrimaryUserIdsForTask(task);
    return primaryUserIds.includes(currentUserId);
  };

  /**
   * Check if current user can edit multiple tasks' status
   */
  const canUserEditMultipleTasksStatus = (tasks: TaskResponse[]): boolean => {
    // Admins and department managers can always edit
    if (hasAdministrativePrivileges()) return true;

    // If no tasks selected, return false
    if (!tasks.length) return false;

    const currentUserId = authStore.currentUser?.id;
    if (!currentUserId) return false;

    // Check if current user is creator or primary assignee for ALL selected tasks
    return tasks.every((task) => {
      // Check if user is creator
      if (task.creator_id === currentUserId) return true;

      // Check if user is primary assignee
      const primaryUserIds = getPrimaryUserIdsForTask(task);
      return primaryUserIds.includes(currentUserId);
    });
  };

  /**
   * Check if user can delete multiple tasks (admin or department manager)
   */
  const canUserDeleteMultipleTasks = (): boolean => {
    return hasAdministrativePrivileges();
  };

  /**
   * Check if user can delete a specific task
   * (admin, department manager, or creator of the task)
   */
  const canUserDeleteTask = (task: TaskResponse): boolean => {
    // Admin or department manager can always delete
    if (hasAdministrativePrivileges()) return true;

    // Task creator can delete their own task
    return isTaskCreator(task);
  };

  /**
   * Check if current user is the creator of a task
   */
  const isTaskCreator = (task: TaskResponse): boolean => {
    const currentUserId = authStore.currentUser?.id;
    return !!currentUserId && task.creator_id === currentUserId;
  };

  /**
   * Check if user can only edit note and state of a task
   * (is assignee but not creator or admin)
   */
  const canUserEditNoteOnly = (task: TaskResponse): boolean => {
    // If user has full edit permissions, this function should return false
    if (hasAdministrativePrivileges()) return false;

    // If user is creator, they have full edit permissions
    if (isTaskCreator(task)) return false;

    // Check if user is primary assignee (can edit note only)
    const primaryUserIds = getPrimaryUserIdsForTask(task);
    const currentUserId = authStore.currentUser?.id;
    return !!currentUserId && primaryUserIds.includes(currentUserId);
  };

  /**
   * Check if user has creator-only permissions for a task
   * (only creator or admin/manager can access, primary assignees cannot)
   */
  const hasCreatorOnlyPermission = (task: TaskResponse): boolean => {
    // Admins and department managers can always access
    if (hasAdministrativePrivileges()) return true;

    // Only task creator has permission
    return isTaskCreator(task);
  };

  /**
   * Check if user has creator-only permissions for multiple tasks
   * (must be creator or admin/manager for ALL tasks)
   */
  const hasCreatorOnlyPermissionForMultiple = (tasks: TaskResponse[]): boolean => {
    // Admins and department managers can always access
    if (hasAdministrativePrivileges()) return true;

    // If no tasks selected, return false
    if (!tasks.length) return false;

    // Must be creator of ALL tasks
    return tasks.every((task) => isTaskCreator(task));
  };

  // Task operations

  /**
   * Update a single task's state
   */
  const updateTaskState = async (taskId: number, newState: TaskStateEnum): Promise<boolean> => {
    try {
      const result = await bulkUpdateTasks({
        id_list: [taskId],
        state: newState,
      });

      if (result) {
        refresh();
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to update task state:", error);
      return false;
    }
  };

  /**
   * Update a task with the given request
   */
  function updateTask(request: TaskUpdateRequest) {
    const { performAsyncAction } = useAsyncAction({
      onError: (error) => {
        console.error("Error updating task:", error);
        notiStore.error({ message: "Cập nhật công việc thất bại" });
      },
      onSuccess: () => {
        notiStore.success({ message: "Cập nhật công việc thành công" });
      },
    });

    return performAsyncAction(async () => {
      const response = await taskUpdate(request);
      return response.data;
    });
  }

  /**
   * Get a task by ID
   */
  async function getTask(taskId: number) {
    const { performAsyncAction } = useAsyncAction({
      onError: (error) => {
        console.error("Error fetching task:", error);
        notiStore.error({ message: "Không thể tải thông tin công việc" });
      },
    });

    return performAsyncAction(async () => {
      const response = await taskGet({ id: taskId });
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return null;
    });
  }

  /**
   * Delete a single task by ID with confirmation
   */
  const deleteTaskWithConfirmation = (task: TaskResponse, confirmFn: any): void => {
    confirmFn.require({
      message: `Bạn có chắc chắn muốn xóa công việc "${task.title}"?`,
      header: "Xác nhận xóa",
      icon: "pi pi-exclamation-triangle",
      acceptClass: "p-button-danger",
      accept: async () => {
        try {
          const result = await bulkDeleteTasks({ id_list: [task.id] });
          if (result) {
            refresh();
          }
        } catch (error) {
          console.error("Failed to delete task:", error);
        }
      },
      reject: () => {
        /* do nothing */
      },
    });
  };

  /**
   * Delete multiple tasks with confirmation
   */
  const deleteMultipleTasksWithConfirmation = (taskIds: number[], confirmFn: any): void => {
    if (!canUserDeleteMultipleTasks()) {
      return;
    }

    confirmFn.require({
      message: "Bạn có muốn xóa các công việc đã chọn?",
      rejectProps: {
        label: "Huỷ",
        severity: "secondary",
        outlined: true,
      },
      acceptProps: {
        label: "Xoá",
        severity: "danger",
      },
      accept: async () => {
        try {
          const result = await bulkDeleteTasks({ id_list: taskIds });
          if (result) {
            refresh();
          }
        } catch (error) {
          console.error("Failed to delete tasks:", error);
        }
      },
    });
  };

  /**
   * Update status for multiple tasks
   */
  const updateMultipleTasksStatus = async (
    taskIds: number[],
    newState: TaskStateEnum,
  ): Promise<boolean> => {
    try {
      const result = await bulkUpdateTasks({
        id_list: taskIds,
        state: newState,
      });

      if (result) {
        refresh();
        return true;
      }
      return false;
    } catch (err) {
      console.error("Failed to update tasks status:", err);
      return false;
    }
  };

  /**
   * Get all primary assignee IDs from multiple tasks
   */
  const getAllPrimaryAssigneeIdsFromTasks = (tasks: TaskResponse[]): number[] => {
    const ids: number[] = [];
    tasks.forEach((task) => {
      ids.push(...getPrimaryUserIdsForTask(task));
    });
    return [...new Set(ids)]; // Remove duplicates
  };

  return {
    // Data
    tasks,
    loading,
    error,
    totalTasks,
    executionTime,

    // Pagination
    pagination,

    // Filter helpers
    filters,

    // Methods
    refresh,
    execute,
    resetParams,
    setParams,
    loadInitialData,
    updateSort,

    // Bulk operations
    bulkDeleteTasks,
    bulkUpdateTasks,
    bulkUpdateTaskAssignments,

    // Filter methods
    applyFilterByViewType,
    applyDataTableFilters,
    applyAssignmentsFilter,
    applyStateFilter,

    // Permission utilities
    hasAdministrativePrivileges,
    getPrimaryUserIdsForTask,
    getPrimaryUsersFromTask,
    canUserEditTaskStatus,
    canUserEditMultipleTasksStatus,
    canUserDeleteMultipleTasks,
    canUserDeleteTask,
    isTaskCreator,
    canUserEditNoteOnly,
    hasCreatorOnlyPermission,
    hasCreatorOnlyPermissionForMultiple,

    // Task operations
    updateTaskState,
    updateTask,
    getTask,

    // Add new functions
    deleteTaskWithConfirmation,
    deleteMultipleTasksWithConfirmation,
    updateMultipleTasksStatus,
    getAllPrimaryAssigneeIdsFromTasks,

    // Add reset and dispose functions to the public API
    resetState,
    $reset,
    $dispose,
  };
});
