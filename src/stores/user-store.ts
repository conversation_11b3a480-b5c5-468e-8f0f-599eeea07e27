// src/store/user.ts
import { defineStore } from "pinia";
import { onMounted, reactive } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import { userList } from "@/api/bcare-v2";

interface State {
  users: UserResponse[];
  error?: number;
  loading?: boolean;
  invalidate: boolean;
}

export const useUserConfigsStore = defineStore("userConfig", () => {
  const state = reactive<State>({
    users: [],
    invalidate: false,
    error: 0,
    loading: false,
  });

  const getUserConfigs = async () => {
    try {
      if (state.loading) return;
      state.loading = true;
      if (!state.invalidate) state.invalidate = true;
      const response = await userList({
        page_size: 0,
        page: 1,
        search: "",
        filter: {
          username: "",
          name: "",
          email: "",
          gender: "",
          department_id: 0,
          phone: "",
          status: 0,
        },
        order_by: "",
      });
      if (response.code === 0) {
        if (response.data) state.users = response.data.users;
      } else state.error = response.code;
    } catch (error) {
      state.error = error as number;
    } finally {
      state.loading = false;
    }
  };

  onMounted(() => {
    if (!state.invalidate) getUserConfigs();
  });

  return {
    state,
    getUserConfigs,
  };
});
