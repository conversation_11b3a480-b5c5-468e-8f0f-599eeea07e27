import { defineStore } from "pinia";
import { ref, shallowRef } from "vue";

import type { HistoryListRequest, HistoryRecord } from "@/api/bcare-types-v2";
import { historyGetHistoryList } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useHistoryStore = defineStore("history", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // State
  const histories = shallowRef<HistoryRecord[]>([]);
  const total = ref<number>(0);
  const totalPage = ref<number>(0);

  // Actions
  function listHistories(req: HistoryListRequest) {
    return performAsyncAction(async () => {
      const response = await historyGetHistoryList(req);
      if (response.data) {
        histories.value = response.data.histories;
        total.value = response.data.total;
        totalPage.value = response.data.total_page;
      } else {
        // Reset on error or no data
        histories.value = [];
        total.value = 0;
        totalPage.value = 0;
      }
      return response.data;
    });
  }

  return {
    // State
    histories,
    total,
    totalPage,
    isLoading,
    error,
    // Actions
    listHistories,
  };
});
