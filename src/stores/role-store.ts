import { defineStore } from "pinia";
import { ref } from "vue";

import type { Role } from "@/api/bcare-types-v2";
import { casbinRoleAdd, casbinRoleList } from "@/api/bcare-v2";
import { useAsyncAction } from "@/composables/useAsyncAction";

export const useRoleStore = defineStore("role", () => {
  const { isLoading, error, performAsyncAction } = useAsyncAction();

  // state
  const roles = ref<Role[]>([]);

  // actions
  function fetchRoles() {
    return performAsyncAction(async () => {
      const res = await casbinRoleList();
      const data = res.data || [];
      roles.value = data;
    });
  }

  function addRole(role: Role) {
    return performAsyncAction(async () => {
      const res = await casbinRoleAdd(role);
      if (res.data) {
        roles.value.push(res.data);
      }
      return res.data;
    });
  }

  function getRoleByName(name: string): Role | undefined {
    return roles.value.find((role) => role.name === name);
  }

  return {
    // state
    isLoading,
    error,
    roles,
    // actions
    fetchRoles,
    addRole,
    getRoleByName,
  };
});
