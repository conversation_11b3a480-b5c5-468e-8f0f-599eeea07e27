import Cookie from "js-cookie";
import { jwtDecode } from "jwt-decode";
import { defineStore } from "pinia";
import { computed, ref } from "vue";

import { LoginResponse, VerifyResponse } from "@/api/bcare-types-v2";
import { authLogin, authVerify } from "@/api/bcare-v2";
import { COOKIE_ENUM } from "@/enums/cookie-enum";
import router from "@/router";

interface DecodedToken {
  exp: number;
  iat: number;
  id: any;
  [key: string]: any;
}

export interface UserBasicInfo {
  id: number;
  name: string;
  username: string;
  profile_image?: string;
  department_id?: number;
  department_position?: string;
  roles?: string[];
}

export const useAuthStore = defineStore("auth", () => {
  const user = ref<LoginResponse | VerifyResponse | null>(null);
  const accessToken = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const otpStatus = ref<{
    pending: boolean;
    sentTo: string | null;
    expiresIn: number | null;
  }>({
    pending: false,
    sentTo: null,
    expiresIn: null,
  });

  const isLoggedIn = computed(() => !!user.value);

  const currentUser = computed((): UserBasicInfo | null => {
    if (user.value && user.value.user) {
      return {
        id: user.value.user.id,
        name: user.value.user.name,
        username: user.value.user.username,
        profile_image: user.value.user.profile_image,
        department_id: user.value.user.department_id,
        department_position: user.value.user.department_position,
        roles: user.value.user.roles,
      };
    }
    return null;
  });

  function setUser(newUser: LoginResponse | VerifyResponse) {
    user.value = newUser;
    const userDataToSave: UserBasicInfo = {
      id: newUser.user.id,
      name: newUser.user.name,
      username: newUser.user.username,
      profile_image: newUser.user.profile_image,
      department_id: newUser.user.department_id,
      department_position: newUser.user.department_position,
      roles: newUser.user.roles,
    };
    Cookie.set(COOKIE_ENUM.USER, JSON.stringify(userDataToSave), {
      expires: new Date(newUser.refresh_expire * 1000),
      secure: true,
      sameSite: "strict",
    });
  }

  function setAccessToken(newToken: string | null, expires: number) {
    if (!newToken) return;
    accessToken.value = newToken;
    Cookie.set(COOKIE_ENUM.ACCESS_TOKEN, newToken, {
      expires: new Date(expires * 1000),
    });
  }

  function setRefreshToken(newToken: string | null, expires: number) {
    if (!newToken) return;
    refreshToken.value = newToken;
    Cookie.set(COOKIE_ENUM.REFRESH_TOKEN, newToken, {
      expires: new Date(expires * 1000),
    });
  }

  async function login(username: string, password: string): Promise<boolean | "otp_required"> {
    try {
      const response = await authLogin({ username, password });
      if (response && response.data && response.code === 0) {
        const loginData = response.data;

        if (loginData.status === "pending_verification") {
          otpStatus.value = {
            pending: true,
            sentTo: loginData.sent_to,
            expiresIn: loginData.expires_in,
          };
          return "otp_required";
        }

        setAccessToken(loginData.access_token, loginData.access_expire);
        setRefreshToken(loginData.refresh_token, loginData.refresh_expire);
        setUser(loginData);
        await router.push("/");
        return true;
      }
      return false;
    } catch (error) {
      console.error("Login error:", error);
      return false;
    }
  }

  async function verifyOtp(username: string, otp: string): Promise<boolean> {
    try {
      const response = await authVerify({ username, otp });
      if (response && response.data && response.code === 0) {
        const loginData = response.data;
        setAccessToken(loginData.access_token, loginData.access_expire);
        setRefreshToken(loginData.refresh_token, loginData.refresh_expire);
        setUser(loginData);
        otpStatus.value = { pending: false, sentTo: null, expiresIn: null };
        await router.push("/");
        return true;
      }
      return false;
    } catch (error) {
      console.error("OTP verification error:", error);
      return false;
    }
  }

  function logout() {
    clearUser();
    clearTokens();
  }

  function clearUser() {
    user.value = null;
    Cookie.remove(COOKIE_ENUM.USER);
  }

  function clearTokens() {
    accessToken.value = null;
    refreshToken.value = null;
    Cookie.remove(COOKIE_ENUM.ACCESS_TOKEN);
    Cookie.remove(COOKIE_ENUM.REFRESH_TOKEN);
  }

  function getUserFromCookie(): UserBasicInfo | null {
    const storedUser = Cookie.get(COOKIE_ENUM.USER);
    if (storedUser) {
      try {
        return JSON.parse(storedUser) as UserBasicInfo;
      } catch (error) {
        console.error("Error parsing user from cookie:", error);
      }
    }
    return null;
  }

  function initializeFromCookies() {
    const storedAccessToken = Cookie.get(COOKIE_ENUM.ACCESS_TOKEN);
    const storedRefreshToken = Cookie.get(COOKIE_ENUM.REFRESH_TOKEN);
    const storedUser = getUserFromCookie();

    if (storedAccessToken) accessToken.value = storedAccessToken;
    if (storedRefreshToken) refreshToken.value = storedRefreshToken;
    if (storedUser) {
      user.value = { user: storedUser } as LoginResponse;
    }
  }

  function isTokenExpired(): boolean {
    const token = Cookie.get(COOKIE_ENUM.ACCESS_TOKEN);
    if (!token) return true;

    try {
      const decodedToken = jwtDecode<DecodedToken>(token);
      const currentTime = Math.floor(Date.now() / 1000);
      return currentTime >= decodedToken.exp - 30;
    } catch (error) {
      console.error("Error decoding token:", error);
      return true;
    }
  }

  // Gọi initializeFromCookies khi store được tạo
  initializeFromCookies();

  return {
    user,
    accessToken,
    refreshToken,
    isLoggedIn,
    currentUser,
    setUser,
    setAccessToken,
    setRefreshToken,
    login,
    logout,
    clearUser,
    clearTokens,
    initializeFromCookies,
    isTokenExpired,
    getUserFromCookie,
    otpStatus,
    verifyOtp,
  };
});
