// src/stores/notificationStore.ts
import { defineStore } from "pinia";
import { ref } from "vue";

export type NotificationType = "success" | "warning" | "error" | "info";

export interface SimpleNotification {
  id: number;
  type: NotificationType;
  title: string;
  message: string;
}

export type NotiPayload = {
  type?: NotificationType;
  title?: string;
  message: string;
};

// const defaultTimeout = 2000;

const createNoti = (
  type: NotificationType,
  title: string,
  message: string,
): SimpleNotification => ({
  id: Math.random() * 1000,
  type,
  title,
  message,
});

export const useNotiStore = defineStore("notification", () => {
  const notifications = ref<SimpleNotification[]>([]);

  function add(payload: NotiPayload) {
    const { type: originType, title: originTitle, message } = payload;
    const title = originTitle === undefined ? "Info" : originTitle;
    const type = originType === undefined ? "info" : originType;

    const noti = createNoti(type, title, message);

    notifications.value.push(noti);
  }

  function next() {
    if (notifications.value.length > 0) {
      const [firstNotification] = notifications.value.splice(0, 1);
      return firstNotification;
    }
    return null;
  }

  function success(payload: NotiPayload) {
    payload.type = "success";
    add(payload);
  }

  function warning(payload: NotiPayload) {
    payload.title = "Warning";
    payload.type = "warning";
    add(payload);
  }

  function error(payload: NotiPayload) {
    payload.type = "error";
    add(payload);
  }

  function info(payload: NotiPayload) {
    payload.type = "info";
    add(payload);
  }

  return {
    notifications,
    add,
    next,
    success,
    warning,
    error,
    info,
  };
});
