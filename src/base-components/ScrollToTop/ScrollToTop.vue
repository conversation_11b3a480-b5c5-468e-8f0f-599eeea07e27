<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useScroll, useThrottleFn } from "@vueuse/core";
import Button from "primevue/button";

const props = withDefaults(defineProps<{
  /**
   * The element to track scrolling on. If not provided, uses document.documentElement
   */
  target?: HTMLElement | null;
  /**
   * Threshold in pixels to show the button
   */
  threshold?: number;
  /**
   * Position of the button (bottom-right, bottom-left, etc.)
   */
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left";
  /**
   * Button appearance
   */
  severity?: "primary" | "secondary" | "success" | "info" | "warning" | "help" | "danger";
  /**
   * Whether to use outline style for the button
   */
  outlined?: boolean;
  /**
   * Whether to use smooth scrolling
   */
  smooth?: boolean;
  /**
   * Z-index of the button
   */
  zIndex?: number;
  /**
   * Custom icon to use (default is pi pi-chevron-up)
   */
  icon?: string;
  /**
   * Throttle time for scroll event in milliseconds
   */
  throttle?: number;
}>(), {
  threshold: 300,
  position: "bottom-right",
  severity: "info",
  outlined: true,
  smooth: true,
  zIndex: 30,
  icon: "pi pi-chevron-up",
  throttle: 200
});

// Use document.documentElement as the default target if none is provided
const defaultTarget = ref<HTMLElement | null>(null);
const targetElement = computed(() => props.target || defaultTarget.value);

// Initialize the scroll tracking with throttling for better performance
const { y } = useScroll(targetElement, {
  behavior: props.smooth ? "smooth" : "auto",
  throttle: props.throttle
});

// Determine if the button should be shown - using throttled computation for performance
const showBackToTop = ref(false);

// Use throttled function to update showBackToTop state
const updateShowBackToTop = useThrottleFn(() => {
  showBackToTop.value = y.value > props.threshold;
}, props.throttle);

// Watch for changes in y value and update showBackToTop state
watch(y, () => {
  updateShowBackToTop();
}, { immediate: true });

// Position classes based on the position prop
const positionClasses = computed(() => {
  switch (props.position) {
    case "bottom-left":
      return "bottom-6 left-6";
    case "top-right":
      return "top-6 right-6";
    case "top-left":
      return "top-6 left-6";
    case "bottom-right":
    default:
      return "bottom-6 right-6";
  }
});

// Z-index class based on the zIndex prop
const zIndexClass = computed(() => {
  return `z-${props.zIndex}`;
});

// Set the default target to document.documentElement on mount
onMounted(() => {
  if (!props.target) {
    defaultTarget.value = document.documentElement;
  }
  // Initial check
  updateShowBackToTop();
});

// Scroll to top function - throttled for performance
const scrollToTop = useThrottleFn(() => {
  if (targetElement.value) {
    y.value = 0;
  }
}, 300);
</script>

<template>
  <Transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 scale-90"
    enter-to-class="opacity-100 scale-100"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 scale-100"
    leave-to-class="opacity-0 scale-90"
  >
    <Button
      v-if="showBackToTop"
      :icon="icon"
      aria-label="Back to top"
      @click="scrollToTop"
      rounded
      :outlined="outlined"
      :severity="severity"
      :class="['fixed', positionClasses, zIndexClass]"
      :pt="{
        root: { class: 'shadow-md hover:shadow-lg' }
      }"
    />
  </Transition>
</template>
