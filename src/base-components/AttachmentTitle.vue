<script setup lang="ts">
import { computed } from 'vue';

import {AttachmentResponse} from "@/api/bcare-types-v2"; // Import ProductName component

import ProductName from './ProductName.vue';

interface Props {
  attachment: AttachmentResponse;
}

const props = defineProps<Props>();

// You can add more computed properties or methods here if needed
const hasProductId = computed(() => props.attachment.product_id ?? false);
</script>

<template>
  <template v-if="hasProductId">
    <ProductName :id="props.attachment.product_id!" />
  </template>
  <template v-else>
    <span>{{ props.attachment.title }}</span>
  </template>
</template>
