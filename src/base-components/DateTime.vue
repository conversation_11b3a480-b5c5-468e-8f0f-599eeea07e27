<!-- DateDisplay.vue -->
<template>
  <span>
    <i class="pi pi-calendar-clock pr-1" :class="`text-${size}`" v-if="showIcon" />
    <span :class="`text-${size}`">{{ formattedDate.trim() }}</span>
  </span>
</template>

<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed } from "vue";

import { formatShortDate, formatShortDateTime } from "@/utils/time-helper";

interface Props {
  time?: string;
  showTime?: boolean;
  format?: string;
  size?: "xs" | "sm" | "md" | "lg";
  showIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showTime: false,
  size: "sm",
  showIcon: true,
});

const formattedDate = computed(() => {
  if (!props.time) {
    return ''
  }
  const time = props.time || new Date().toISOString();

  if (props.format) {
    return useDateFormat(time, props.format).value;
  }

  return props.showTime ? formatShortDateTime(time) : formatShortDate(time);
});
</script>
