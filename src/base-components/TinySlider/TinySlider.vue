<script setup lang="ts">
import { TinySliderInstance, TinySliderSettings } from "tiny-slider/src/tiny-slider";
import { HTMLAttributes, inject, onMounted, ref } from "vue";

import { init, reInit } from "./tiny-slider";

export interface TinySliderElement extends HTMLDivElement {
  tns: TinySliderInstance;
}

export type ProvideTinySlider = (el: TinySliderElement) => void;

export interface TinySliderProps extends /* @vue-ignore */ HTMLAttributes {
  refKey?: string;
  options?: TinySliderSettings;
}

const props = defineProps<TinySliderProps>();

const sliderRef = ref<TinySliderElement>();

const bindInstance = (el: TinySliderElement) => {
  if (props.refKey) {
    const bind = inject<ProvideTinySlider>(`bind[${props.refKey}]`, () => {});
    if (bind) {
      bind(el);
    }
  }
};

const vSliderDirective = {
  mounted(el: TinySliderElement) {
    init(el, props);
  },
  updated(el: TinySliderElement) {
    reInit(el);
  },
};

onMounted(() => {
  if (sliderRef.value) {
    bindInstance(sliderRef.value);
  }
});
</script>

<template>
  <div ref="sliderRef" v-slider-directive class="tiny-slider">
    <slot></slot>
  </div>
</template>
