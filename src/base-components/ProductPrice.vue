<script setup lang="ts">
import { computed } from 'vue';

import Money from "@/base-components/Money.vue";
import useProduct from "@/hooks/useProduct";

interface Props {
  id: number;
}

const props = defineProps<Props>();

const { getProductById } = useProduct({ autoLoad: true });

const product = computed(() => getProductById(props.id));
</script>

<template>
  <Money v-if="product" :amount="product.price" />
</template>
