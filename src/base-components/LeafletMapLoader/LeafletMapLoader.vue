<script setup lang="ts">
import { HTMLAttributes, onMounted, ref } from "vue";

import { initializeMap, LeafletElement, MapConfig } from "./leaflet-map-loader";

export type Init = (
  callback: (mapConfig: MapConfig) => ReturnType<typeof initializeMap> | undefined,
) => void;

interface LeafletMapLoaderProps extends /* @vue-ignore */ HTMLAttributes {
  init: Init;
  darkMode?: boolean;
}

const props = defineProps<LeafletMapLoaderProps>();

const mapRef = ref<LeafletElement>();

onMounted(() => {
  props.init((mapConfig) => {
    if (mapRef.value) {
      return initializeMap(mapRef.value, mapConfig);
    }
  });
});
</script>

<template>
  <div
    :class="{
      '[&_.leaflet-tile-pane]:saturate-[.3]': !props.darkMode,
      '[&_.leaflet-tile-pane]:brightness-90 [&_.leaflet-tile-pane]:grayscale [&_.leaflet-tile-pane]:hue-rotate-15 [&_.leaflet-tile-pane]:invert':
        props.darkMode,
    }"
  >
    <div ref="mapRef" class="h-full w-full"></div>
  </div>
</template>
