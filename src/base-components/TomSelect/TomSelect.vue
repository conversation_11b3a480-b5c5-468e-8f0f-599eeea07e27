<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<script setup lang="ts">
import TomSelectPlugin from "tom-select";
import { RecursivePartial, TomSettings } from "tom-select/src/types";
import { computed, inject, onMounted, ref, SelectHTMLAttributes } from "vue";

import { init, setValue, updateValue } from "./tom-select";

export interface TomSelectElement extends HTMLSelectElement {
  TomSelect: TomSelectPlugin;
}

export interface TomSelectProps extends /* @vue-ignore */ SelectHTMLAttributes {
  modelValue?: string | string[] | any;
  options?: RecursivePartial<TomSettings>;
  refKey?: string;
  create?: boolean;
}

export interface TomSelectEmit {
  (e: "update:modelValue", value: string | string[] | number): void;
  (e: "optionAdd", value: string | number): void;
  (e: "blur"): void;
}

export type ProvideTomSelect = (el: TomSelectElement) => void;

const props = withDefaults(defineProps<TomSelectProps>(), {});

const emit = defineEmits<TomSelectEmit>();

const tomSelectRef = ref<TomSelectElement>();

// Compute all default options
const computedOptions = computed(() => {
  let options: TomSelectProps["options"] = {
    ...props.options,
    plugins: {
      dropdown_input: {},
      clear_button: {
        html: () => {
          const icon = document.createElement("i");
          icon.className = "lucide lucide-icon-clouse"; // Replace 'lucide-icon-name' with the actual icon class name
          return icon;
        },
        className:
          "invisible absolute right-6 flex items-center h-full mr-2 text-xl text-slate-500 group-hover:visible z-10 cursor-pointer", // Thêm các class Tailwind CSS
      },
      ...props.options?.plugins,
    },
    allowEmptyOption: true,
    create: props.create,
  };
  if (Array.isArray(props.modelValue)) {
    options = {
      persist: false,
      create: true,
      onDropdownClose: () => {
        emit("blur");
      },
      onDelete: function (values: string[]) {
        return confirm(
          values.length > 1
            ? "Are you sure you want to remove these " + values.length + " items?"
            : 'Are you sure you want to remove "' + values[0] + '"?',
        );
      },
      ...options,
      plugins: {
        remove_button: {
          title: "Remove this item",
        },
        ...options.plugins,
      },
    };
  }

  return options;
});

const vSelectDirective = {
  mounted(el: TomSelectElement) {
    // Unique attribute
    el.setAttribute("data-id", "_" + Math.random().toString(36).substr(2, 9));

    // Clone the select element to prevent tom select remove the original element
    const clonedEl = el.cloneNode(true) as TomSelectElement;

    // Save initial classnames
    const classNames = el?.getAttribute("class");
    classNames && clonedEl.setAttribute("data-initial-class", classNames);

    // Hide original element
    el?.parentNode && el?.parentNode.appendChild(clonedEl);
    el.setAttribute("hidden", "true");

    // Initialize tom select
    setValue(clonedEl, props);
    init(el, clonedEl, props, computedOptions.value, emit);
  },
  updated(el: TomSelectElement) {
    const clonedEl = document.querySelectorAll(
      `[data-id='${el.getAttribute("data-id")}'][data-initial-class]`,
    )[0] as TomSelectElement;
    const value = props.modelValue;
    updateValue(el, clonedEl, value, props, computedOptions.value, emit);
  },
};

const bindInstance = (el: TomSelectElement) => {
  if (props.refKey) {
    const bind = inject<ProvideTomSelect>(`bind[${props.refKey}]`);
    if (bind) {
      bind(el);
    }
  }
};

onMounted(() => {
  if (tomSelectRef.value) {
    bindInstance(tomSelectRef.value);
  }
});
</script>

<template>
  <select
    ref="tomSelectRef"
    v-select-directive
    :value="props.modelValue"
    class="tom-select group"
    @change="
      (event) => {
        emit('update:modelValue', (event.target as HTMLSelectElement).value);
      }
    "
  >
    <slot></slot>
  </select>
</template>
