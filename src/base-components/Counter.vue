<template>
  <span class="">
    <span class="group inline-flex items-center">
      <i
        v-if="!readonly"
        class="pi pi-minus mr-1 cursor-pointer rounded p-1 text-xs text-gray-500 opacity-0 shadow transition-opacity duration-200 hover:text-gray-700 group-hover:opacity-100"
        @click="decrease"
        :class="{ 'cursor-not-allowed opacity-50': unsigned && modelValue <= 0 }"
      ></i>
      <span>{{ prefix }}{{ modelValue }}</span>
      <i
        v-if="!readonly"
        class="pi pi-plus ml-1 cursor-pointer rounded p-1 text-xs text-gray-500 opacity-0 shadow transition-opacity duration-200 hover:text-gray-700 group-hover:opacity-100"
        @click="increase"
      ></i>
    </span>
  </span>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface Props {
  modelValue: number;
  unsigned?: boolean;
  step?: number;
  prefix?: string;
  readonly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  unsigned: false,
  step: 1,
  readonly: false,
});

const emit = defineEmits(["update:modelValue"]);

const showControls = ref(false);

const increase = () => {
  emit("update:modelValue", props.modelValue + props.step);
};

const decrease = () => {
  if (!props.unsigned || props.modelValue > 0) {
    emit("update:modelValue", Math.max(0, props.modelValue - props.step));
  }
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (props.unsigned && newValue < 0) {
      emit("update:modelValue", 0);
    }
  },
);
</script>
