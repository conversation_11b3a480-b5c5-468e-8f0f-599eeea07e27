<script setup lang="ts">
import { computed } from "vue";

import type { Discount } from "@/api/bcare-types-v2";
import { formatCurrency } from "@/utils/helper";

interface Props {
  discount: Discount;
  onRemove?: (discount: Discount) => void;
}

const props = defineProps<Props>();

const discountValue = computed(() => {
  const { type, value } = props.discount;
  return type === "percent" ? `${(value * 100).toFixed(0)}%` : formatCurrency(value);
});

const emit = defineEmits<{
  (e: "remove-discount", discount: Discount): void;
}>();

const handleRemove = () => {
  emit("remove-discount", props.discount);
};
</script>

<template>
  <Chip
    class="border border-sky-400 bg-sky-50 py-0.5 text-sm text-sky-500"
    removable
    @remove="handleRemove"
  >
    {{ discount.name }}: <span class="font-medium">{{ discountValue }}</span>
  </Chip>
</template>
