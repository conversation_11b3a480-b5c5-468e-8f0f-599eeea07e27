<template>
  <div v-if="totalPages > 0" class="pagination">
    <button type="button" :disabled="currentPage === 1" @click="goToPage(1)">
      <Lucide icon="ChevronsLeft" class="h-4 w-4" />
    </button>
    <button type="button" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">
      <Lucide icon="ChevronLeft" class="h-4 w-4" />
    </button>
    <template v-if="currentPage - 2 > 1">
      <span @click="goToPage(1)">1</span>
      <span>...</span>
    </template>

    <template v-for="pageNumber in visiblePages" :key="pageNumber">
      <span :class="{ active: pageNumber === currentPage }" @click="goToPage(pageNumber)">
        {{ pageNumber }}
      </span>
    </template>

    <!-- <template v-if="totalPages - currentPage > 2">
      <span>...</span>
      <span @click="goToPage(totalPages)">{{ totalPages }}</span>
    </template> -->

    <button type="button" :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)">
      <Lucide icon="ChevronRight" class="h-4 w-4" />
    </button>
    <button type="button" :disabled="currentPage === totalPages" @click="goToPage(totalPages)">
      <Lucide icon="ChevronsRight" class="h-4 w-4" />
    </button>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from "vue";

import Lucide from "@/base-components/Lucide";

export default defineComponent({
  components: { Lucide },
  props: {
    totalPages: {
      type: Number,
      required: true,
    },
    currentPage: {
      type: Number,
      required: true,
    },
  },
  setup(props, { emit }) {
    const visiblePages = ref([]);

    const generateVisiblePages = () => {
      const start = Math.max(1, props.currentPage - 2);
      const end = Math.min(props.totalPages, start + 4);

      visiblePages.value = Array.from({ length: end - start + 1 }, (_, index) => start + index);
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
        emit("page-change", page);
      }
    };

    watch(() => props.currentPage, generateVisiblePages);
    watch(() => props.totalPages, generateVisiblePages);

    return {
      visiblePages,
      goToPage,
    };
  },
});
</script>

<style scoped>
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.pagination button,
.pagination span {
  margin: 0 5px;
  padding: 5px 10px;
  cursor: pointer;
  min-width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  font-weight: 600;
}

.pagination .active {
  box-shadow: 0px 3px 5px #0000000b;
  border: 1px solid #e2e8f0;
}
</style>
