<template>
  <div class="pagination">
    <button :disabled="currentPage === 1" @click="goToPage(1)">
      <Lucide icon="ChevronsLeft" class="h-4 w-4" />
    </button>
    <button :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">
      <Lucide icon="ChevronLeft" class="h-4 w-4" />
    </button>
    <div class="flex">
      <FormInput v-model="inputPage" type="number" min="1" :max="totalPages" class="w-10" />
      <span>/ {{ totalPages }}</span>
    </div>
    <button :disabled="currentPage === totalPages" @click="goToPage(currentPage + 1)">
      <Lucide icon="ChevronRight" class="h-4 w-4" />
    </button>
    <button :disabled="currentPage === totalPages" @click="goToPage(totalPages)">
      <Lucide icon="ChevronsRight" class="h-4 w-4" />
    </button>
  </div>
</template>

<script>
import { defineComponent, ref, watch } from "vue";

import { FormInput } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";

export default defineComponent({
  components: { FormInput, Lucide },
  props: {
    totalPages: {
      type: Number,
      required: true,
    },
    currentPage: {
      type: Number,
      required: true,
    },
  },
  setup(props, { emit }) {
    const visiblePages = ref([]);
    const inputPage = ref(props.currentPage);
    const generateVisiblePages = () => {
      const start = Math.max(1, props.currentPage - 2);
      const end = Math.min(props.totalPages, start + 4);

      visiblePages.value = Array.from({ length: end - start + 1 }, (_, index) => start + index);
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
        emit("page-change", page);
      }
    };
    watch(
      () => props.currentPage,
      (newValue) => {
        inputPage.value = newValue;
      },
    );
    watch(
      () => inputPage.value,
      (newValue) => {
        goToPage(newValue);
      },
    );

    watch(() => props.currentPage, generateVisiblePages);
    watch(() => props.totalPages, generateVisiblePages);

    return {
      visiblePages,
      inputPage,
      goToPage,
    };
  },
});
</script>

<style scoped>
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pagination button,
.pagination span {
  padding: 5px;
  cursor: pointer;
  min-width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  font-weight: 600;
}

.pagination .active {
  box-shadow: 0px 3px 5px #0000000b;
  border: 1px solid #e2e8f0;
}
</style>
