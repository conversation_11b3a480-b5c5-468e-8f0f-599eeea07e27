<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<script setup lang="ts">
import Color from "color";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import { computed, HTMLAttributes, useAttrs, useSlots } from "vue";

import { ColorCollection, useHashColor } from "@/composables/useHashColor";

export type Variant = "default" | "outline" | "soft";
type Elevated = boolean;
type Size = "sm" | "lg";
type Rounded = boolean;

interface TagProps extends /* @vue-ignore */ HTMLAttributes {
  as?: string | object;
  variant?: Variant;
  elevated?: Elevated;
  size?: Size;
  rounded?: Rounded;
  colorKey?: string;
  collectionKey?: keyof ColorCollection;
}

const props = withDefaults(defineProps<TagProps>(), {
  as: "span",
  variant: "default",
  collectionKey: "Tailwind300",
});

const attrs = useAttrs();
const slots = useSlots();

// Function to strip HTML tags
const stripHtml = (html: string) => {
  return html.replace(/<[^>]*>/g, "");
};

// Get the text content for color calculation
const colorText = computed(() => {
  if (props.colorKey) {
    return props.colorKey;
  }
  if (slots.default) {
    const slotContent = slots.default();
    const textContent = slotContent
      .map((node) => {
        if (typeof node.children === "string") {
          return stripHtml(node.children);
        }
        return "";
      })
      .join("");
    return textContent || "default";
  }
  return "default";
});

// Get background color using hashColor
const backgroundColor = computed(() => useHashColor(colorText.value, props.collectionKey));

// Calculate border and text colors for soft variant
const borderColor = computed(() => {
  if (props.variant === "soft") {
    return Color(backgroundColor.value).darken(0.1).hex();
  }
  return backgroundColor.value;
});

const textColor = computed(() => {
  if (props.variant === "soft" || props.variant === "outline") {
    return Color(backgroundColor.value).darken(0.5).hex();
  }

  // Thay đổi màu văn bản cho trường hợp màu sáng
  return Color(backgroundColor.value).isLight()
    ? Color(backgroundColor.value).darken(0.6).hex()
    : "#FFFFFF";
});

// General Styles
const generalStyles = [
  "transition duration-200 border shadow-sm inline-flex items-center justify-center py-1 px-2 rounded-md font-normal",
  "focus:ring-4 focus:ring-opacity-20",
  "focus-visible:outline-none",
  "[&:not(span)]:text-center",
];

// Sizes
const small = ["text-xs py-0.5 px-1.5"];
const large = ["text-base py-1.5 px-3"];

// Inline styles
const inlineStyles = computed(() => {
  const styles: Record<string, string> = {
    backgroundColor: props.variant === "outline" ? "transparent" : backgroundColor.value,
    borderColor: props.variant === "soft" ? borderColor.value : backgroundColor.value,
    color: textColor.value,
  };

  if (props.variant === "soft") {
    styles.backgroundColor = Color(backgroundColor.value).alpha(0.2).string();
    styles.borderColor = Color(borderColor.value).alpha(0.3).string();
  }

  return styles;
});

const computedClass = computed(() =>
  twMerge([
    generalStyles,
    props.size === "sm" && small,
    props.size === "lg" && large,
    props.rounded && "rounded-full",
    props.elevated && "shadow-md",
    typeof attrs.class === "string" && attrs.class,
  ]),
);
</script>

<template>
  <component
    :is="props.as"
    :class="computedClass"
    :style="inlineStyles"
    v-bind="_.omit(attrs, 'class', 'style')"
  >
    <slot></slot>
  </component>
</template>
