import { onMounted, onUnmounted, Ref } from "vue";

let lastAnimationFrame: number | null = null;

const cancelAnimation = () => {
  if (lastAnimationFrame !== null) {
    cancelAnimationFrame(lastAnimationFrame);
    lastAnimationFrame = null;
  }
};

const useDragScroll = (
  element: Ref<HTMLElement | undefined>,
  scrollThreshold = 800,
  maxScrollSpeed = 2000,
) => {
  const dragOver = (event: MouseEvent) => {
    event.preventDefault();
    cancelAnimation();
    lastAnimationFrame = requestAnimationFrame(() => {
      if (!element.value) return;

      const rect = element.value.getBoundingClientRect();
      const distanceFromRightEdge = rect.right - event.clientX;
      const distanceFromLeftEdge = event.clientX - rect.left;
      let scrollAmount = 0;
      if (distanceFromRightEdge < scrollThreshold) {
        scrollAmount =
          maxScrollSpeed * ((scrollThreshold - distanceFromRightEdge) / scrollThreshold);
      } else if (distanceFromLeftEdge < scrollThreshold) {
        scrollAmount =
          -maxScrollSpeed * ((scrollThreshold - distanceFromLeftEdge) / scrollThreshold);
      }
      element.value.scrollBy({ left: scrollAmount, behavior: "smooth" });
    });
  };

  const addListeners = () => {
    if (element.value) {
      element.value.addEventListener("dragover", dragOver);
    }
  };

  const removeListeners = () => {
    if (element.value) {
      element.value.removeEventListener("dragover", dragOver);
    }
    cancelAnimation();
  };

  onMounted(() => {
    addListeners();
  });

  onUnmounted(() => {
    removeListeners();
  });

  return { addListeners, removeListeners, dragOver };
};

export { useDragScroll };
