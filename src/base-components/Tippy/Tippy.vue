<script setup lang="ts">
import tippy, {
  animateFill as animateFillPlugin,
  PopperElement,
  Props,
  roundArrow,
} from "tippy.js";
import {inject, onMounted, onUnmounted, ref, watch} from "vue";

export type ProvideTippy = (el: PopperElement) => void;

interface TippyProps {
  refKey?: string;
  content?: string;
  disable?: boolean;
  as?: string | object;
  options?: Partial<Props>;
}

const props = withDefaults(defineProps<TippyProps>(), {
  as: "span",
  disable: false,
});

const tippyRef = ref<PopperElement>();

const shouldInitTippy = (content?: string) => {
  return content !== undefined && content !== null && content.trim() !== '';
};

const init = (el: PopperElement, props: TippyProps) => {
  if (!shouldInitTippy(props.content)) {
    return;
  }

  tippy(el, {
    plugins: [animateFillPlugin],
    content: props.content,
    arrow: roundArrow,
    theme: "material",
    popperOptions: {
      modifiers: [
        {
          name: "preventOverflow",
          options: {
            rootBoundary: "viewport",
          },
        },
      ],
    },
    animateFill: false,
    animation: "shift-away",
    allowHTML: true,
    interactive: true,
    ...props.options,
  });
};

const bindInstance = (el: PopperElement) => {
  if (props.refKey) {
    const bind = inject<ProvideTippy>(`bind[${props.refKey}]`, () => {});
    if (bind) {
      bind(el);
    }
  }
};

const vTippyDirective = {
  mounted(el: PopperElement) {
    tippyRef.value = el;
  },
};

const isDisabled = () => {
  if (tippyRef.value && tippyRef.value._tippy !== undefined) {
    props.disable ? tippyRef.value._tippy.disable() : tippyRef.value._tippy.enable();
  }
};

watch(props, () => {
  isDisabled();
});

onMounted(() => {
  if (tippyRef.value) {
    init(tippyRef.value, props);
    bindInstance(tippyRef.value);
    isDisabled();
  }
});

onUnmounted(() => {
  if (tippyRef.value?._tippy) {
    tippyRef.value._tippy.destroy()
    delete tippyRef.value._tippy
  }
})
</script>

<template>
  <component
    :is="as"
    v-tippy-directive
    :class="{ 'cursor-pointer': shouldInitTippy(content) }"
  >
    <slot></slot>
  </component>
</template>
