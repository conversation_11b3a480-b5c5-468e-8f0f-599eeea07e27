<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted,ref } from "vue";

import { PersonAvatar } from "@/components/Person";
import { useSipClient } from "@/composables/useSipClient";
import useConstant from "@/hooks/useConstant";
import { useCallInfoStore } from "@/stores/call-info";

const phoneNumber = ref("");
const { isRegistered, isInCall, callDuration, initializeSip, makeCall, hangUp, isCallConfirmed } =
  useSipClient();
const callInfoStore = useCallInfoStore();
const { callInfo, isVisible } = storeToRefs(callInfoStore);
const { getConstants } = useConstant();

const callSetting = computed(() => getConstants.value?.call_setting);

const posX = ref(0);
const posY = ref(0);
const isDragging = ref(false);
let startX = 0;
let startY = 0;

const startDrag = (e: MouseEvent) => {
  isDragging.value = true;
  startX = e.clientX - posX.value;
  startY = e.clientY - posY.value;

  document.addEventListener("mousemove", onDrag, { passive: true });
  document.addEventListener("mouseup", stopDrag, { passive: true });
};

const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;

  requestAnimationFrame(() => {
    posX.value = e.clientX - startX;
    posY.value = e.clientY - startY;
  });
};

const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
};

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, "0")}`;
};

const handleCall = () => {
  if (callInfo.value) {
    makeCall(callInfo.value.phone);
  } else if (phoneNumber.value) {
    makeCall(phoneNumber.value);
  }
};

const handleCancel = () => {
  callInfoStore.clear();
};

const handleHangUp = () => {
  hangUp();
  callInfoStore.clear();
  phoneNumber.value = "";
};

const handleReconnect = async () => {
  await initializeSip({
    socketUrl: callSetting.value?.uri_ws ?? "",
    sipUri: callSetting.value?.line_id ?? "",
    password: callSetting.value?.password ?? "",
    uri: callSetting.value?.uri ?? "",
  });
};

onMounted(() => {
  initializeSip({
    socketUrl: callSetting.value?.uri_ws ?? "",
    sipUri: callSetting.value?.line_id ?? "",
    password: callSetting.value?.password ?? "",
    uri: callSetting.value?.uri ?? "",
  });
});

onUnmounted(() => {
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
});
</script>

<template>
  <div
    :key="'sip-phone'"
    v-show="isVisible || isInCall"
    class="pointer-events-auto fixed bottom-4 left-4 z-[9999] w-72 cursor-move rounded-lg border border-gray-700 bg-gray-800 text-white"
    :style="{ transform: `translate(${posX}px, ${posY}px)` }"
    @mousedown="startDrag"
  >
    <!-- Compact Person Info -->
    <div
      v-if="callInfo"
      :key="'person-info'"
      class="flex cursor-move items-center justify-between border-b border-gray-700 p-2"
      @mousedown="startDrag"
    >
      <div class="flex items-center gap-2">
        <PersonAvatar :full-name="callInfo.name" size="small" shape="square" />
        <div class="min-w-0 flex-1">
          <div class="max-w-[100%] truncate text-xs font-medium">{{ callInfo.name }}</div>
          <div class="text-xs text-gray-400">{{ callInfo.phone }}</div>
          <div v-if="callInfo.address" class="max-w-[90%] truncate text-xs text-gray-500">
            {{ callInfo.address }}
          </div>
        </div>
      </div>

      <div class="flex items-center gap-2">
        <div
          class="h-1.5 w-1.5 rounded-full"
          :class="isRegistered ? 'bg-green-500' : 'bg-red-500'"
        />
        <i
          v-if="!isRegistered"
          class="pi pi-refresh cursor-pointer text-xs text-white"
          v-tooltip="'Kết nối lại'"
          @click="handleReconnect"
        />
      </div>
    </div>

    <!-- Compact Dialer -->
    <div class="p-2">
      <div class="flex flex-col gap-2">
        <div class="flex gap-2">
          <template v-if="!isInCall">
            <Button
              @click="handleCall"
              :disabled="!isRegistered || !callInfo"
              class="relative h-7 flex-1 !text-xs"
            >
              <i class="pi pi-phone mr-1 text-xs" />
              Call
            </Button>
            <Button
              v-if="callInfo"
              severity="secondary"
              @click="handleCancel"
              class="h-7 flex-1 !text-xs"
            >
              Cancel
            </Button>
          </template>

          <Button v-else severity="danger" @click="handleHangUp" class="group h-7 flex-1 !text-xs">
            <i
              class="pi pi-phone mr-1 animate-[wiggle_1s_ease-in-out_infinite] text-xs"
              v-if="!isCallConfirmed"
            />
            <i class="pi pi-times mr-1 text-xs" v-else />
            <span class="group-hover:hidden">{{
              !isCallConfirmed ? "Đang gọi ..." : formatDuration(callDuration)
            }}</span>
            <span class="hidden group-hover:inline">Kết thúc</span>
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
