<script setup lang="ts">
import { PersonAvatar } from "@/components/Person";
import { useIncomingCallManager } from "@/composables/useIncomingCallManager";
import { useSipClient } from "@/composables/useSipClient";

const { sortedCalls, handleSkipCall } = useIncomingCallManager();
const { answerIncomingCall, hangUpIncomingCall, callDuration } = useSipClient();

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, "0")}`;
};

const handleAnswer = async (sessionId: string) => {
  try {
    await answerIncomingCall(sessionId);
  } catch (error) {
    console.error("Không thể trả lời cuộc gọi:", error);
  }
};

const handleSkip = (sessionId: string) => {
  handleSkipCall(sessionId);
};

const handleHangUp = (sessionId: string) => {
  hangUpIncomingCall(sessionId);
};

const handleNavigateToProfile = (personId?: number) => {
  if (personId) {
    const profileUrl = `/customer/profile/${personId}`;
    window.open(profileUrl, "_blank");
  }
};
</script>

<template>
  <TransitionGroup
    tag="div"
    class="fixed bottom-4 right-4 z-[9999] flex flex-col gap-2"
    name="call-list"
    appear
  >
    <div
      v-for="call in sortedCalls"
      :key="`call-container-${call.sessionId}`"
      class="animate-slide-in w-72 rounded-lg border border-gray-700 bg-gray-800 shadow-lg"
    >
      <div
        :key="`header-${call.sessionId}`"
        class="flex items-center justify-between border-b border-gray-700 p-2"
      >
        <div
          :key="`info-${call.sessionId}`"
          class="flex cursor-pointer items-center gap-2"
          @click.stop="handleNavigateToProfile(call.personInfo?.id)"
        >
          <PersonAvatar
            :key="`avatar-${call.sessionId}`"
            :full-name="call.personInfo?.name || 'Unknown'"
            size="small"
            shape="square"
          />
          <div :key="`details-${call.sessionId}`" class="min-w-0 flex-1">
            <div
              :key="`name-${call.sessionId}`"
              class="max-w-[100%] truncate text-xs font-medium text-white"
            >
              {{ call.personInfo?.name || "Số máy lạ" }}
            </div>
            <div :key="`phone-${call.sessionId}`" class="text-xs text-gray-400">
              {{ call.phone }}
            </div>
            <div
              v-if="call.personInfo?.address"
              :key="`address-${call.sessionId}`"
              class="max-w-[90%] truncate text-xs text-gray-500"
            >
              {{ call.personInfo.address }}
            </div>
          </div>
        </div>
      </div>

      <div :key="`actions-${call.sessionId}`" class="p-2">
        <div :key="`buttons-${call.sessionId}`" class="flex gap-2">
          <template v-if="call.status === 'ringing'">
            <Button
              :key="`answer-${call.sessionId}`"
              severity="success"
              class="h-7 flex-1 !text-xs"
              @click="handleAnswer(call.sessionId)"
            >
              <i class="pi pi-phone-plus mr-1" />
              Trả lời
            </Button>
            <Button
              :key="`skip-${call.sessionId}`"
              severity="danger"
              class="h-7 flex-1 !text-xs"
              @click="handleSkip(call.sessionId)"
            >
              <i class="pi pi-times mr-1" />
              Bỏ qua
            </Button>
          </template>

          <Button
            v-else-if="call.status === 'accepted'"
            :key="`hangup-${call.sessionId}`"
            severity="danger"
            class="group h-7 flex-1 !text-xs"
            @click="handleHangUp(call.sessionId)"
          >
            <i :key="`icon-${call.sessionId}`" class="pi pi-times mr-1 text-xs" />
            <span :key="`duration-${call.sessionId}`" class="group-hover:hidden">
              {{ formatDuration(callDuration) }}
            </span>
            <span :key="`end-text-${call.sessionId}`" class="hidden group-hover:inline">
              Kết thúc
            </span>
          </Button>

          <div
            v-else
            :key="`status-${call.sessionId}`"
            class="flex h-7 w-full items-center justify-center text-xs text-gray-400"
          >
            {{
              call.status === "rejected"
                ? "Đã từ chối"
                : call.status === "missed"
                  ? "Cuộc gọi nhỡ"
                  : call.status
            }}
          </div>
        </div>
      </div>
    </div>
  </TransitionGroup>
</template>

<style scoped>
.call-list-move,
.call-list-move,
.call-list-enter-active,
.call-list-leave-active {
  transition: all 0.3s ease;
}

.call-list-enter-from,
.call-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.call-list-leave-active {
  position: absolute;
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}
</style>
