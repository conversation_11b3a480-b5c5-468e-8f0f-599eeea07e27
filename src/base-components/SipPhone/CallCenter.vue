<script setup lang="ts">
import { defineAsyncComponent } from "vue";

const LazyOutgoingCall = defineAsyncComponent(() => import("./OutgoingCall.vue"));
const LazyIncomingCallList = defineAsyncComponent(() => import("./IncomingCallList.vue"));
</script>

<template>
  <Teleport to="body">
    <div class="call-center">
      <LazyOutgoingCall />
      <LazyIncomingCallList />
    </div>
  </Teleport>
</template>
