<script setup lang="ts">
import { provide, ref, watch } from "vue";

import Lucide from "@/base-components/Lucide";
import { Icon } from "@/base-components/Lucide/Lucide.vue";
import Notification from "@/base-components/Notification";
import { NotificationElement } from "@/base-components/Notification";
import { NotificationType, SimpleNotification, useNotiStore } from "@/stores/notification";

const notificationClassMap: Record<NotificationType, string> = {
  warning: "warning",
  error: "danger",
  success: "success",
  info: "secondary",
};

const notificationIconMap: Record<NotificationType, Icon> = {
  warning: "AlertTriangle",
  error: "XCircle",
  success: "CheckSquare",
  info: "Activity",
};

const notiStore = useNotiStore();
const notiElement = ref<NotificationElement | null>(null); //Dùng để manipulate dom object
const simpleNotiItem = ref<SimpleNotification>();

const showNotification = () => {
  const nextNoti = notiStore.next();
  if (nextNoti && notiElement.value) {
    simpleNotiItem.value = nextNoti;
    notiElement.value?.showToast();
  }
};

watch(
  () => notiStore.notifications.length,
  (newVal) => {
    if (newVal > 0) {
      showNotification();
    }
  },
  { immediate: true },
);

provide("bind[simpleNotification]", (el: NotificationElement) => {
  notiElement.value = el;
});
</script>

<template>
  <Notification ref-key="simpleNotification" class="flex" :options="{ duration: 2000 }">
    <Lucide
      :icon="simpleNotiItem?.type ? notificationIconMap[simpleNotiItem.type] : 'Activity'"
      :class="simpleNotiItem?.type ? 'text-' + notificationClassMap[simpleNotiItem.type] : ''"
    />
    <div class="ml-4 mr-4">
      <div class="font-medium">{{ simpleNotiItem?.title }}</div>
      <div class="mt-1 text-slate-500">
        {{ simpleNotiItem?.message }}
      </div>
    </div>
    <slot></slot>
  </Notification>
</template>

<style scoped></style>
