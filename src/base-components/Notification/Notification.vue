<script setup lang="ts">
import Toastify, { Options } from "toastify-js";
import { HTMLAttributes, inject, onMounted, ref } from "vue";

import { init, reInit } from "./notification";

export interface NotificationElement extends HTMLDivElement {
  toastify: ReturnType<typeof Toastify>;
  showToast: () => void;
  hideToast: () => void;
}

export interface NotificationProps extends /* @vue-ignore */ HTMLAttributes {
  options?: Options;
  refKey?: string;
}

export type ProvideNotification = (el: NotificationElement) => void;

const props = defineProps<NotificationProps>();

const toastifyRef = ref<NotificationElement>();

const bindInstance = (el: NotificationElement) => {
  if (props.refKey) {
    const bind = inject<ProvideNotification>(`bind[${props.refKey}]`);
    if (bind) {
      bind(el);
    }
  }
};

const vNotificationDirective = {
  mounted(el: NotificationElement) {
    init(el, props);
  },
  updated(el: NotificationElement) {
    reInit(el);
  },
};

onMounted(() => {
  if (toastifyRef.value) {
    bindInstance(toastifyRef.value);
  }
});
</script>

<template>
  <div
    ref="toastifyRef"
    v-notification-directive
    class="hidden rounded-lg border border-slate-200/60 bg-white py-5 pl-5 pr-14 shadow-xl dark:border-darkmode-600 dark:bg-darkmode-600 dark:text-slate-300"
  >
    <slot></slot>
  </div>
</template>
