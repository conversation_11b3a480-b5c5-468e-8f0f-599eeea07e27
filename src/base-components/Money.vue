<!-- Money.vue -->
<template>
  <span :class="[textColorClass, sizeClass, customClass]">
    <i v-if="showIcon" class="pi pi-dollar pr-0.5" :class="iconSizeClass" />
    <span>{{ formattedAmount.trim() }}</span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { formatCurrency } from "@/utils/helper";

interface Props {
  amount: number;
  showIcon?: boolean;
  // Add new props for color customization
  variant?: 'success' | 'danger' | 'warning' | 'info' | 'default';
  customClass?: string;
  size?: 'small' | 'medium' | 'large';
}

const props = withDefaults(defineProps<Props>(), {
  showIcon: false,
  variant: 'success',
  customClass: '',
  size: 'medium',
});

const formattedAmount = computed(() => {
  return formatCurrency(props.amount);
});

const textColorClass = computed(() => {
  const colorMap = {
    success: 'text-success',
    danger: 'text-danger',
    warning: 'text-warning',
    info: 'text-info',
    default: 'text-gray-700'
  };

  return colorMap[props.variant];
});

const sizeClass = computed(() => {
  const sizeMap = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  return sizeMap[props.size];
});

const iconSizeClass = computed(() => {
  const sizeMap = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  return sizeMap[props.size];
});
</script>
