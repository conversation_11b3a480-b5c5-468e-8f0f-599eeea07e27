<template>
  <span v-html="highlightedText"></span>
</template>

<script lang="ts" setup>
import { computed } from "vue";

import { normalizeVietnamese } from "@/utils/string";

const props = defineProps<{
  text: string;
  highlight: string;
}>();

const highlightedText = computed(() => {
  if (!props.highlight.trim()) {
    return props.text;
  }

  const normalizedText = normalizeVietnamese(props.text);
  const normalizedHighlight = normalizeVietnamese(props.highlight);

  const regex = new RegExp(`(${escapeRegExp(normalizedHighlight)})`, "gi");
  let lastIndex = 0;
  let result = "";

  normalizedText.replace(regex, (match, p1, offset) => {
    const beforeMatch = props.text.slice(lastIndex, offset);
    const matchedText = props.text.slice(offset, offset + match.length);
    result += beforeMatch;
    result += `<mark class="bg-yellow-200 dark:bg-yellow-700">${matchedText}</mark>`;
    lastIndex = offset + match.length;
    return match;
  });

  result += props.text.slice(lastIndex);
  return result;
});

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
</script>
