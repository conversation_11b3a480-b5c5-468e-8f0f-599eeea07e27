<template>
  <Button :class="$attrs.class" @click="toggleValue">
    <component :is="iconComponent" v-bind="currentIconProps" />
  </Button>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import Button from "@/base-components/Button";

interface IconObject {
  icon: string;
  class?: string | object;
  [key: string]: any;
}

interface ToggleOption {
  value: any;
  icon: string | IconObject;
}

interface Props {
  options: [ToggleOption, ToggleOption];
  modelValue: any;
  iconComponent?: any;
  iconClass?: string | object;
}

const props = withDefaults(defineProps<Props>(), {
  iconComponent: "span",
  iconClass: "",
});

const emit = defineEmits(["update:modelValue"]);

defineOptions({
  inheritAttrs: false,
});

const currentIndex = ref(
  props.options.findIndex((option) => option.value === props.modelValue) || 0,
);

const currentValue = computed(() => props.options[currentIndex.value].value);
const currentIcon = computed(() => props.options[currentIndex.value].icon);

const currentIconProps = computed(() => {
  if (typeof currentIcon.value === "string") {
    return {
      icon: currentIcon.value,
      class: props.iconClass,
    };
  }
  // Xử lý trường hợp icon là object
  const iconObject = currentIcon.value as IconObject;
  return {
    ...iconObject,
    class: [iconObject.class, props.iconClass].filter(Boolean),
  };
});

const toggleValue = () => {
  currentIndex.value = (currentIndex.value + 1) % 2;
  emit("update:modelValue", currentValue.value);
};
</script>
