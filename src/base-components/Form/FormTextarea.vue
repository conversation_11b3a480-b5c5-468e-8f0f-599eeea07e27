<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<script setup lang="ts">
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import {
  computed,
  inject,
  InputHTMLAttributes,
  nextTick,
  onMounted,
  onUpdated,
  ref,
  useAttrs,
} from "vue";

import { ProvideFormInline } from "./FormInline.vue";
import { ProvideInputGroup } from "./InputGroup/InputGroup.vue";

interface FormTextareaProps extends /* @vue-ignore */ InputHTMLAttributes {
  modelValue?: InputHTMLAttributes["value"];
  formTextareaSize?: "sm" | "lg";
  rounded?: boolean;
  autoResize?: boolean;
}

interface FormTextareaEmit {
  (e: "update:modelValue", value: string): void;
}

const props = withDefaults(defineProps<FormTextareaProps>(), {
  autoResize: false,
});
const emit = defineEmits<FormTextareaEmit>();
const attrs = useAttrs();
const formInline = inject<ProvideFormInline>("formInline", false);
const inputGroup = inject<ProvideInputGroup>("inputGroup", false);

const textareaRef = ref<HTMLTextAreaElement | null>(null);

const computedClass = computed(() =>
  twMerge([
    "disabled:bg-slate-100 disabled:cursor-not-allowed dark:disabled:bg-darkmode-800/50 dark:disabled:border-transparent",
    "[&[readonly]]:bg-slate-100 [&[readonly]]:cursor-not-allowed [&[readonly]]:dark:bg-darkmode-800/50 [&[readonly]]:dark:border-transparent",
    "transition duration-200 ease-in-out w-full text-sm border-slate-200 shadow-sm rounded-md placeholder:text-slate-400/90 focus:ring-2 focus:ring-active focus:ring-opacity-90 focus:border-primary focus:border-opacity-0 dark:bg-darkmode-800 dark:border-transparent dark:focus:ring-slate-700 dark:focus:ring-opacity-50 dark:placeholder:text-slate-500/80",
    props.formTextareaSize == "sm" && "text-xs py-1.5 px-2",
    props.formTextareaSize == "lg" && "text-lg py-1.5 px-4",
    props.rounded && "rounded-full",
    formInline && "flex-1",
    inputGroup &&
      "rounded-none [&:not(:first-child)]:border-l-transparent first:rounded-l last:rounded-r z-10",
    typeof attrs.class === "string" && attrs.class,
    props.autoResize && "resize-none",
  ]),
);

const resize = () => {
  if (textareaRef.value && props.autoResize) {
    textareaRef.value.style.height = "auto";
    textareaRef.value.style.height = textareaRef.value.scrollHeight + "px";

    if (
      parseFloat(textareaRef.value.style.height) >= parseFloat(textareaRef.value.style.maxHeight)
    ) {
      textareaRef.value.style.overflowY = "scroll";
      textareaRef.value.style.height = textareaRef.value.style.maxHeight;
    } else {
      textareaRef.value.style.overflow = "hidden";
    }
  }
};

const onInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement;
  emit("update:modelValue", target.value);
  if (props.autoResize) {
    resize();
  }
};

onMounted(() => {
  nextTick(() => {
    if (textareaRef.value && props.autoResize) {
      resize();
    }
  });
});

onUpdated(() => {
  nextTick(() => {
    if (textareaRef.value && props.autoResize) {
      resize();
    }
  });
});

// Computed properties
const filled = computed(() => {
  return props.modelValue != null && props.modelValue.toString().length > 0;
});

const ptmParams = computed(() => {
  return {
    context: {
      disabled: attrs.disabled || attrs.disabled === "",
    },
  };
});
</script>

<template>
  <textarea
    ref="textareaRef"
    v-bind="_.omit(attrs, 'class')"
    :value="modelValue"
    :class="computedClass"
    @input="onInput"
  />
</template>
