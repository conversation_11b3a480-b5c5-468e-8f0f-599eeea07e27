<script setup lang="ts">
import { Tab as HeadlessTab } from "@headlessui/vue";
import { inject } from "vue";

import { ProvideList } from "../List.vue";

import Provider from "./Provider.vue";

interface TabProps extends /* @vue-ignore */ ExtractProps<typeof HeadlessTab> {
  fullWidth?: boolean;
}

const { fullWidth = true } = defineProps<TabProps>();

const list = inject<ProvideList>("list");
</script>

<template>
  <HeadlessTab v-slot="{ selected }" as="template">
    <li
      :class="[
        'focus-visible:outline-none',
        { 'flex-1': fullWidth },
        { '-mb-px': list && list.variant == 'tabs' },
      ]"
    >
      <Provider :selected="selected">
        <slot :selected="selected"></slot>
      </Provider>
    </li>
  </HeadlessTab>
</template>
