<script lang="ts">
export default {
  inheritAttrs: false,
};

type Variant = "tabs" | "pills" | "boxed-tabs" | "link-tabs";
</script>

<script setup lang="ts">
import { TabList as HeadlessTabList } from "@headlessui/vue";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import { computed, provide, useAttrs } from "vue";

export type ProvideList = {
  variant?: Variant;
};

interface ListProps extends /* @vue-ignore */ ExtractProps<typeof HeadlessTabList> {
  variant?: Variant;
}

const { variant = "tabs" } = defineProps<ListProps>();

const attrs = useAttrs();
const computedClass = computed(() =>
  twMerge([
    variant == "tabs" && "border-b border-slate-200 dark:border-darkmode-400",
    "w-full flex",
    typeof attrs.class === "string" && attrs.class,
  ]),
);

provide<ProvideList>("list", {
  variant: variant,
});
</script>

<template>
  <HeadlessTabList as="ul" :class="computedClass" v-bind="_.omit(attrs, 'class')">
    <slot></slot>
  </HeadlessTabList>
</template>
