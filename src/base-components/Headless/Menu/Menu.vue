<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<script setup lang="ts">
import { Menu as HeadlessMenu } from "@headlessui/vue";
import _ from "lodash";
import { twMerge } from "tailwind-merge";
import { computed, useAttrs } from "vue";

interface MenuProps extends /* @vue-ignore */ ExtractProps<typeof HeadlessMenu> {
  as?: string | object;
}

const { as = "div" } = defineProps<MenuProps>();

const attrs = useAttrs();
const computedClass = computed(() =>
  twMerge(["relative", typeof attrs.class === "string" && attrs.class]),
);
</script>

<template>
  <HeadlessMenu v-slot="{ open, close }" as="template">
    <component :is="as" :class="computedClass" v-bind="_.omit(attrs, 'class')">
      <slot :open="open" :close="close"></slot>
    </component>
  </HeadlessMenu>
</template>
