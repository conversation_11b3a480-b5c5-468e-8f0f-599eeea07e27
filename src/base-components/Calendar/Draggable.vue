<script setup lang="ts">
import { Draggable as FullCalendarDraggable } from "@fullcalendar/interaction";
import { HTMLAttributes, onMounted, ref } from "vue";

interface DraggableProps extends /* @vue-ignore */ HTMLAttributes {
  options: FullCalendarDraggable["settings"];
}

const props = defineProps<DraggableProps>();

const draggableRef = ref<HTMLDivElement>();

const init = () => {
  if (draggableRef.value) {
    new FullCalendarDraggable(draggableRef.value, props.options);
  }
};

onMounted(() => {
  init();
});
</script>

<template>
  <div ref="draggableRef">
    <slot></slot>
  </div>
</template>
