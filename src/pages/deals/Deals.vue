<template>
  <ContentWithFixedToolbar>
    <template #left-toolbar>
      <DealFilterPreset v-model="currentPreset" />
    </template>

    <template #right-toolbar>
      <div class="flex flex-col-reverse items-center sm:flex-row md:ml-3">
        <div class="relative mr-3 mt-3 w-full sm:mt-0 sm:w-auto"></div>
        <PopSetting title="Tuỳ chỉnh" setting-key="deals" />
      </div>
    </template>

    <template #footer>
      <div class="relative flex h-[40px] w-full items-center justify-center">
        <div class="flex items-center gap-4">
          <span class="whitespace-nowrap text-sm font-normal">
            {{ paginatorText }}
          </span>
          <Paginator
            :first="(dealStore.pagination.page - 1) * dealStore.pagination.pageSize"
            :rows="dealStore.pagination.pageSize"
            :totalRecords="dealStore.totalDeals"
            :rowsPerPageOptions="[30, 50, 100]"
            @page="handlePageChange"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
            :pt="{
              root: { class: '!bg-transparent !p-0' },
              pcCurrentPageReport: { root: { class: '!text-sm !font-normal !px-2' } },
              pcRowsPerPageDropdown: { root: { class: 'h-8' }, input: { class: '!text-sm' } },
              pcFirstPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcPrevPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcNextPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
              pcLastPageButton: { root: { class: '!w-8 !h-8 !min-w-0' } },
            }"
          />
        </div>
      </div>
    </template>

    <DataTable
      :columns="columns"
      :data="dealStore.deals"
      :total-records="dealStore.totalDeals || 0"
      :loading="dealStore.loading"
      :paginator="false"
      :show-header="false"
      :hide-header-row="true"
      filterDisplay="row"
      size="small"
      class="h-full"
      :rows="dealStore.pagination.pageSize"
      :show-actions="{ custom: true }"
      :custom-action-items="getDealActionItems"
    >
      <template #created_at="{ data }">
        <DateTime :time="data.created_at" :showTime="true" size="sm" />
      </template>
      <template #name="{ data }">
        <div class="flex cursor-default items-center gap-1 !font-light">
          <i class="pi pi-shopping-cart text-lime-500" />
          <DealName
            v-if="data"
            :name="data.name || 'Deal chưa đặt tên'"
            :state="data.state"
            :class="['whitespace-normal break-words border-none p-0 shadow-none']"
            name-class="!font-normal"
          />
          <span v-else class="italic text-gray-500">Deal chưa đặt tên</span>
        </div>
      </template>
      <template #person_id="{ data }">
        <PersonCard :person="data.person" submit-type="new-tab" />
      </template>
      <template #stage_id="{ data }">
        <PipelineStageSelect
          :modelValue="data.stage?.id"
          placeholder="Stage"
          selectionMode="single"
          :editable="false"
          :pipeline-id="2"
          v-if="data.stage?.id"
        />
        <span v-else class="italic text-gray-400">-</span>
      </template>

      <template #related_users="{ data }">
        <div
          class="flex cursor-pointer items-center gap-1"
          @click.capture="(event) => toggleUserPopover(event, data)"
        >
          <UserAvatarGroup
            :users="combineUsers(data.deal_assignment || [])"
            animationStyle="lift"
            size="small"
          />
          <span
            v-if="!data.sale_user && (!data.deal_assignment || data.deal_assignment.length === 0)"
            class="italic text-gray-400"
            >-</span
          >
        </div>
      </template>

      <template #tags="{ data }">
        <EntityTags
          :entity-id="data.id"
          entity-type="deal"
          :initial-tags="data.tags || []"
          @reload="refreshDeals"
          placeholder="Thêm tag"
        />
      </template>

      <template #created_at.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <DatePicker
            :key="'deal-filter-created_at'"
            class="h-10 w-full flex-grow"
            v-model="datePickerModel"
            showIcon
            selectionMode="range"
            manualInput
            dateFormat="dd/mm/yy"
            placeholder="Ngày tạo"
            size="small"
            fluid
            showOtherMonths
            selectOtherMonths
            hideOnRangeSelection
          />
          <Button
            v-if="
              dealStore.filters.createdDateRange.start || dealStore.filters.createdDateRange.end
            "
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('created_at')"
            aria-label="Clear Created Date Filter"
          />
        </div>
      </template>

      <template #name.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <IconField class="h-10 flex-grow">
            <InputIcon class="pi pi-search" />
            <InputText
              :key="'deal-filter-name-input'"
              v-model="dealStore.filters.dealName"
              @update:modelValue="debouncedApplyFilters()"
              type="text"
              class="h-full w-full"
              placeholder="Tìm tên deal"
              size="small"
              fluid
            />
          </IconField>
          <Button
            v-if="dealStore.filters.dealName"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('name')"
            aria-label="Clear Name Filter"
          />
        </div>
      </template>

      <template #person_id.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <IconField class="h-10 flex-grow">
            <InputIcon class="pi pi-search" />
            <InputText
              :key="'deal-filter-person-input'"
              v-model="dealStore.filters.personInfo"
              @update:modelValue="debouncedApplyFilters()"
              type="text"
              class="h-full w-full"
              placeholder="Tìm tên, SĐT, mã KH"
              size="small"
              fluid
            />
          </IconField>
          <Button
            v-if="dealStore.filters.personInfo"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('person_id')"
            aria-label="Clear Person Filter"
          />
        </div>
      </template>

      <template #stage_id.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <PipelineStageSelect
            :key="'deal-filter-stage'"
            v-model="stageFilterModel"
            placeholder="Lọc stage"
            selectionMode="multiple"
            @update:modelValue="debouncedApplyFilters()"
            class="p-column-filter h-10 w-full flex-grow"
            :pipeline-id="2"
          />
          <Button
            v-if="dealStore.filters.stages && dealStore.filters.stages.length > 0"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('stage_id')"
            aria-label="Clear Stage Filter"
          />
        </div>
      </template>

      <template #related_users.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <UserMultiAssign
            :key="'deal-filter-users'"
            use-prime-vue-input
            v-model="userFilterModel"
            @update:modelValue="debouncedApplyFilters()"
            show-inactive-users
            :max-display="1"
            placeholder="Chọn người liên quan"
            class="p-column-filter !h-10 w-full flex-grow"
          />
          <Button
            v-if="dealStore.filters.relatedUsers && dealStore.filters.relatedUsers.length > 0"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('related_users')"
            aria-label="Clear Related Users Filter"
          />
        </div>
      </template>

      <template #tags.filter="{ filterModel, filterCallback }">
        <div class="p-column-filter flex items-center gap-2">
          <TagCategorySelect
            :key="'deal-filter-tags'"
            v-model="tagFilterModel"
            @update:modelValue="debouncedApplyFilters()"
            placeholder="Lọc tag"
            class="p-column-filter !h-10 w-full flex-grow"
          />
          <Button
            v-if="dealStore.filters.tags && dealStore.filters.tags.length > 0"
            icon="pi pi-filter-slash"
            class="p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text p-datatable-column-filter-clear-button flex-shrink-0"
            @click="dealStore.clearFilterByKey('tags')"
            aria-label="Clear Tags Filter"
          />
        </div>
      </template>
    </DataTable>

    <Popover ref="op" :pt="{ content: { class: 'p-1 max-w-[400px]' } }">
      <TrackUserGroup
        v-if="selectedRowData"
        :sale-user="selectedRowData.sale_user"
        :deal-assignment="selectedRowData.deal_assignment"
      />
    </Popover>

    <DealRatingDialog
      v-model:visible="isRatingDialogVisible"
      :deal="selectedDealForRating"
      @save="handleSaveRating"
    />
  </ContentWithFixedToolbar>
</template>

<script setup lang="ts">
import { useDebounceFn } from "@vueuse/core";
import type { MenuItem } from "primevue/menuitem";
import { PageState } from "primevue/paginator";
import { computed, onBeforeUnmount, onMounted, ref } from "vue";

import type { DealResponse as OriginalDealResponse, UserShort } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { DataTable } from "@/components/DataTable";
import DealFilterPreset from "@/components/Deal/DealFilterPreset.vue";
import { PersonCard } from "@/components/Person";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import PopSetting from "@/components/Settings/PopSetting.vue";
import TagCategorySelect from "@/components/Tags/TagCategorySelect.vue";
import TrackUserGroup from "@/components/Track/TrackUserGroup.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import UserMultiAssign from "@/components/User/UserMultiAssign.vue";
import { dealColumns } from "@/constants/columns/deal-columns";
import useDeal, { type RatingUpdatePayload } from "@/hooks/useDeal";
import ContentWithFixedToolbar from "@/layouts/ContentWithFixedToolbar.vue";
import DealRatingDialog from "@/pages/customer/components/DealsTab/DealRatingDialog.vue";
import { useDealDatatableStore } from "@/stores/deal-datatable-store";
import { useNotiStore } from "@/stores/notification";
import {
  DEAL_FILTER_PRESETS,
  type DealFilterPreset as DealFilterPresetType,
} from "../customer/components/DealsTab/constants";
import DealName from "@/components/Deal/DealName.vue";
import EntityTags from "@/components/Tags/EntityTags.vue";
import Popover from "primevue/popover";

const dealStore = useDealDatatableStore();
const notiStore = useNotiStore();
const { addDealUserRating, updateDealUserRating } = useDeal({ useStore: false });
const columns = dealColumns;

const isRatingDialogVisible = ref(false);
const selectedDealForRating = ref<OriginalDealResponse | null>(null);
const currentPreset = ref<DealFilterPresetType>(DEAL_FILTER_PRESETS.ALL);

interface DealResponseWithSaleUser extends OriginalDealResponse {
  sale_user: UserShort | null;
}

const userFilterModel = computed<UserShort[]>({
  get: () => {
    return dealStore.filters.relatedUsers ?? [];
  },
  set: (selectedUsers: UserShort[] | undefined) => {
    dealStore.filters.relatedUsers =
      selectedUsers && selectedUsers.length > 0 ? selectedUsers : null;
  },
});

const datePickerModel = computed<Date[] | undefined>({
  get: () => {
    const start = dealStore.filters.createdDateRange?.start;
    const end = dealStore.filters.createdDateRange?.end;
    const startDate = start ? new Date(start) : undefined;
    const endDate = end ? new Date(end) : undefined;
    return startDate || endDate ? ([startDate, endDate].filter(Boolean) as Date[]) : undefined;
  },
  set: (value: Date[] | undefined | null) => {
    if (value) {
      dealStore.filters.createdDateRange = {
        start: value[0],
        end: value[1],
      };
    } else {
      dealStore.filters.createdDateRange = { start: null, end: null };
    }
    debouncedApplyFilters();
  },
});

const stageFilterModel = computed<number[] | undefined>({
  get: () => {
    return dealStore.filters.stages ?? undefined; // Return undefined if null
  },
  set: (selectedIds: number[] | undefined) => {
    dealStore.filters.stages = selectedIds && selectedIds.length > 0 ? selectedIds : null;
  },
});

const tagFilterModel = computed<number[] | null>({
  get: () => {
    return dealStore.filters.tags ?? null;
  },
  set: (selectedTagIds: number[] | null | undefined) => {
    dealStore.filters.tags = selectedTagIds && selectedTagIds.length > 0 ? selectedTagIds : null;
  },
});

const paginatorText = computed(() => {
  const total = dealStore.totalDeals || 0;
  if (total === 0) return "0 mục";
  const first = (dealStore.pagination.page - 1) * dealStore.pagination.pageSize + 1;
  const last = Math.min(first + dealStore.pagination.pageSize - 1, total);
  return `Hiển thị ${first} - ${last} của ${total} mục`;
});

const debouncedApplyFilters = useDebounceFn(() => {
  dealStore.applyFilters();
}, 500);

const handleTagFilterChange = (newValue: number[] | null) => {
  dealStore.applyFilters();
};

const refreshDeals = () => {
  dealStore.applyFilters();
};

// --- Event handlers ---
const handlePageChange = (event: PageState) => {
  const newPage = event.page + 1;
  const newPageSize = event.rows;

  if (dealStore.pagination.pageSize !== newPageSize) {
    dealStore.pagination.setPageSize(newPageSize);
  } else if (dealStore.pagination.page !== newPage) {
    dealStore.pagination.goToPage(newPage);
  }
};

const getDealActionItems = (data: OriginalDealResponse): MenuItem[] => {
  const items: MenuItem[] = [];
  items.push({
    label: "Đánh giá",
    icon: "pi pi-star",
    command: () => openRatingDialog(data),
  });
  return items;
};

const openRatingDialog = (deal: OriginalDealResponse | undefined) => {
  if (deal) {
    selectedDealForRating.value = deal;
    isRatingDialogVisible.value = true;
  }
};

const handleSaveRating = async (ratingsToSave: RatingUpdatePayload[]) => {
  if (!selectedDealForRating.value || !ratingsToSave || ratingsToSave.length === 0) {
    isRatingDialogVisible.value = false;
    return;
  }

  try {
    const promises = ratingsToSave.map((ratingData) => {
      if (ratingData.existing_rating_id) {
        return updateDealUserRating({
          id: ratingData.existing_rating_id,
          rating: ratingData.rating,
        });
      } else {
        return addDealUserRating({
          deal_user_id: ratingData.deal_user_id,
          category: ratingData.category,
          rating: ratingData.rating,
        });
      }
    });

    await Promise.all(promises);
    notiStore.success({ message: "Lưu đánh giá thành công." });
    await dealStore.refresh();
  } catch (error) {
    console.error("Failed to save ratings:", error);
    notiStore.error({ message: "Lưu đánh giá thất bại." });
  } finally {
    isRatingDialogVisible.value = false;
  }
};

// --- Combine Users Helper ---
const combineUsers = (dealAssignment: any[]) => {
  const users: any[] = [];
  if (Array.isArray(dealAssignment)) {
    const mappedUsers = dealAssignment
      .filter((u) => u && u.user_id)
      .map((u) => ({
        ...u,
        id: u.user_id,
      }));
    users.push(...mappedUsers);
  }
  return Array.from(new Map(users.map((user) => [user.id, user])).values());
};

const op = ref<InstanceType<typeof Popover> | null>(null);
const selectedRowData = ref<DealResponseWithSaleUser | null>(null);

const toggleUserPopover = (event: MouseEvent, rowData: OriginalDealResponse) => {
  selectedRowData.value = rowData as DealResponseWithSaleUser;
  op.value?.toggle(event);
};

onMounted(() => {
  if (!dealStore.deals || dealStore.deals.length === 0) {
    dealStore.loadInitialData();
  }
});

onBeforeUnmount(() => {
  dealStore.$dispose();
});
</script>
