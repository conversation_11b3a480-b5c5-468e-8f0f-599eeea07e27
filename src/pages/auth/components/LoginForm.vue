<script setup lang="ts">
import <PERSON><PERSON> from "primevue/button";
import { reactive, ref } from "vue";

import { FormInput } from "@/base-components/Form";
import { useAuth } from "@/pages/auth/config/auth-composable";

import OtpForm from "./OtpForm.vue";

const { login, loading } = useAuth();

const formData = reactive({
  username: "",
  password: "",
});

const showOtp = ref(false);

const handleLogin = async () => {
  const result = await login(formData.username, formData.password);
  if (result === "otp_required") {
    showOtp.value = true;
  }
};

const handleBack = () => {
  showOtp.value = false;
};
</script>

<template>
  <OtpForm v-if="showOtp" :username="formData.username" :on-back="handleBack" />
  <div v-else class="my-10 flex h-screen py-5 xl:my-0 xl:h-auto xl:py-0">
    <form
      class="mx-auto my-auto w-full rounded-md bg-white px-5 py-8 shadow-md dark:bg-darkmode-600 sm:w-3/4 sm:px-8 lg:w-2/4 xl:ml-20 xl:w-auto xl:bg-transparent xl:p-0 xl:shadow-none"
      @submit.prevent="handleLogin"
    >
      <h2 class="intro-x text-center text-2xl font-bold xl:text-left xl:text-3xl">Đăng nhập</h2>

      <div class="intro-x mt-8">
        <InputText
          id="username"
          v-model="formData.username"
          type="text"
          class="intro-x login__input block min-w-full px-4 py-3 xl:min-w-[350px]"
          placeholder="Tài khoản"
          :disabled="loading"
        />
        <Password
          id="password"
          v-model="formData.password"
          class="intro-x login__input mt-4 block min-w-full xl:min-w-[350px]"
          :feedback="false"
          placeholder="Mật khẩu"
          :disabled="loading"
          toggleMask
          inputClass="w-full px-4 py-3"
        />
      </div>

      <div class="intro-x mt-5 text-center xl:mt-8 xl:text-left">
        <Button type="submit" :label="'Đăng nhập'" fluid :loading="loading" :disabled="loading" />
      </div>
    </form>
  </div>
</template>
