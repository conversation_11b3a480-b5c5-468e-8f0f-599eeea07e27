<script setup lang="ts">
import Button from "primevue/button";
import InputOtp from "primevue/inputotp";
import { computed, ref } from "vue";

import { useAuth } from "../config/auth-composable";

const props = defineProps<{
  username: string;
  onBack: () => void;
}>();

const { verifyOtp, otpStatus } = useAuth();
const otp = ref("");
const loading = ref(false);

const maskedEmail = computed(() => otpStatus.value.sentTo || "");
const expiresIn = computed(() => otpStatus.value.expiresIn || 300);

const handleVerify = async () => {
  if (otp.value.length !== 6) return;

  loading.value = true;
  try {
    await verifyOtp(props.username, otp.value);
  } finally {
    loading.value = false;
  }
};

const handleResend = () => {
  // Implement resend logic
};
</script>

<template>
  <div class="my-10 flex h-screen py-5 xl:my-0 xl:h-auto xl:py-0">
    <div
      class="mx-auto my-auto w-full rounded-md bg-white px-5 py-8 shadow-md dark:bg-darkmode-600 sm:w-3/4 sm:px-8 lg:w-2/4 xl:ml-20 xl:w-auto xl:bg-transparent xl:p-0 xl:shadow-none"
    >
      <h2 class="intro-x text-center text-2xl font-bold xl:text-left xl:text-3xl">Xác thực OTP</h2>

      <div class="intro-x mt-2 text-slate-400">Mã xác thực đã được gửi đến {{ maskedEmail }}</div>

      <div class="intro-x mt-8 flex flex-col gap-4">
        <InputOtp v-model="otp" :length="6" fluid size="large" class="flex justify-center" />

        <div class="flex justify-between">
          <Button label="Quay lại" link @click="onBack" />
          <Button
            label="Xác nhận"
            :loading="loading"
            :disabled="loading || otp.length !== 6"
            @click="handleVerify"
          />
        </div>
      </div>
    </div>
  </div>
</template>
