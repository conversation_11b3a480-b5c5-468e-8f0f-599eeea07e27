import { computed, readonly, ref } from "vue";

import router from "@/router";
import { useAuthStore, UserBasicInfo } from "@/stores/auth-store";
import { useNotiStore } from "@/stores/notification";

export function useAuth() {
  const authStore = useAuthStore();
  const notiStore = useNotiStore();
  const loading = ref(false);

  const isAuthenticated = computed(() => authStore.isLoggedIn);

  const login = async (username: string, password: string): Promise<boolean | "otp_required"> => {
    loading.value = true;
    try {
      const result = await authStore.login(username, password);
      if (result === "otp_required") {
        return "otp_required";
      }

      if (result) {
        notiStore.success({
          title: "Đăng nhập thành công",
          message: "",
        });
        return true;
      }

      notiStore.error({
        title: "Đăng nhập thất bại",
        message: "<PERSON><PERSON> lòng kiểm tra lại thông tin đăng nhập",
      });
      return false;
    } catch (error) {
      console.error("Login error:", error);
      notiStore.error({
        title: "Đăng nhập thất bại",
        message: "Có lỗi xảy ra khi đăng nhập",
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const logout = async () => {
    try {
      authStore.logout();
      await router.push("/login");
      notiStore.success({
        title: "Đăng xuất thành công",
        message: "",
      });
    } catch (error) {
      console.error("Logout error:", error);
      notiStore.error({
        title: "Đăng xuất thất bại",
        message: "Có lỗi xảy ra khi đăng xuất",
      });
    }
  };

  const getCurrentUser = computed((): UserBasicInfo | null => {
    return authStore.currentUser;
  });

  const getUserFromCookie = (): UserBasicInfo | null => {
    return authStore.getUserFromCookie();
  };

  const verifyOtp = async (username: string, otp: string): Promise<boolean> => {
    try {
      const success = await authStore.verifyOtp(username, otp);
      if (success) {
        notiStore.success({
          title: "Xác thực thành công",
          message: "Đăng nhập thành công",
        });
        return true;
      }
      notiStore.error({
        title: "Xác thực thất bại",
        message: "Mã OTP không chính xác",
      });
      return false;
    } catch (error) {
      console.error("OTP verification error:", error);
      notiStore.error({
        title: "Xác thực thất bại",
        message: "Có lỗi xảy ra khi xác thực OTP",
      });
      return false;
    }
  };

  const otpStatus = computed(() => authStore.otpStatus);

  return {
    isAuthenticated,
    login,
    logout,
    currentUser: getCurrentUser,
    getUserFromCookie,
    verifyOtp,
    otpStatus,
    loading: readonly(loading),
  };
}
