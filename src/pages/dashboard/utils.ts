import { Issue } from "@/api/bcare-types";
import { parseExtraNotes } from "@/pages/customer/components/AppointmentTab/utils";

export const getExpectedTask = (expectedTask: string) => {
  const extraNotes = parseExtraNotes(expectedTask);
  return extraNotes.expectedTask;
};
export const getExpectedTaskOther = (expectedTaskOther: string) => {
  const extraNotes = parseExtraNotes(expectedTaskOther);
  return extraNotes.expectedTaskOther;
};
export const hasComplainIssue = (issues: Issue) => {
  return Array.isArray(issues) && issues.some((issue) => issue.type === "complain");
};
