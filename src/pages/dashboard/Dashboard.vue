<script lang="ts" setup>
// Vue imports
import { storeToRefs } from "pinia";
import { onMounted, ref, watch } from "vue";

import PipelineLayout from "@/components/PipelineLayout.vue";
import { config } from "@/config";
import { useAuthStore } from "@/stores/auth-store";
import { useWsStore, WsStore } from "@/stores/ws-store";

import Toolbar from "./components/Toolbar.vue";

const checkInPerson = ref(0);
const PIPELINE_ID = config.clinic.offline_pipeline_id;
const search = ref("");
const dealCardViewMode = ref({ note: true, user: true, comment: false });

const { currentUser } = useAuthStore();

const wsStore = useWsStore();
const { isReady: isWsReady } = storeToRefs(wsStore);
const { realtime } = wsStore as WsStore;
const WS_ROOM = "pipeline_" + PIPELINE_ID;

onMounted(async () => {
  watch(isWsReady, (newIsReady) => {
    if (newIsReady) {
      realtime.join(WS_ROOM);
      realtime.emit("user_join", currentUser?.id);
      realtime.on("user_join", (msg) => {
        console.log("user_id: " + msg.data);
      });
    }
  });
});

const handleUpdateCheckinPerson = (personId: number) => {
  checkInPerson.value = personId;
};
</script>

<template>
  <Toolbar
    v-model:deal-card-view-mode="dealCardViewMode"
    v-model:search="search"
    :check-in-person="handleUpdateCheckinPerson"
    @update-check-in-person="handleUpdateCheckinPerson"
  >
    <PipelineLayout
      :filter-track="search"
      :new-person="checkInPerson"
      :pipeline-id="PIPELINE_ID"
      :toggle-mode="dealCardViewMode"
    />
  </Toolbar>
</template>
