<template>
  <div class="flex">
    <button
      :class="[
        'inline-flex cursor-pointer items-center justify-center border px-3 py-2 font-medium shadow-sm transition duration-200 disabled:cursor-not-allowed disabled:opacity-70 [&:hover:not(:disabled)]:border-opacity-90 [&:hover:not(:disabled)]:bg-opacity-90 [&:not(button)]:text-center',
        modelValue.note
          ? 'border-primary bg-primary text-white dark:border-primary'
          : 'border-primary text-primary dark:border-primary [&:hover:not(:disabled)]:bg-primary/10',
        'rounded-none rounded-l-md border-r-transparent',
      ]"
      @click="toggleMode('note')"
    >
      <Lucide icon="List" class="h-5 w-5 stroke-[1.3]" />
    </button>
    <button
      :class="[
        'inline-flex cursor-pointer items-center justify-center border px-3 py-2 font-medium shadow-sm transition duration-200 disabled:cursor-not-allowed disabled:opacity-70 [&:hover:not(:disabled)]:border-opacity-90 [&:hover:not(:disabled)]:bg-opacity-90 [&:not(button)]:text-center',
        modelValue.user
          ? 'border-primary bg-primary text-white dark:border-primary'
          : 'border-primary text-primary dark:border-primary [&:hover:not(:disabled)]:bg-primary/10',
        'rounded-none border-r-transparent',
      ]"
      @click="toggleMode('user')"
    >
      <Lucide icon="User" class="h-5 w-5 stroke-[1.3]" />
    </button>
    <button
      :class="[
        'inline-flex cursor-pointer items-center justify-center border px-3 py-2 font-medium shadow-sm transition duration-200 disabled:cursor-not-allowed disabled:opacity-70 [&:hover:not(:disabled)]:border-opacity-90 [&:hover:not(:disabled)]:bg-opacity-90 [&:not(button)]:text-center',
        modelValue.comment
          ? 'border-primary bg-primary text-white dark:border-primary'
          : 'border-primary text-primary dark:border-primary [&:hover:not(:disabled)]:bg-primary/10',
        'rounded-none rounded-r-md',
      ]"
      @click="toggleMode('comment')"
    >
      <Lucide icon="MessageSquare" class="h-5 w-5 stroke-[1.3]" />
    </button>
  </div>
</template>

<script setup lang="ts">
import Lucide from "@/base-components/Lucide";

export interface ToggleState {
  note: boolean;
  user: boolean;
  comment: boolean;
}

const props = withDefaults(
  defineProps<{
    modelValue: ToggleState;
  }>(),
  {
    modelValue: () => ({ note: false, user: false, comment: false }),
  },
);

const emit = defineEmits<{
  (e: "update:modelValue", value: ToggleState): void;
}>();

const toggleMode = (mode: keyof ToggleState) => {
  const newState = { ...props.modelValue, [mode]: !props.modelValue[mode] };
  emit("update:modelValue", newState);
};
</script>
