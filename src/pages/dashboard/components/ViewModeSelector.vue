<template>
  <div class="">
    <Button
      :variant="modelValue === 'collapsed' ? 'primary' : 'outline-primary'"
      class="rounded-none rounded-l-md border-r-transparent"
      @click="updateViewMode('collapsed')"
    >
      <Lucide icon="List" class="h-5 w-5" />
    </Button>
    <Button
      :variant="modelValue === 'compact' ? 'primary' : 'outline-primary'"
      class="rounded-none border-r-transparent"
      @click="updateViewMode('compact')"
    >
      <Lucide icon="Server" class="h-5 w-5" />
    </Button>
    <Button
      :variant="modelValue === 'expanded' ? 'primary' : 'outline-primary'"
      class="rounded-none rounded-r-md"
      @click="updateViewMode('expanded')"
    >
      <Lucide icon="Tablet" class="h-5 w-5" />
    </Button>
  </div>
</template>

<script setup lang="ts">
import Button from "@/base-components/Button";
import Lucide from "@/base-components/Lucide";

type ViewMode = "collapsed" | "compact" | "expanded";

const props = defineProps<{
  modelValue: ViewMode;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: ViewMode): void;
}>();

const updateViewMode = (mode: ViewMode) => {
  emit("update:modelValue", mode);
};
</script>
