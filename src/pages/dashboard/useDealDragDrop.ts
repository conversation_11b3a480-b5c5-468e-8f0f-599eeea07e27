import { reactive, Ref, ref, toValue } from "vue";

import { DealStatus } from "@/api/bcare-enum";
import { DealResponse } from "@/api/bcare-types-v2";
import { StageResponse } from "@/api/bcare-types-v2";
import { dealUpdate } from "@/api/bcare-v2";
import { WsStore } from "@/stores/ws-store";

export default function useDealDragDrop(
  dealList: Ref<DealResponse[]>,
  wsStore: WsStore,
  WS_ROOM: string,
) {
  const draggingDeals = ref<Set<number>>(new Set());
  const dealId = ref<number>(0);
  const dragStageId = ref<number>(0);
  const stageDragId = ref<number>(0);
  const drag = ref(false);

  const { realtime } = wsStore;

  const dealsByStageId = reactive<{ [key: number]: DealResponse[] }>({});

  const initializeDealsByStageId = (stages: StageResponse[]) => {
    if (!stages || !Array.isArray(stages)) {
      stages = toValue(stages);
    }

    const tempDealsByStageId: { [key: number]: DealResponse[] } = {};
    // Initialize each stage with an empty array
    stages.forEach((stage) => {
      tempDealsByStageId[stage.id] = [];
    });

    // Populate the stages with deals
    dealList.value.forEach((deal) => {
      const stageId = deal.stage_id;
      if (!tempDealsByStageId[stageId]) {
        tempDealsByStageId[stageId] = [];
      }
      tempDealsByStageId[stageId].push(deal);
    });

    // Assign tempDealsByStageId to dealsByStageId
    Object.assign(dealsByStageId, tempDealsByStageId);
  };

  const insertDeal = (deal: DealResponse) => {
    const stageId = deal.stage_id;
    if (!dealsByStageId[stageId]) {
      dealsByStageId[stageId] = [];
    }

    // Insert the deal at the beginning of the stage
    dealsByStageId[stageId].unshift(deal);

    // Also add to dealList if it's not already there
    if (!dealList.value.some((d) => d.id === deal.id)) {
      dealList.value.push(deal);
    }
  };

  const removeDealFromStage = (dealId: number, stageId: number) => {
    const stageDeals = dealsByStageId[stageId];
    if (Array.isArray(stageDeals)) {
      const index = stageDeals.findIndex((deal) => deal.id === dealId);
      if (index !== -1) {
        stageDeals.splice(index, 1);
      }
    }
  };

  const addDealToStage = (deal: DealResponse, stageId: number, position: number) => {
    if (!(stageId in dealsByStageId) || !Array.isArray(dealsByStageId[stageId])) {
      dealsByStageId[stageId] = [];
    }

    const existingIndex = dealsByStageId[stageId].findIndex((d) => d.id === deal.id);

    if (existingIndex !== -1) {
      // Nếu deal đã tồn tại, xóa nó
      dealsByStageId[stageId].splice(existingIndex, 1);
      // Điều chỉnh vị trí chèn nếu cần
      if (existingIndex < position) {
        position--;
      }
    }

    // Đảm bảo position nằm trong khoảng hợp lệ
    position = Math.max(0, Math.min(position, dealsByStageId[stageId].length));

    // Chèn deal vào vị trí mong muốn
    dealsByStageId[stageId].splice(position, 0, deal);
  };

  const replaceDealInLists = (oldDealId: number, newDeal: DealResponse) => {
    const oldDealIndex = dealList.value.findIndex((d) => d.id === oldDealId);
    if (oldDealIndex !== -1) {
      dealList.value.splice(oldDealIndex, 1, newDeal);
    }

    Object.values(dealsByStageId).forEach((stageDeals) => {
      const stageIndex = stageDeals.findIndex((d) => d.id === oldDealId);
      if (stageIndex !== -1) {
        stageDeals.splice(stageIndex, 1, newDeal);
      }
    });
  };

  const onStart = (evt: any) => {
    const { clone } = evt;
    const dealID = +clone.dataset.dealid;

    const fromStageId = +clone.dataset.stageid;
    stageDragId.value = fromStageId || 0;
    drag.value = true;

    broadcastDealDragStart(dealID);
    return { dealID, fromStageId };
  };

  const onEnd = async (evt: any) => {
    const { clone, to, newIndex } = evt;
    const dealID = +clone.dataset.dealid;
    const dropStageId = +to.dataset.stageid;
    const fromStageId = +clone.dataset.stageid;
    drag.value = false;

    broadcastDealDragEnd(dealID, dropStageId, newIndex);

    if (fromStageId != dropStageId) {
      const deal = dealList.value.find((d) => d.id === dealID);
      if (deal) {
        deal.stage_id = dropStageId;
        broadcastDealUpdate(dealID, dropStageId, fromStageId, newIndex);
        const res = await dealUpdate({ id: deal.id, stage_id: dropStageId });
        if (res.code === 0 && res.data) {
          //TODO Trigger reload
        }
      }
    }

    return { dealID, dropStageId, fromStageId, newIndex };
  };

  //Deprecate
  const handleChangeDealStage = async (
    dealId: number,
    newStageId: number,
    oldStageId: number,
    newIndex: number,
  ) => {
    const deal = dealList.value.find((d) => d.id === dealId);
    if (deal) {
      //We broadcast deal stage change even before call dealUpdate
      broadcastDealUpdate(dealId, newStageId, oldStageId, newIndex);
      const res = await dealUpdate({ id: deal.id, stage_id: newStageId });
      if (res.code === 0 && res.data) {
        const updatedDeal = res.data;
        removeDealFromStage(deal.id, oldStageId);
        addDealToStage(updatedDeal, newStageId, newIndex);
        return updatedDeal;
      }
    }
    return null;
  };

  const switchDeal = (oldDealId: number, newDeal: DealResponse) => {
    replaceDealInLists(oldDealId, newDeal);
    broadcastDealSwitch(oldDealId, newDeal);
  };

  const handleDealSwitch = (oldDealId: number, newDeal: DealResponse) => {
    replaceDealInLists(oldDealId, newDeal);
  };

  const onMove = (evt: any) => {
    dealId.value = evt.draggedContext.element.id;
    dragStageId.value = evt.from.dataset.stageid;
    return true;
  };

  // WebSocket broadcast functions
  const broadcastDealDragStart = (dealID: number) => {
    realtime.broadcast.to(WS_ROOM).emit("deal_drag_start", dealID);
  };

  const broadcastDealDragEnd = (dealID: number, dropStageId: number, newIndex: number) => {
    realtime.broadcast.to(WS_ROOM).emit("deal_drag_end", { dealID, dropStageId, newIndex });
  };

  const broadcastDealUpdate = (
    dealId: number,
    newStageId: number,
    oldStageId: number,
    newIndex: number,
  ) => {
    realtime.broadcast
      .to(WS_ROOM)
      .emit("deal_update", { dealId, newStageId, oldStageId, newIndex });
  };

  const broadcastDealAdd = (newDeal: DealResponse) => {
    realtime.broadcast.to(WS_ROOM).emit("deal_add", newDeal);
  };

  const broadcastDealSwitch = (oldDealId: number, newDeal: DealResponse) => {
    realtime.broadcast.to(WS_ROOM).emit("deal_switch", { oldDealId, newDeal });
  };

  // WebSocket event listeners setup
  const setupDealWebSocketListeners = () => {
    realtime.on("deal_stage_change", () => {
      // const updatedDeal = msg.data;
      //addDealToStage(updatedDeal, updatedDeal.stage_id, 0);
    });

    realtime.on("deal_add", (msg) => {
      const newDeal = msg.data;
      insertDeal(newDeal);
    });

    realtime.on("deal_drag_start", (msg) => {
      draggingDeals.value.add(+msg.data);
    });

    realtime.on("deal_update", (msg) => {
      const { dealId, newStageId, oldStageId, newIndex } = msg.data;
      const deal = dealList.value.find((d) => d.id === dealId);
      if (deal) {
        //TODO Use status to force refresh on DealCard. Is it a proper way lol?
        deal.status = DealStatus.REFRESH;

        if (deal.stage_id !== newStageId) {
          deal.stage_id = newStageId;
          removeDealFromStage(deal.id, oldStageId);

          if (newStageId != 0) {
            addDealToStage(deal, newStageId, newIndex);
          }
        } else {
          // We temporarily do not support reordering deals within the same stage
          //replaceDealInLists(updatedDeal.id, updatedDeal);
        }
      }
    });

    realtime.on("deal_switch", (msg) => {
      const { oldDealId, newDeal } = msg.data;
      handleDealSwitch(oldDealId, newDeal);
    });

    realtime.on("deal_drag_end", (msg) => {
      const { dealID } = msg.data;
      draggingDeals.value.delete(dealID);
    });
  };

  return {
    drag,
    draggingDeals,
    onStart,
    onEnd,
    onMove,
    handleChangeDealStage,
    addDealToStage,
    broadcastDealAdd,
    broadcastDealUpdate,
    setupDealWebSocketListeners,
    switchDeal,
    dealsByStageId,
    initializeDealsByStageId,
    insertDeal,
    removeDealFromStage,
  };
}
