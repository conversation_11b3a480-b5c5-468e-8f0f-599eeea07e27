<script setup lang="ts">
import Button from "@/base-components/Button";
import { FormCheck, FormInput } from "@/base-components/Form";
import DarkModeSwitcher from "@/components/DarkModeSwitcher";
import MainColorSwitcher from "@/components/MainColorSwitcher";

import illustrationUrl from "@/assets/images/illustration.svg";
import logoUrl from "@/assets/images/logo.svg";
</script>

<template>
  <div
    :class="[
      'relative -m-3 h-screen bg-primary p-3 dark:bg-darkmode-800 sm:-mx-8 sm:px-8 lg:overflow-hidden xl:bg-white xl:dark:bg-darkmode-600',
      'before:absolute before:inset-y-0 before:left-0 before:-mb-[16%] before:-ml-[13%] before:-mt-[28%] before:hidden before:w-[57%] before:rotate-[-4.5deg] before:transform before:rounded-[100%] before:bg-primary/20 before:content-[\'\'] before:dark:bg-darkmode-400 before:xl:block',
      'after:absolute after:inset-y-0 after:left-0 after:-mb-[13%] after:-ml-[13%] after:-mt-[20%] after:hidden after:w-[57%] after:rotate-[-4.5deg] after:transform after:rounded-[100%] after:bg-primary after:content-[\'\'] after:dark:bg-darkmode-700 after:xl:block',
    ]"
  >
    <DarkModeSwitcher />
    <MainColorSwitcher />
    <div class="container relative z-10 sm:px-10">
      <div class="block grid-cols-2 gap-4 xl:grid">
        <!-- BEGIN: Register Info -->
        <div class="hidden min-h-screen flex-col xl:flex">
          <a href="" class="-intro-x flex items-center pt-5">
            <img alt="Midone Tailwind HTML Admin Template" class="w-6" :src="logoUrl" />
            <span class="ml-3 text-lg text-white"> Rubick </span>
          </a>
          <div class="my-auto">
            <img
              alt="Midone Tailwind HTML Admin Template"
              class="-intro-x -mt-16 w-1/2"
              :src="illustrationUrl"
            />
            <div class="-intro-x mt-10 text-4xl font-medium leading-tight text-white">
              A few more clicks to <br />
              sign up to your account.
            </div>
            <div class="-intro-x mt-5 text-lg text-white text-opacity-70 dark:text-slate-400">
              Manage all your e-commerce accounts in one place
            </div>
          </div>
        </div>
        <!-- END: Register Info -->
        <!-- BEGIN: Register Form -->
        <div class="my-10 flex h-screen py-5 xl:my-0 xl:h-auto xl:py-0">
          <div
            class="mx-auto my-auto w-full rounded-md bg-white px-5 py-8 shadow-md dark:bg-darkmode-600 sm:w-3/4 sm:px-8 lg:w-2/4 xl:ml-20 xl:w-auto xl:bg-transparent xl:p-0 xl:shadow-none"
          >
            <h2 class="intro-x text-center text-2xl font-bold xl:text-left xl:text-3xl">Sign Up</h2>
            <div class="intro-x mt-2 text-center text-slate-400 dark:text-slate-400 xl:hidden">
              A few more clicks to sign in to your account. Manage all your e-commerce accounts in
              one place
            </div>
            <div class="intro-x mt-8">
              <FormInput
                type="text"
                class="intro-x login__input block min-w-full px-4 py-3 xl:min-w-[350px]"
                placeholder="First Name"
              />
              <FormInput
                type="text"
                class="intro-x login__input mt-4 block min-w-full px-4 py-3 xl:min-w-[350px]"
                placeholder="Last Name"
              />
              <FormInput
                type="text"
                class="intro-x login__input mt-4 block min-w-full px-4 py-3 xl:min-w-[350px]"
                placeholder="Email"
              />
              <FormInput
                type="text"
                class="intro-x login__input mt-4 block min-w-full px-4 py-3 xl:min-w-[350px]"
                placeholder="Password"
              />
              <div class="intro-x mt-3 grid h-1 w-full grid-cols-12 gap-4">
                <div class="col-span-3 h-full rounded bg-success"></div>
                <div class="col-span-3 h-full rounded bg-success"></div>
                <div class="col-span-3 h-full rounded bg-success"></div>
                <div class="col-span-3 h-full rounded bg-slate-100 dark:bg-darkmode-800"></div>
              </div>
              <a href="" class="intro-x mt-2 block text-xs text-slate-500 sm:text-sm">
                What is a secure password?
              </a>
              <FormInput
                type="text"
                class="intro-x login__input mt-4 block min-w-full px-4 py-3 xl:min-w-[350px]"
                placeholder="Password Confirmation"
              />
            </div>
            <div
              class="intro-x mt-4 flex items-center text-xs text-slate-600 dark:text-slate-500 sm:text-sm"
            >
              <FormCheck.Input id="remember-me" type="checkbox" class="mr-2 border" />
              <label class="cursor-pointer select-none" htmlFor="remember-me">
                I agree to the Envato
              </label>
              <a class="ml-1 text-primary dark:text-slate-200" href=""> Privacy Policy </a>
              .
            </div>
            <div class="intro-x mt-5 text-center xl:mt-8 xl:text-left">
              <Button variant="primary" class="w-full px-4 py-3 align-top xl:mr-3 xl:w-32">
                Register
              </Button>
              <Button
                variant="outline-secondary"
                class="mt-3 w-full px-4 py-3 align-top xl:mt-0 xl:w-32"
              >
                Sign in
              </Button>
            </div>
          </div>
        </div>
        <!-- END: Register Form -->
      </div>
    </div>
  </div>
</template>
