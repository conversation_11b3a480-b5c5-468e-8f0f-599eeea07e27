<template>
  <div class="p-4 md:p-6">
    <h2 class="mb-6 text-xl font-semibold text-surface-800 dark:text-surface-100">
      Task Template Test - New Reusable Component
    </h2>

    <div class="space-y-4">
      <!-- Comparison Section -->
      <div class="rounded-lg border border-gray-200 p-4">
        <h3 class="mb-3 text-lg font-medium">Comparison: Old vs New Implementation</h3>
        
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <!-- Old Implementation -->
          <div class="rounded-md bg-red-50 p-3">
            <h4 class="font-medium text-red-800">Old Implementation (test.vue)</h4>
            <ul class="mt-2 text-sm text-red-700">
              <li>• 162 lines of code</li>
              <li>• Duplicated logic in each component</li>
              <li>• No API integration for last task check</li>
              <li>• Manual template selection only</li>
              <li>• Hard to maintain and reuse</li>
            </ul>
          </div>

          <!-- New Implementation -->
          <div class="rounded-md bg-green-50 p-3">
            <h4 class="font-medium text-green-800">New Implementation</h4>
            <ul class="mt-2 text-sm text-green-700">
              <li>• Reusable component + composable</li>
              <li>• Automatic last task detection</li>
              <li>• Clean API integration</li>
              <li>• Can be used anywhere</li>
              <li>• Easy to maintain and extend</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Demo Buttons -->
      <div class="space-y-3">
        <h3 class="text-lg font-medium">Demo Actions</h3>
        
        <div class="flex flex-wrap gap-2">
          <Button
            label="Manual Template Selection"
            icon="pi pi-list"
            @click="showManualTemplateSelector"
            class="bg-primary px-4 py-2 font-medium text-white hover:bg-primary-600"
          />
          
          <Button
            label="Simulate Task Completion"
            icon="pi pi-check"
            @click="simulateTaskCompletion"
            class="bg-green-600 px-4 py-2 font-medium text-white hover:bg-green-700"
          />
          
          <Button
            label="Test with Specific Task"
            icon="pi pi-cog"
            @click="testWithSpecificTask"
            class="bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700"
          />
        </div>

        <!-- Input for testing -->
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium">Test Task ID:</label>
          <InputNumber
            v-model="testTaskId"
            placeholder="Enter task ID"
            class="w-32"
          />
          <label class="text-sm font-medium">Serial:</label>
          <InputNumber
            v-model="testTaskSerial"
            placeholder="Optional"
            class="w-32"
          />
        </div>
      </div>

      <!-- Status Display -->
      <div v-if="lastAction" class="rounded-md bg-gray-100 p-4">
        <h4 class="font-medium">Last Action Result:</h4>
        <div class="mt-2 space-y-1 text-sm">
          <div><strong>Action:</strong> {{ lastAction.action }}</div>
          <div v-if="lastAction.taskId"><strong>Task ID:</strong> {{ lastAction.taskId }}</div>
          <div v-if="lastAction.serial"><strong>Serial:</strong> {{ lastAction.serial }}</div>
          <div v-if="lastAction.dialogShown !== undefined">
            <strong>Dialog Shown:</strong> {{ lastAction.dialogShown ? 'Yes' : 'No' }}
          </div>
          <div v-if="lastAction.templateId">
            <strong>Template Selected:</strong> {{ lastAction.templateId }}
          </div>
          <div><strong>Timestamp:</strong> {{ lastAction.timestamp }}</div>
        </div>
      </div>

      <!-- Usage Example -->
      <div class="rounded-lg border border-gray-200 p-4">
        <h3 class="mb-3 text-lg font-medium">Usage Example</h3>
        <pre class="overflow-x-auto rounded bg-gray-100 p-3 text-sm"><code>// In your component:
&lt;TaskTemplateSelector
  ref="taskTemplateSelector"
  :person-id="personId"
  @task-created="handleTaskCreated"
  @template-selected="handleTemplateSelected"
  @cancelled="handleCancelled"
/&gt;

// In your task completion handler:
const handleTaskCompletion = async (taskId, serial) => {
  await updateTaskStatus(taskId, "completed");
  
  if (taskTemplateSelector.value) {
    await taskTemplateSelector.value
      .checkAndShowTemplateSelector(taskId, serial);
  }
};</code></pre>
      </div>
    </div>

    <!-- The New Reusable Component -->
    <TaskTemplateSelector
      ref="taskTemplateSelector"
      :person-id="123"
      @task-created="handleTaskCreated"
      @template-selected="handleTemplateSelected"
      @cancelled="handleCancelled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Button from "primevue/button";
import InputNumber from "primevue/inputnumber";
import TaskTemplateSelector from "@/components/Task/TaskTemplateSelector.vue";

// Refs
const taskTemplateSelector = ref<InstanceType<typeof TaskTemplateSelector>>();
const testTaskId = ref<number>(1);
const testTaskSerial = ref<number | undefined>(undefined);
const lastAction = ref<any>(null);

// Demo methods
const showManualTemplateSelector = async () => {
  if (taskTemplateSelector.value) {
    await taskTemplateSelector.value.showTemplateSelector();
    lastAction.value = {
      action: "Manual template selector opened",
      timestamp: new Date().toLocaleTimeString(),
    };
  }
};

const simulateTaskCompletion = async () => {
  // Simulate a task completion flow
  const taskId = Math.floor(Math.random() * 1000) + 1;
  const serial = Math.floor(Math.random() * 5) + 1;
  
  if (taskTemplateSelector.value) {
    const dialogShown = await taskTemplateSelector.value.checkAndShowTemplateSelector(taskId, serial);
    lastAction.value = {
      action: "Simulated task completion",
      taskId,
      serial,
      dialogShown,
      timestamp: new Date().toLocaleTimeString(),
    };
  }
};

const testWithSpecificTask = async () => {
  if (taskTemplateSelector.value && testTaskId.value) {
    const dialogShown = await taskTemplateSelector.value.checkAndShowTemplateSelector(
      testTaskId.value,
      testTaskSerial.value
    );
    lastAction.value = {
      action: "Tested with specific task",
      taskId: testTaskId.value,
      serial: testTaskSerial.value,
      dialogShown,
      timestamp: new Date().toLocaleTimeString(),
    };
  }
};

// Event handlers
const handleTaskCreated = () => {
  lastAction.value = {
    action: "Task created successfully",
    timestamp: new Date().toLocaleTimeString(),
  };
  console.log("Task created successfully");
};

const handleTemplateSelected = (templateId: string) => {
  lastAction.value = {
    action: "Template selected",
    templateId,
    timestamp: new Date().toLocaleTimeString(),
  };
  console.log("Template selected:", templateId);
};

const handleCancelled = () => {
  lastAction.value = {
    action: "Template selection cancelled",
    timestamp: new Date().toLocaleTimeString(),
  };
  console.log("Template selection cancelled");
};
</script>
