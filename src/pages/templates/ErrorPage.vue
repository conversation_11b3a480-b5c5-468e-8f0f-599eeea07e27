<script setup lang="ts">
import Button from "@/base-components/Button";
import DarkModeSwitcher from "@/components/DarkModeSwitcher";
import MainColorSwitcher from "@/components/MainColorSwitcher";

import errorIllustration from "@/assets/images/error-illustration.svg";
</script>

<template>
  <div class="py-2">
    <DarkModeSwitcher />
    <MainColorSwitcher />
    <div class="container">
      <!-- BEGIN: Error Page -->
      <div
        class="error-page flex h-screen flex-col items-center justify-center text-center lg:flex-row lg:text-left"
      >
        <div class="-intro-x lg:mr-20">
          <img
            alt="Midone Tailwind HTML Admin Template"
            class="h-48 w-[450px] lg:h-auto"
            :src="errorIllustration"
          />
        </div>
        <div class="mt-10 text-white lg:mt-0">
          <div class="intro-x text-8xl font-medium">404</div>
          <div class="intro-x mt-5 text-xl font-medium lg:text-3xl">
            Oops. This page has gone missing.
          </div>
          <div class="intro-x mt-3 text-lg">
            You may have mistyped the address or the page may have moved.
          </div>
          <Button
            class="intro-x mt-10 border-white px-4 py-3 text-white dark:border-darkmode-400 dark:text-slate-200"
          >
            Back to Home
          </Button>
        </div>
      </div>
      <!-- END: Error Page -->
    </div>
  </div>
</template>
