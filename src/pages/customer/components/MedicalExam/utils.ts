import { AnswerType } from "@/api/bcare-enum";
import { Question, Response } from "@/api/bcare-types";

import {
  configKhamRang,
  dentalExaminationAdultConfig,
  dentalExaminationChildrenConfig,
} from "./contants";
export interface AnswerResponse {
  bool?: boolean;
  contentBool?: string;
  int?: number;
  string?: string;
  text?: string;
  list?: string[];
  tableChoice?: {
    question: string;
    childQuestions: string[];
    answer: {
      [key: number]: string;
    };
  };
}

export interface KhamRangAnswer {
  type: "adult" | "children";
  answer: Record<string, { note: string; checks: Record<string, boolean> }>;
}

export const parseAnswer = (answer_value: string): { checked: string; text: string } => {
  try {
    const data = JSON.parse(answer_value);

    if (data && typeof data === "object" && !Array.isArray(data)) {
      const { checked, text } = data;
      return {
        checked: typeof checked === "string" ? checked : "",
        text: typeof text === "string" ? text : "",
      };
    }
    return {
      checked: "",
      text: "",
    };
  } catch (error) {
    return {
      checked: "",
      text: "",
    };
  }
};

export const parseAnswerKhamRang = (answer_value: string): KhamRangAnswer => {
  try {
    const { answer, type } = JSON.parse(answer_value);
    const dentalExaminationToothConfig =
      type === "adult" ? dentalExaminationAdultConfig.tooth : dentalExaminationChildrenConfig.tooth;
    if (answer && typeof answer === "object" && !Array.isArray(answer)) {
      return {
        type: type === "children" ? "children" : "adult",
        answer: dentalExaminationToothConfig.reduce((result, item) => {
          const content = answer[item.id];
          if (content && typeof content === "object") {
            const { note, checks } = content;
            return {
              ...result,
              [item.id]: {
                note: typeof note === "string" ? note : "",
                checks: configKhamRang.reduce((checkResult, check) => {
                  return { ...checkResult, [check.id]: !!checks?.[check.id] };
                }, {}),
              },
            };
          }

          return {
            ...result,
            [item.id]: {
              note: "",
              checks: configKhamRang.reduce((checkResult, check) => {
                return { ...checkResult, [check.id]: false };
              }, {}),
            },
          };
        }, {}),
      };
    }
    return {
      type: type === "children" ? "children" : "adult",
      answer: dentalExaminationToothConfig.reduce(
        (result, item) => ({
          ...result,
          [item.id]: {
            note: "",
            checks: configKhamRang.reduce((checkResult, check) => {
              return { ...checkResult, [check.id]: false };
            }, {}),
          },
        }),
        {},
      ),
    };
  } catch (error) {
    return {
      type: "adult",
      answer: dentalExaminationAdultConfig.tooth.reduce(
        (result, item) => ({
          ...result,
          [item.id]: {
            note: "",
            checks: configKhamRang.reduce((checkResult, check) => {
              return { ...checkResult, [check.id]: false };
            }, {}),
          },
        }),
        {},
      ),
    };
  }
};

export const parseAnswerTiltedSkull = (
  answer_value: string,
): { afterTreatment: string; beforeTreatment: string; evaluate: string } => {
  try {
    const data = JSON.parse(answer_value);

    if (data && typeof data === "object" && !Array.isArray(data)) {
      const { afterTreatment, evaluate, beforeTreatment } = data;
      return {
        afterTreatment: typeof afterTreatment === "string" ? afterTreatment : "",
        beforeTreatment: typeof beforeTreatment === "string" ? beforeTreatment : "",
        evaluate: typeof evaluate === "string" ? evaluate : "",
      };
    }
    return {
      afterTreatment: "",
      beforeTreatment: "",
      evaluate: "",
    };
  } catch (error) {
    return {
      afterTreatment: "",
      beforeTreatment: "",
      evaluate: "",
    };
  }
};
export const getText = (text: string) => {
  const description = parseAnswer(text);
  return description.text;
};
export const initResponses = (question: Question, responseList: { [key: number]: Response }) => {
  const defaultResponse: AnswerResponse = {};
  const answer_value = responseList[question.id]?.answer_value;
  const answer = JSON.parse(answer_value || "{}");
  switch (question.answer_type) {
    case AnswerType.TABLE_CHOICE:
      defaultResponse.tableChoice = {
        question: question.question_text,
        childQuestions: question.options.split("|"),
        answer,
      };
      break;
    case AnswerType.SINGLE_CHOICE:
    case AnswerType.BOOLEAN:
      defaultResponse.bool = !!answer_value;
      defaultResponse.contentBool = getText(answer_value) ?? "";
      break;
    case AnswerType.INTEGER:
      defaultResponse.int = answer_value ? Number(answer_value) : 0;
      break;
    case AnswerType.MULTI_CHOICE:
    case AnswerType.STRING:
    case AnswerType.TEXT:
      defaultResponse.string = answer_value ?? "";
      break;
    case AnswerType.LIST:
      defaultResponse.list = answer_value ? answer_value.split(" | ") : [];
      break;
    default:
      defaultResponse.int = 0;
  }
  return defaultResponse;
};

export function stringToOptions(optionsString: string): string[] {
  return optionsString.split("|");
}
