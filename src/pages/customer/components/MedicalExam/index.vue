<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";

import { usePrint } from "@/composables/usePrint";

import { examTabs } from "./contants";

const props = defineProps<{ personId?: number; personName?: string }>();

const currentTab = ref(0);
const { printMedicalExam, printOutpatientMedical } = usePrint();

const comps = [
  defineAsyncComponent(() => import("./DynamicComponent/BenhAnNgoaiTru.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/DauSinhHieu.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/TienSuYKhoa.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/TienSuNhaKhoa.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/LyDoKham.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/KhamNgoaiMat.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/KhamRang.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/CanKhop.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/KhamCoLuoi.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/DanhGiaCN.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/VanDong.vue")),
  defineAsyncComponent(() => import("./DynamicComponent/ChiDinh.vue")),
];

const handlePrint = () => {
  if (!props.personId) return;

  if (currentTab.value === 0 || currentTab.value === 1) {
    printOutpatientMedical(props.personId);
  } else {
    printMedicalExam(props.personId, {
      tabIndex: currentTab.value,
    });
  }
};
</script>
<template>
  <div class="px-5">
    <Tabs v-model:value="currentTab" scrollable unstyled lazy>
      <TabList
        :pt="{
          activeBar: { style: { display: 'none !important' } },
          tabList: { style: { border: 'none' } },
        }"
        class="b-tab-list"
      >
        <Tab v-for="(tab, index) in examTabs" :key="index" :value="index">
          {{ tab.text }}
        </Tab>
      </TabList>
      <TabPanels unstyled>
        <TabPanel v-for="(tab, index) in examTabs" :key="index" :value="index" unstyled>
          <component :is="comps[index]" :person-id="props.personId ?? 0" />
        </TabPanel>
      </TabPanels>
    </Tabs>
    <div class="border-t border-slate-200/60 pt-5 dark:border-darkmode-400">
      <div class="flex items-center justify-end">
        <Button
          @click="handlePrint"
          icon="pi pi-print"
          class="text-sm"
          :label="
            currentTab === 0 || currentTab === 1
              ? 'In bệnh án'
              : `In giai đoạn ${Number(currentTab) < 5 ? 1 : 2}`
          "
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.b-tab-list {
  @apply overflow-hidden rounded-md border border-gray-200;
}

.p-tab {
  @apply border-none px-4 py-3 text-sm font-normal hover:border-gray-300 hover:text-gray-700 focus:outline-none;
}

.p-tab-active {
  @apply border-none bg-slate-100 text-primary;
}
</style>
