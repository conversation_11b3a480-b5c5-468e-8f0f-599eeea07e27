<script setup lang="ts">
import Tooth from "./Tooth.vue";
import ToothGap from "./ToothGap.vue";

interface Props {
  position: number;
  state: Record<string, boolean>;
  treatments: Record<string, any>;
  mode: "state" | "treatment";
  gapAnswers: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  gapAnswers: () => ({}),
});

const emit = defineEmits<{
  (e: "updateState", position: number, key: string, value: boolean): void;
  (e: "rightClick", position: number, event: MouseEvent): void;
  (e: "updateGap", id: string, value: string): void;
}>();

const handleUpdateState = (key: string, value: boolean) => {
  emit("updateState", props.position, key, value);
};

const handleRightClick = (position: number, event: MouseEvent) => {
  emit("rightClick", position, event);
};

// <PERSON><PERSON><PERSON> l<PERSON>y răng kế tiếp dựa trên vị tr<PERSON> hiện tại
const getNextToothNumber = (position: number): number | null => {
  const arch = Math.floor(position / 10);
  const toothPosition = position % 10;

  // Răng gi<PERSON>a ng<PERSON><PERSON>i lớn (41-31)
  if (position === 41) return 31;
  if (position === 31) return 32;
  if (position === 21) return 22;
  if (position === 11) return 21;

  // Răng giữa trẻ em (71-81)
  if (position === 71) return 72;
  if (position === 81) return 71;

  // Răng giữa trẻ em (51-61)
  if (position === 51) return 61;
  if (position === 61) return 62;

  // Răng cuối cùng của mỗi cung không có răng kế tiếp
  if ((arch === 1 || arch === 4) && toothPosition === 1) return null;
  if ((arch === 2 || arch === 3) && toothPosition === 8) return null;
  if ((arch === 5 || arch === 8) && toothPosition === 1) return null;
  if ((arch === 6 || arch === 7) && toothPosition === 5) return null;

  // Các răng còn lại
  if ([1, 4, 5, 8].includes(arch)) return arch * 10 + (toothPosition - 1);
  return arch * 10 + (toothPosition + 1);
};

// Hàm lấy răng trước đó dựa trên vị trí hiện tại
const getPrevToothNumber = (position: number): number | null => {
  const arch = Math.floor(position / 10);
  const toothPosition = position % 10;

  // Răng giữa người lớn (31-41)
  if (position === 31) return 41;
  if (position === 41) return 42;
  if (position === 21) return 11;
  if (position === 11) return 12;

  // Răng giữa trẻ em (81-71)
  if (position === 81) return 82;
  if (position === 71) return 81;

  // Răng giữa trẻ em (61-51)
  if (position === 61) return 51;
  if (position === 51) return 52;

  // Răng đầu tiên của mỗi cung không có răng trước đó
  if ((arch === 1 || arch === 4) && toothPosition === 8) return null;
  if ((arch === 2 || arch === 3) && toothPosition === 1) return null;
  if ((arch === 5 || arch === 8) && toothPosition === 5) return null;
  if ((arch === 6 || arch === 7) && toothPosition === 1) return null;

  // Các răng còn lại
  if ([1, 4, 5, 8].includes(arch)) return arch * 10 + (toothPosition + 1);
  return arch * 10 + (toothPosition - 1);
};

// Tạo ID cho kẽ răng
const getGapId = (toothNumber: number, nextToothNumber: number | null): string => {
  if (!nextToothNumber) return "";
  const [smallerNumber, largerNumber] = [toothNumber, nextToothNumber].sort((a, b) => a - b);
  return `tooth_gap_${smallerNumber}_${largerNumber}`;
};

// Kiểm tra xem răng có ở hàm dưới không
const isLowerArch = (position: number): boolean => {
  const arch = Math.floor(position / 10);
  return [3, 4, 7, 8].includes(arch);
};

const isLeftSideArch = (position: number): boolean => {
  const arch = Math.floor(position / 10);
  // Xử lý đặc biệt cho răng người lớn 31 và 41
  if (position === 31) return false;
  if (position === 41) return true;

  // Xử lý đặc biệt cho răng trẻ em 71 và 81
  if (position === 71) return true; // Răng 71 nằm bên phải
  if (position === 81) return false; // Răng 81 nằm bên trái

  return [1, 4, 5, 8].includes(arch);
};
</script>

<template>
  <div class="p-4">
    <div class="mb-1 text-center text-sm font-medium">Răng {{ position }}</div>
    <div class="flex items-center justify-center gap-2">
      <!-- Kẽ răng bên trái -->
      <div
        v-if="getPrevToothNumber(position)"
        class="absolute"
        :class="[isLowerArch(position) ? 'top-[80px]' : 'bottom-[60px]', 'left-[1.6rem]']"
      >
        <ToothGap
          :gap="gapAnswers[getGapId(position, getPrevToothNumber(position))]?.gap ?? ''"
          :gapId="getGapId(position, getPrevToothNumber(position))"
          :lower="isLowerArch(position)"
          class="absolute z-10"
          :class="[
            isLowerArch(position) ? '-top-10' : '-bottom-10',
            isLeftSideArch(position) ? '-left-10 -translate-x-1/2' : '-right-10 translate-x-1/2',
          ]"
          @updateGap="(id, value) => emit('updateGap', id, value)"
        />
      </div>

      <!-- Răng -->
      <div class="relative scale-75">
        <Tooth
          :position="position"
          :state="state"
          :treatments="treatments"
          :mode="mode"
          @update-state="handleUpdateState"
          @right-click="handleRightClick"
        />
      </div>

      <!-- Kẽ răng bên phải -->
      <div
        v-if="getNextToothNumber(position)"
        class="absolute"
        :class="[isLowerArch(position) ? 'top-[80px]' : 'bottom-[60px]', 'right-[1.6rem]']"
      >
        <ToothGap
          :gap="gapAnswers[getGapId(position, getNextToothNumber(position))]?.gap ?? ''"
          :gapId="getGapId(position, getNextToothNumber(position))"
          :lower="isLowerArch(position)"
          class="absolute z-10"
          :class="[
            isLowerArch(position) ? '-top-10' : '-bottom-10',
            isLeftSideArch(position) ? '-left-10 -translate-x-1/2' : '-right-10 translate-x-1/2',
          ]"
          @updateGap="(id, value) => emit('updateGap', id, value)"
        />
      </div>
    </div>
  </div>
</template>
