<script setup lang="ts">
import { onClickOutside } from "@vueuse/core";
import { computed,ref } from 'vue';

import { vFocus } from "@/base-components/Focus";
import Lucide from "@/base-components/Lucide";

interface Props {
  gapId: string;
  gap: string;
  lower?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  lower: false
});

const popupRef = ref<HTMLElement | null>(null);
const isOpen = ref(false);

const emit = defineEmits<{
  (e: 'updateGap', id: string, value: string): void;
}>();

const formatValue = (value: string): string => {
  const trimmedValue = value.trim();
  if (trimmedValue === 'R' || trimmedValue === '') return trimmedValue;
  const num = parseFloat(trimmedValue);
  if (isNaN(num)) return trimmedValue;
  return num > 10 ? (num / 10).toFixed(2).replace(/\.?0+$/, '') : trimmedValue;
};

const gapValue = computed({
  get: () => formatValue(props.gap || ''),
  set: (newValue) => {
    let processedValue = newValue.trim();

    if (processedValue !== 'R' && processedValue !== '') {
      if (!isNaN(Number(processedValue)) && Number(processedValue) > 10) {
        processedValue = (Number(processedValue) / 10).toString();
      }

      const parts = processedValue.split('.');
      if (parts[1] && parts[1].length > 2) {
        processedValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
      }
    }

    emit('updateGap', props.gapId, processedValue);
  }
});

const toggleDialog = () => {
  isOpen.value = !isOpen.value;
};

const updateGap = (value?: string) => {
  emit('updateGap', props.gapId, value || gapValue.value);
  isOpen.value = false;
};

const handleFocus = (event: FocusEvent) => {
  const target = event.target as HTMLInputElement;
  target.select();
};

onClickOutside(popupRef, () => {
  isOpen.value = false;
});

defineOptions({
  inheritAttrs: false,
});
</script>

<template>
  <div class="relative">
    <div class="flex flex-col items-center">
      <div class="relative w-7 h-7">
        <span v-if="lower" class="absolute -top-4 left-1/2 transform -translate-x-1/2 text-xs whitespace-nowrap">
          {{ Number(gap) !== 0 && !isNaN(Number(gap)) ? `${formatValue(gap)} mm` : formatValue(gap) }}
        </span>

        <Lucide
          @click="toggleDialog"
          icon="Triangle"
          class="cursor-pointer h-7 w-7 text-slate-200 hover:text-slate-400"
          :class="{ 'scale-y-[-1]': lower }"
        />

        <span v-if="!lower" class="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-xs whitespace-nowrap">
          {{ Number(gap) !== 0 && !isNaN(Number(gap)) ? `${formatValue(gap)} mm` : formatValue(gap) }}
        </span>
      </div>
    </div>
    <div
      v-if="isOpen"
      ref="popupRef"
      :class="[
        'absolute z-50 bg-white border rounded shadow-lg p-2 w-48',
        lower ? 'top-full' : 'bottom-full'
      ]"
    >
      <InputGroup>
        <InputText
          v-focus
          v-model="gapValue"
          placeholder="mm"
          class="text-sm"
          @keyup.enter="updateGap()"
          @focus="handleFocus"
        />
        <Button label="R" @click="updateGap('Răng dư')" severity="warn" class="text-sm w-12"/>
        <Button label="OK" @click="updateGap()" class="text-sm w-12"/>
      </InputGroup>
    </div>
  </div>
</template>
