import { defineStore } from "pinia";
import { ref } from "vue";

export const useTeethStore = defineStore("teeth_chart", () => {
  const selectedTeeth = ref<number[]>([]);
  const isMultiSelectMode = ref(false);
  const popupVisible = ref(false);

  function toggleTooth(position: number) {
    const index = selectedTeeth.value.indexOf(position);
    if (index === -1) {
      selectedTeeth.value.push(position);
    } else {
      selectedTeeth.value.splice(index, 1);
    }
  }

  function clearSelection() {
    selectedTeeth.value = [];
  }

  function setMultiSelectMode(isActive: boolean) {
    isMultiSelectMode.value = isActive;
  }

  function isToothSelected(position: number): boolean {
    return selectedTeeth.value.includes(position);
  }

  return {
    popupVisible,
    selectedTeeth,
    isMultiSelectMode,
    isToothSelected,
    toggleTooth,
    clearSelection,
    setMultiSelectMode,
  };
});
