<script setup lang="ts">
//Tooth.vue
import {computed, ref} from "vue";

import Lucide from "@/base-components/Lucide";
import { useTeethStore } from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import {
  getToothStatesByGroup,
  toothStates,
} from "@/pages/customer/components/MedicalExam/Tooth/toothStates";

interface Props {
  position: number;
  state: Record<string, boolean>;
  treatments: Record<string, any>;
  mode: "state" | "treatment";
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "updateState", key: string, value: boolean): void;
  (e: "rightClick", position: number, event: MouseEvent): void;
}>();

const teethStore = useTeethStore();
const toothState = computed(() => props.state || {});
const pop = ref();
const toothContainer = ref<HTMLElement | null>(null);
const customState = ref("");

const customStates = computed(() => {
  const standardStateKeys = toothStates.map((state) => state.key);
  standardStateKeys.push("question_id");
  return Object.keys(toothState.value).filter((key) => !standardStateKeys.includes(key));
});

const showPop = (event: MouseEvent) => {
  if (toothContainer.value && pop.value) {
    const fakeEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });

    Object.defineProperty(fakeEvent, 'target', {value: toothContainer.value});
    Object.defineProperty(fakeEvent, 'currentTarget', {value: toothContainer.value});

    pop.value.toggle(fakeEvent);
  }
};

const toothQuarter = computed(() => {
  const archNumber = Math.floor(props.position / 10);
  const toothNumber = props.position % 10;
  if (archNumber === 1 || archNumber === 5) return "upper-right";
  if (archNumber === 2 || archNumber === 6) return "upper-left";
  if (archNumber === 3 || archNumber === 7) return "lower-left";
  if (archNumber === 4 || archNumber === 8) return "lower-right";
  return "";
});

type ToothType = "rang-cua" | "rang-ham" | "rang-ham-4" | "rang-ham-5";
const toothType = computed<ToothType>(() => {
  const toothNumber = props.position % 10;
  if (toothNumber >= 1 && toothNumber <= 3) {
    return "rang-cua";
  } else if (toothNumber === 4) {
    return "rang-ham-4";
  } else if (toothNumber === 5) {
    return "rang-ham-5";
  } else {
    return "rang-ham";
  }
});

const toothStateMenu = computed(() => {
  const groupedStates = getToothStatesByGroup();
  return Object.entries(groupedStates).map(([group, states]) => ({
    group,
    states: states.map((state) => ({
      label: state.label,
      command: () => toggleToothState(state.key),
      active: isStateActive(state.key),
    })),
  }));
});

const toggleToothState = (state: string) => {
  if (props.mode === "state") {
    emit("updateState", state, !props.state[state]);
  }
};

const addCustomState = () => {
  if (customState.value) {
    emit("updateState", customState.value, true);
    customState.value = "";
  }
};

const removeCustomState = (state: string) => {
  emit("updateState", state, false);
};

const isStateActive = (state: string) => {
  return props.state[state] ?? false;
};

defineOptions({
  inheritAttrs: false,
});

const isSelected = computed(() => teethStore.selectedTeeth.includes(props.position));

const handleToothClick = (event: MouseEvent) => {
  if (props.mode === "treatment") {
    if (teethStore.isMultiSelectMode) {
      teethStore.toggleTooth(props.position);
    } else {
      teethStore.clearSelection();
      teethStore.toggleTooth(props.position);
    }
  } else {
    showPop(event);
  }
};

//Ctrl version
// const handleToothClick = (event: MouseEvent) => {
//   if (props.mode === "treatment") {
//     if (event.ctrlKey) {
//       // Ctrl key is pressed, toggle the tooth without clearing selection
//       teethStore.toggleTooth(props.position);
//     } else {
//       // Ctrl key is not pressed and tooth is not selected
//       teethStore.clearSelection();
//       teethStore.toggleTooth(props.position);
//     }
//   } else {
//     showPop(event);
//   }
// };

const handleRightClick =  (event: MouseEvent) => {
  event.preventDefault();
  //When user right-click on the tooth which is currently not in selected list. We clear all selected list
  if (!isSelected.value) {
    teethStore.clearSelection();
    teethStore.toggleTooth(props.position);
  }

  const fakeEvent = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    view: window
  });

  Object.defineProperty(fakeEvent, 'target', {value: toothContainer.value});
  Object.defineProperty(fakeEvent, 'currentTarget', {value: toothContainer.value});

  emit("rightClick", props.position, fakeEvent);
};

</script>

<template>
  <div
    ref="toothContainer"
    :class="[
      'group/tooth relative',
      toothQuarter,
      {
        'scale-x-[-1]': toothQuarter === 'lower-right' || toothQuarter === 'upper-right',
        'scale-y-[-1]': toothQuarter === 'upper-left' || toothQuarter === 'upper-right',
        '-translate-y-3': (toothState.troi && toothQuarter === 'lower-right') || (toothState.troi && toothQuarter === 'lower-left'),
        'translate-y-3': (toothState.troi && toothQuarter === 'upper-right') || (toothState.troi && toothQuarter === 'upper-left'),
        'rotate-12': toothState.xoay,
      }
    ]"
  >
    <div
      v-if="teethStore.isMultiSelectMode"
      @contextmenu="handleRightClick"
      @click="handleToothClick"
      class="absolute inset-0 bg-transparent cursor-pointer z-10"
      @mouseenter.stop
      @mouseleave.stop
    ></div>
    <Lucide
      v-if="mode === 'state'"
      @click="toggleToothState('troi')"
      class="absolute left-1/2 -top-2 h-10 w-10 -translate-x-1/2 -translate-y-2/3 cursor-pointer text-slate-200 opacity-0 hover:text-slate-400 group-hover/tooth:opacity-100 z-20"
      icon="ChevronUp"
    />

    <Lucide
      v-if="mode === 'state'"
      @click="toggleToothState('mat_rang')"
      :class="[
        'absolute h-5 w-5 cursor-pointer text-slate-400 opacity-0 hover:text-slate-600 group-hover/tooth:opacity-100 z-20',
        toothQuarter === 'lower-left' ? 'bottom-2 right-2' : 'bottom-2 left-2',
      ]"
      :icon="toothState.mat_rang ? 'PlusCircle' : 'Slash'"
    />

    <Lucide
      v-if="mode === 'state'"
      @click="toggleToothState('xoay')"
      :class="[
        'absolute h-5 w-5 cursor-pointer text-slate-400 opacity-0 hover:text-slate-600 group-hover/tooth:opacity-100 z-20',
        toothQuarter === 'lower-left' ? 'bottom-2 left-2' : 'bottom-2 right-2',
      ]"
      :icon="toothState.xoay ? 'RefreshCw' : 'RefreshCcw'"
    />

    <svg viewBox="0 0 30.02 48.03" xmlns="http://www.w3.org/2000/svg" class="h-full w-full group-hover/tooth:scale-105">
      <g>
        <path
          @click="showPop"
          :class="{'fill-slate-200': isSelected, 'fill-transparent': !isSelected, 'group-hover/tooth:fill-slate-100': props.mode === 'state' }"
          class="cursor-pointer"
          d="M5 0h20.02c2.76 0 5 2.24 5 5v38.03c0 2.76-2.24 5-5 5H5c-2.76 0-5-2.24-5-5V5C0 2.24 2.23 0 5 0z"
        />

        <g v-if="!toothState.mat_rang" class="tooth">
          <!-- Root section -->
          <g class="chan-rang group" @click="toggleToothState('mat_than_rang')">
            <!-- Răng hàm lớn (3 chân) -->
            <g v-if="toothType === 'rang-ham'">
              <title>chan-rang-3</title>
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M17.52 40.48L18.5 18.71C18.5 18.71 17.09 18.8 14.96 18.78C12.88 18.78 11.06 18.63 11.06 18.63L12.49 40.42C12.49 40.42 13.35 45.99 14.98 45.99C16.64 45.99 17.52 40.48 17.52 40.48Z"
              />
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M22.02 35.11L23.31 18.1L18.72 18.57L18.4 18.63L17.65 35.11C17.65 35.11 17.93 40.34 19.54 40.34C21.18 40.34 22.02 35.11 22.02 35.11Z"
              />
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M12.12 34.81L11.06 18.71L10.27 18.58L6.48 18.18L7.79 34.87C7.79 34.87 8.67 40.17 10.31 40.17C11.99 40.17 12.12 34.81 12.12 34.81Z"
              />
            </g>
            <!-- Răng có 2 chân: răng số 4, răng số 5 khi state.hai_chan là true -->
            <g
              v-else-if="
                toothState.hai_chan && (toothType === 'rang-ham-4' || toothType === 'rang-ham-5')
              "
            >
              <title>chan-rang-2</title>
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M13.293 40.582L14.953 18.742C14.953 18.742 12.323 18.672 10.283 18.542C8.193 18.342 6.533 18.142 6.533 18.142L8.283 40.522C8.283 40.522 9.143 46.112 10.763 46.112C12.423 46.122 13.293 40.582 13.293 40.582Z"
              />
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M21.643 40.582L23.323 18.132C23.323 18.132 20.983 18.502 18.783 18.612C16.483 18.732 14.953 18.752 14.953 18.752L16.633 40.542C16.633 40.542 17.493 46.132 19.113 46.132C20.773 46.122 21.643 40.582 21.643 40.582Z"
              />
            </g>
            <!-- Răng có 1 chân: răng cửa, răng số 4, răng số 5 khi không có hai_chan -->
            <g v-else>
              <title>chan-rang-1</title>
              <path
                :class="[
                  'fill-white stroke-gray-400 group-hover:fill-secondary',
                  { 'stroke-red-500': toothState.nho_rang },
                ]"
                d="M19.98 40.53L22.829 25.05L23.32 18.08C23.32 18.08 19.01 18.7 14.97 18.7C10.92 18.7 6.55 18.1 6.55 18.1L7.056 25.034L10.02 40.48C10.02 40.48 11.72 46.07 14.95 46.07C18.25 46.07 19.98 40.53 19.98 40.53Z"
              />
            </g>
          </g>

          <g v-if="!toothState.mat_than_rang" class="than-rang">
            <title>than-rang-ham</title>
            <path
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_xa,
                  'fill-muted': toothState.sau_xa,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_xa')"
              d="M23.34 18.05C23.34 18.05 28.97 16.99 29 9.65C29.04 2.26 23.35 1.86 23.35 1.86L23.34 18.05Z"
            />
            <path
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_ngoai,
                  'fill-muted': toothState.sau_ngoai,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_ngoai')"
              d="M23.33 18.12C23.33 18.12 19.16 18.69 14.95 18.69S6.53 18.1 6.53 18.1V9.7C6.53 9.7 9.3 14.2 14.93 14.2C19.97 14.2 23.32 9.7 23.32 9.7L23.33 18.12Z"
            />
            <path
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_gan,
                  'fill-muted': toothState.sau_gan,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_gan')"
              d="M6.54 18.1C6.54 18.1 0.99 17.07 1 9.71C1.01 2.33 6.55 1.88 6.55 1.88L6.54 18.1Z"
            />
            <path
              v-if="!toothState.vo"
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_trong,
                  'fill-muted': toothState.sau_trong,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_trong')"
              d="M6.54 1.83C6.54 1.83 10.71 1.3 14.92 1.3C19.13 1.3 23.34 1.85 23.34 1.85V9.7C23.34 9.7 19.54 5.54 14.93 5.5C10.97 5.46 6.55 9.7 6.55 9.7L6.54 1.83Z"
            />
            <path
              v-if="toothType !== 'rang-cua'"
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_nhai,
                  'fill-muted': toothState.sau_nhai,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_nhai')"
              d="M10.41 6.88C12.79 5.56 14.92 5.5 14.92 5.5C14.92 5.5 16.85 5.4 19.35 6.71C22.37 8.29 23.17 9.7 23.17 9.7C23.17 9.7 22.62 11.12 19.98 12.66C17.6 14.05 15.23 14.19 15.23 14.19C15.23 14.19 12.79 14.34 10.24 13.01C7.87 11.78 6.665 9.68 6.665 9.68C6.665 9.68 8.13 8.14 10.41 6.88Z"
            />
          </g>
          <g v-if="toothType == 'rang-cua' && !toothState.mat_than_rang" class="than-rang">
            <title>than-rang-cua</title>
            <path
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_ngoai,
                  'fill-muted': toothState.sau_ngoai,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_ngoai')"
              d="M23.34 18.166C23.34 18.166 19.17 18.736 14.96 18.736S6.54 18.146 6.54 18.146V9.746H23.33L23.34 18.166Z"
            />
            <path
              v-if="!toothState.vo"
              :class="[
                'stroke-gray-400 hover:fill-secondary',
                {
                  'fill-white': !toothState.sau_trong,
                  'fill-muted': toothState.sau_trong,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('sau_trong')"
              d="M6.55 1.876C6.55 1.876 10.72 1.346 14.93 1.346S23.35 1.896 23.35 1.896V9.736H6.56V1.876Z"
            />
          </g>
          <g class="co-rang">
            <title>Cổ răng</title>
            <path
              :class="[
                'opacity-0 hover:fill-secondary hover:stroke-gray-400 hover:opacity-100',
                {
                  'stroke-gray-400': toothState.mon_co_rang,
                  'opacity-100': toothState.mon_co_rang,
                  'fill-muted': toothState.mon_co_rang,
                  'stroke-red-500': toothState.nho_rang,
                },
              ]"
              @click="toggleToothState('mon_co_rang')"
              d="M23.3 18.122C23.3 18.122 19.13 18.743 14.92 18.743C10.71 18.743 6.5 18.1 6.5 18.1L7.043 24.917C6.993 26.831 22.794 26.837 22.843 24.928L23.3 18.122Z"
            />
          </g>
        </g>
      </g>
    </svg>

    <Popover v-if="mode === 'state'" ref="pop">
      <div class="flex flex-col gap-4" @wheel.prevent>
        <div class="grid grid-cols-3 md:grid-cols-6 divide-x">
          <div
            v-for="(column, index) in toothStateMenu"
            :key="index"
            class="flex flex-col gap-y-1 px-2 first:pl-0 last:pr-0"
          >
            <div
              v-for="item in column.states"
              :key="item.label"
              @click="item.command"
              :class="[
                'cursor-pointer rounded p-1 px-2 hover:bg-slate-100',
                { 'bg-soft ring-1 ring-highlight': item.active },
              ]"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div>
          <div v-if="customStates.length" class="mb-2 flex max-w-lg flex-wrap gap-1">
            <Chip
              v-for="state in customStates"
              :key="state"
              :label="state"
              removable
              @remove="removeCustomState(state)"
            />
          </div>
          <InputGroup>
            <InputText
              autofocus
              v-model.trim="customState"
              placeholder="Vấn đề khác"
              class="text-sm"
              @keyup.enter="addCustomState"
            />
            <Button label="Thêm" @click="addCustomState" class="text-sm" />
          </InputGroup>
        </div>
      </div>
    </Popover>
  </div>
</template>

<style scoped>
.rotate-15 {
  transform: rotate(15deg);
}
</style>
