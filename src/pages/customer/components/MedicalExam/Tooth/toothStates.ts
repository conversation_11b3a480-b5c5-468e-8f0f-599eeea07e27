export interface ToothState {
  key: string;
  label: string;
  group: string;
  short: string;
}

export const toothStates: ToothState[] = [
/*  { key: 'nho_rang', label: 'Nhổ răng', short: 'NR', group: 'general' },*/
  { key: 'mat_than_rang', label: 'Chân răng', short: 'CR', group: 'general' },
  { key: 'mat_rang', label: 'M<PERSON>t răng', short: 'MR', group: 'general' },
  { key: 'mon_nhai', label: 'Mòn nhai', short: 'MN', group: 'general' },
  { key: 'loi_trum', label: 'Lợi trùm', short: 'Trùm', group: 'general' },

  { key: 'sau_gan', label: 'Sâu gần', short: 'SG', group: 'decay' },
  { key: 'sau_xa', label: 'Sâu xa', short: 'SX', group: 'decay' },
  { key: 'sau_trong', label: 'Sâu trong', short: 'ST', group: 'decay' },
  { key: 'sau_ngoai', label: 'Sâu ngoài', short: 'SN', group: 'decay' },
  { key: 'sau_nhai', label: 'Sâu nhai', short: 'SNh', group: 'decay' },

  { key: 'lech_gan', label: 'Lệch gần', short: 'LG', group: 'position' },
  { key: 'lech_xa', label: 'Lệch xa', short: 'LX', group: 'position' },
  { key: 'lech_trong', label: 'Lệch trong', short: 'LT', group: 'position' },
  { key: 'lech_ngoai', label: 'Lệch ngoài', short: 'LN', group: 'position' },

  { key: 'mon_co_rang', label: 'Mòn cổ răng', short: 'MCR', group: 'other' },
  { key: 'xoay', label: 'Xoay', short: 'Xoay', group: 'other' },
  { key: 'troi', label: 'Trồi', short: 'Trồi', group: 'other' },
  { key: 'vo', label: 'Vỡ lớn', short: 'VL', group: 'other' },
  { key: 'ket', label: 'Kẹt', short: 'Kẹt', group: 'other' },

  { key: 'am', label: 'Amalgam', short: 'AM', group: 'treatment' },
  { key: 'composite', label: 'Composite', short: 'CO', group: 'treatment' },
  { key: 'gic', label: 'GIC', short: 'GIC', group: 'treatment' },
  { key: 'eu', label: 'Eugenol', short: 'EU', group: 'treatment' },

  { key: 'chua_moc', label: 'Chưa mọc', short: 'CM', group: 'child' },
  { key: 'dang_moc', label: 'Đang mọc', short: 'DM', group: 'child' },
  { key: 'rang_sua', label: 'Răng sữa', short: 'RS', group: 'child' },
];

export const toothTreatments: ToothState[] = [
  { key: 'dieu_tri_viem_nuou', label: 'Điều trị viêm nướu', short: 'VT', group: 'treatment' },
  { key: 'cao_voi_rang', label: 'Cạo vôi răng', short: 'CV', group: 'treatment' },
  { key: 'kiem_tra', label: 'Kiểm tra', short: 'KT', group: 'treatment' },
  { key: 'tram_rang', label: 'Trám răng', short: 'TR', group: 'treatment' },
  { key: 'xet_nghiem_mau', label: 'Xét nghiệm máu', short: 'XN', group: 'treatment' },
  { key: 'nho', label: 'Nhổ', short: 'N', group: 'treatment' },
  { key: 'tieu_phau', label: 'Tiểu phẫu', short: 'TP', group: 'treatment' },
  { key: 'chua_tuy_lan_dau', label: 'Chữa tủy lần đầu', short: 'CT', group: 'treatment' },
  { key: 'boc_mao_sau_ning', label: 'Bọc mão sau niềng', short: 'BM', group: 'treatment' },
  { key: 'chup_ctcb_kiem_tra', label: 'Chụp CTCB kiểm tra', short: 'CTCB', group: 'treatment' },
  { key: 'chua_tuy_lai', label: 'Chữa tủy lại', short: 'CTL', group: 'treatment' },
  { key: 'gay_te_xu_ly_mat_goc_rang', label: 'Gây tê xử lý mặt gốc răng', short: 'GT', group: 'treatment' },
  { key: 'kiem_tra_chua_tuy_hoac_tram', label: 'Kiểm tra - chữa tủy hoặc trám', short: 'KTC', group: 'treatment' },
  { key: 'kiem_tra_chua_tuy_hoac_nho', label: 'Kiểm tra - chữa tủy hoặc nhổ', short: 'KTN', group: 'treatment' },
  { key: 'tieu_phau_cat_chop', label: 'Tiểu phẫu cắt chóp', short: 'TPC', group: 'treatment' }
];

export const getToothStatesByGroup = () => {
  const groups: Record<string, ToothState[]> = {};
  toothStates.forEach(state => {
    if (!groups[state.group]) {
      groups[state.group] = [];
    }
    groups[state.group].push(state);
  });
  return groups;
};

export const getToothStateLabel = (key: string): string => {
  const state = toothStates.find(s => s.key === key);
  return state ? state.label : key;
};

export const getToothTreatmentLabel = (key: string): string => {
  const treatment = toothTreatments.find(t => t.key === key);
  return treatment ? treatment.label : key;
};

export const getToothStateOrTreatmentLabel = (key: string): string => {
  const state = toothStates.find(s => s.key === key);
  if (state) return state.label;

  const treatment = toothTreatments.find(t => t.key === key);
  if (treatment) return treatment.label;

  return key;
};
