<script lang="ts" setup>
//SelectTreatment.vue
import {computed,ref } from "vue";

import Empty from "@/base-components/Empty";
import { Answer } from "@/hooks/useDynamicForm";
import {useTeethStore} from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import {
  getToothTreatmentLabel,
  ToothState,
  toothTreatments,
} from "@/pages/customer/components/MedicalExam/Tooth/toothStates";

const teethStore = useTeethStore();

interface Props {
  modelValue: Answer;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:modelValue", value: Answer): void;
  (e: "toggled", treatment: ToothState, state: boolean): void;
}>();

const treatmentFilter = ref("");

const displayedTreatments = computed(() => {
  if (!treatmentFilter.value) return toothTreatments;

  return toothTreatments.filter((treatment) =>
    treatment.label.toLowerCase().includes(treatmentFilter.value.toLowerCase()),
  );
});

const toggleTreatment = (treatment: ToothState) => {
  treatmentFilter.value = "";

  const newValue = { ...props.modelValue };
  let state: boolean;
  if (treatment.key in newValue) {
    delete newValue[treatment.key];
    state = false;
  } else {
    newValue[treatment.key] = treatment;
    state = true;
  }
  emit("update:modelValue", newValue);
  emit("toggled", treatment, state);
};

const removeTreatment = (treatmentKey: string) => {
  const newValue = { ...props.modelValue };
  delete newValue[treatmentKey];
  emit("update:modelValue", newValue);
  emit("toggled", { key: treatmentKey, label: '', group: 'custom_treatment', short: '' }, false);
};

const isTreatmentActive = (treatmentKey: string) => {
  return treatmentKey in props.modelValue;
};

const clearFilter = () => {
  treatmentFilter.value = "";
};

const addCustomTreatment = () => {
  if (treatmentFilter.value) {
    const newTreatment: ToothState = {
      key: treatmentFilter.value, // Sử dụng nội dung làm key
      label: treatmentFilter.value,
      short: treatmentFilter.value.substring(0, 2).toUpperCase(),
      group: "custom_treatment",
    };
    const newValue = { ...props.modelValue, [newTreatment.key]: true };
    emit("update:modelValue", newValue);
    emit("toggled", newTreatment, true);
    treatmentFilter.value = "";
  }
};

const popoverRef = ref();

const onHide = () => {
  clearFilter();
  teethStore.popupVisible = false
}

defineExpose({
  popoverRef,
});
</script>

<template>
  <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }"
           @show="teethStore.popupVisible = true"
           @hide="onHide">
    <div class="w-[25rem]">
      <!-- Main content area with fixed height -->
      <div class="h-[15rem] overflow-hidden">
        <div v-if="displayedTreatments.length > 0" class="h-full p-2">
          <ul
            class="m-0 h-full snap-y scroll-py-1 list-none space-y-1 overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="treatment in displayedTreatments"
              :key="treatment.key"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-2 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isTreatmentActive(treatment.key)
              }"
              @click="toggleTreatment(treatment)"
            >
              <div class="flex-1">
                <span class="hyphens-auto">
                  {{ treatment.label }}
                </span>
              </div>
              <div class="ml-3 text-gray-600 dark:text-gray-400">
                <div
                  v-if="isTreatmentActive(treatment.key)"
                  class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                  :class="{ 'bg-primary': isTreatmentActive(treatment.key) }"
                ></div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="h-full p-2">
          <Empty class="h-full" />
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t p-2">
        <div v-if="Object.keys(modelValue).length" class="mb-2 flex max-w-lg flex-wrap gap-1">
          <Chip
            v-for="key in Object.keys(modelValue)"
            :key="key"
            :label="getToothTreatmentLabel(key)"
            removable
            @remove="removeTreatment(key)"
          />
        </div>
        <div class="flex items-center gap-2">
          <IconField class="flex-grow">
            <InputText
              autofocus
              v-model="treatmentFilter"
              class="w-full text-sm"
              placeholder="Tìm kiếm"
              type="text"
              @keyup.enter="addCustomTreatment"
            />
            <InputIcon
              :class="treatmentFilter ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="clearFilter"
            />
          </IconField>
          <Button
            label="OK"
            @click="
              () => {
                addCustomTreatment();
                popoverRef?.hide();
                clearFilter();
              }
            "
            class="text-sm"
          />
        </div>
      </div>
    </div>
  </Popover>
</template>
