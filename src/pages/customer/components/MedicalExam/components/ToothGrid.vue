<!-- ToothGrid.vue -->
<script lang="ts" setup>
import <PERSON><PERSON> from "primevue/button";
import Tag from "primevue/tag";
import { computed, ref } from "vue";

import { ExtendedAttachmentAddRequest, ExtendedAttachmentResponse } from "@/api/extend-types";
import AttachmentModal from "@/components/Attachment/AttachmentModal.vue";
import SelectTreatment from "@/pages/customer/components/MedicalExam/Tooth/SelectTreatment.vue";
import { useTeethStore } from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import {
  getToothStateLabel,
  getToothTreatmentLabel,
  toothStates,
} from "@/pages/customer/components/MedicalExam/Tooth/toothStates";
import { simpleGlobalId } from "@/utils/string";

const props = defineProps<{
  personId: number;
  mode: "state" | "treatment";
  toothStatesAnswers: Record<string, any>;
  syncToothStates: (key: string) => void;
  debouncedSyncToothStates: (key: string) => void;
  toothTreatmentsAnswers: Record<string, any>;
  syncToothTreatments: (key: string) => void;
  debouncedSyncToothTreatments: (key: string) => void;
  customIssues: Record<string, any>;
  syncCustomIssues: (key: string) => void;
  debouncedSyncCustomIssues: (key: string) => void;
  customTreatments: Record<string, any>;
  syncCustomTreatments: (key: string) => void;
  debouncedSyncCustomTreatments: (key: string) => void;
}>();

const teethStore = useTeethStore();

const rightClickedTooth = ref<number | null>(11);
const selectTreatmentPopover = ref();
const attachmentModal = ref();
const currentAttachments = ref<ExtendedAttachmentResponse[]>([]);

// Display Mode
const displayMode = computed(() => {
  return props.mode === "state" ? "Trạng thái" : "Điều trị";
});

// Computed property to get teeth with active states
const teethToDisplay = computed(() => {
  const teethSet = new Set<number>();

  // Thêm răng có vấn đề
  Object.keys(props.toothStatesAnswers).forEach((toothKey) => {
    const states = props.toothStatesAnswers[toothKey];
    if (Object.values(states).some((value) => value === true)) {
      teethSet.add(parseInt(toothKey.replace("tooth_", ""), 10));
    }
  });

  // Thêm răng đang được điều trị
  Object.keys(props.toothTreatmentsAnswers).forEach((toothKey) => {
    const treatments = props.toothTreatmentsAnswers[toothKey];
    if (Object.values(treatments).some((value) => value === true)) {
      teethSet.add(parseInt(toothKey.replace("tooth_", ""), 10));
    }
  });

  return Array.from(teethSet).sort((a, b) => a - b);
});

// Function to get active states
const getActiveStates = (toothNumber: number) => {
  const stateObj = props.toothStatesAnswers[`tooth_${toothNumber}`] || {};
  return Object.entries(stateObj)
    .filter(([_, value]) => value === true)
    .map(([key, _]) => toothStates.find((state) => state.key === key) || { key, short: key })
    .sort((a, b) => getToothStateLabel(a.key).localeCompare(getToothStateLabel(b.key)));
};

// Function to get active treatments
const getActiveTreatments = (toothNumber: number) => {
  const treatmentObj = props.toothTreatmentsAnswers[`tooth_${toothNumber}`] || {};
  return Object.entries(treatmentObj)
    .filter(([_, value]) => value === true)
    .map(
      ([key, _]) => toothStates.find((treatment) => treatment.key === key) || { key, short: key },
    )
    .sort((a, b) => getToothTreatmentLabel(a.key).localeCompare(getToothTreatmentLabel(b.key)));
};

// Function to update tooth state
const updateToothState = (toothNumber: number, key: string, value: boolean) => {
  const toothKey = `tooth_${toothNumber}`;
  if (value) {
    props.toothStatesAnswers[toothKey] = { ...props.toothStatesAnswers[toothKey], [key]: value };
  } else if (props.toothStatesAnswers[toothKey]) {
    delete props.toothStatesAnswers[toothKey][key];
  }
  props.syncToothStates(toothKey);
};

// Function to update tooth treatment
const updateToothTreatment = (toothNumber: number, key: string, value: boolean) => {
  const toothKey = `tooth_${toothNumber}`;
  if (value) {
    props.toothTreatmentsAnswers[toothKey] = {
      ...props.toothTreatmentsAnswers[toothKey],
      [key]: value,
    };
  } else if (props.toothTreatmentsAnswers[toothKey]) {
    delete props.toothTreatmentsAnswers[toothKey][key];
  }
  props.syncToothTreatments(toothKey);
};

// Handle double-click on state tag
const handleStateTagDoubleClick = (toothNumber: number, state: { key: string; short: string }) => {
  const isStandardState = toothStates.some((s) => s.key === state.key);
  updateToothState(
    toothNumber,
    state.key,
    isStandardState ? !props.toothStatesAnswers[`tooth_${toothNumber}`][state.key] : false,
  );
};

// Handle double-click on treatment tag
const handleTreatmentTagDoubleClick = (
  toothNumber: number,
  treatment: { key: string; short: string },
) => {
  updateToothTreatment(
    toothNumber,
    treatment.key,
    !props.toothTreatmentsAnswers[`tooth_${toothNumber}`][treatment.key],
  );
};

// Handle right-click to open treatment popover
const handleRightClick = async (toothNumber: number, event: MouseEvent) => {
  rightClickedTooth.value = toothNumber;
  teethStore.selectedTeeth = [toothNumber];
  selectTreatmentPopover.value?.popoverRef.toggle(event);
};

// Function to update treatments for multiple teeth
const multiUpdateToothTreatment = (treatment: any, value: boolean) => {
  teethStore.selectedTeeth.forEach((toothNumber) => {
    const toothKey = `tooth_${toothNumber}`;
    if (value) {
      props.toothTreatmentsAnswers[toothKey] = {
        ...props.toothTreatmentsAnswers[toothKey],
        [treatment.key]: value,
      };
    } else if (props.toothTreatmentsAnswers[toothKey]) {
      delete props.toothTreatmentsAnswers[toothKey][treatment.key];
    }
  });

  teethStore.selectedTeeth.forEach((toothNumber) => {
    props.syncToothTreatments(`tooth_${toothNumber}`);
  });
};

// Function to handle attachments added
const handleAttachmentsUpdated = (attachments: ExtendedAttachmentResponse[]) => {
  currentAttachments.value = attachments;
};

// Function to group treatments and create attachments
const createInitialAttachments = (): ExtendedAttachmentAddRequest[] => {
  const attachments: ExtendedAttachmentAddRequest[] = [];
  const treatmentMap: Record<string, number> = {};

  // Collect treatments from teeth
  Object.keys(props.toothTreatmentsAnswers).forEach((toothKey) => {
    const treatments = props.toothTreatmentsAnswers[toothKey];
    Object.keys(treatments).forEach((treatmentKey) => {
      if (treatments[treatmentKey] === true) {
        treatmentMap[treatmentKey] = (treatmentMap[treatmentKey] || 0) + 1;
      }
    });
  });

  // Collect treatments from custom issues
  Object.keys(props.customTreatments).forEach((issueKey) => {
    const treatments = props.customTreatments[issueKey];
    Object.keys(treatments).forEach((treatmentKey) => {
      if (treatments[treatmentKey] === true) {
        treatmentMap[treatmentKey] = (treatmentMap[treatmentKey] || 0) + 1;
      }
    });
  });

  // Create attachments
  Object.entries(treatmentMap).forEach(([treatmentKey, quantity]) => {
    attachments.push({
      person_id: props.personId,
      kind: "product",
      title: getToothTreatmentLabel(treatmentKey),
      quantity: quantity,
      meta: { price: { min: undefined, max: undefined, discount: undefined } },
      temp_id: simpleGlobalId(),
    });
  });

  return attachments;
};

// Function to handle opening Attachment Modal
const openAttachmentModal = () => {
  // Luôn tạo mới initialAttachments để reflect latest treatments
  const initialAttachments = createInitialAttachments();
  attachmentModal.value.open(initialAttachments);
};
</script>

<template>
  <div>
    <!-- Mode Display -->
    <div class="mb-4 flex items-center justify-between"></div>

    <!-- Table Headers -->
    <div class="grid grid-cols-12 items-end gap-4 border-b pb-2 font-semibold">
      <div class="col-span-1">Răng</div>
      <div class="col-span-6">Vấn đề (Issues)</div>
      <div class="col-span-5 flex items-end justify-between">
        <span>Điều trị (Treatments)</span>
        <Button
          icon="pi pi-cart-arrow-down"
          severity="success"
          size="small"
          outlined
          class="size-8 text-xs"
          aria-label="Add to attachment"
          @click="openAttachmentModal"
        />
      </div>
    </div>

    <!-- Table Content -->
    <div>
      <!-- Iterate over teeth with issues -->
      <div
        v-for="toothNumber in teethToDisplay"
        :key="toothNumber"
        class="group grid grid-cols-12 items-center gap-4 border-b border-gray-100 py-1.5 odd:bg-white even:bg-gray-50"
      >
        <!-- Tooth Number -->
        <div class="col-span-1 flex flex-wrap gap-2">
          <Tag
            :value="toothNumber"
            @click="teethStore.toggleTooth(toothNumber)"
            class="size-8 cursor-pointer"
            :class="{
              'ring-2 ring-highlight': teethStore.isToothSelected(toothNumber),
              border: !teethStore.isToothSelected(toothNumber),
            }"
            severity="secondary"
          />
        </div>

        <!-- Issues (States) -->
        <div class="col-span-6 flex flex-wrap gap-2">
          <Tag
            v-for="state in getActiveStates(toothNumber)"
            :key="state.key"
            :value="getToothStateLabel(state.key)"
            class="cursor-pointer font-medium"
            severity="warn"
            @dblclick="handleStateTagDoubleClick(toothNumber, state)"
          />
        </div>

        <!-- Treatments -->
        <div class="col-span-5 flex flex-wrap gap-2">
          <Tag
            v-for="treatment in getActiveTreatments(toothNumber)"
            :key="treatment.key"
            :value="getToothTreatmentLabel(treatment.key)"
            class="cursor-pointer font-medium"
            severity="info"
            @dblclick="handleTreatmentTagDoubleClick(toothNumber, treatment)"
          />
          <!-- Option to add a new treatment -->
          <Button
            @click="handleRightClick(toothNumber, $event)"
            icon="pi pi-plus"
            severity="info"
            size="small"
            outlined
            class="invisible size-8 text-xs group-hover:visible"
          />
        </div>
      </div>
    </div>

    <!-- Treatment Selection Popover -->
    <SelectTreatment
      :modelValue="props.toothTreatmentsAnswers['tooth_' + rightClickedTooth]"
      @toggled="multiUpdateToothTreatment"
      ref="selectTreatmentPopover"
    />

    <!-- Attachment Modal -->
    <AttachmentModal
      ref="attachmentModal"
      :personId="props.personId"
      @attachmentsUpdated="handleAttachmentsUpdated"
    />
  </div>
</template>
