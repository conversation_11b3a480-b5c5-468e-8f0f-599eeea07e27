<script lang="ts" setup>
//ToothChart.vue
import Dialog from "primevue/dialog";
import Popover from "primevue/popover";
import Tag from "primevue/tag";

import useDynamicForm from "@/hooks/useDynamicForm";
import { useTeethStore } from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import Tooth from "@/pages/customer/components/MedicalExam/Tooth/Tooth.vue";
import ToothGap from "@/pages/customer/components/MedicalExam/Tooth/ToothGap.vue";
import { ToothState, toothStates } from "@/pages/customer/components/MedicalExam/Tooth/toothStates";
const teethStore = useTeethStore();
import { onClickOutside } from "@vueuse/core";
import { computed, nextTick, onMounted, ref } from "vue";

import ToothGrid from "@/pages/customer/components/MedicalExam/components/ToothGrid.vue";
import SelectTreatment from "@/pages/customer/components/MedicalExam/Tooth/SelectTreatment.vue";
import ToothDetail from "@/pages/customer/components/MedicalExam/Tooth/ToothDetail.vue";

import CustomIssueGrid from "./CustomIssueGrid.vue";

const props = defineProps<{
  personId: number;
  defaultArchMode?: "adult" | "children";
  mode: "state" | "treatment";
}>();

const rightClickedTooth = ref<number | null>(11); //To prevent console warning
const selectTreatmentPopover = ref();
const handleRightClick = async (position: number, event: MouseEvent) => {
  rightClickedTooth.value = position;
  selectTreatmentPopover.value?.popoverRef.hide(event);
  await nextTick();
  selectTreatmentPopover.value?.popoverRef.show(event);
};

const chart = ref();
const FORM_ID = "rang";

const generateQuestions = (start: number, end: number, step: number) => {
  const questions: Record<string, any> = {};
  for (let i = start; i <= end; i += step) {
    if (i % 10 > 8) continue;
    questions[`tooth_${i}`] = {
      question: `Tooth ${i}`,
      options: Object.fromEntries(toothStates.map((state) => [state.key, state.label])),
    };
  }
  return questions;
};

const generateGapQuestions = (arches: number[]) => {
  const questions: Record<string, any> = {};
  for (let arch of arches) {
    for (let i = 1; i <= (arch <= 4 ? 7 : 4); i++) {
      const currentTooth = arch * 10 + i;
      const nextTooth = arch * 10 + i + 1;
      questions[`tooth_gap_${currentTooth}_${nextTooth}`] = {
        question: `Gap between Tooth ${currentTooth} and ${nextTooth}`,
        type: "text",
      };
    }
  }
  return questions;
};

const toothQuestions = {
  ...generateQuestions(11, 48, 1),
  ...generateQuestions(51, 85, 1),
};

const toothGapQuestions = {
  ...generateGapQuestions([1, 2, 3, 4, 5, 6, 7, 8]),
  tooth_gap_11_21: { question: "Gap between Tooth 11 and 21", type: "text" },
  tooth_gap_31_41: { question: "Gap between Tooth 31 and 41", type: "text" },
  tooth_gap_51_61: { question: "Gap between Tooth 51 and 61", type: "text" },
  tooth_gap_71_81: { question: "Gap between Tooth 71 and 81", type: "text" },
};

const customIssueQuestions = {
  custom_issues_0: {
    question: "Custom Issue",
    type: "object", // or whatever type matches your data structure
  },
};

const {
  answers: customIssues,
  syncData: syncCustomIssues,
  debouncedSyncData: debouncedSyncCustomIssues,
} = useDynamicForm("custom", {}, props.personId, true);

const {
  answers: customTreatments,
  syncData: syncCustomTreatments,
  debouncedSyncData: debouncedSyncCustomTreatments,
} = useDynamicForm("custom_treatment", {}, props.personId, true);

const {
  answers,
  syncData: syncToothData,
  debouncedSyncData: debouncedSyncToothData,
} = useDynamicForm(FORM_ID, { ...toothQuestions, ...toothGapQuestions }, props.personId);

const {
  answers: teethTreatments,
  syncData: syncToothTreatment,
  debouncedSyncData: debouncedSyncToothTreatment,
} = useDynamicForm(
  FORM_ID + "__" + props.mode,
  { ...toothQuestions, ...toothGapQuestions },
  props.personId,
);

const getToothNumber = (quadrant: number, position: number): number => {
  const quadrantMap = {
    1: 19 - position,
    2: 20 + position,
    3: 30 + position,
    4: 49 - position,
    5: 56 - position,
    6: 60 + position,
    7: 70 + position,
    8: 86 - position,
  };
  return quadrantMap[quadrant as keyof typeof quadrantMap] || 0;
};

const getGapId = (toothNumber: number, nextToothNumber: number) => {
  const [smallerNumber, largerNumber] = [toothNumber, nextToothNumber].sort((a, b) => a - b);
  return `tooth_gap_${smallerNumber}_${largerNumber}`;
};

const getActiveStates = (toothNumber: number) => {
  const stateObj = answers[`tooth_${toothNumber}`] || {};
  return Object.entries(stateObj)
    .filter(([_, value]) => value === true)
    .map(([key, _]) => toothStates.find((state) => state.key === key) || { key, short: key });
};

const updateToothState = (toothNumber: number, key: string, value: boolean) => {
  const toothKey = `tooth_${toothNumber}`;
  if (value) {
    answers[toothKey] = { ...answers[toothKey], [key]: value };
  } else if (answers[toothKey]) {
    delete answers[toothKey][key];
  }
  syncToothData(toothKey);
};

const multiUpdateToothTreatment = (treatment: ToothState, value: boolean) => {
  teethStore.selectedTeeth.forEach((toothNumber) => {
    const toothKey = `tooth_${toothNumber}`;
    if (value) {
      // Add or update the treatment
      teethTreatments[toothKey] = {
        ...teethTreatments[toothKey],
        [treatment.key]: value,
      };
    } else if (teethTreatments[toothKey]) {
      // Remove the treatment
      delete teethTreatments[toothKey][treatment.key];
    }
  });

  // Sync all updated teeth at once
  teethStore.selectedTeeth.forEach((toothNumber) => {
    syncToothTreatment(`tooth_${toothNumber}`);
  });
};

const handleTagDoubleClick = (toothNumber: number, state: { key: string; short: string }) => {
  const isStandardState = toothStates.some((s) => s.key === state.key);
  updateToothState(
    toothNumber,
    state.key,
    isStandardState ? !answers[`tooth_${toothNumber}`][state.key] : false,
  );
};

const updateToothGap = (id: string, value: string) => {
  answers[id].gap = value;
  debouncedSyncToothData(id);
};

const isChildrenMode = ref(false);

onMounted(() => {
  isChildrenMode.value = props.defaultArchMode === "children";
});

const toggleArchMode = () => {
  isChildrenMode.value = !isChildrenMode.value;
};

const triTueCuaLamDaiCa = computed(() => (isChildrenMode.value ? 4 : 0));
const toothNumberPerArch = computed(() => (isChildrenMode.value ? 5 : 8));

onClickOutside(chart, () => {
  if (!teethStore.popupVisible) {
    teethStore.clearSelection();
  }
});

const toothDetailPopover = ref();
const selectedToothForDetail = ref<number | null>(null);

const showToothDetail = (event: MouseEvent, position: number) => {
  selectedToothForDetail.value = position;
  toothDetailPopover.value?.hide();
  nextTick(() => {
    toothDetailPopover.value?.show(event);
  });
};
</script>

<template>
  <div>
    <div ref="chart" :class="isChildrenMode ? 'mx-auto w-8/12' : 'w-full'">
      <!-- Upper teeth -->
      <div class="flex w-full justify-center">
        <div
          v-for="quadrant in [1 + triTueCuaLamDaiCa, 2 + triTueCuaLamDaiCa]"
          :key="`upper-${quadrant}`"
          :class="{
            'pb-4 pr-2': quadrant === 1 + triTueCuaLamDaiCa,
            'pb-4 pl-2': quadrant === 2 + triTueCuaLamDaiCa,
          }"
          class="relative flex flex-1"
        >
          <div
            v-for="position in toothNumberPerArch"
            :key="`upper-${quadrant}-${position}`"
            class="relative flex-1 p-1"
          >
            <div class="flex h-full flex-col items-center justify-end">
              <div class="mb-2 flex flex-wrap gap-0.5">
                <Tag
                  v-for="state in getActiveStates(getToothNumber(quadrant, position))"
                  :key="state.key"
                  :value="state.short"
                  class="cursor-pointer p-1 py-0 text-xs font-normal"
                  severity="warn"
                  @dblclick="handleTagDoubleClick(getToothNumber(quadrant, position), state)"
                />
              </div>
              <div
                class="mb-1 cursor-pointer text-center text-xs hover:text-primary"
                @click="showToothDetail($event, getToothNumber(quadrant, position))"
              >
                {{ getToothNumber(quadrant, position) }}
              </div>
              <Tooth
                :position="getToothNumber(quadrant, position)"
                :state="answers[`tooth_${getToothNumber(quadrant, position)}`]"
                :treatments="teethTreatments[`tooth_${getToothNumber(quadrant, position)}`]"
                :mode="props.mode"
                @update-state="
                  (key, value) => updateToothState(getToothNumber(quadrant, position), key, value)
                "
                @rightClick="handleRightClick"
              />
            </div>
            <div
              v-if="position < toothNumberPerArch"
              class="absolute bottom-1 right-0 z-10 translate-x-1/2 transform"
            >
              <ToothGap
                :gap="
                  answers[
                    getGapId(
                      getToothNumber(quadrant, position),
                      getToothNumber(quadrant, position + 1),
                    )
                  ]?.gap ?? ''
                "
                :gapId="
                  getGapId(
                    getToothNumber(quadrant, position),
                    getToothNumber(quadrant, position + 1),
                  )
                "
                @updateGap="(id, value) => updateToothGap(id, value)"
              />
            </div>
            <div
              v-if="getToothNumber(quadrant, position) == 11 + triTueCuaLamDaiCa * 10"
              class="absolute -right-2 bottom-1 z-10 translate-x-1/2 transform"
            >
              <ToothGap
                :gap="
                  answers[getGapId(11 + triTueCuaLamDaiCa * 10, 21 + triTueCuaLamDaiCa * 10)]
                    ?.gap ?? ''
                "
                :gapId="getGapId(11 + triTueCuaLamDaiCa * 10, 21 + triTueCuaLamDaiCa * 10)"
                @updateGap="(id, value) => updateToothGap(id, value)"
              />
            </div>
          </div>
          <div
            v-if="quadrant === 1 + triTueCuaLamDaiCa"
            class="absolute bottom-[3.5rem] right-0 top-0 w-[2px] translate-x-[1px] transform bg-gray-300"
          ></div>
        </div>
      </div>

      <!-- Separator -->
      <div class="relative">
        <div class="absolute inset-x-0 top-1/2 h-[2px] -translate-y-[1px] bg-gray-300"></div>
        <div
          class="relative z-10 mx-auto w-20 cursor-pointer bg-white text-center text-xs"
          @click="toggleArchMode"
        >
          {{ isChildrenMode ? "Trẻ em" : "Người lớn" }}
        </div>
      </div>

      <!-- Lower teeth -->
      <div class="flex w-full justify-center">
        <div
          v-for="quadrant in [4 + triTueCuaLamDaiCa, 3 + triTueCuaLamDaiCa]"
          :key="`lower-${quadrant}`"
          :class="{
            'pr-2 pt-4': quadrant === 4 + triTueCuaLamDaiCa,
            'pl-2 pt-4': quadrant === 3 + triTueCuaLamDaiCa,
          }"
          class="relative flex flex-1"
        >
          <div
            v-for="position in toothNumberPerArch"
            :key="`lower-${quadrant}-${position}`"
            class="relative flex-1 p-1"
          >
            <div class="flex h-full flex-col items-center justify-start">
              <Tooth
                :position="getToothNumber(quadrant, position)"
                :state="answers[`tooth_${getToothNumber(quadrant, position)}`]"
                :treatments="teethTreatments[`tooth_${getToothNumber(quadrant, position)}`]"
                :mode="props.mode"
                class="h-auto w-full rotate-180"
                @update-state="
                  (key, value) => updateToothState(getToothNumber(quadrant, position), key, value)
                "
                @rightClick="handleRightClick"
              />
              <div
                class="mt-1 cursor-pointer text-center text-xs hover:text-primary"
                @click="showToothDetail($event, getToothNumber(quadrant, position))"
              >
                {{ getToothNumber(quadrant, position) }}
              </div>
              <div class="mt-2 flex flex-wrap gap-1">
                <Tag
                  v-for="state in getActiveStates(getToothNumber(quadrant, position))"
                  :key="state.key"
                  :value="state.short"
                  class="cursor-pointer p-1 py-0 text-xs font-normal"
                  severity="warn"
                  @dblclick="handleTagDoubleClick(getToothNumber(quadrant, position), state)"
                />
              </div>
              <div
                v-if="position < toothNumberPerArch"
                class="absolute right-0 top-1 z-10 translate-x-1/2 transform"
              >
                <ToothGap
                  :gap="
                    answers[
                      getGapId(
                        getToothNumber(quadrant, position),
                        getToothNumber(quadrant, position + 1),
                      )
                    ]?.gap ?? ''
                  "
                  :gapId="
                    getGapId(
                      getToothNumber(quadrant, position),
                      getToothNumber(quadrant, position + 1),
                    )
                  "
                  lower
                  @updateGap="(id, value) => updateToothGap(id, value)"
                />
              </div>
              <div
                v-if="getToothNumber(quadrant, position) == 41 + triTueCuaLamDaiCa * 10"
                class="absolute -right-2 top-1 z-10 translate-x-1/2 transform"
              >
                <ToothGap
                  :gap="
                    answers[getGapId(31 + triTueCuaLamDaiCa * 10, 41 + triTueCuaLamDaiCa * 10)]
                      ?.gap ?? ''
                  "
                  :gapId="getGapId(31 + triTueCuaLamDaiCa * 10, 41 + triTueCuaLamDaiCa * 10)"
                  lower
                  @updateGap="(id, value) => updateToothGap(id, value)"
                />
              </div>
            </div>
          </div>
          <div
            v-if="quadrant === 4 + triTueCuaLamDaiCa"
            class="absolute bottom-0 right-0 top-[3.5rem] w-[2px] translate-x-[1px] transform bg-gray-300"
          ></div>
        </div>
      </div>
    </div>
    <ToothGrid
      :personId="props.personId"
      :mode="props.mode"
      :toothStatesAnswers="answers"
      :syncToothStates="syncToothData"
      :debouncedSyncToothStates="debouncedSyncToothData"
      :toothTreatmentsAnswers="teethTreatments"
      :syncToothTreatments="syncToothTreatment"
      :debouncedSyncToothTreatments="debouncedSyncToothTreatment"
      :customIssues="customIssues"
      :syncCustomIssues="syncCustomIssues"
      :debouncedSyncCustomIssues="debouncedSyncCustomIssues"
      :customTreatments="customTreatments"
      :syncCustomTreatments="syncCustomTreatments"
      :debouncedSyncCustomTreatments="debouncedSyncCustomTreatments"
    />
    <CustomIssueGrid
      :personId="props.personId"
      :mode="props.mode"
      :issuesAnswers="customIssues"
      :syncIssues="syncCustomIssues"
      :debouncedSyncIssues="debouncedSyncCustomIssues"
      :treatmentsAnswers="customTreatments"
      :syncTreatments="syncCustomTreatments"
      :debouncedSyncTreatments="debouncedSyncCustomTreatments"
    />
    <SelectTreatment
      :modelValue="teethTreatments['tooth_' + rightClickedTooth]"
      @toggled="multiUpdateToothTreatment"
      ref="selectTreatmentPopover"
    />
    <Popover ref="toothDetailPopover" class="max-w-[200px] !p-0">
      <ToothDetail
        v-if="selectedToothForDetail"
        :position="selectedToothForDetail"
        :state="answers[`tooth_${selectedToothForDetail}`]"
        :treatments="teethTreatments[`tooth_${selectedToothForDetail}`]"
        :gap-answers="answers"
        :mode="props.mode"
        @update-state="updateToothState"
        @right-click="handleRightClick"
        @update-gap="updateToothGap"
      />
    </Popover>
  </div>
</template>
