<script lang="ts" setup>
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Tag from "primevue/tag";
import { computed, ref } from "vue";

import SelectTreatment from "@/pages/customer/components/MedicalExam/Tooth/SelectTreatment.vue";
import { getToothTreatmentLabel } from "@/pages/customer/components/MedicalExam/Tooth/toothStates";

interface Props {
  personId: number;
  mode: "state" | "treatment";
  issuesAnswers: Record<string, any>;
  syncIssues: (key: string) => void;
  debouncedSyncIssues: (key: string) => void;
  treatmentsAnswers: Record<string, any>;
  syncTreatments: (key: string) => void;
  debouncedSyncTreatments: (key: string) => void;
}

const props = defineProps<Props>();
const rightClickedIssue = ref<number | null>(null);
const selectTreatmentPopover = ref();

// Computed để lấy danh sách các issues hiện có
const existingIssues = computed(() => {
  const issues: number[] = [];
  for (const key in props.issuesAnswers) {
    if (key.startsWith("custom_issues_")) {
      const index = parseInt(key.split("_").pop() || "");
      if (!isNaN(index)) {
        const issueData = props.issuesAnswers[key];
        const treatmentData = props.treatmentsAnswers[key];
        const isEmptyData =
          JSON.stringify(issueData) === "{}" &&
          (!treatmentData || JSON.stringify(treatmentData) === "{}");

        if (!isEmptyData) {
          issues.push(index);
        }
      }
    }
  }
  return issues.sort((a, b) => a - b);
});

// Thêm issue mới
const addNewIssue = () => {
  const index = existingIssues.value.length > 0 ? Math.max(...existingIssues.value) + 1 : 0;
  const issueKey = `custom_issues_${index}`;

  props.issuesAnswers[issueKey] = { text: "" };
  props.treatmentsAnswers[issueKey] = {};

  props.syncIssues(issueKey);
  props.syncTreatments(issueKey);
};

// Cập nhật text của issue
const updateIssueText = (index: number, text: string) => {
  const issueKey = `custom_issues_${index}`;
  props.issuesAnswers[issueKey] = { text };
  props.debouncedSyncIssues(issueKey);
};

// Handle treatment changes
const handleTreatmentToggle = (treatment: any, state: boolean) => {
  if (rightClickedIssue.value === null) return;

  const issueKey = `custom_issues_${rightClickedIssue.value}`;
  if (state) {
    props.treatmentsAnswers[issueKey] = {
      ...props.treatmentsAnswers[issueKey],
      [treatment.key]: true,
    };
  } else {
    delete props.treatmentsAnswers[issueKey][treatment.key];
  }
  props.syncTreatments(issueKey);
};

const getActiveTreatments = (index: number) => {
  const treatments = props.treatmentsAnswers[`custom_issues_${index}`] || {};
  return Object.entries(treatments)
    .filter(([_, value]) => value === true)
    .map(([key]) => key);
};

const selectedTreatments = computed(() => {
  if (rightClickedIssue.value === null) return {};
  return props.treatmentsAnswers[`custom_issues_${rightClickedIssue.value}`] || {};
});

const handleTreatmentTagDoubleClick = (index: number, treatmentKey: string) => {
  const issueKey = `custom_issues_${index}`;
  delete props.treatmentsAnswers[issueKey][treatmentKey];
  props.syncTreatments(issueKey);
};
</script>

<template>
  <div>
    <div v-if="existingIssues.length > 0">
      <div
        v-for="index in existingIssues"
        :key="`custom_issue_${index}`"
        class="group grid grid-cols-12 items-center gap-4 border-b border-gray-100 py-1.5 odd:bg-white even:bg-gray-50"
      >
        <!-- Remove button -->
        <div class="col-span-1">
          <!-- <Button
            @click="removeIssue(index)"
            icon="pi pi-trash"
            severity="danger"
            text
            size="small"
            class="size-8"
          /> -->
        </div>

        <!-- Issue text input -->
        <div class="col-span-6">
          <InputText
            v-model="issuesAnswers[`custom_issues_${index}`].text"
            class="w-full"
            @input="updateIssueText(index, $event.target.value)"
            placeholder="Nhập vấn đề..."
          />
        </div>

        <!-- Treatments -->
        <div class="col-span-5">
          <div class="flex flex-wrap items-center gap-2">
            <Tag
              v-for="treatmentKey in getActiveTreatments(index)"
              :key="treatmentKey"
              :value="getToothTreatmentLabel(treatmentKey)"
              severity="info"
              class="cursor-pointer font-medium"
              @dblclick="handleTreatmentTagDoubleClick(index, treatmentKey)"
            />
            <Button
              @click="
                (e) => {
                  rightClickedIssue = index;
                  selectTreatmentPopover?.popoverRef.toggle(e);
                }
              "
              icon="pi pi-plus"
              severity="info"
              text
              size="small"
              class="size-8"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Add Custom Issue Button -->
    <div class="group grid grid-cols-12 items-center gap-4 border-b border-gray-100 py-1.5">
      <div class="col-span-12">
        <Button
          icon="pi pi-plus"
          label="Thêm vấn đề"
          @click="addNewIssue"
          severity="secondary"
          text
          size="small"
        />
      </div>
    </div>

    <SelectTreatment
      :modelValue="selectedTreatments"
      @toggled="handleTreatmentToggle"
      ref="selectTreatmentPopover"
    />
  </div>
</template>
