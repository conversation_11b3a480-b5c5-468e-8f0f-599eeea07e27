<script setup lang="ts">
import { ref } from "vue";

import {FormTextarea} from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import ToothChart from "@/pages/customer/components/MedicalExam/components/ToothChart.vue";
import {QuestionGroup, Questions} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import {useTeethStore} from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import {joinRunes} from "@/utils/string";
const teethStore = useTeethStore();
teethStore.setMultiSelectMode(false);

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "kr";

const questions0: Questions = {
  ghi_chu: {
    question: "Ghi chú thêm về răng",
    text: true,
  },

};

const questions1: Questions = {
  hinh_dang_cung_rang_ham_tren: {
    question: "<PERSON>àm trên",
    options: {
      "1": "Oval",
      "2": "Vuông",
      "3": "Tam giác",
    },
  },
  hinh_dang_cung_rang_ham_duoi: {
    question: "Hàm dưới",
    options: {
      "1": "Oval",
      "2": "Vuông",
      "3": "Tam giác",
    },
  },
};

const questions2: Questions = {
  nha_chu_voi_rang: {
    question: "Vôi răng, mảng bám",
    options: {
      "1": "Ít",
      "2": "Trung bình",
      "3": "Nhiều",
    },
  },
  nha_chu_chuan_doan: {
    question: "Chẩn đoán",
    options: {
      "1": "Viêm nướu",
      "2": "Viêm nha chu",
    },
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "Ghi chú", questions: questions0 },
  { title: "Hình dạng cung răng", questions: questions1 },
  { title: "Nha chu", questions: questions2 },
];

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions0, ...questions1, ...questions2 },
  props.personId
);
</script>
<template>
  <div class="my-5 grid grid-cols-1 rounded-md">
    <ToothChart :person-id="personId" default-arch-mode="adult" mode="state"/>
  </div>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
      <div class="bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ questionGroup.title }}
      </div>
      <div
        v-for="(item, questionId, index) in questionGroup.questions"
        :key="`${groupIndex}-${questionId}`"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div class="font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
          </div>
          <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, questionId, optionId)"
                v-model="answers[questionId][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
              <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                  option
                }}</label>
            </div>
          </div>
        </div>
        <div class="col-span-6">
          <FormTextarea
            v-model="answers[questionId].text"
            :min-rows="3"
            class="h-full w-full"
            @input="debouncedSyncData(questionId)"
            @change="syncData(questionId)"
          />
        </div>
      </div>
    </template>
  </div>
</template>
