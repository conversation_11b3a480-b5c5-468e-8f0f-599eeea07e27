<script setup lang="ts">
import { FormTextarea } from "@/base-components/Form";
import useDynamicForm from "@/hooks/useDynamicForm";
import { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "tsnk";

const questions: Questions = {
  kham_rang_dinh_ky: {
    question: "Khám răng định kỳ",
    options: {
      "1": "Không",
      "2": "6 tháng",
      "3": "1 năm",
      "4": "2 năm",
    },
  },
  da_tung_dieu_tri: {
    question: "Đã từng điều trị",
    options: {
      "1": "Trám răng",
      "2": "Chữa tủy",
      "3": "Phục hình implant",
      "4": "Chỉnh nha",
      "5": "Phẫu thuật hàm mặt",
      "6": "Viêm nha chu",
    },
  },
  so_lan_danh_rang: {
    question: "Số lần đánh răng / ngày",
    options: {
      "1": "1 lần",
      "2": "2 lần",
      "3": "3 lần",
    },
  },
  cach_chai_rang: {
    question: "Cách chải răng",
    options: {
      "1": "Ngang",
      "2": "Dọc",
      "3": "Xoay tròn",
    },
  },
  phuong_phap_lam_sach_khac: {
    question: "Sử dụng các phương pháp làm sạch khác",
    options: {
      "1": "Bàn chãi kẻ",
      "2": "Tăm nước",
      "3": "Tăm xỉa răng",
      "4": "Chỉ nha khoa",
    },
  },
  thoi_quen_xau: {
    question: "Thói quen xấu",
    options: {
      "1": "Nghiến răng",
      "2": "Mút cắn ngón tay",
      "3": "Thở miệng",
      "4": "Đẩy lưỡi",
      "5": "Mút môi",
      "6": "Nhai một bên",
    },
  },
  nguoi_than_lech_lac_rang: {
    question:
      "Có người thân nào trong gia đình có những lệch lạc về răng hay xương hàm tương tự không?",
    options: {
      "1": "Có",
      "2": "Không",
      "3": "Cha mẹ",
      "4": "Anh chị",
      "5": "Con cái",
    },
  },
  nguyen_nhan_nieng_rang: {
    question: "Bệnh nhân tự nguyện muốn niềng răng hay gia đình, bạn bè tác động",
    options: {
      "1": "Bệnh nhân tự nguyện",
      "2": "Gia đình",
      "3": "Bạn bè",
      "4": "Khác",
    },
  },
  danh_gia_tang_truong: {
    question: "Đánh giá tăng trưởng",
    text: true,
  },
};

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions },
  props.personId,
);
</script>

<template>
  <div class="my-5 overflow-hidden rounded-md">
    <div class="grid grid-cols-12 gap-4 border-b-2 bg-gray-100 px-4 py-2 font-semibold">
      <div class="col-span-6">Câu hỏi</div>
      <div class="col-span-6">Ghi chú</div>
    </div>
    <div
      v-for="(item, questionId, index) in questions"
      :key="questionId"
      class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
    >
      <div class="col-span-6 flex flex-col justify-center">
        <div class="font-medium">
          <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
        </div>
        <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
          <div v-for="(option, optionId) in item.options" :key="optionId" class="flex items-center">
            <input
              :id="joinRunes(FORM_ID, questionId, optionId)"
              v-model="answers[questionId][optionId]"
              type="checkbox"
              class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
              @change="syncData(questionId)"
            />
            <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{ option }}</label>
          </div>
        </div>
      </div>
      <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
        <input
          :id="joinRunes(FORM_ID, questionId, 'checkbox')"
          v-model="answers[questionId].checkbox"
          type="checkbox"
          class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
          @change="syncData(questionId)"
        />
      </div>
      <div class="col-span-6">
        <FormTextarea
          v-model="answers[questionId].text"
          :min-rows="3"
          class="h-full w-full"
          @input="debouncedSyncData(questionId)"
          @change="syncData(questionId)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
