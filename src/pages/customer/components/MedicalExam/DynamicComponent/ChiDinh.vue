<script setup lang="ts">
import { ref } from "vue";

import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  Questions,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "cd";

const questions1: Questions = {
  phim_quanh_chop: {
    question: "Phim quanh chóp",
    checkbox: true,
  },
  phim_toan_canh: {
    question: "Phim toàn cảnh",
    checkbox: true,
  },
  phim_so_nghieng: {
    question: "Phim sọ nghiêng",
    checkbox: true,
  },
  phim_so_thang: {
    question: "Phim sọ thẳng",
    checkbox: true,
  },
  phim_ct_1_2_ham: {
    question: "Phim CT 1 hàm ( T/P), CT 2 hàm",
    checkbox: true,
  },
};

const questions2: Questions = {
  tong_phan_tich_te_bao_mau: {
    question: "Tổng phân tích tế bào máu",
    checkbox: true,
  },
  duong_huyet_doi: {
    question: "Đường huyết đối",
    checkbox: true,
  },
  thoi_gian_prohrmbin: {
    question: "Thời gian Prohrmbin (PT)",
    checkbox: true,
  },
  thoi_gian_thromboplastin: {
    question: "Thời gian Thromboplastin từng phần hoạt hóa (APTT)",
    checkbox: true,
  },
  thoi_gian_thrombin: {
    question: "Thời gian Thrombin",
    checkbox: true,
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "1. Chỉ định cận lâm sàng", questions: questions1 },
  { title: "2. Xét nghiệm máu", questions: questions2 },
];

const { answers, syncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2 },
  props.personId,
);

const handleChange = (questionId: string) => {
  syncData(questionId);
};
</script>

<template>
  <div class="my-5 grid grid-cols-2 gap-4">
    <div
      v-for="(group, groupIndex) in questionGroups"
      :key="groupIndex"
      class="flex flex-col gap-4"
    >
      <div class="overflow-hidden rounded-md">
        <div class="bg-gray-100">
          <div class="border-b-2 p-4 font-semibold">
            <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ group.title }}
          </div>
        </div>
        <div class="grid flex-grow grid-cols-5">
          <div
            v-for="(item, questionId, index) in group.questions"
            :key="`${questionId}`"
            class="col-span-5 odd:bg-white even:bg-gray-50"
          >
            <label
              :for="joinRunes(FORM_ID, questionId, 'checkbox')"
              class="grid grid-cols-12 gap-4 px-4 py-2"
            >
              <span class="col-span-6 flex flex-col justify-center">
                <span class="font-medium">
                  <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
                </span>
              </span>
              <span v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
                <input
                  :id="joinRunes(FORM_ID, questionId, 'checkbox')"
                  v-model="answers[questionId].checkbox"
                  type="checkbox"
                  class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                  @change="handleChange(questionId)"
                />
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
