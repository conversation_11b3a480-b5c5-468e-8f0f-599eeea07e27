<script setup lang="ts">
import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  Questions,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "kcml";

const questions1: Questions = {
  thang_luoi: {
    question: "Thắng lưỡi",
    options: {
      "1": "<PERSON><PERSON><PERSON> thường",
      "2": "<PERSON><PERSON><PERSON> thấp",
      "3": "Bám cao",
    },
  },
  kich_thuoc: {
    question: "<PERSON><PERSON><PERSON> thước",
    options: {
      "1": "Lớn",
      "2": "<PERSON><PERSON><PERSON> thường",
      "3": "Nhỏ",
    },
  },
  tu_the: {
    question: "<PERSON><PERSON> thế",
    options: {
      "1": "<PERSON><PERSON><PERSON> sang bên khi nuốt",
      "2": "<PERSON><PERSON><PERSON> thường",
      "3": "<PERSON><PERSON><PERSON> ra trước khi nuốt",
    },
  },
};

const questions2: Questions = {
  moi: {
    question: "Môi",
    options: {
      "1": "Cường cơ môi",
      "2": "Thiếu trương lực",
      "3": "Bình thường",
    },
  },
  thang_moi: {
    question: "Thắng môi",
    options: {
      "1": "B<PERSON>nh thường",
      "2": "Bám thấp",
      "3": "Bám cao",
    },
  },
};
const questions3: Questions = {
  benh_ly_duong_ho_hap_tren: {
    question: "Bệnh lý đường hô hấp trên",
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "1. Khám cơ lưỡi", questions: questions1 },
  { title: "2. Khám cơ môi", questions: questions2 },
  { title: "3. Bệnh lý đường hô hấp trên", questions: questions3 },
];

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2, ...questions3 },
  props.personId,
);
</script>

<template>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
      <div class="bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ questionGroup.title }}
      </div>
      <div
        v-for="(item, questionId, index) in questionGroup.questions"
        :key="`${groupIndex}-${questionId}`"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div class="font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
          </div>
          <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, questionId, optionId)"
                v-model="answers[questionId][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
              <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                option
              }}</label>
            </div>
          </div>
        </div>
        <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
          <input
            :id="joinRunes(FORM_ID, questionId, 'checkbox')"
            v-model="answers[questionId].checkbox"
            type="checkbox"
            class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
            @change="syncData(questionId)"
          />
        </div>
        <div class="col-span-6">
          <FormTextarea
            v-model="answers[questionId].text"
            :min-rows="3"
            class="h-full w-full"
            @input="debouncedSyncData(questionId)"
            @change="syncData(questionId)"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
