<script setup lang="ts">
import { FormTextarea } from "@/base-components/Form";
import useDynamicForm from "@/hooks/useDynamicForm";
import { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "dgcn";

const questions: Questions = {
  mut_tay: {
    question: "Mút tay",
    checkbox: true,
  },
  mut_moi: {
    question: "Mút môi",
    checkbox: true,
  },
  tho_mieng: {
    question: "Thở miệng",
    checkbox: true,
  },
  ngay: {
    question: "Ngáy",
    checkbox: true,
  },
  tu_the_moi: {
    question: "Tứ thế môi",
    text: true,
  },
  phat_am_kho_1_so_tu: {
    question: "Phát âm khó 1 số từ",
    text: true,
  },
  ghi_chu: {
    question: "<PERSON><PERSON> chú",
    text: true,
  },
};

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions },
  props.personId,
);
</script>

<template>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <div class="grid grid-cols-12 gap-4 border-b-2 bg-gray-100 px-4 py-2 font-semibold">
      <div class="col-span-6">Vấn đề</div>
      <div class="col-span-1 text-center">Có</div>
      <div class="col-span-5">Ghi chú</div>
    </div>
    <div
      v-for="(item, questionId, index) in questions"
      :key="questionId"
      class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
    >
      <div class="col-span-6 flex flex-col justify-center">
        <div class="font-medium">
          <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
        </div>
        <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
          <div v-for="(option, optionId) in item.options" :key="optionId" class="flex items-center">
            <input
              :id="joinRunes(FORM_ID, questionId, optionId)"
              v-model="answers[questionId][optionId]"
              type="checkbox"
              class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
              @change="syncData(questionId)"
            />
            <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{ option }}</label>
          </div>
        </div>
      </div>
      <div class="col-span-1 flex items-center justify-center">
        <input
          v-if="item.checkbox"
          :id="joinRunes(FORM_ID, questionId, 'checkbox')"
          v-model="answers[questionId].checkbox"
          type="checkbox"
          class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
          @change="syncData(questionId)"
        />
      </div>
      <div class="col-span-5">
        <FormTextarea
          v-model="answers[questionId].text"
          :min-rows="3"
          class="h-full w-full"
          @input="debouncedSyncData(questionId)"
          @change="syncData(questionId)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
