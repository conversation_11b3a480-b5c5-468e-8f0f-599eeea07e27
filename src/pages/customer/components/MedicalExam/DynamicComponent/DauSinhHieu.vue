<script setup lang="ts">
import { ref } from "vue";
import Select from "primevue/select";
import { FormInput } from "@/base-components/Form";
import useDynamicForm from "@/hooks/useDynamicForm";
import { joinRunes } from "@/utils/string";
import { questions, formTemplates } from "./DauSinhHieu";
import { templateOptions } from "./BenhAnNgoaiTru";

const props = defineProps<{
  personId: number;
  showHeader?: boolean;
}>();

const FORM_ID = "dsh";

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions },
  props.personId,
);

const selectedTemplateKey = ref<string | null>(null);

const applyTemplate = () => {
  const key = selectedTemplateKey.value;
  if (!key) return;

  const templateData = formTemplates[key];
  if (!templateData) return;
  console.log(templateData);

  templateData.forEach((item) => {
    console.log(item);
    if (answers[item.id]) {
      answers[item.id].text = item.value;
      syncData(item.id);
    }
  });
};
</script>

<template>
  <div class="">
    <div class="flex items-center gap-2 px-4 py-4">
      <label for="dsh-template-select" class="font-semibold">Chọn mẫu:</label>
      <Select
        id="dsh-template-select"
        v-model="selectedTemplateKey"
        :options="templateOptions"
        optionLabel="name"
        optionValue="key"
        placeholder="Chọn mẫu dấu sinh hiệu..."
        class="w-full md:w-1/3"
        @change="applyTemplate"
      />
    </div>

    <div class="grid grid-cols-1 overflow-hidden rounded-md border">
      <div class="grid grid-cols-12 gap-4 border-b-2 bg-gray-100 px-4 py-2 font-semibold">
        <div class="col-span-6">Chỉ số</div>
        <div class="col-span-6">Giá trị</div>
      </div>
      <div
        v-for="(item, questionId, index) in questions"
        :key="questionId"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div class="font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
          </div>
          <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, questionId, optionId)"
                v-model="answers[questionId][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
              <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                option
              }}</label>
            </div>
          </div>
        </div>
        <div class="col-span-6 flex items-center justify-center">
          <FormInput
            :id="joinRunes(FORM_ID, questionId)"
            v-model="answers[questionId].text"
            :type="item.number ? 'number' : 'text'"
            :placeholder="item.placeholder"
            class="w-full"
            @input="debouncedSyncData(questionId)"
            @change="syncData(questionId)"
          />
        </div>
      </div>
    </div>
  </div>
</template>
