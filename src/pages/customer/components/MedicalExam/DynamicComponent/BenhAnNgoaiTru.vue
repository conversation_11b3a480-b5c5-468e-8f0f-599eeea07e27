<script setup lang="ts">
import { ref } from "vue";
import { joinRunes } from "@/utils/string";
import { QuestionGroup, TemplateItem } from "./types";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  FORM_ID,
  questionsLyDo,
  questionsHoiBenh,
  questionsKhamBenh,
  questionsTongKet,
  defaultQuestionGroups,
  formTemplates,
  templateOptions,
} from "./BenhAnNgoaiTru";

const props = defineProps<{
  personId: number;
}>();

const questionGroups = ref<QuestionGroup[]>(defaultQuestionGroups);

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  {
    ...questionsLyDo,
    ...questionsHoiBenh,
    ...questionsKhamBenh,
    ...questionsTongKet,
  },
  props.personId,
);

const selectedTemplateKey = ref<string | null>(null);

const applyTemplate = () => {
  if (!selectedTemplateKey.value) return;

  const templateData = formTemplates[selectedTemplateKey.value];

  if (!templateData) {
    const selectedOption = templateOptions.find((opt) => opt.key === selectedTemplateKey.value);
    const displayName = selectedOption ? selectedOption.name : selectedTemplateKey.value;
    console.error(
      `Template data not found for key: ${selectedTemplateKey.value} (Template: ${displayName})`,
    );
    return;
  }

  const selectedOption = templateOptions.find((opt) => opt.key === selectedTemplateKey.value);
  const displayName = selectedOption ? selectedOption.name : selectedTemplateKey.value;

  templateData.forEach((item: TemplateItem) => {
    if (item.id in answers) {
      answers[item.id].text = item.value;
      syncData(item.id);
    } else {
      console.warn(
        `Question ID "${item.id}" from template "${displayName}" (key: ${selectedTemplateKey.value}) not found in form answers.`,
      );
    }
  });
};
</script>

<template>
  <div class="overflow-hidden rounded-md">
    <div class="flex items-center gap-2 px-4 py-4">
      <label for="template-select" class="font-semibold">Chọn mẫu:</label>
      <Select
        id="template-select"
        v-model="selectedTemplateKey"
        :options="templateOptions"
        optionLabel="name"
        optionValue="key"
        placeholder="Chọn mẫu bệnh án..."
        class="w-full md:w-1/3"
        @change="applyTemplate"
      />
    </div>

    <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
      <div class="bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ questionGroup.title }}
      </div>

      <div
        v-for="(item, questionId, index) in questionGroup.questions"
        :key="`${groupIndex}-${questionId}`"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div>
            <label :for="joinRunes(FORM_ID, questionId as string)" class="font-medium">
              <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
            </label>
          </div>
        </div>

        <div class="col-span-1 flex items-center justify-center"></div>

        <div class="col-span-5">
          <input
            :id="joinRunes(FORM_ID, questionId as string)"
            v-model="(answers[questionId as string] as any).text"
            type="text"
            class="w-full min-w-[20rem] rounded-md border-slate-200 text-sm shadow-sm transition duration-200 ease-in-out placeholder:text-slate-400/90 focus:border-primary focus:border-opacity-0 focus:ring-2 focus:ring-active focus:ring-opacity-90 disabled:cursor-not-allowed disabled:bg-slate-100 dark:border-transparent dark:bg-darkmode-800 dark:placeholder:text-slate-500/80 dark:focus:ring-slate-700 dark:focus:ring-opacity-50 dark:disabled:border-transparent dark:disabled:bg-darkmode-800/50 [&[readonly]]:cursor-not-allowed [&[readonly]]:bg-slate-100 [&[readonly]]:dark:border-transparent [&[readonly]]:dark:bg-darkmode-800/50"
            @input="debouncedSyncData(questionId as string)"
            @change="syncData(questionId as string)"
          />
        </div>
      </div>
    </template>
  </div>
</template>
