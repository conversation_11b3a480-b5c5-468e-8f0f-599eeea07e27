import { Questions, TemplateItem } from "./types";

export const FORM_ID = "bant";

export const questionsLyDo: Questions = {
  ly_do_vao_vien: { question: "Lý do vào viện" },
};

export const questionsHoiBenh: Questions = {
  qua_trinh_benh_ly: { question: "Quá trình bệnh lý" },
  tien_su_ban_than: { question: "Bản thân" },
  tien_su_gia_dinh: { question: "Gia đình" },
};

export const questionsKhamBenh: Questions = {
  toan_than: { question: "Toàn thân" },
  benh_chuyen_khoa: { question: "Bệnh chuyên khoa" },
  hinh_ve_mo_ta: { question: "Hình vẽ mô tả tổn thương khi vào viện" },
  tom_tat_benh_an: { question: "Tóm tắt bệnh án" },
  chan_doan_khoa: { question: "Chẩn đo<PERSON> của khoa khá<PERSON> bệnh" },
  da_xu_ly_tuyen_duoi: { question: "Đã xử lý của tuyến dưới" },
  dieu_tri_tu_ngay: { question: "Điều trị từ ngày" },
  dieu_tri_toi_ngay: { question: "Điều trị tới ngày" },
};

export const questionsTongKet: Questions = {
  tk_qua_trinh_benh_ly: { question: "Quá trình bệnh lý và diễn biến lâm sàn" },
  tk_tom_tat_ket_qua: {
    question: "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chuẩn đoán",
  },
  tk_chuan_doan_benh_chinh: { question: "Bệnh chính" },
  tk_chuan_doan_benh_kem_theo: { question: "Bệnh kèm theo" },
  tk_phuong_phap_dieu_tri: { question: "Phương pháp điều trị" },
  tk_tinh_trang_ra_vien: { question: "Tình trạng người bệnh ra viện" },
  tk_huong_dieu_tri_tiep: {
    question: "Hướng điều trị và các chế độ tiếp theo",
  },
  tk_x_quang: { question: "Hồ sơ X-quang" },
  tk_ct_scanner: { question: "Hồ sơ CT Scanner" },
  tk_sieu_am: { question: "Hồ sơ Siêu âm" },
  tk_xet_nghiem: { question: "Hồ sơ Xét nghiệm" },
  tk_khac: { question: "Hồ sơ Khác" },
  tk_toan_bo_ho_so: { question: "Toàn bộ hồ sơ (tóm tắt)" },
  tk_bac_si_kham: { question: "Bác sĩ khám bệnh" },
  tk_so_ngoai_tru: { question: "Số ngoại trú" },
  tk_so_luu_tru: { question: "Số lưu trữ" },
};

export const defaultQuestionGroups = [
  {
    title: "I. Lý do vào viện",
    questions: questionsLyDo,
  },
  {
    title: "II. Hỏi bệnh",
    questions: questionsHoiBenh,
  },
  {
    title: "III. Khám bệnh",
    questions: questionsKhamBenh,
  },
  {
    title: "IV. Tổng kết bệnh án",
    questions: questionsTongKet,
  },
];

export const formTemplates: Record<string, TemplateItem[]> = {
  cao_voi_rang: [
    { id: "ly_do_vao_vien", value: "Chảy máu nướu" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị chảy máu nướu khi chải răng. Bệnh nhân đến khám tại phòng khám.",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    {
      id: "benh_chuyen_khoa",
      value:
        "Vôi răng viêm nướu (++).Vôi răng trên nướu + mảng bám nhiều.Vôi răng dưới nướu ít.Không có túi nha chu.",
    },
    { id: "tom_tat_benh_an", value: "Chảy máu nướu do vôi răng" },
    { id: "chan_doan_khoa", value: "Viêm nướu" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value:
        "Bệnh nhân thường bị chảy máu nướu khi chải răng. Bệnh nhân đến khám tại phòng khám và được chẩn đoán là viêm nướu",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Viêm nướu" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    { id: "tk_phuong_phap_dieu_tri", value: "Cạo vôi răng" },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  tram_rang: [
    { id: "ly_do_vao_vien", value: "Ê buốt răng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị ê buốt răng khi ăn nhai. Bệnh nhân đến khám tại phòng khám",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "" },
    { id: "tom_tat_benh_an", value: "Ê buốt răng khi ăn nhai" },
    { id: "chan_doan_khoa", value: "Sâu răng" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value:
        "Bệnh nhân thường bị ê buốt răng khi ăn nhai, Bệnh nhân đến khám tại phòng khám và được chẩn đoán là sâu răng",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Sâu răng" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    { id: "tk_phuong_phap_dieu_tri", value: "Trám composite" },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  nho_rang: [
    { id: "ly_do_vao_vien", value: "Đau nhức răng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị đau nhức răng. Bệnh nhân đến khám tại phòng khám",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "Đau nhức răng vùng hàm dưới" },
    { id: "tom_tat_benh_an", value: "Đau nhức răng vùng hàm dưới" },
    { id: "chan_doan_khoa", value: "Sâu răng" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value:
        "Bệnh nhân bị đau nhức răng vùng hàm dưới. Bệnh nhân đến khám tại phòng khám và được chẩn đoán là sâu răng",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Sâu răng" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    {
      id: "tk_phuong_phap_dieu_tri",
      value:
        "Nhổ răng+ toa thuốc: efferalgan 500mg: số lượng 03 viên( sáng:1 , trưa:1 , chiều:1).Transamin 500mg: số lượng 2 viên (uống sau khi nhả gòn)",
    },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  tay_trang_rang: [
    { id: "ly_do_vao_vien", value: "Răng ố vàng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thấy răng ố vàng + xỉn màu nên đến khám và điều trị",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "VRVN (++).Mảng bám ít" },
    { id: "tom_tat_benh_an", value: "Bệnh nhân thấy răng ố vàng+ xỉn màu" },
    { id: "chan_doan_khoa", value: "Răng ố vàng" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value: "Bệnh nhân thấy răng ố vàng+ xỉn màu. Cạo vôi răng+ đáng bóng. Tẩy trắng",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Răng ố vàng" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    { id: "tk_phuong_phap_dieu_tri", value: "Tẩy trắng" },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  nho_rang_48: [
    { id: "ly_do_vao_vien", value: "Đau nhức răng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị đau nhức răng vùng hàm dưới, bệnh nhân đến khám tại phòng khám\n",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "Đau nhức răng vùng hàm dưới, răng 48 chưa mọc" },
    { id: "tom_tat_benh_an", value: "Đau nhức răng vùng hàm dưới, răng 48 chưa mọc" },
    { id: "chan_doan_khoa", value: "Răng 48 mọc ngầm" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị đau nhức răng vùng hàm dưới, răng 48 chưa mọc",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Răng 48 mọc ngầm" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    {
      id: "tk_phuong_phap_dieu_tri",
      value:
        "Tiểu phẫu thuật răng 48+ toa thuốc. Augmentin 625mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). Medrol 16mg: 5 viên ( sáng: 1). Efferalgan 500mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). Transamin 500mg: 03 viên ( sáng: 1, trưa: 1, chiều: 1)\n",
    },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  nho_rang_38: [
    { id: "ly_do_vao_vien", value: "Đau nhức răng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị đau nhức răng vùng hàm dưới, bệnh nhân đến khám tại phòng khám",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "Đau nhức răng vùng hàm dưới, răng 38 chưa mọc" },
    { id: "tom_tat_benh_an", value: "Đau nhức răng vùng hàm dưới, răng 38 chưa mọc" },
    { id: "chan_doan_khoa", value: "Răng 38 mọc ngầm" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value: "Bệnh nhân thường bị đau nhức răng vùng hàm dưới, răng 38 chưa mọc",
    },
    { id: "tk_tom_tat_ket_qua", value: "Chưa ghi nhận bất thường" },
    { id: "tk_chuan_doan_benh_chinh", value: "Răng 38 mọc ngầm" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    {
      id: "tk_phuong_phap_dieu_tri",
      value:
        "Tiểu phẫu thuật răng 38+ toa thuốc. Augmentin 625mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). Medrol 16mg: 5 viên ( sáng: 1). Efferalgan 500mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). Transamin 500mg: 03 viên ( sáng: 1, trưa: 1, chiều: 1)\n",
    },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
  noi_nha: [
    { id: "ly_do_vao_vien", value: "Đau răng" },
    {
      id: "qua_trinh_benh_ly",
      value: "Răng bị sâu, cách đây 3 ngày bị bể, đau nhiều không ngủ được\n",
    },
    { id: "tien_su_ban_than", value: "Bình thường" },
    { id: "tien_su_gia_dinh", value: "Bình thường" },
    { id: "toan_than", value: "Ổn" },
    { id: "benh_chuyen_khoa", value: "Đau răng" },
    { id: "tom_tat_benh_an", value: "Răng vỡ lớn đến tủy" },
    { id: "chan_doan_khoa", value: "Viêm quanh chóp" },
    { id: "da_xu_ly_tuyen_duoi", value: "" },
    { id: "dieu_tri_tu_ngay", value: "" },
    { id: "dieu_tri_toi_ngay", value: "" },
    {
      id: "tk_qua_trinh_benh_ly",
      value:
        "Răng… vỡ lớn, đau. Mở tủy. Đo CD: 21mm, SSOT/ F2 - bơm rửa. Quay canxi, trám tạm cavic. Trám bít ống tủy. Trám kết thúc",
    },
    { id: "tk_tom_tat_ket_qua", value: "Thấy quang quanh chóp" },
    { id: "tk_chuan_doan_benh_chinh", value: "Viêm quanh chóp" },
    { id: "tk_chuan_doan_benh_kem_theo", value: "" },
    {
      id: "tk_phuong_phap_dieu_tri",
      value: "Điều trị tủy răng và trám bít ống tủy bằng gutta pencha\n",
    },
    { id: "tk_tinh_trang_ra_vien", value: "Ổn" },
    { id: "tk_huong_dieu_tri_tiep", value: "Hướng dẫn vệ sinh răng miệng" },
  ],
};

export const templateOptions = [
  { name: "Cạo vôi răng", key: "cao_voi_rang" },
  { name: "Trám răng", key: "tram_rang" },
  { name: "Nhổ răng", key: "nho_rang" },
  { name: "Tẩy trắng răng", key: "tay_trang_rang" },
  { name: "Nhổ răng 48", key: "nho_rang_48" },
  { name: "Nhổ răng 38", key: "nho_rang_38" },
  { name: "Nội nha", key: "noi_nha" },
];
