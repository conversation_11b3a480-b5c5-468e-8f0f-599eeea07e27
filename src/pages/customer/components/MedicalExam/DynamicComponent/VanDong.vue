<script setup lang="ts">
import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  Questions,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "vd";

const questions1: Questions = {
  ha: {
    question: "Há",
    text: true,
  },
  sang_ben_p: {
    question: "Sang bên P",
    text: true,
  },
  sang_ben_t: {
    question: "Sang bên T",
    text: true,
  },
  lui_sau: {
    question: "<PERSON>i sau",
    text: true,
  },
  ra_truoc: {
    question: "Ra trước",
    text: true,
  },
};

const questions2: Questions = {
  tinh_chat: {
    question: "Tính chất",
    options: {
      "1": "<PERSON>au",
      "2": "Không đau",
    },
  },
  tieng_keu_khop: {
    question: "Tiếng kêu khớp",
    options: {
      "1": "<PERSON>ụ<PERSON> cụp",
      "2": "Lạo xạo",
    },
  },
  trat_khop_tdh: {
    question: "Trật khớp TDH",
    options: {
      "1": "Có",
      "2": "Không",
    },
  },
  ben: {
    question: "Bên",
    options: {
      "1": "Phải",
      "2": "Trái",
    },
  },
  thi: {
    question: "Thì",
    options: {
      "1": "Há",
      "2": "Ngậm",
      "3": "Cả 2 thì",
    },
  },
};

const questions3: Questions = {
  tinh_chat_van_dong: {
    question: "Tính chất vận động",
    options: {
      "1": "Thẳng",
      "2": "Lệch P",
      "3": "Lệch T",
      "4": "Zig zắc",
    },
  },
};

const questions4: Questions = {
  cac_nhom_co_khac: {
    question: "Ghi nhận tính chất các nhóm cơ khác",
    text: true,
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "1. Biên độ vận động", questions: questions1 },
  { title: "2. Khớp TDH", questions: questions2 },
  { title: "3. Vận động", questions: questions3 },
  { title: "4. Nhóm cơ khác", questions: questions4 },
];

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2, ...questions3, ...questions4 },
  props.personId,
);
</script>

<template>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
      <div class="bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ questionGroup.title }}
      </div>
      <div
        v-for="(item, questionId, index) in questionGroup.questions"
        :key="`${groupIndex}-${questionId}`"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div class="font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
          </div>
          <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, questionId, optionId)"
                v-model="answers[questionId][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
              <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                option
              }}</label>
            </div>
          </div>
        </div>
        <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
          <input
            :id="joinRunes(FORM_ID, questionId, 'checkbox')"
            v-model="answers[questionId].checkbox"
            type="checkbox"
            class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
            @change="syncData(questionId)"
          />
        </div>
        <div class="col-span-6">
          <FormTextarea
            v-model="answers[questionId].text"
            :min-rows="3"
            class="h-full w-full"
            @input="debouncedSyncData(questionId)"
            @change="syncData(questionId)"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
