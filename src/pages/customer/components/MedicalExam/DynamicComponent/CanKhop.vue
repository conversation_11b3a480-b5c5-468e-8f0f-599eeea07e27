<script setup lang="ts">
import { computed, ref } from "vue";

import { UniversalSetting } from "@/api/extend-types";
import { FormInput, FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useDynamicForm from "@/hooks/useDynamicForm";
import { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "ck";

interface OptionType {
  [key: string]: string;
}

interface OptionsType {
  hang_1: OptionType;
  hang_2_1: OptionType;
  hang_2_2: OptionType;
  hang_3: OptionType;
  [key: string]: OptionType;
}

const defaultOptions: OptionsType = {
  hang_1: { "1": "" },
  hang_2_1: {
    "25": "25%",
    "50": "50%",
    "100": "100%",
  },
  hang_2_2: {
    "25": "25%",
    "50": "50%",
    "100": "100%",
  },
  hang_3: {
    "25": "25%",
    "50": "50%",
    "100": "100%",
  },
};

const settingName = "can_khop__examination";
const { currentSettingValue, refreshCurrentSetting } = useComponentSetting(settingName);

const inputType = computed(() => {
  return currentSettingValue.value?.input_type ? "checkbox" : "textarea";
});

const options = computed(() => {
  if (inputType.value === "textarea") {
    return {
      hang_1: { "1": "" },
      hang_2_1: { "1": "" },
      hang_2_2: { "1": "" },
      hang_3: { "1": "" },
    };
  }
  return defaultOptions;
});

const questions1: Questions = {
  ben_phai_r6: {
    question: "Bên phải : R6",
    options,
  },
  ben_phai_r3: {
    question: "Bên phải : R3",
    options,
  },
  ben_trai_r6: {
    question: "Bên trái : R6",
    options,
  },
  ben_trai_r3: {
    question: "Bên trái : R3",
    options,
  },
};

const questions2: Questions = {
  muc_do_can_chia: {
    question: "Mức độ cắn chìa (OJ)",
    options: {
      "1": "Bình thường(2-3mm)",
      "2": "4-6mm",
      "3": "8-10mm",
      "4": "Cắn đối đầu",
      "5": "Cắn chéo",
    },
  },
};

const questions3: Questions = {
  can_phu: {
    question: "Cắn phủ (OB)",
    options: {
      "1": "20-40% (bình thường)",
      "2": "50%",
      "3": "Lớn hơn 50%",
      "4": "Cắn hở",
    },
  },
};

const questions4: Questions = {
  ham_tren: {
    question: "Hàm trên so với đường giữa mặt",
    options: {
      "1": "Lệch trái",
      "2": "Lệch phải",
      "3": "Đối xứng",
    },
  },
  ham_duoi: {
    question: "Hàm dưới so với đường giữa mặt",
    options: {
      "1": "Cao",
      "2": "Trung bình",
      "3": "Thấp",
    },
  },
  can_doi_dau: {
    question: "Cắn đối đầu răng sau",
  },
  can_cheo: {
    question: "Cắn chéo răng sau",
  },
  can_keo: {
    question: "Cắn kéo răng sau ",
  },
  ghi_chu: {
    question: "Ghi chú",
  },
};

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2, ...questions3, ...questions4 },
  props.personId,
);

const khopCan = [
  { label: "Hạng I", optionKey: "hang_1" },
  { label: "Hạng II chi 1", optionKey: "hang_2_1" },
  { label: "Hạng II chi 2", optionKey: "hang_2_2" },
  { label: "Hạng III", optionKey: "hang_3" },
];

const settingsSchema: UniversalSetting[] = [
  {
    type: "toggle_switch",
    label: "Checkbox type",
    field_name: "input_type",
    value: false // false = textarea, true = checkbox
  }
];
</script>

<template>
  <div class="my-5 grid grid-cols-2 gap-4">
    <div class="flex flex-col gap-4">
      <div class="overflow-hidden rounded-md">
        <div class="bg-gray-100">
          <div class="flex items-center gap-2 border-b-2 p-4 font-semibold">
            <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />Theo chiều trước sau
            <DynamicSetting
              :settingName="settingName"
              :settingsSchema="settingsSchema"
              title="Cài đặt hiển thị"
              @popover-close="refreshCurrentSetting"
            />
          </div>
          <!-- Header row -->
          <div class="grid grid-cols-5">
            <div class="col-span-1 flex items-center px-4 py-2 font-semibold">Khớp cắn</div>
            <div
              v-for="(question, index) in Object.values(questions1)"
              :key="index"
              class="col-span-1 px-4 py-2 text-center font-semibold"
            >
              {{ question.question }}
            </div>
          </div>
        </div>
        <div class="grid flex-grow grid-cols-5">
          <template v-for="(hang, hangIndex) in khopCan" :key="hangIndex">
            <div
              :class="[
                'col-span-5 grid grid-cols-5',
                hangIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50',
              ]"
            >
              <div class="col-span-1 flex items-center px-4 py-2">
                {{ hang.label }}
              </div>
              <div
                v-for="(question, questionId) in questions1"
                :key="`${hangIndex}-${questionId}`"
                class="col-span-1 px-4 py-2"
              >
                <template v-if="inputType === 'checkbox'">
                  <div class="grid gap-2">
                    <div
                      v-for="(option, optionId) in options[hang.optionKey]"
                      :key="optionId"
                      class="grid grid-cols-2 items-center"
                    >
                      <div class="flex justify-end pr-1">
                        <input
                          :id="joinRunes(FORM_ID, questionId, hang.optionKey, optionId)"
                          v-model="answers[questionId][joinRunes(hang.optionKey, optionId)]"
                          type="checkbox"
                          class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                          @change="syncData(questionId)"
                        />
                      </div>
                      <label
                        :for="joinRunes(FORM_ID, questionId, hang.optionKey, optionId)"
                        class="pl-1"
                      >
                        {{ option }}
                      </label>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <FormTextarea
                    v-model="answers[questionId][joinRunes(hang.optionKey, '1')]"
                    :min-rows="2"
                    class="w-full"
                    @input="debouncedSyncData(questionId)"
                    @change="syncData(questionId)"
                  />
                </template>
              </div>
            </div>
          </template>

          <div
            v-for="(item, questionId, index) in questions2"
            :key="`${questionId}`"
            class="col-span-5 grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
          >
            <div class="col-span-6 flex flex-col justify-center">
              <div class="font-medium">
                <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
              </div>
              <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
                <div
                  v-for="(option, optionId) in item.options"
                  :key="optionId"
                  class="flex items-center"
                >
                  <input
                    :id="joinRunes(FORM_ID, questionId, optionId)"
                    v-model="answers[questionId][optionId]"
                    type="checkbox"
                    class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                    @change="syncData(questionId)"
                  />
                  <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                    option
                  }}</label>
                </div>
              </div>
            </div>
            <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
              <input
                :id="joinRunes(FORM_ID, questionId, 'checkbox')"
                v-model="answers[questionId].checkbox"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
            </div>
            <div class="col-span-6">
              <FormTextarea
                v-model="answers[questionId].text"
                :min-rows="3"
                class="h-full w-full"
                @input="debouncedSyncData(questionId)"
                @change="syncData(questionId)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-4">
      <div class="overflow-hidden rounded-md">
        <div class="bg-gray-100">
          <div class="border-b-2 p-4 font-semibold">
            <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />Theo chiều đứng
          </div>
        </div>
        <div class="grid flex-grow grid-cols-5">
          <div
            v-for="(item, questionId, index) in questions3"
            :key="`${questionId}`"
            class="col-span-5 grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
          >
            <div class="col-span-6 flex flex-col justify-center">
              <div class="font-medium">
                <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
              </div>
              <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
                <div
                  v-for="(option, optionId) in item.options"
                  :key="optionId"
                  class="flex items-center"
                >
                  <input
                    :id="joinRunes(FORM_ID, questionId, optionId)"
                    v-model="answers[questionId][optionId]"
                    type="checkbox"
                    class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                    @change="syncData(questionId)"
                  />
                  <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                    option
                  }}</label>
                </div>
              </div>
            </div>
            <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
              <input
                :id="joinRunes(FORM_ID, questionId, 'checkbox')"
                v-model="answers[questionId].checkbox"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
            </div>
            <div class="col-span-6">
              <FormTextarea
                v-model="answers[questionId].text"
                :min-rows="3"
                class="h-full w-full"
                @input="debouncedSyncData(questionId)"
                @change="syncData(questionId)"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-hidden rounded-md">
        <div class="bg-gray-100">
          <div class="border-b-2 p-4 font-semibold">
            <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />Theo chiều ngang
          </div>
        </div>
        <div class="grid flex-grow grid-cols-5">
          <div
            v-for="(item, questionId, index) in questions4"
            :key="`${questionId}`"
            class="col-span-5 grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
          >
            <div class="col-span-6 flex flex-col justify-center">
              <div class="font-medium">
                <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
              </div>
              <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
                <div
                  v-for="(option, optionId) in item.options"
                  :key="optionId"
                  class="flex items-center"
                >
                  <input
                    :id="joinRunes(FORM_ID, questionId, optionId)"
                    v-model="answers[questionId][optionId]"
                    type="checkbox"
                    class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                    @change="syncData(questionId)"
                  />
                  <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                    option
                  }}</label>
                </div>
              </div>
            </div>
            <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
              <input
                :id="joinRunes(FORM_ID, questionId, 'checkbox')"
                v-model="answers[questionId].checkbox"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
            </div>
            <div class="col-span-6">
              <FormTextarea
                v-model="answers[questionId].text"
                :min-rows="3"
                class="h-full w-full"
                @input="debouncedSyncData(questionId)"
                @change="syncData(questionId)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
