<script setup lang="ts">
import { computed } from "vue";

import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  questions1,
  questions2,
  questions3,
  questions4,
  questions5,
  questions6,
  questions7,
  questions8,
  questions9,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/TienSuYKhoa";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "tsyk";

const questionGroups: QuestionGroup[] = [
  { title: "1. B<PERSON>nh về tai mũi họng", questions: questions2 },
  { title: "2. <PERSON><PERSON>nh liên quan đến não thần kinh", questions: questions3 },
  { title: "3. <PERSON><PERSON><PERSON> về đường hô hấp", questions: questions4 },
  { title: "4. <PERSON><PERSON><PERSON> hỏi về bện tim mạch, tuần hoàn máu", questions: questions5 },
  { title: "5. <PERSON><PERSON><PERSON> về đường tiêu hoá", questions: questions6 },
  { title: "6. <PERSON><PERSON><PERSON> bệnh về thận - đường tiết niệu", questions: questions7 },
  { title: "7. Các bệnh khác", questions: questions8 },
  { title: "8. Các bệnh về chuyển hóa – Hormon – Máu – Xương khớp", questions: questions9 },
];

const isAllOptionsUnchecked = computed(() => {
  return Object.fromEntries(
    Object.entries({
      ...questions2,
      ...questions3,
      ...questions4,
      ...questions5,
      ...questions6,
      ...questions7,
      ...questions8,
      ...questions9,
    }).map(([questionId, item]) => [
      questionId,
      Object.keys(item.options ?? []).every(
        (optionId) => !answers[questionId][optionId + "_checkbox"],
      ),
    ]),
  );
});

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  {
    ...questions1,
    ...questions2,
    ...questions3,
    ...questions4,
    ...questions5,
    ...questions6,
    ...questions7,
    ...questions8,
    ...questions9,
  },
  props.personId,
);
</script>

<template>
  <div>
    <Accordion :value="['0', '1']" multiple>
      <AccordionPanel value="0" class="border-none">
        <AccordionHeader class="px-0">Câu hỏi chung</AccordionHeader>
        <AccordionContent unstyled>
          <div class="grid grid-cols-1 overflow-hidden rounded-md">
            <div class="grid grid-cols-12 gap-4 border-b-2 bg-gray-100 px-4 py-2 font-semibold">
              <div class="col-span-6">Câu hỏi</div>
              <div class="col-span-1 text-center">Có</div>
              <div class="col-span-5">Ghi chú</div>
            </div>
            <div
              v-for="(item, questionId, index) in questions1"
              :key="questionId"
              class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
            >
              <div class="col-span-6 flex flex-col justify-center">
                <div>
                  <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
                </div>
                <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
                  <div
                    v-for="(option, optionId) in item.options"
                    :key="optionId"
                    class="flex items-center"
                  >
                    <input
                      :id="joinRunes(FORM_ID, questionId, optionId)"
                      v-model="answers[questionId][optionId]"
                      type="checkbox"
                      class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                      @change="syncData(questionId)"
                    />
                    <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                      option
                    }}</label>
                  </div>
                </div>
              </div>
              <div class="col-span-1 flex items-center justify-center">
                <input
                  v-if="item.checkbox"
                  :id="joinRunes(FORM_ID, questionId, 'checkbox')"
                  v-model="answers[questionId].checkbox"
                  type="checkbox"
                  class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                  @change="syncData(questionId)"
                />
              </div>
              <div class="col-span-5">
                <input
                  v-model="answers[questionId].text"
                  type="text"
                  class="w-full min-w-[20rem] rounded-md border-slate-200 text-sm shadow-sm transition duration-200 ease-in-out placeholder:text-slate-400/90 focus:border-primary focus:border-opacity-0 focus:ring-2 focus:ring-active focus:ring-opacity-90 disabled:cursor-not-allowed disabled:bg-slate-100 dark:border-transparent dark:bg-darkmode-800 dark:placeholder:text-slate-500/80 dark:focus:ring-slate-700 dark:focus:ring-opacity-50 dark:disabled:border-transparent dark:disabled:bg-darkmode-800/50 [&[readonly]]:cursor-not-allowed [&[readonly]]:bg-slate-100 [&[readonly]]:dark:border-transparent [&[readonly]]:dark:bg-darkmode-800/50"
                  @input="debouncedSyncData(questionId)"
                  @change="syncData(questionId)"
                />
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionPanel>
      <AccordionPanel value="1" class="border-none">
        <AccordionHeader class="px-0">Phiếu trả lời</AccordionHeader>
        <AccordionContent unstyled class="pb-5">
          <div class="grid grid-cols-1 overflow-hidden rounded-md">
            <div class="grid grid-cols-12 gap-4 border-b-2 bg-gray-100 px-4 py-2 font-semibold">
              <div class="col-span-2">Bệnh</div>
              <div class="col-span-2 flex justify-center">Đã từng phẫu thuật</div>
              <div class="col-span-2 flex justify-center">Đã từng được điều trị bằng thuốc</div>
              <div class="col-span-2 flex justify-center">
                Đang được theo dõi định kỳ tại cơ sở y tế
              </div>
              <div class="col-span-2 flex justify-center">Đang điều trị ngoại trú bằng thuốc</div>
              <div class="col-span-2 flex justify-center">
                Được chỉ thị điều trị nhưng chưa thực hiện
              </div>
            </div>
            <div
              :class="[
                'w-full snap-y',
                !$route.name?.toString().startsWith('print-') && 'h-[900px] overflow-y-auto',
              ]"
            >
              <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
                <div class="snap-start bg-gray-100 p-4 font-semibold">
                  <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{
                    questionGroup.title
                  }}
                </div>
                <div
                  v-for="(item, questionId, index) in questionGroup.questions"
                  :key="`${groupIndex}-${questionId}`"
                  class="grid snap-start grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
                >
                  <div class="col-span-2 flex flex-col justify-center">
                    <div class="flex font-medium">
                      <span class="mr-2">{{ index + 1 }}.</span>
                      <span>{{ item.question }}</span>
                    </div>
                    <div
                      v-if="isAllOptionsUnchecked[questionId]"
                      class="mt-2 flex font-medium text-success"
                    >
                      <Lucide icon="CheckCircle" class="mr-2 h-5 w-5" />
                      <span>Không có</span>
                    </div>
                  </div>
                  <div
                    v-for="(option, optionId) in item.options"
                    :key="optionId"
                    class="col-span-2 flex flex-col items-center"
                  >
                    <div class="mb-2">
                      <input
                        :id="joinRunes(FORM_ID, questionId, optionId, 'checkbox')"
                        v-model="answers[questionId][optionId + '_checkbox']"
                        type="checkbox"
                        class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                        @change="syncData(questionId)"
                      />
                    </div>
                    <div class="w-full">
                      <FormTextarea
                        v-model="answers[questionId][optionId + '_text']"
                        :min-rows="3"
                        :disabled="!answers[questionId][optionId + '_checkbox']"
                        @input="debouncedSyncData(questionId)"
                        @change="syncData(questionId)"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </AccordionContent>
      </AccordionPanel>
    </Accordion>
  </div>
</template>

<style scoped>
@media print {
  .overflow-y-auto {
    overflow: visible !important;
  }

  .snap-y {
    scroll-snap-type: none !important;
  }
}
</style>
