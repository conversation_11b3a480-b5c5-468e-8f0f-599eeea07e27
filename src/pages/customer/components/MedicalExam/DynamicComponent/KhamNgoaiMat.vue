<script setup lang="ts">
import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  Questions,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "knm";

const questions1: Questions = {
  su_can_xung_qua_duong_giua_mat: {
    question: "Sự cân xứng qua đường giữa mặt",
    options: {
      "1": "Cân xứng",
      "2": "Lệch T",
      "3": "Lệch P",
    },
  },
  dang_mat: {
    question: "Dạng mặt",
    options: {
      "1": "Ngắn",
      "2": "Dài",
      "3": "Trung bình",
    },
  },
  ti_le_cac_tang_mat: {
    question: "Tỉ lệ các tầng mặt",
    options: {
      "1": "<PERSON><PERSON><PERSON> hòa",
      "2": "Tầng mặt dưới dài",
      "3": "Tầng mặt dưới ngắn",
    },
  },
  moi_o_tu_the_nghi: {
    question: "Môi ở tư thế nghỉ",
    options: {
      "1": "Khép kín",
      "2": "Hở",
    },
  },
  cam: {
    question: "Cằm",
    options: {
      "1": "Cân xứng",
      "2": "Lớn",
      "3": "Nhỏ",
      "4": "Lệch T",
      "5": "Lệch P",
    },
  },
};
const questions2: Questions = {
  duong_giua: {
    question: "Đường giữa",
    options: {
      "1": "Đối xứng",
      "2": "Lệch phải",
      "3": "Lệch trái",
    },
  },
  duong_cuoi: {
    question: "Đường cười",
    options: {
      "1": "Cao",
      "2": "Trung bình",
      "3": "Thấp",
    },
  },
  khoang_toi_2_ben: {
    question: "Khoảng tối 2 bên",
    options: {
      "1": "Nhiều",
      "2": "Ít",
    },
  },
  dac_diem_khac: {
    question: "Đặc điểm khác",
    options: {
      "1": "Lộ nướu",
      "2": "Cười lệch môi",
    },
  },
};
const questions3: Questions = {
  dang_mat_nghieng: {
    question: "Dạng mặt (nghiêng)",
    options: {
      "1": "Thẳng",
      "2": "Lõm",
      "3": "Nhô",
    },
  },
  ti_le_cac_tang_mat_nghieng: {
    question: "Tỉ lệ các tầng mặt (nghiêng)",
    options: {
      "1": "Hài hòa",
      "2": "Tầng mặt dưới dài",
      "3": "Tầng mặt dưới ngắn",
    },
  },
  goc_mat_phang_ham_duoi: {
    question: "Góc mặt phẳng hàm dưới",
    options: {
      "1": "Bình thường",
      "2": "Nhỏ",
      "3": "Lớn",
    },
  },
  goc_mui_moi: {
    question: "Góc mũi môi",
    options: {
      "1": "Sâu",
      "2": "Cạn",
      "3": "Dẹp",
    },
  },
  do_nghieng_chan_mui: {
    question: "Độ nghiêng chân mũi",
    text: true,
  },
  vi_tri_moi_tren: {
    question: "Vị trí môi trên",
    text: true,
  },
  duong_tham_my_S: {
    question: "Đường thẩm mỹ S",
    text: true,
  },
  duong_tham_my_E: {
    question: "Đường thẩm mỹ E",
    text: true,
  },
  ranh_moi_cam: {
    question: "Rãnh môi cằm",
    options: {
      "1": "Bình thường",
      "2": "Sâu",
      "3": "Cạn",
    },
  },
  go_ma: {
    question: "Gò má",
    options: {
      "1": "Bình thường",
      "2": "Lép",
      "3": "Nhô",
    },
  },
  mui: {
    question: "Mũi",
    options: {
      "1": "Bình thường",
      "2": "Nhỏ",
      "3": "Lớn",
    },
  },
  cam_nghieng: {
    question: "Cằm (nghiêng)",
    options: {
      "1": "Bình thường",
      "2": "Nhô",
      "3": "Lùi",
    },
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "1. Nhìn thẳng", questions: questions1 },
  { title: "2. Nhìn thẳng khi cười", questions: questions2 },
  { title: "3. Nhìn nghiêng", questions: questions3 },
];

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2, ...questions3 },
  props.personId,
);
</script>

<template>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <template v-for="(questionGroup, groupIndex) in questionGroups" :key="groupIndex">
      <div class="bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />{{ questionGroup.title }}
      </div>
      <div
        v-for="(item, questionId, index) in questionGroup.questions"
        :key="`${groupIndex}-${questionId}`"
        class="grid grid-cols-12 gap-4 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-6 flex flex-col justify-center">
          <div class="font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
          </div>
          <div v-if="item.options" class="mt-2 grid grid-cols-2 gap-2">
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, questionId, optionId)"
                v-model="answers[questionId][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(questionId)"
              />
              <label :for="joinRunes(FORM_ID, questionId, optionId)" class="ml-2">{{
                option
              }}</label>
            </div>
          </div>
        </div>
        <div v-if="item.checkbox" class="col-span-6 flex items-center justify-center">
          <input
            :id="joinRunes(FORM_ID, questionId, 'checkbox')"
            v-model="answers[questionId].checkbox"
            type="checkbox"
            class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
            @change="syncData(questionId)"
          />
        </div>
        <div class="col-span-6">
          <FormTextarea
            v-model="answers[questionId].text"
            :min-rows="3"
            class="h-full w-full"
            @input="debouncedSyncData(questionId)"
            @change="syncData(questionId)"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
