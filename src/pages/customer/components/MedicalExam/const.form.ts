import { cloneDeep } from "lodash";

const step1Config = {
  size: "md",
  class: ["table-form"],
  schema: {
    containerTitles: {
      type: "group",
      schema: {
        title: {
          type: "static",
          content: "Tình trạng",
          tag: "strong",
          columns: {
            container: 7,
            label: 12,
            wrapper: 12,
          },
        },
        title2: {
          type: "static",
          content: "Có",
          tag: "strong",
          columns: {
            container: 1,
            label: 12,
            wrapper: 12,
          },
        },
        title3: {
          type: "static",
          content: "Nội dung",
          tag: "strong",
          columns: {
            container: 4,
            label: 12,
            wrapper: 12,
          },
        },
      },
    },
  },
};

const step2Config = {
  size: "md",
  schema: {},
};

const step3Config = cloneDeep(step2Config);

const step4Config = {
  size: "md",
  class: ["table-form"],
  schema: {
    containerTitles: {
      type: "group",
      schema: {
        title: {
          type: "static",
          content: "Tình trạng",
          tag: "strong",
          columns: {
            container: 7,
            label: 12,
            wrapper: 12,
          },
        },
        title2: {
          type: "static",
          content: "Có",
          tag: "strong",
          columns: {
            container: 1,
            label: 12,
            wrapper: 12,
          },
        },
        title3: {
          type: "static",
          content: "Nội dung",
          tag: "strong",
          columns: {
            container: 4,
            label: 12,
            wrapper: 12,
          },
        },
      },
    },
  },
};
const step5Config = {
  ...step2Config,
  class: ["border-form"],
};

const step6Config = cloneDeep(step5Config);
const step7Config = cloneDeep(step5Config);

export const step7Rows = {
  containerTitles: {
    type: "group",
    schema: {
      title: {
        type: "static",
        content: "Khớp cắn",
        tag: "strong",
        columns: {
          container: 3,
          label: 12,
          wrapper: 12,
        },
      },
      title2: {
        type: "static",
        content: "Bên phải: R6",
        tag: "strong",
        columns: {
          container: 2,
          label: 12,
          wrapper: 12,
        },
      },
      title3: {
        type: "static",
        content: "Bên phải: R3",
        tag: "strong",
        columns: {
          container: 2,
          label: 12,
          wrapper: 12,
        },
      },
      title4: {
        type: "static",
        content: "Bên trái: R6",
        tag: "strong",
        columns: {
          container: 2,
          label: 12,
          wrapper: 12,
        },
      },
      title5: {
        type: "static",
        content: "Bên trái: R3",
        tag: "strong",
        columns: {
          container: 2,
          label: 12,
          wrapper: 12,
        },
      },
    },
  },
};

const step8Config = cloneDeep(step5Config);
const step9Config = cloneDeep(step2Config);
const step10Config = cloneDeep(step5Config);
const step11Config = cloneDeep(step1Config);
const step12Config = {
  ...step4Config,
  schema: {
    containerTitles: {
      type: "group",
      schema: {
        title: {
          type: "static",
          content: "Góc độ và khoảng cách",
          tag: "strong",
          columns: {
            container: 4,
            label: 12,
            wrapper: 12,
          },
        },
        title2: {
          type: "static",
          content: "Đơn vị",
          tag: "strong",
          columns: {
            container: 1,
            label: 12,
            wrapper: 12,
          },
        },
        title3: {
          type: "static",
          content: "Chuẩn",
          tag: "strong",
          columns: {
            container: 1,
            label: 12,
            wrapper: 12,
          },
        },
        title4: {
          type: "static",
          content: "Trước điều trị	",
          tag: "strong",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title5: {
          type: "static",
          content: "Sau điều trị	",
          tag: "strong",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title6: {
          type: "static",
          content: "Đánh giá",
          tag: "strong",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
      },
    },
  },
};

export const phieuTraLoiConfig = {
  size: "md",
  class: "table-form border-form table-choice-form",
  schema: {
    containerTitles: {
      type: "group",
      schema: {
        title: {
          type: "static",
          content: "",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title2: {
          type: "static",
          content: "Đã từng phẫu thuật",
          tag: "span",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title3: {
          type: "static",
          content: "Trước đây đã từng được điều trị bằng thuốc",
          tag: "span",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title4: {
          type: "static",
          content: "Hiện tại đang được theo dõi định kỳ tại cơ sở y tế nhất định",
          tag: "span",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title5: {
          type: "static",
          content: "Hiện tại đang điều trị ngoại trú bằng thuốc",
          tag: "span",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
        title6: {
          type: "static",
          content: " Trước đây đã từng được chỉ thị điều trị nhưng vẫn chưa thực hiện",
          tag: "span",
          columns: {
            container: 2,
            label: 12,
            wrapper: 12,
          },
        },
      },
    },
  },
};

export default [
  step1Config,
  step2Config,
  step4Config,
  step3Config,
  step5Config,
  step6Config,
  step7Config,
  step8Config,
  step9Config,
  step10Config,
  step11Config,
  step12Config,
];
