<script setup lang="ts">
import { ref } from "vue";

import { usePrint } from "@/composables/usePrint";

import NiengRang from "./DynamicComponent/NiengRang.vue";
import TongQuat from "./DynamicComponent/TongQuat.vue";

const props = defineProps<{ personId?: number; personName?: string }>();

const examTabs = [
  {
    text: "Tổng quát",
    type: "general",
  },
  {
    text: "Niềng răng",
    type: "braces",
  },
  {
    text: "Trồng răng",
    type: "implant",
  },
];

const currentTab = ref(0);

const comps = [TongQuat, NiengRang];

// Add print functionality
const { printX } = usePrint();

const handlePrint = () => {
  if (!props.personId) return;

  printX({
    entity: "plan",
    personId: props.personId,
    options: {
      type: examTabs[currentTab.value].type,
    },
  });
};
</script>
<template>
  <div class="px-5">
    <Tabs v-model:value="currentTab" scrollable unstyled lazy>
      <TabList
        :pt="{
          activeBar: { style: { display: 'none !important' } },
          tabList: { style: { border: 'none' } },
        }"
        class="b-tab-list"
      >
        <Tab v-for="(tab, index) in examTabs" :key="index" :value="index">
          {{ tab.text }}
        </Tab>
      </TabList>
      <TabPanels unstyled>
        <TabPanel v-for="(tab, index) in examTabs" :key="index" :value="index" unstyled>
          <component :is="comps[index]" :person-id="props.personId ?? 0" />
        </TabPanel>
      </TabPanels>
    </Tabs>
    <div class="border-t border-slate-200/60 dark:border-darkmode-400">
      <div class="col-span-12 mt-5 flex items-center justify-center sm:justify-end">
        <Button icon="pi pi-print" label="In kế hoạch" @click="handlePrint" />
      </div>
    </div>
  </div>

  <!-- Remove the ModalPrint component if it's no longer needed -->
</template>

<style scoped>
.b-tab-list {
  @apply overflow-hidden rounded-md border border-gray-200;
}

.p-tab {
  @apply border-none px-4 py-3 text-sm font-normal hover:border-gray-300 hover:text-gray-700 focus:outline-none;
}

.p-tab-active {
  @apply border-none bg-slate-100 text-primary;
}
</style>
