<script setup lang="ts">
import { computed, ref } from "vue";

import { UniversalSetting } from "@/api/extend-types";
import { FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useDynamicForm from "@/hooks/useDynamicForm";
import {
  QuestionGroup,
  Questions,
} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "nieng_rang";

const settingName = "nieng_rang__examination";

const settingsSchema: UniversalSetting[] = [
  {
    type: "toggle_switch",
    label: "Mở rộng",
    field_name: "is_extended",
    value: true,
  },
];

const { currentSettingValue, refreshCurrentSetting } = useComponentSetting(settingName);

const isExtended = computed(() => {
  return currentSettingValue.value?.is_extended ?? true;
});

const questions1: Questions = {
  xuong: {
    question: "Xương",
    options: {
      hang_i: "Hạng I",
      hang_ii: "Hạng II",
      hang_iii: "Hạng III",
      mom_xuong: "Móm xương",
      ho_xuong: "Hô xương",
    },
  },
  mo_mem: {
    question: "Mô mềm",
    options: {
      moi_day: "Môi dày",
      nho_2_moi: "Nhô 2 môi",
      moi_mong: "Môi mỏng",
      mom: "Móm",
      cam_lem: "Cằm lẹm",
      cam_lui: "Cằm lùi",
    },
  },
  rang: {
    question: "Răng",
    text: true,
  },
  danh_gia_chuc_nang: {
    question: "Đánh giá sự tăng trưởng - chức năng",
    text: true,
  },
};

const questions2: Questions = {
  thong_nhat: {
    question: "Thống nhất mục tiêu sau điều trị niềng răng giữa bác sĩ và bệnh nhân",
    text: true,
  },
};

const questions3: Questions = {
  van_de_nho_rang: {
    question: "Vấn đề nhô răng",
    options: {
      rang_coi_nho: "Răng cối nhỏ",
      rang_khon: "Răng khôn",
      chan_rang_hu: "Chân răng hư",
      rang_du_ke_giua: "Răng dư kẽ giữa",
      rang_ngam: "Răng ngầm",
      rang_sua: "Răng sữa",
      rang_hu: "Răng hư",
      chan_rang_sot: "Chân răng sót",
      bac_si_nieng_bao_sau: "Bác sĩ niềng báo sau",
    },
  },
  mang_khi_cu: {
    question: "Mang khí cụ",
    options: {
      tpa: "TPA",
      minivis: "Minivis",
      ha: "HA",
      hf: "HF",
      nance: "Nance",
      mat_phang_nghieng: "Mặt phẳng nghiêng",
    },
  },
};

const questions4: Questions = {
  do_kho: {
    question: "Phân loại mức độ khó",
    get options() {
      const options = {
        "1": "Mức độ 1",
        "2": "Mức độ 2",
        "3": "Mức độ 3",
        "4": "Mức độ 4",
      };
      return isExtended.value ? { ...options, "5": "Mức độ 5" } : options;
    },
  },
  thoi_gian_dieu_tri: {
    question: "Dự kiến thời gian điều trị niềng răng",
    get options() {
      return {
        "1-1.5": "1 - 1,5 năm",
        "1.5-2": "1,5 - 2 năm",
        "2-2.5": "2 - 2,5 năm",
        "3-3.5": "3 - 3,5 năm",
      };
    },
    get hasAdditionalText() {
      return isExtended.value;
    },
  },
  thoi_gian_duy_tri: {
    question: "Thời gian đeo hàm duy trì sau khi tháo niềng",
    get options() {
      const options = {
        "6": "6 tháng",
        "12": "12 tháng",
        "18": "18 tháng",
        "30": "30 tháng",
        "36": "36 tháng",
        ca_doi: "Suốt đời",
      };
      return isExtended.value ? { ...options, "24": "24 tháng" } : options;
    },
  },
  loai_mac_cai: {
    question: "Loại mắc cài hoặc khay sử dụng để điều trị niềng răng",
    options: {
      mac_cai_kim_loai_mat_ngoai: "Mắc cài kim loại (Mặt ngoài)",
      mac_cai_kim_loai_tu_dong_mat_ngoai: "Mắc cài kim loại tự đóng (Mặt ngoài)",
      mac_cai_su_mat_ngoai: "Mắc cài sứ (Mặt ngoài)",
      mac_cai_su_tu_dong_mat_ngoai: "Mắc cài sứ tự đóng (Mặt ngoài)",
      mac_cai_kim_loai_mat_trong: "Mắc cài kim loại (Mặt trong)",
      nieng_rang_khay_trong_suot_invisalign: "Niềng răng khay trong suốt Invisalign",
      nieng_rang_khay_trong_suot_ecligner: "Niềng răng khay trong suốt Ecligner",
    },
  },
  ham_can_nieng: {
    question: "Loại mắc cài hoặc khay sử dụng để điều trị niềng răng",
    options: {
      tren: "Hàm trên",
      duoi: "Hàm dưới",
      "2_ham": "Cả 2",
    },
  },
  chu_y: {
    question: "Chú ý liên quan",
    options: {
      tut_nuou: "Tụt nướu ở vùng răng chen chúc",
      ve_sinh_rang_mieng: "Vệ sinh răng miệng kỹ để tránh bị viêm nha chu",
      cvr_dinh_ky: "Cạo vôi răng định kỳ 4 - 6 tháng",
      deo_thun_lien_ham: "Đeo thun liên hàm theo hướng dẫn của bác sĩ",
    },
  },
  khong_can_thiep: {
    question: "Các vấn đề không can thiệp được bằng chỉnh nha",
    options: {
      cam_lui_hoac_phan_cam_dua_ra_truoc: "Không can thiệp phần cằm lùi hoặc phần cằm đưa ra trước",
      lech_xuong_lech_mat_lech_cam: "Không can thiệp lệch xương- lệch mặt- lệch cằm",
      phan_lep_xuong_ham_tren: "Không can thiệp phần lép xương hàm trên",
      hoan_toan_phan_cuoi_ho_loi: "Không can thiệp hoàn toàn phần cười hở lợi",
      hoan_toan_phan_ho_mom_do_xuong: "Không can thiệp hoàn toàn phần hô/ móm do xương",
      phau_thuat: "Nếu muốn cải thiện thẩm mỹ tuyệt đối thì can thiệp bằng phẫu thuật",
    },
  },
};

const questionGroups: QuestionGroup[] = [
  { title: "1. Chẩn đoán", questions: questions1 },
  { title: "2. Mục tiêu", questions: questions2 },
  { title: "3. Kế hoạch điều trị cụ thể", questions: questions3 },
  { title: "4. Tổng kết vấn đề", questions: questions4 },
];

const LONG_TEXT_THRESHOLD = 20;

const isLongText = (options: Record<string, any>): boolean => {
  if (!options) return false;
  const values = Object.values(options);
  if (!values.length) return false;

  return values.some((option) => {
    return typeof option === "string" && option.length > LONG_TEXT_THRESHOLD;
  });
};

const enhancedQuestionGroups = computed(() => {
  return questionGroups.map((group) => ({
    ...group,
    questions: Object.entries(group.questions).map(([questionId, question]) => ({
      id: questionId,
      ...question,
      isLongText: question.options ? isLongText(question.options) : false,
    })),
  }));
});

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  { ...questions1, ...questions2, ...questions3, ...questions4 },
  props.personId,
);

const rangeValue = ref([0, 24]); // Giá trị mặc định từ 0 đn 24 tháng
const sliderValue = ref(24); // Giá trị mặc định 24 tháng
const marks = [0, 6, 12, 18, 24, 30, 36, 42, 48];

const checkAll = (questionId: string, options?: Record<string, string>) => {
  for (const optionId in options) {
    answers[questionId][optionId] = true;
  }
  syncData(questionId);
};

const uncheckAll = (questionId: string, options?: Record<string, string>) => {
  for (const optionId in options) {
    answers[questionId][optionId] = false;
  }
  syncData(questionId);
};
</script>

<template>
  <div class="my-5 grid grid-cols-1 overflow-hidden rounded-md">
    <template v-for="(questionGroup, groupIndex) in enhancedQuestionGroups" :key="groupIndex">
      <div class="flex items-center gap-2 bg-gray-100 p-4 font-semibold">
        <Lucide icon="FolderPlus" class="mr-2 inline-block h-5 w-5" />
        {{ questionGroup.title }}
        <DynamicSetting
          v-if="groupIndex === 3"
          :settingName="settingName"
          :settingsSchema="settingsSchema"
          title="Cài đặt hiển thị"
          @popover-close="refreshCurrentSetting"
        />
      </div>
      <div
        v-for="(item, index) in questionGroup.questions"
        :key="`${groupIndex}-${item.id}`"
        class="grid grid-cols-12 gap-2 px-4 py-2 odd:bg-white even:bg-gray-50"
      >
        <div class="col-span-12 flex flex-col justify-center">
          <div class="flex flex-row font-medium">
            <span class="mr-2">{{ index + 1 }}.</span>{{ item.question }}
            <i
              v-if="item.id == 'chu_y' || item.id == 'khong_can_thiep'"
              class="pi pi-check-circle ml-2 cursor-pointer text-green-500 opacity-60 hover:opacity-100"
              @click="checkAll(item.id, item.options)"
            />
            <i
              v-if="item.id == 'chu_y' || item.id == 'khong_can_thiep'"
              class="pi pi-times-circle ml-2 cursor-pointer text-red-500 opacity-60 hover:opacity-100"
              @click="uncheckAll(item.id, item.options)"
            />
          </div>
          <div
            v-if="item.options"
            :class="['mt-2', 'grid', item.isLongText ? 'grid-cols-2' : 'grid-cols-6', 'gap-2']"
          >
            <div
              v-for="(option, optionId) in item.options"
              :key="optionId"
              class="flex items-center"
            >
              <input
                :id="joinRunes(FORM_ID, item.id, optionId)"
                v-model="answers[item.id][optionId]"
                type="checkbox"
                class="h-5 w-5 rounded text-active focus:opacity-90 focus:ring-active"
                @change="syncData(item.id)"
              />
              <label :for="joinRunes(FORM_ID, item.id, optionId)" class="ml-2 text-sm">
                {{ option }}
              </label>
            </div>
            <div v-if="item.hasAdditionalText" class="col-span-2 flex items-center gap-2">
              <span class="text-sm">Khác</span>
              <FormTextarea
                v-model="answers[item.id].additionalText"
                class="mt-2 w-[200px]"
                @input="debouncedSyncData(item.id)"
                @change="syncData(item.id)"
                autoResize
              />
            </div>
          </div>
        </div>
        <div class="col-span-12">
          <template v-if="item.text">
            <FormTextarea
              v-model="answers[item.id].text"
              class="w-full"
              @input="debouncedSyncData(item.id)"
              @change="syncData(item.id)"
              autoResize
            />
          </template>
          <template v-else-if="item.range">
            <div class="w-full">
              <Slider
                v-model="rangeValue"
                :min="0"
                :max="48"
                :step="2"
                range
                class="w-full"
                @change="syncData(item.id)"
              />
              <div class="mt-2 flex justify-between text-sm">
                <span>{{ rangeValue[0] }} tháng</span>
                <span>{{ rangeValue[1] }} tháng</span>
              </div>
            </div>
          </template>
          <template v-else-if="item.slider">
            <div class="w-full">
              <Slider
                v-model="sliderValue"
                :min="0"
                :max="48"
                :step="6"
                class="w-full"
                @change="syncData(item.id)"
              />
              <div class="slider-marks mt-2 flex justify-between text-sm">
                <span v-for="mark in marks" :key="mark">{{ mark }}</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>
