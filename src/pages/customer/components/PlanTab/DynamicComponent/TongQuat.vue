<script setup lang="ts">

import {FormTextarea} from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import useDynamicForm from "@/hooks/useDynamicForm";
import ToothChart from "@/pages/customer/components/MedicalExam/components/ToothChart.vue";
import {QuestionGroup, Questions} from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import {useTeethStore} from "@/pages/customer/components/MedicalExam/Tooth/teethStore";
import {joinRunes} from "@/utils/string";
const teethStore = useTeethStore();
teethStore.setMultiSelectMode(true);

const props = defineProps<{
  personId: number;
}>();

</script>
<template>
  <div class="my-5 grid grid-cols-1 rounded-md">
    <ToothChart :person-id="personId" default-arch-mode="adult" mode="treatment"/>
  </div>
</template>
