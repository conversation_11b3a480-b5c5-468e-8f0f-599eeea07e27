/* eslint-disable no-useless-catch */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { reactive, toRefs } from "vue";

import { noteAdd, noteDelete, noteList, noteUpdate } from "@/api/bcare";
import {
  NoteAddRequest,
  NoteDeleteRequest,
  NoteListRequest,
  NoteResponse,
  NoteUpdateRequest,
} from "@/api/bcare-types";
import { useNotiStore } from "@/stores/notification";

export default function useNote() {
  const notiStore = useNotiStore();
  const state = reactive<{
    notes?: NoteResponse[];
    infoPage: any;
  }>({
    infoPage: {},
    notes: [] as NoteResponse[],
  });
  const fetchNoteList = async (request: NoteListRequest) => {
    const response = await noteList(request);
    if (response.code === 0) {
      state.notes = response.data?.notes;
      state.infoPage.total_item = response.data?.total;
      state.infoPage.total_page = response.data?.total_page;
      return response.code;
    }
    return response.code;
  };
  const onAddNote = async (request: NoteAddRequest) => {
    try {
      const response = await noteAdd(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Thành công",
          message: "Thêm mới ghi chú",
        });
        return response.code;
      }
      return response.code;
    } catch (error) {
      throw error;
    }
  };
  const onUpdateNote = async (request: NoteUpdateRequest) => {
    try {
      const response = await noteUpdate(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Thành công",
          message: "Chỉnh sửa ghi chú",
        });
        return response.code;
      }
      return response.code;
    } catch (error) {
      return [];
    }
  };
  const onDeleteNote = async (request: NoteDeleteRequest) => {
    try {
      const response = await noteDelete(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Thành công",
          message: "Xóa ghi chú",
        });
        return true;
      }
    } catch (error) {
      throw error;
    }
  };
  return {
    ...toRefs(state),
    fetchNoteList,
    onAddNote,
    onUpdateNote,
    onDeleteNote,
  };
}
