import { reactive, toRefs } from "vue";

import {
  Call,
  CallListRequest,
  HistoryRequest,
  ListRequest,
  ListResponse,
  MessageHistory,
  NoteListRequest,
  Note<PERSON><PERSON><PERSON>nese,
  TaskListRequest,
  TaskResponse,
} from "@/api/bcare-types-v2";
import { activityList, callList, noteList, personHistoryMessage, taskList } from "@/api/bcare-v2";

export default function useAllActivity() {
  const state = reactive<{
    notes: NoteResponese[];
    tasks: TaskResponse[];
    messages: MessageHistory[];
    calls: Call[];
    activities: ListResponse["activities"];
  }>({
    notes: [],
    calls: [],
    messages: [],
    tasks: [],
    activities: [],
  });
  const fetchNoteList = async (request: NoteListRequest) => {
    const response = await noteList(request);
    if (response.code === 0) {
      state.notes = response.data?.notes ?? [];
      return response.code;
    }
    return response.code;
  };
  const fetchTaskList = async (request: TaskListRequest) => {
    const response = await taskList(request);
    if (response.code === 0) {
      state.tasks = response.data?.tasks ?? [];
      return response.code;
    }
    return response.code;
  };
  const fetchMessageList = async (request: HistoryRequest) => {
    const response = await personHistoryMessage(request);
    if (response.code === 0) {
      state.messages = response.data?.message_histories ?? [];
      return response.code;
    }
    return response.code;
  };
  const fetchCallList = async (request: CallListRequest) => {
    const response = await callList(request);
    if (response.code === 0) {
      state.calls = response.data?.calls ?? [];
      return response.code;
    }
    return response.code;
  };

  const fetchActivityList = async (request: ListRequest) => {
    const response = await activityList(request);
    if (response.code === 0) {
      state.activities = response.data?.activities ?? [];
      return response.code;
    }
    return response.code;
  };

  return {
    ...toRefs(state),
    fetchNoteList,
    fetchTaskList,
    fetchMessageList,
    fetchCallList,
    fetchActivityList,
  };
}
