<script setup lang="ts">
import Chip from "primevue/chip";
import { onMounted, ref } from "vue";

import Lucide from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy/Tippy.vue";
import useToggle from "@/hooks/useToggle";
import useMessage from "@/hooks/useMessage";
import { highlightText } from "@/utils/helper";

import ActivityHeader from "./ActivityHeader.vue";
import NoteItem from "./NoteItem.vue";
import MessageModal from "@/components/Messages/MessageModal.vue";

import { MessageHistory } from "@/api/bcare-types-v2";
import Empty from "@/base-components/Empty";

const { fetchMessageList, messages } = useMessage();
const modalSmsToggle = useToggle(false);
const reversedMessages = ref<MessageHistory[]>([]);
const props = defineProps<{
  personId?: number;
}>();

const handleSendSms = () => {
  modalSmsToggle.show();
};

const getStatusChip = (status: string) => {
  return {
    icon: status === "sent" ? "Check" : "X",
    class: status === "sent" ? "bg-success" : "bg-danger",
    text: status === "sent" ? "Đã gửi" : "Chưa gửi",
  } as const;
};

onMounted(async () => {
  if (props.personId) {
    await fetchMessageList({ person_id: props.personId });
    if (messages && messages.value) reversedMessages.value = [...messages.value].reverse();
  }
});
</script>

<template>
  <div class="mt-4">
    <ActivityHeader title="" button-label="Gửi SMS/ZNS" @button-click="handleSendSms" />
    <div class="mt-5">
      <NoteItem
        v-for="message in reversedMessages"
        v-if="messages && messages.length > 0"
        :key="message.id"
        :title="message.type"
        :notes="highlightText(message.content)"
        :created-at="message.created_at"
        :type="message.message_status === 'sent' ? 1 : 2"
      >
        <template #addon-title>
          <span class="ml-1 font-normal">
            đến
            <Tippy
              class="ml-1 underline decoration-dotted transition hover:text-primary"
              variant="primary"
              content="Số chính"
            >
              {{ message.phone }}
            </Tippy>
          </span>
        </template>
        <template #addon-information>
          <Chip
            :class="[
              'flex items-center rounded-full px-2 py-1 text-xs text-white',
              getStatusChip(message.message_status).class,
            ]"
          >
            <Lucide :icon="getStatusChip(message.message_status).icon" class="mr-1 h-3 w-3" />
            {{ getStatusChip(message.message_status).text }}
          </Chip>
        </template>
      </NoteItem>
      <div v-else><Empty /></div>
    </div>
  </div>
  <MessageModal
    :person-id="props.personId ?? 0"
    :is-open="modalSmsToggle.isVisible.value"
    @on-close="modalSmsToggle.hide"
  />
</template>
