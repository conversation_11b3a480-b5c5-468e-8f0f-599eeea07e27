<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import Checkbox from "primevue/checkbox";
import Chip from "primevue/chip";
import { computed, ref } from "vue";

import { TaskPriorityEnum, TaskStateEnum } from "@/api/bcare-enum";
import { TaskResponse } from "@/api/bcare-types-v2";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide/Lucide.vue";
import { StateSelectBtn } from "@/components/Select";
import { UserAvatar } from "@/components/User";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import { RichTextNote } from "@/components/WysiwgEditor";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import useUser from "@/hooks/useUser";
import { useTaskStore } from "@/stores/task-store-v2";
import { calculateLateDays } from "@/utils/time-helper";

const props = withDefaults(
  defineProps<{
    task: TaskResponse | any;
    showActions?: boolean;
    showCheckbox?: boolean;
  }>(),
  {
    showActions: true,
    showCheckbox: true,
  },
);

const emit = defineEmits<{
  (e: "updateStatus", id: number): void;
  (e: "edit", task: TaskResponse): void;
  (e: "delete", id: number): void;
}>();

const { getUserById } = useUser();

const taskStore = useTaskStore();

const formattedDates = computed(() => ({
  start: useDateFormat(props.task.start_date, "HH:mm DD/MM/YYYY").value,
  due: useDateFormat(props.task.due_date, "HH:mm DD/MM/YYYY").value,
}));

const overdueDays = computed(() =>
  calculateLateDays(
    props.task.due_date,
    props.task.state as TaskStateEnum,
    props.task.completed_at,
  ),
);
const isCompleted = computed(() =>
  [TaskStateEnum.COMPLETED, TaskStateEnum.COMPLETED_EARLY].includes(props.task.state),
);
const creator = computed(() => getUserById(props.task.creator_id));

const priorityClass = computed(() => {
  const priority = Number(props.task.priority);
  switch (priority) {
    case TaskPriorityEnum.HIGH:
      return "text-red-500";
    case TaskPriorityEnum.MEDIUM:
      return "text-yellow-500";
    case TaskPriorityEnum.LOW:
      return "text-blue-500";
    default:
      return "text-gray-500";
  }
});

const { confirm } = useConfirmTippy();

const canDeleteTask = computed(() => {
  return taskStore.canUserDeleteTask(props.task);
});

const handleUpdateStatus = async (id: number, event: MouseEvent) => {
  if (isCompleted.value) return;

  confirm(event, {
    title: "Bạn có chắc chắn muốn đánh dấu công việc này là hoàn thành?",
    icon: "pi pi-check-circle",
    acceptLabel: "Đồng ý",
    rejectLabel: "Hủy",
    onAccept: () => {
      emit("updateStatus", id);
    },
  });
};

const handleDelete = async (id: number, event: MouseEvent) => {
  if (!canDeleteTask.value) return;

  confirm(event, {
    title: "Bạn có chắc chắn muốn xóa công việc này?",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: () => {
      emit("delete", id);
    },
  });
};

const handleItemClick = () => {
  if (props.showActions) {
    emit("edit", props.task);
  }
};

const primaryAssignees = computed(() =>
  props.task.assignments
    ?.filter((assignee: any) => assignee.role === "primary")
    .map((assignee: any) => assignee.user),
);
</script>

<template>
  <div
    class="box mb-2 mt-2 cursor-pointer rounded-lg bg-white p-3 shadow-sm transition-all duration-300 ease-in-out first:mt-0 last:mb-0 hover:bg-slate-100"
    @click="handleItemClick"
  >
    <div class="flex justify-between gap-4">
      <div class="flex items-center space-x-4">
        <div
          v-if="props.showCheckbox"
          @click.stop="(e) => handleUpdateStatus(props.task.id, e as MouseEvent)"
        >
          <Checkbox
            v-model="isCompleted"
            binary
            :disabled="isCompleted"
            aria-label="Task completion status"
          />
        </div>

        <UserAvatarGroup
          v-if="props.task.assignments?.length > 0"
          :users="primaryAssignees"
          :max-display="3"
        />

        <div>
          <div class="flex items-center space-x-2">
            <h3 :class="['text-base font-medium', { 'text-gray-500 line-through': isCompleted }]">
              {{ props.task.title }}
            </h3>
            <i
              v-if="props.task.task_recurring_id"
              v-tooltip.top="
                props.task.serial > 1
                  ? `Công việc lặp lại lần thứ ${props.task.serial}`
                  : 'Công việc lặp lại'
              "
              class="pi pi-refresh text-blue-500"
            />
            <RichTextNote
              :content="props.task.note"
              :wrapper-mode="true"
              :medium="true"
              class="max-w-[70%]"
            />
          </div>
          <p class="flex items-center space-x-1 text-sm text-gray-600">
            <span>Được giao bởi</span>
            <UserAvatar :user="creator" size="small" />
            <span class="font-medium"> {{ creator?.name }} </span>
          </p>
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <div class="flex flex-col items-end">
          <div class="flex items-center space-x-2">
            <i :class="['pi', 'pi-flag-fill', 'text-xs', priorityClass]" />
            <span class="text-sm text-gray-600">
              {{ formattedDates.start }} - {{ formattedDates.due }}
            </span>
          </div>
          <div class="mt-1 flex flex-wrap items-center gap-2">
            <StateSelectBtn :state="props.task.state" size="small" :show-dot="false" />
            <Chip
              v-if="overdueDays"
              :label="`Chậm ${overdueDays} ngày`"
              class="bg-red-600 px-2 py-0.5 text-xs text-white"
            />
          </div>
        </div>
        <div v-if="props.showActions" class="flex space-x-2">
          <Menu class="font-medium" v-if="canDeleteTask">
            <Menu.Button
              class="flex items-center border-none p-0 text-primary shadow-none focus:ring-0"
              @click.stop
            >
              <Lucide icon="MoreVertical" class="h-4 w-4 text-primary" />
            </Menu.Button>
            <Menu.Items class="z-20 w-40">
              <div>
                <Menu.Item
                  class="text-danger hover:bg-danger/10"
                  @click.stop="(e) => handleDelete(props.task.id, e)"
                >
                  <Lucide icon="Trash" class="mr-2 h-4 w-4" />
                  Xóa
                </Menu.Item>
              </div>
            </Menu.Items>
          </Menu>
        </div>
      </div>
    </div>
  </div>
</template>
