<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { computed } from "vue";

import { UserShort } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide/Lucide.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import RichTextNote from "@/components/WysiwgEditor/RichTextNote.vue";
import useUser from "@/hooks/useUser";

const props = defineProps<{
  users?: UserShort[];
  userId?: number;
  isAvatar?: boolean;
  title?: string | number;
  notes: string;
  createdAt: string;
  type?: number | string;
  sentStatus?: string;
}>();

const emit = defineEmits<{
  (e: "edit"): void;
  (e: "delete"): void;
}>();

const { getUserById } = useUser();
const user = computed(() => (props.userId ? getUserById(props.userId) : undefined));
const title = computed(() => (props.userId ? user.value?.name || "Unknown" : props.title || ""));

const formattedDate = computed(() => useDateFormat(props.createdAt, "HH:mm - DD/MM/YYYY").value);
</script>

<template>
  <div
    class="box mb-2 cursor-pointer rounded-lg bg-white p-3 shadow-sm transition-all duration-300 ease-in-out first:mt-0 last:mb-0 hover:bg-slate-100"
    @click="emit('edit')"
  >
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <UserAvatar v-if="isAvatar" :user="user" class="flex-shrink-0" />
        <div>
          <div class="flex items-center space-x-2">
            <h3 class="text-base font-medium">{{ title }}</h3>
            <slot name="addon-title"></slot>
          </div>
          <RichTextNote :content="notes" wrapper-mode :max-height="600" class="mt-1" />
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <div class="flex flex-col items-end">
          <div class="flex items-center space-x-2">
            <Lucide icon="Calendar" class="h-4 w-4 text-green-500" />
            <span class="text-sm text-gray-600">
              {{ formattedDate }}
            </span>
          </div>
          <span class="mt-1">
            <slot name="addon-information"></slot>
          </span>
        </div>
        <slot name="menu" class="mt-1 flex flex-wrap items-center gap-2">
          <!-- Default empty slot for menu icon -->
        </slot>
      </div>
    </div>
  </div>
</template>
