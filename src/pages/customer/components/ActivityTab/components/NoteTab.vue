<script lang="ts" setup>
import <PERSON><PERSON> from "js-cookie";
import <PERSON><PERSON> from "primevue/button";
import Select from "primevue/select";
import { computed, nextTick, onMounted, reactive, ref, watch } from "vue";

import { NoteListRequest, NoteResponese, NoteUpdateRequest, User } from "@/api/bcare-types-v2";
import { FormCheck } from "@/base-components/Form";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy/Tippy.vue";
import { TimelineModal } from "@/components/Timeline";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { COOKIE_ENUM } from "@/enums/cookie-enum";
import useConstant from "@/hooks/useConstant";
import useDepartment from "@/hooks/useDepartment";
import { useNote } from "@/hooks/useNote";
import useToggle from "@/hooks/useToggle";
import {
  NOTE_COMPLAIN,
  noteData,
  notePayload,
} from "@/pages/customer/components/ActivityTab/constants";
import { toObject } from "@/utils/helper";

import ActivityHeader from "./ActivityHeader.vue";
import NoteItem from "./NoteItem.vue";
import Empty from "@/base-components/Empty";

// Import Tiptap directly instead of using defineAsyncComponent
import Tiptap from "@/components/WysiwgEditor/Tiptap.vue";

// Sử dụng hook useConstant
const { getConstants } = useConstant();

const { notes, totalItems, isLoading, fetchNoteList, createNote, updateNote, deleteNote } =
  useNote();
const { departmentOptions } = useDepartment();

const userItem = ref<User>();
const perPage = ref<number>(10);
const modalDelete = useToggle(false);
const noteId = ref(0);
const props = defineProps<{
  personId: number;
}>();
const formData = reactive<NoteUpdateRequest>({
  ...noteData,
  person_id: props.personId,
});
const filterParams = reactive<NoteListRequest>({
  ...notePayload,
  page: 1,
  page_size: +perPage.value,
  filter: { ...notePayload.filter, person_id: props.personId },
});
const loadNote = async () => {
  if (props?.personId) await fetchNoteList(filterParams);
};
const editingNoteId = ref<number | null>(null);
const handleUpdateNote = async (item: NoteResponese) => {
  editingNoteId.value = item.id;
  Object.assign(formData, item);
  await nextTick();
};
const { confirm } = useConfirmTippy();
const handleDeleteNote = async (note: NoteResponese, e?: MouseEvent) => {
  confirm(e, {
    title: "Bạn có muốn xóa ghi chú này?",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      const success = await deleteNote({ id: note.id });
      if (success) {
        await loadNote();
      }
    },
  });
};
const onSubmitNote = async () => {
  const { id } = formData;
  if (!formData.body) return;

  let success;
  const currentType = formData.type || 0;
  if (id) {
    success = await updateNote(formData);
  } else {
    success = await createNote({
      body: formData.body || "",
      type: currentType,
      person_id: formData.person_id || 0,
    });
  }

  if (success) {
    await loadNote();
    editingNoteId.value = null;
    Object.assign(formData, {
      ...noteData,
      person_id: props.personId,
      type: currentType,
    });
  }
};
const handlePageChange = (event: { first: number; rows: number; page: number }) => {
  filterParams.page = event.page + 1;
  perPage.value = event.rows;
  filterParams.page_size = event.rows;
  fetchNoteList(filterParams);
};

watch(filterParams, () => {
  fetchNoteList(filterParams);
});

watch([() => filterParams.department_id, () => filterParams.filter?.type], () => {
  filterParams.page = 1;
});

onMounted(async () => {
  const getUser = Cookie.get(COOKIE_ENUM.USER);
  if (getUser) {
    userItem.value = toObject(getUser);
  }
  await loadNote();
});

const noteTypeOptions = computed(() => [
  { value: "0", label: "Tất cả" },
  ...Object.entries(getConstants.value?.note_type || {}).map(([key, value]) => ({
    value: key,
    label: value as string,
  })),
]);

// Thêm biến để theo dõi giá trị filter
const selectedFilter = ref("0");

// Cập nhật hàm handleNoteTypeChange
const handleNoteTypeChange = (value: string) => {
  if (!filterParams.filter) filterParams.filter = {};
  filterParams.filter.type = Number(value);
  // Đồng bộ với radio buttons
  formData.type = Number(value);
  selectedFilter.value = value;
};

// Thêm hàm handleCancelEdit
const handleCancelEdit = () => {
  editingNoteId.value = null;
  Object.assign(formData, { ...noteData, person_id: props.personId });
};

// Thêm các biến để quản lý modal timeline
const modalHistoryToggle = useToggle(false);
const selectedNote = ref<NoteResponese>();

// Thêm hàm xử lý khi click vào nút xem lịch sử
const handleViewHistory = (note: NoteResponese) => {
  selectedNote.value = note;
  modalHistoryToggle.show();
};

const noteBody = computed({
  get: () => formData.body || "",
  set: (value: string) => (formData.body = value),
});

const canEditNote = (note: NoteResponese) => {
  const createdTime = new Date(note.created_at).getTime();
  const currentTime = new Date().getTime();
  const oneHourInMs = 60 * 60 * 1000; // 1 giờ tính bằng milliseconds
  return currentTime - createdTime <= oneHourInMs;
};
</script>
<template>
  <div class="mt-4">
    <form action="" @submit.prevent="onSubmitNote">
      <Tiptap v-model="noteBody" key="note-editor" />

      <div class="mt-2 flex flex-col items-center justify-between sm:flex-row">
        <div class="flex flex-col sm:flex-row">
          <FormCheck v-for="(radio, index) in noteTypeOptions" :key="index" class="mr-2">
            <FormCheck.Label
              :for="'radio-switch-' + index"
              class="ml-0 flex items-center rounded-md border p-2"
            >
              <FormCheck.Input
                :id="'radio-switch-' + index"
                v-model="selectedFilter"
                :value="radio.value"
                class="mr-2"
                type="radio"
                @change="handleNoteTypeChange(String(index))"
              />
              {{ radio.label }}
            </FormCheck.Label>
          </FormCheck>
        </div>
        <div class="flex items-center">
          <Button
            v-if="editingNoteId"
            class="mr-2 px-4 py-2 shadow-md"
            outlined
            severity="danger"
            @click="handleCancelEdit"
          >
            <i class="pi pi-times"></i>
            Hủy
          </Button>
          <Button class="ml-auto px-4 py-2 shadow-md" severity="primary" @click="onSubmitNote">
            <i class="pi pi-save"></i>
            Lưu
          </Button>
        </div>
      </div>
    </form>

    <div class="mt-4">
      <ActivityHeader
        :options="noteTypeOptions"
        v-model="selectedFilter"
        title=""
        @filter-change="handleNoteTypeChange"
      >
        <template #extra-filter>
          <Select
            v-model="filterParams.department_id"
            :options="departmentOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Phòng ban"
            filter
            fluid
            showClear
          />
        </template>
      </ActivityHeader>
      <div v-if="isLoading" class="p-5 text-center">
        <i class="pi pi-spin pi-spinner text-2xl"></i>
      </div>
      <NoteItem
        v-for="note in notes"
        v-if="notes && notes.length > 0"
        :key="note.id"
        :class="[
          'border-l-4 transition-colors duration-200',
          editingNoteId === note.id ? 'border-l-primary' : '',
        ]"
        :created-at="note.created_at"
        :notes="note.body"
        :type="note.type"
        :user-id="note.user_id"
        is-avatar
        @delete="
          () => {
            noteId = note.id;
            modalDelete.show();
          }
        "
      >
        <template #addon-title>
          <span
            ><Tippy
              class="h-1.5 rounded-full bg-blue-400 px-2 py-0.5 text-xs text-white"
              content="Tên deal"
            >
              <span>#{{ note.id }}</span>
            </Tippy></span
          >
        </template>
        <template #addon-information>
          <span
            v-if="note.type !== 0"
            :class="[
              'mt-1 rounded-full px-2 py-0.5 text-xs text-white',
              { 'bg-danger': note.type === NOTE_COMPLAIN },
              { 'bg-success': note.type !== NOTE_COMPLAIN },
            ]"
          >
            {{ getConstants?.note_type[note.type] }}
          </span>
        </template>
        <template #menu>
          <Menu class="font-medium">
            <Menu.Button
              class="flex items-center border-none p-0 text-primary shadow-none focus:ring-0"
            >
              <Lucide class="ml-2 h-4 w-4 text-primary" icon="MoreVertical" />
            </Menu.Button>
            <Menu.Items class="z-20 w-40">
              <Menu.Item v-if="canEditNote(note)" @click.stop="handleUpdateNote(note)">
                <Lucide class="mr-2 h-4 w-4" icon="Edit" />
                <span class="truncate">Chỉnh sửa</span>
              </Menu.Item>
              <Menu.Item @click.stop="handleViewHistory(note)">
                <Lucide class="mr-2 h-4 w-4" icon="Activity" />
                <span class="truncate">Lịch sử</span>
              </Menu.Item>
              <Menu.Item
                v-if="note.creator.id === userItem?.id"
                class="text-danger hover:bg-danger hover:text-white"
                @click.stop="(e) => handleDeleteNote(note, e)"
              >
                <Lucide class="mr-2 h-4 w-4" icon="Delete" />
                <span class="truncate">Xóa</span>
              </Menu.Item>
            </Menu.Items>
          </Menu>
        </template>
      </NoteItem>
      <div v-else-if="!isLoading"><Empty /></div>
      <Paginator
        v-if="notes.length > 0 && totalItems > 0"
        :rows="perPage"
        :totalRecords="totalItems"
        @page="handlePageChange"
        template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
        :rowsPerPageOptions="[10, 20, 50]"
        class="mt-4"
      />
    </div>
  </div>

  <TimelineModal
    :model-value="modalHistoryToggle.isVisible.value"
    :history="selectedNote?.history ?? []"
    :creator-id="selectedNote?.creator?.id"
    :created-at="selectedNote?.created_at"
    @update:model-value="modalHistoryToggle.setValue"
  />
</template>
