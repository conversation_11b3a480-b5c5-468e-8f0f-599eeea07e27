<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import { NoteType } from "@/api/bcare-enum";
import { Call } from "@/api/bcare-types-v2";
import Lucide, { Icon } from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy";
import { useActivityQuery } from "@/hooks/useActivity";
import { highlightText } from "@/utils/helper";

import ActivityHeader from "./ActivityHeader.vue";
import CallItem from "./CallItem.vue";
import NoteItem from "./NoteItem.vue";
import TaskItem from "./TaskItem.vue";
import Empty from "@/base-components/Empty";

// Types
type ActivityItem = {
  created_at: string;
  data: {
    body?: string;
    creator?: {
      id: number;
      name: string;
      avatar?: string;
    };
    type?: number | string;
    title?: string;
    status?: number;
    priority?: number;
    start_date?: string;
    due_date?: string;
    end_date?: string;
    note?: string;
    assignment?: Array<{
      user: {
        id: number;
        name: string;
        avatar?: string;
      };
    }>;
    version?: number;
    user_id?: number;
    phone?: string;
    message_status?: string;
    content?: string;
  } & Call;
  entity_id: number;
  kind: "note" | "task" | "message_history" | "call";
  person_id: number;
  record: any;
};

// Props
const props = defineProps<{
  personId?: number;
}>();

// Composables
const { activities, total, dynamicQuery, fetchActivities } = useActivityQuery();

// Refs
const currentPage = ref(1);
const rows = ref(10);
const getStatusChip = (status?: string) => {
  if (!status) return { icon: "XCircle", class: "bg-danger", text: "Chưa gửi" };
  return {
    icon: status === "sent" ? "Check" : "XCircle", // Changed to valid Lucide icon names
    class: status === "sent" ? "bg-success" : "bg-danger",
    text: status === "sent" ? "Đã gửi" : "Chưa gửi",
  };
};

// Computed
const paginatedData = computed(() => activities.value as unknown as ActivityItem[]);

const filterOptions = [{ label: "Tất cả", value: 0 }];

// Methods
const handlePageChange = (event: { page: number; rows: number }) => {
  currentPage.value = event.page + 1;
  rows.value = event.rows;

  dynamicQuery.limit = event.rows;
  dynamicQuery.offset = event.page * event.rows;
  fetchActivities(dynamicQuery);
};

const handleUpdateTaskStatus = (task: any) => {
  // Implement if needed
  console.log("Update task status:", task);
};

// Lifecycle
onMounted(() => {
  if (props.personId) {
    dynamicQuery.filters = [
      { field: "person_id", operator: "EQ", value: props.personId.toString() },
    ];
    fetchActivities(dynamicQuery);
  }
});
</script>

<template>
  <div class="mt-4">
    <div v-if="paginatedData.length === 0">
      <Empty />
    </div>
    <div v-else class="space-y-2">
      <template v-for="item in paginatedData" :key="item.entity_id">
        <TaskItem
          v-if="item.kind === 'task'"
          :task="item.data"
          :show-actions="false"
          :show-checkbox="false"
          @update-status="handleUpdateTaskStatus(item.data)"
        />

        <NoteItem
          v-if="item.kind === 'note'"
          :user-id="item.data.user_id"
          is-avatar
          :notes="item.data.body ?? ''"
          :created-at="item.created_at"
          :type="item.data.type ?? NoteType.OTHER"
        >
          <template #addon-title>
            <span>
              <Tippy
                content="Tên deal"
                class="h-1.5 rounded-full bg-blue-400 px-2 py-0.5 text-xs text-white"
              >
                <span>#{{ item.entity_id }}</span>
              </Tippy>
            </span>
          </template>
          <template #addon-information>
            <span
              v-if="item.data.type"
              :class="[
                'mt-1 rounded-full px-2 py-0.5 text-xs text-white',
                { 'bg-danger': item.data.type === NoteType.COMPLAIN },
                { 'bg-success': item.data.type !== NoteType.COMPLAIN },
              ]"
            >
              {{ item.data.type === NoteType.COMPLAIN ? "Phàn nàn" : "Khác" }}
            </span>
          </template>
        </NoteItem>

        <NoteItem
          v-if="item.kind === 'message_history'"
          :title="item.data.type"
          :notes="highlightText(item.data.content ?? '')"
          :created-at="item.created_at"
          :sent-status="item.data.message_status"
        >
          <template #addon-title>
            <span class="ml-1 font-normal">
              đến
              <Tippy
                class="ml-1 underline decoration-dotted transition hover:text-primary"
                variant="primary"
                content="Số chính"
              >
                {{ item.data.phone }}
              </Tippy>
            </span>
          </template>
          <template #addon-information>
            <Chip
              :class="[
                'flex items-center rounded-full px-2 py-1 text-xs text-white',
                getStatusChip(item.data.message_status).class,
              ]"
            >
              <Lucide
                :icon="getStatusChip(item.data.message_status).icon as Icon"
                class="h-3 w-3"
              />
              {{ getStatusChip(item.data.message_status).text }}
            </Chip>
          </template>
        </NoteItem>

        <CallItem
          v-if="item.kind === 'call'"
          :call="item.data"
          class="!pointer-events-none"
          is-view-mode
        />
      </template>
    </div>
  </div>

  <Paginator
    v-if="paginatedData.length > 0"
    :rows="rows"
    :totalRecords="total"
    :first="(currentPage - 1) * rows"
    @page="handlePageChange"
    template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
  />
</template>
