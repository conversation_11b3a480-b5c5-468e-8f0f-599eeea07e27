<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import * as lucideIcons from "lucide-vue-next";

import { Call } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { NoteInfo } from "@/components/InfoText";
import { UserAvatar } from "@/components/User";
import { formatTimer } from "@/utils/helper";
import { computed } from "vue";

const props = defineProps<{
  call: Call;
  isViewMode?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:feedback", call: Call, newFeedback: string): void;
  (e: "update:rating", call: Call, newRating: number): void;
}>();

const formattedDate = computed(
  () => useDateFormat(props.call.start_time, "HH:mm - DD/MM/YYYY").value,
);

const handleUpdateFeedback = (newFeedback: string) => {
  emit("update:feedback", props.call, newFeedback);
};

const handleUpdateRating = (newRating: number) => {
  emit("update:rating", props.call, newRating);
};

const CALL_STATES = {
  INBOUND_ANSWERED: "inbound_answered",
  INBOUND_MISSED: "inbound_missed",
  OUTBOUND_ANSWERED: "outbound_answered",
  OUTBOUND_MISSED: "outbound_missed",
  UNDEFINED: "undefined",
} as const;

const getStateConfig = (direction: string, state: string) => {
  const isInbound = direction === "inbound";
  const callLabel = isInbound ? "Cuộc gọi đến" : "Cuộc gọi đi";

  const configs: Record<
    (typeof CALL_STATES)[keyof typeof CALL_STATES],
    {
      icon: keyof typeof lucideIcons;
      color: string;
      label: string;
      subLabel: string;
    }
  > = {
    [CALL_STATES.INBOUND_ANSWERED]: {
      icon: "PhoneIncoming",
      color: "text-success",
      label: callLabel,
      subLabel: "Đã trả lời",
    },
    [CALL_STATES.INBOUND_MISSED]: {
      icon: "PhoneMissed",
      color: "text-danger",
      label: callLabel,
      subLabel: "Nhỡ",
    },
    [CALL_STATES.OUTBOUND_ANSWERED]: {
      icon: "PhoneOutgoing",
      color: "text-info",
      label: callLabel,
      subLabel: "Đã kết nối",
    },
    [CALL_STATES.OUTBOUND_MISSED]: {
      icon: "PhoneMissed",
      color: "text-danger",
      label: callLabel,
      subLabel: "Không trả lời",
    },
    [CALL_STATES.UNDEFINED]: {
      icon: isInbound ? "PhoneIncoming" : "PhoneOutgoing",
      color: isInbound ? "text-success" : "text-info",
      label: callLabel,
      subLabel: "Chưa xác định",
    },
  };

  return configs[state as keyof typeof configs] || configs[CALL_STATES.UNDEFINED];
};
</script>

<template>
  <div class="box rounded-lg bg-white p-3 shadow-sm">
    <!-- Top Section: All primary info and actions -->
    <div class="flex items-center justify-between gap-x-6">
      <!-- Top-Left Area: Avatar, Status, Line/Duration -->
      <div class="flex items-center space-x-3 space-y-2">
        <UserAvatar :user-id="call.user_id" class="mt-1 flex-shrink-0" />
        <div class="flex-grow">
          <!-- Status -->
          <div class="flex items-center space-x-2">
            <Lucide
              :icon="getStateConfig(call.direction, call.state).icon"
              :class="['h-4 w-4 flex-shrink-0', getStateConfig(call.direction, call.state).color]"
            />
            <h3 class="text-base font-medium">
              {{ getStateConfig(call.direction, call.state).label }} -
              {{ getStateConfig(call.direction, call.state).subLabel }}
            </h3>
          </div>
          <!-- Line Info & Duration -->
          <div class="mt-1 flex flex-wrap items-center space-x-2 text-sm text-slate-500">
            <div class="flex items-center">
              <i class="pi pi-clock mr-1 flex-shrink-0 text-sm" />
              <span>{{ formatTimer(+call.duration) }}</span>
            </div>
            <span class="mx-1.5 text-slate-400">·</span>
            <span>Line: {{ call.direction === "inbound" ? call.destination : call.source }}</span>
          </div>
        </div>
      </div>

      <div class="flex flex-shrink-0 items-center justify-between space-x-3">
        <NoteInfo
          v-if="!props.isViewMode"
          :notes="call.feedback"
          mode="tag"
          @update:notes="handleUpdateFeedback"
          :editable="true"
          class="w-full"
        />

        <div v-if="!props.isViewMode" class="min-w-[250px]">
          <div
            v-if="!call.recording_file"
            class="flex items-center justify-start gap-2 text-sm text-slate-500"
          >
            <Lucide icon="MicOff" class="h-5 w-5" />
            <span>Không có bản ghi âm</span>
          </div>
          <div v-else>
            <audio controls :src="call.recording_file" class="h-8 w-full" />
          </div>
        </div>

        <div class="flex min-w-[170px] flex-col items-end space-y-2">
          <div class="flex items-center justify-end space-x-1 text-sm">
            <Lucide icon="Calendar" class="h-4 w-4 flex-shrink-0 text-green-500" />
            <span class="text-gray-600">{{ formattedDate }}</span>
          </div>

          <div class="flex flex-wrap gap-0.5">
            <Lucide
              v-for="star in [1, 2, 3, 4, 5]"
              :key="star"
              icon="Star"
              :class="[
                'h-5 w-5 flex-shrink-0 cursor-pointer',
                { 'text-warning': star <= call.rating },
              ]"
              @click="handleUpdateRating(star)"
              v-if="!props.isViewMode"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
