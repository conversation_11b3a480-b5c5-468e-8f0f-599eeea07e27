<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import { Call } from "@/api/bcare-types-v2";
import { useCall } from "@/hooks/useCall";

import ActivityHeader from "./ActivityHeader.vue";
import CallItem from "./CallItem.vue";
import Empty from "@/base-components/Empty";

const { fetchCallList, isLoading, updateCallFeedback } = useCall();

const calls = ref<Call[]>([]);
const first = ref(0);
const totalRecords = ref(0);

const props = defineProps<{
  personId: number;
}>();

// Computed for call direction options
const filterOptions = computed(() => [
  { label: "Tất cả", value: "all" },
  { label: "Cuộc gọi đến", value: "inbound" },
  { label: "Cuộc gọi đi", value: "outbound" },
  { label: "Cuộc gọi nhỡ", value: "missed" },
]);

const currentFilter = ref("all");

const fetchData = async () => {
  const response = await fetchCallList({
    page: 1,
    page_size: 100,
    filter: {
      person_id: props.personId,
      direction: currentFilter.value === "all" ? "" : currentFilter.value,
      call_status: currentFilter.value === "missed" ? "missed" : "",
      status: 0,
      user_id: 0,
    },
    order_by: "start_time DESC",
  });

  if (response) {
    calls.value = response.calls;
    totalRecords.value = response.total;
  }
};

const handleFilterChange = async (value: string) => {
  currentFilter.value = value;
  first.value = 0;
  await fetchData();
};

const handleUpdateFeedback = async (call: Call, newFeedback: string) => {
  try {
    await updateCallFeedback({ id: call.id, feedback: newFeedback });
    const index = calls.value.findIndex((c) => c.id === call.id);
    if (index !== -1) {
      calls.value[index] = { ...calls.value[index], feedback: newFeedback };
    }
  } catch (error) {}
};
const handleUpdateRating = async (call: Call, newRating: number) => {
  try {
    await updateCallFeedback({ id: call.id, rating: newRating });
    const index = calls.value.findIndex((c) => c.id === call.id);
    if (index !== -1) {
      calls.value[index] = { ...calls.value[index], rating: newRating };
    }
  } catch (error) {}
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="mt-4">
    <ActivityHeader title="" :options="filterOptions" @filter-change="handleFilterChange" />

    <DataView
      data-key="id"
      :value="calls"
      :rows="10"
      :first="first"
      :totalRecords="totalRecords"
      :paginator="totalRecords > 0"
      :loading="isLoading"
      paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
      showEmptyView
    >
      <template #list="slotProps">
        <div class="flex flex-col space-y-2">
          <div v-for="item in slotProps.items" :key="item.id">
            <CallItem
              :call="item"
              @update:feedback="handleUpdateFeedback"
              @update:rating="handleUpdateRating"
            />
          </div>
        </div>
      </template>

      <template #empty>
        <Empty />
      </template>
    </DataView>
  </div>
</template>
