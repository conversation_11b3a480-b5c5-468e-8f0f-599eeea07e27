<script setup lang="ts">
import Select from "primevue/select";

import Button from "@/base-components/Button/Button.vue";
import Lucide from "@/base-components/Lucide";

interface Option {
  value: string | null | number;
  label: string;
}

withDefaults(
  defineProps<{
    title: string;
    options?: Option[];
    buttonLabel?: string;
    showPlusIcon?: boolean;
    modelValue?: string;
  }>(),
  {
    options: () => [],
    showPlusIcon: true,
    buttonLabel: "",
    modelValue: "0",
  },
);

const emit = defineEmits<{
  (e: "buttonClick"): void;
  (e: "filterChange", value: string): void;
  (e: "update:modelValue", value: string): void;
}>();

const handleButtonClick = () => emit("buttonClick");
const handleFilterChange = (value: string | null) => {
  const newValue = value === null ? "0" : value.toString();
  emit("update:modelValue", newValue);
  emit("filterChange", newValue);
};
</script>

<template>
  <div class="mb-4 flex h-10 items-center justify-between">
    <h2 class="truncate text-lg font-medium">{{ title }}</h2>

    <div class="flex h-10 items-center gap-2">
      <!-- Slot for extra filter -->
      <slot name="extra-filter"></slot>

      <Select
        v-if="options.length"
        :model-value="modelValue"
        :options="options"
        optionLabel="label"
        optionValue="value"
        :placeholder="'Tất cả'"
        fluid
        @change="(e) => handleFilterChange(e.value)"
      />

      <Button v-if="buttonLabel" variant="primary" @click="handleButtonClick">
        <Lucide v-if="showPlusIcon" icon="Plus" class="mr-2 h-5 w-5" />
        {{ buttonLabel }}
      </Button>
    </div>
  </div>
</template>
