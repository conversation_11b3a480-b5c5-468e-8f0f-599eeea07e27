import { TaskPriorityEnum } from "@/api/bcare-enum";
import { TaskListRequest } from "@/api/bcare-types-v2";

export const NOTE_COMPLAIN = 4;
export const NOTE_ORTHER = 1;
export const notePayload = {
  page_size: 0,
  page: 0,
  filter: {
    person_id: 0,
    user_id: 0,
    type: 0,
  },
  order_by: "created_at desc",
};
export const callsPayload = {
  page_size: 0,
  page: 0,
  filter: {
    direction: "",
    call_status: "",
    status: 0,
    person_id: 0,
    user_id: 0,
  },
  order_by: "created_at desc",
};
export const tasksPayload: TaskListRequest = {
  page_size: 0,
  primary_id: 0,
  contributor_id: 0,
  page: 0,
  slow_process: false,
  filter: {
    person_id: 0,
    start_date: "",
    due_date: "",
    status: 0,
    creator_id: 0,
    priority: TaskPriorityEnum.ALL,
  },
  order_by: "created_at desc",
};
export const noteData = {
  id: 0,
  body: "",
  type: NOTE_ORTHER,
  person_id: 0,
  user_id: 0,
  status: 0,
};
