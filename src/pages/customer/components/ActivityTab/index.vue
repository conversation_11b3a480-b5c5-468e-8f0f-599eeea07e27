<script setup lang="ts">
import { useLocalStorage } from "@vueuse/core";
import { type ComponentPublicInstance, computed, onMounted, onUnmounted, ref } from "vue";

import ActivityAllTab from "@/pages/customer/components/ActivityTab/components/ActivityAllTab.vue";
import CallTab from "@/pages/customer/components/ActivityTab/components/CallTab.vue";
import NoteTab from "@/pages/customer/components/ActivityTab/components/NoteTab.vue";
import TaskTab from "@/pages/customer/components/ActivityTab/components/TaskTab.vue";
import TrackTab from "@/pages/customer/components/ActivityTab/components/TrackTab.vue";
import HistoryTab from "@/pages/customer/components/ActivityTab/components/HistoryTab.vue";
import MessageTab from "./components/MessageTab.vue";

interface TabConfig {
  id: string;
  label: string;
  icon: string;
  component: any;
}

interface PopoverInstance extends ComponentPublicInstance {
  toggle: (event: MouseEvent) => void;
  visible: boolean;
}

const popoverRef = ref<PopoverInstance | null>(null);
const isCtrlPressed = ref(false);
const activeTabIndex = ref(0);

const defaultTabs: TabConfig[] = [
  { id: "all", label: "Tất cả", icon: "pi pi-file", component: ActivityAllTab },
  { id: "note", label: "Ghi chú", icon: "pi pi-pencil", component: NoteTab },
  { id: "task", label: "Công việc", icon: "pi pi-list", component: TaskTab },
  { id: "call", label: "Cuộc gọi", icon: "pi pi-phone", component: CallTab },
  { id: "sms", label: "Tin nhắn", icon: "pi pi-mobile", component: MessageTab },
  { id: "track", label: "Lịch sử", icon: "pi pi-history", component: TrackTab },
  { id: "history", label: "Lịch sử chỉnh sửa", icon: "pi pi-user-edit", component: HistoryTab },
];

const storedTabOrder = useLocalStorage<string[]>(
  "activity-tab-order",
  defaultTabs.map((tab) => tab.id),
);

const tabs = computed(() => {
  return storedTabOrder.value
    .map((id) => defaultTabs.find((tab) => tab.id === id))
    .filter(Boolean) as TabConfig[];
});

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey) {
    isCtrlPressed.value = true;
  }
};

const handleKeyUp = (event: KeyboardEvent) => {
  if (!event.ctrlKey) {
    isCtrlPressed.value = false;
  }
};

// Toggle popover
const togglePopover = (event: MouseEvent) => {
  if (popoverRef.value) {
    popoverRef.value.toggle(event);
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
  window.addEventListener("keyup", handleKeyUp);
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
  window.removeEventListener("keyup", handleKeyUp);
});

const props = defineProps<{
  personId?: number;
}>();
</script>

<template>
  <div class="relative">
    <Popover ref="popoverRef">
      <OrderList
        v-model="storedTabOrder"
        selectionMode="none"
        dataKey="id"
        class="w-full min-w-[200px]"
        listStyle="height:auto"
      >
        <template #item="slotProps">
          <div class="p-2">
            {{ defaultTabs.find((t) => t.id === slotProps.item)?.label || slotProps.item }}
          </div>
        </template>
      </OrderList>
    </Popover>

    <Tabs v-model:value="activeTabIndex" scrollable unstyled lazy class="px-5">
      <TabList
        :pt="{
          activeBar: { style: { display: 'none !important' } },
          tabList: { style: { border: 'none' } },
        }"
        class="overflow-hidden rounded-md border border-gray-200"
      >
        <div class="flex w-full items-center !justify-between gap-2">
          <div>
            <Tab
              v-for="(tabConfig, index) in tabs"
              :key="tabConfig.id"
              :value="index"
              class="border-none px-4 py-3 text-sm font-normal hover:border-gray-300 hover:text-gray-700 focus:outline-none"
              :class="{ 'bg-slate-100 text-primary': activeTabIndex === index }"
            >
              <span class="flex items-center gap-2 px-2">
                {{ tabConfig.label }}
              </span>
            </Tab>
          </div>
          <i
            v-show="isCtrlPressed"
            class="pi pi-wrench mr-3 cursor-pointer text-lg text-gray-300 transition-colors hover:text-primary"
            @click="togglePopover"
            aria-haspopup="true"
            aria-controls="config-popover"
          />
        </div>
      </TabList>

      <TabPanels unstyled>
        <TabPanel v-for="(tabConfig, index) in tabs" :key="tabConfig.id" :value="index" unstyled>
          <component :is="tabConfig.component" :person-id="props.personId ?? 0" />
        </TabPanel>
      </TabPanels>
    </Tabs>
  </div>
</template>
