<template>
  <div>
    <!-- Display selected medications as chips -->
    <div v-if="modelValue.length" class="flex flex-wrap items-center gap-1">
      <Chip
        v-for="medication in modelValue"
        :key="medication.ten"
        v-tooltip="`Toa thuốc: ${medication.tieu_de}`"
        :label="medication.tieu_de"
        :pt="{
          root: { class: 'flex gap-1.5 py-1.5' },
          label: { class: 'text-xs font-medium' },
          removeIcon: { class: 'text-[var(--p-button-outlined-help-color)]' },
        }"
        class="cursor-pointer border border-[var(--p-button-outlined-help-border-color)] bg-transparent text-[var(--p-button-outlined-help-color)]"
        removable
        @click="handlePrint(medication)"
        @remove.stop="handleRemoveMedication(medication)"
      />
    </div>

    <!-- Existing Popover -->
    <Popover ref="popoverRef" :pt="{ content: { class: 'p-0' } }" @show="onPopoverShow">
      <div class="w-[30rem]">
        <div v-if="filteredMedications.length > 0" class="p-2">
          <ul
            class="m-0 h-[25vh] snap-y scroll-py-1 list-none space-y-1 overflow-hidden overflow-y-auto overscroll-contain p-1"
          >
            <li
              v-for="medication in filteredMedications"
              :key="medication.ten"
              :class="{
                'bg-soft text-primary ring-1 ring-highlight': isMedicationSelected(medication),
              }"
              class="group flex cursor-pointer snap-start items-center rounded-md px-2 py-1 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
              @click="handleItemClick(medication)"
            >
              <div class="flex-1">
                <span class="hyphens-auto font-medium">
                  <HighlightText :highlight="searchQuery" :text="medication.chuan_doan" />
                </span>
                <div class="text-sm text-gray-600">
                  <span>{{ `${medication.thuoc.length} thuốc` }}</span>
                </div>
              </div>

              <!-- Action buttons container -->
              <div class="flex items-center gap-2">
                <!-- Print button - only show on hover -->
                <Button
                  v-tooltip.top="'In toa thuốc'"
                  class="opacity-0 transition-opacity group-hover:opacity-100"
                  icon="pi pi-print"
                  rounded
                  size="small"
                  text
                  @click.stop="handlePrint(medication)"
                />

                <!-- Selection indicator -->
                <div class="ml-3 text-gray-600 dark:text-gray-400">
                  <div
                    v-if="isMedicationSelected(medication)"
                    :class="{ 'bg-primary': isMedicationSelected(medication) }"
                    class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                  ></div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="p-2">
          <Empty />
        </div>

        <!-- Footer -->
        <div class="border-t p-2">
          <IconField class="mb-2">
            <InputText
              v-model="searchQuery"
              autofocus
              class="w-full text-sm"
              placeholder="Tìm kiếm đơn thuốc"
              type="text"
            />
            <InputIcon
              :class="searchQuery ? 'pi-times' : 'pi-search'"
              class="pi cursor-pointer"
              @click="
                () => {
                  searchQuery = '';
                }
              "
            />
          </IconField>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script lang="ts" setup>
import Button from "primevue/button";
import Chip from "primevue/chip";
import Popover from "primevue/popover";
import { computed, ref } from "vue";

import { MedicationData } from "@/api/extend-types";
import Empty from "@/base-components/Empty";
import HighlightText from "@/base-components/HighlightText.vue";
import { usePrint } from "@/composables/usePrint";
import { useConfigurations } from "@/hooks/useSetting";
import { normalizeVietnamese } from "@/utils/string";

const props = defineProps<{
  modelValue: MedicationData[];
  multiple?: boolean;
  personId?: number;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: MedicationData[]): void;
}>();

const { isLoading, getSetting, fetchSettings } = useConfigurations();

const searchQuery = ref("");
const medications = ref<MedicationData[]>([]);

const filteredMedications = computed(() => {
  const normalizedSearch = normalizeVietnamese(searchQuery.value.toLowerCase());
  return medications.value.filter((medication) => {
    const normalizedName = normalizeVietnamese(medication.chuan_doan.toLowerCase());
    return normalizedName.includes(normalizedSearch);
  });
});

const isMedicationSelected = (medication: MedicationData) =>
  props.modelValue.some((med) => med.ten === medication.ten);

const handleItemClick = (medication: MedicationData) => {
  if (props.multiple) {
    const newSelection = [...props.modelValue];
    const index = newSelection.findIndex((m) => m.ten === medication.ten);
    if (index === -1) {
      newSelection.push(medication);
    } else {
      newSelection.splice(index, 1);
    }
    emit("update:modelValue", newSelection);
  } else {
    emit("update:modelValue", [medication]);
    popoverRef.value?.hide();
  }
};

const fetchMedications = async () => {
  if (isLoading.value) return;

  try {
    await fetchSettings("", "");
    const setting = getSetting("toa_thuoc", "toa_thuoc");
    if (setting?.value) {
      medications.value = setting.value.toa_thuoc || [];
    }
  } catch (error) {
    console.error("Error fetching medications:", error);
  }
};

const onPopoverShow = () => {
  fetchMedications();
};

const popoverRef = ref();

const handleRemoveMedication = (medication: MedicationData) => {
  const newSelection = props.modelValue.filter((med) => med.ten !== medication.ten);
  emit("update:modelValue", newSelection);
};

const { printMedication } = usePrint();

const handlePrint = (medication: MedicationData) => {
  if (props.personId) {
    printMedication(medication.ten, props.personId);
    popoverRef.value?.hide();
  }
};

defineExpose({
  popoverRef,
});
</script>
