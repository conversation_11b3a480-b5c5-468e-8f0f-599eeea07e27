<script lang="ts" setup>
import { onMounted, onUnmounted, ref, toRefs, watch } from "vue";

import { AttachmentStatus } from "@/api/bcare-enum";
import AttachmentTree from "@/components/Attachment/AttachmentTree.vue";
import SearchAttachment from "@/components/Attachment/SearchAttachment.vue";
import OperationSelect from "@/components/Operation/OperationSelect.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import UserAssignPromax from "@/components/User/UserAssignPromax.vue";
import SelectMedication from "@/pages/customer/components/TreatmentTab/SelectMedication.vue";

import { useTreatmentFormStore } from "@/stores/treatment-form-store";

const props = defineProps<{
  attachmentId?: number | null;
  personId: number;
}>();

const emit = defineEmits([
  "update:selectedProduct",
  "update:selectedAttachments",
  "update:selectedAttachment",
  "update:selectedMedication",
  "save",
]);

const savedParticipants = ref<Record<string, number[]>>({});

// Khởi tạo store
const attachmentStore = useTreatmentFormStore();

// Refs cho popovers
const attachmentPopRef = ref();
const productPopRef = ref();
const nextOperationPop = ref();
const medicationPopRef = ref();

const { attachmentId } = toRefs(props);

// Khởi tạo data
const init = async () => {
  // Clear form trước khi init
  attachmentStore.clearAll();

  if (props.attachmentId) {
    // Load dữ liệu nếu đang ở chế độ edit
    await attachmentStore.loadExistingAttachment(props.attachmentId);
  } else {
    // Khởi tạo mới nếu ở chế độ create
    attachmentStore.setPersonId(props.personId);
  }
};

// Lifecycle hooks
onMounted(() => {
  init();
});

// Watchers để emit updates
watch(
  () => attachmentStore.pendingAttachments,
  (newAttachments) => {
    emit("update:selectedAttachments", Object.values(newAttachments));
  },
  { deep: true },
);

// Toggle handlers
const toggleAttachmentSearch = async (event: MouseEvent, attachment: any | null) => {
  attachmentStore.toggleAttachmentSearch(attachment);
  attachmentPopRef.value?.popoverRef.toggle(event);
};

const toggleProductSearch = (event: MouseEvent, attachment: any | null) => {
  attachmentStore.toggleProductSearch(attachment);
  productPopRef.value?.popoverRef.toggle(event);
};

const toggleMedicationSelect = (event: MouseEvent) => {
  medicationPopRef.value?.popoverRef.toggle(event);
};

const toggleNextOperationPop = (event: MouseEvent) => {
  attachmentStore.toggleNextOperationPop();
  nextOperationPop.value?.popoverRef.toggle(event);
};

// Action handlers
const selectParentAttachment = async (selectedParent: any) => {
  await attachmentStore.selectParentAttachment(selectedParent);
  emit("update:selectedAttachment", selectedParent);
};

const selectProduct = async () => {
  await attachmentStore.selectProduct();
  emit("update:selectedProduct", attachmentStore.selectedProduct);
};

const handleRemoveOperation = (id: number | string) => {
  attachmentStore.handleRemoveOperation(id);
};

const removeAttachment = async (id: number) => {
  await attachmentStore.removeAttachment(id);
};

const saveParticipant = async (role: string, value: number | number[] | undefined) => {
  if (Array.isArray(value)) return;

  await attachmentStore.saveParticipant(role, value || 0);
  emit("update:selectedAttachments", Object.values(attachmentStore.pendingAttachments));
};

// Save handler với xử lý edit/create
const saveAll = async (status: AttachmentStatus) => {
  const successfulUpdates = await attachmentStore.saveAll(status);
  if (successfulUpdates.length > 0) {
    attachmentStore.clearAll();
    emit("save");
  }
};

// Menu items
const menuItems = [
  {
    label: "Clear",
    command: () => {
      saveAll(AttachmentStatus.DELETED);
    },
  },
  {
    label: "Delete",
    command: () => {
      saveAll(AttachmentStatus.DELETED);
    },
  },
];

// Cleanup
onUnmounted(() => {
  //saveAll(AttachmentStatus.DELETED);
});

watch(
  attachmentId,
  async (newId) => {
    if (newId) {
      // Load attachment data for editing
      await init();
    } else {
      // Reset form for creating new
      attachmentStore.clearAll();
    }
  },
  { immediate: true },
);
</script>

<template>
  <div class="grid grid-cols-12 gap-2 py-2">
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': attachmentStore.operationFocused === 'today',
          'hover:border-gray-300': attachmentStore.operationFocused !== 'today',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="attachmentStore.operationFocused = 'today'"
      >
        <div v-if="attachmentStore.hasPendingAttachments" class="mb-2 space-y-1">
          <!-- Render attachments with parents -->
          <AttachmentTree
            v-for="(record, id) in Object.values(attachmentStore.pendingAttachments).filter(
              (r) => r.parent,
            )"
            :key="id"
            :attachment="record.self"
            :parent="record.parent"
            @remove-attachment="removeAttachment(record.self.id)"
          />
        </div>
        <div class="flex gap-2">
          <Button
            aria-label="Add bought service"
            class="size-8 text-xs"
            icon="pi pi-search-plus"
            outlined
            severity="success"
            size="small"
            :disabled="attachmentStore.isEditing"
            @click="toggleAttachmentSearch($event, null)"
          />
          <Button
            aria-label="Add product"
            class="size-8 text-xs"
            icon="pi pi-cart-plus"
            outlined
            severity="warn"
            size="small"
            :disabled="attachmentStore.isEditing"
            @click="toggleProductSearch($event, null)"
          />
          <div v-if="!attachmentStore.hasPendingAttachments" class="content-center text-muted">
            Công việc hôm nay
          </div>
        </div>
      </div>
    </div>

    <!-- People Search Sections -->
    <div class="col-span-6 flex flex-col space-y-2">
      <div
        :class="{
          'border-highlight': attachmentStore.operationFocused === 'next',
          'hover:border-gray-300': attachmentStore.operationFocused !== 'next',
        }"
        class="flex-1 rounded border-2 border-dashed p-2"
        @click="attachmentStore.operationFocused = 'next'"
      >
        <div v-if="attachmentStore.nextOperations.length" class="flex flex-wrap gap-1">
          <Chip
            v-for="operation in attachmentStore.nextOperations"
            :key="operation.id"
            :label="operation.name"
            class="cursor-pointer"
            removable
            @click="toggleNextOperationPop"
            @remove.stop="handleRemoveOperation(operation.id)"
          />
        </div>
        <div v-else class="flex gap-2">
          <Button
            aria-label="Add bought service"
            class="size-8 text-xs"
            icon="pi pi-hammer"
            outlined
            severity="info"
            size="small"
            @click="toggleNextOperationPop"
          />
          <div class="content-center text-muted">Công việc dự kiến</div>
        </div>
      </div>

      <!--      <TinyToothChart
              v-if="attachmentStore.operationFocused === 'today'"
              v-model="attachmentStore.selectedTeeth"
              :defaultArchMode="'adult'"
              label="Hôm nay"
            />
            <TinyToothChart
              v-if="attachmentStore.operationFocused === 'next'"
              v-model="attachmentStore.selectedTeethNext"
              :defaultArchMode="'adult'"
              label="Dự kiến"
            />-->
    </div>

    <div class="col-start-1 col-end-9">
      <div class="grid grid-cols-2 gap-3 lg:grid-cols-4">
        <!-- Bác sĩ -->
        <div
          class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            v-model="attachmentStore.participant.bac_si"
            :global-unique-name="'bac_si'"
            class="w-full"
            placeholder="Bác sĩ"
            @update:model-value="saveParticipant('bac_si', $event)"
          />
        </div>

        <!-- Phụ tá -->
        <div
          class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            v-model="attachmentStore.participant.phu_ta"
            :global-unique-name="'phu_ta'"
            class="w-full"
            placeholder="Phụ tá"
            @update:model-value="saveParticipant('phu_ta', $event)"
          />
        </div>

        <!-- Điều phối -->
        <div
          class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            v-model="attachmentStore.participant.dieu_phoi"
            :global-unique-name="'dieu_phoi'"
            class="w-full"
            placeholder="Điều phối"
            @update:model-value="saveParticipant('dieu_phoi', $event)"
          />
        </div>

        <!-- X-Quang -->
        <div
          class="flex items-center rounded-lg border border-gray-200 p-2 transition-colors hover:border-primary-200 hover:bg-primary-50/30"
        >
          <UserAssignPromax
            v-model="attachmentStore.participant.x_quang"
            :global-unique-name="'x_quang'"
            class="w-full"
            placeholder="X-Quang"
            @update:model-value="saveParticipant('x_quang', $event)"
          />
        </div>
      </div>
    </div>
    <div class="col-span-4 flex h-full flex-row items-center justify-between">
      <Button
        v-if="Object.keys(attachmentStore.selectedMedications).length === 0"
        v-tooltip="'Thêm toa thuốc'"
        aria-label="Add medication"
        class="size-9 text-xs"
        icon="pi pi-file-plus"
        outlined
        severity="help"
        @click="toggleMedicationSelect($event)"
      />
      <SelectMedication
        ref="medicationPopRef"
        v-model="attachmentStore.selectedMedications"
        :multiple="true"
        :person-id="props.personId"
      />
      <SplitButton
        v-tooltip.left="!attachmentStore.canSave ? 'Chọn dịch vụ điều trị trước khi lưu' : ''"
        :disabled="!attachmentStore.canSave"
        :model="menuItems"
        class="ml-2"
        label="Save"
        severity="primary"
        size="small"
        @click="saveAll(AttachmentStatus.UNPAID)"
      ></SplitButton>
    </div>
  </div>

  <!-- Popovers and Selects -->
  <SearchAttachment
    ref="attachmentPopRef"
    v-model:selectedAttachment="attachmentStore.selectedParentAttachment"
    :default-filters="{ kind: 'product', product_type: 'service' }"
    :filter-product-type="true"
    :multiple="false"
    :person-id="props.personId"
    search-mode="paid_attachment"
    @update:selectedAttachment="selectParentAttachment"
  />

  <SearchProduct
    ref="productPopRef"
    v-model:selectedProduct="attachmentStore.selectedProduct"
    :defaultFilters="{ type: 'service' }"
    :multiple="false"
    @update:selectedProduct="selectProduct"
  />

  <OperationSelect
    ref="nextOperationPop"
    v-model="attachmentStore.nextOperations"
    :product-ids="attachmentStore.productIds"
  />
</template>
