<template>
  <Popover ref="popoverRef" :pt="{ root: { class: 'surface-0 shadow-md rounded-lg' } }">
    <div class="min-w-[400px] max-w-md">
      <!-- Header -->
      <div class="surface-border flex items-center justify-between border-b">
        <span class="text-lg font-medium">Thông tin ngân hàng</span>
        <Button icon="pi pi-times" text rounded severity="secondary" @click="hide" />
      </div>

      <!-- Content: Form Fields -->
      <div class="space-y-2 py-3">
        <FormField label="Tên tài khoản">
          <InputText
            id="bank_account_name_editor"
            v-model="editablePersonField.bank_account_name"
            class="w-full"
            placeholder="Nhập tên tài khoản"
          />
        </FormField>
        <FormField label="Số tài khoản">
          <InputText
            id="bank_account_number_editor"
            v-model="editablePersonField.bank_account_number"
            class="w-full"
            placeholder="Nhập số tài khoản"
          />
        </FormField>
        <FormField label="Ngân hàng">
          <InputText
            id="bank_name_editor"
            v-model="editablePersonField.bank"
            class="w-full"
            placeholder="Nhập tên ngân hàng"
          />
        </FormField>
        <FormField label="Chi nhánh">
          <InputText
            id="bank_branch_editor"
            v-model="editablePersonField.bank_branch"
            class="w-full"
            placeholder="Nhập chi nhánh"
          />
        </FormField>
      </div>

      <!-- Footer -->
      <div class="surface-border">
        <Button
          label="Lưu"
          icon="pi pi-save"
          severity="primary"
          @click="handleSubmit"
          class="w-full"
        />
      </div>
    </div>
  </Popover>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Popover from "primevue/popover";
import { ref, watch } from "vue";

import type { PersonMeta } from "@/api/bcare-types-v2";
import { FormField } from "@/components/Form";
import usePerson from "@/hooks/usePerson";

const props = defineProps<{
  personId: number;
  initialPersonField: PersonMeta | undefined | null;
}>();

const emit = defineEmits<{
  (e: "person-updated"): void;
}>();

const { updatePerson } = usePerson();
const popoverRef = ref();

const editablePersonField = ref<Partial<PersonMeta>>({
  bank_account_name: "",
  bank_account_number: "",
  bank: "",
  bank_branch: "",
});

watch(
  () => props.initialPersonField,
  (newField) => {
    if (newField) {
      editablePersonField.value.bank_account_name = newField.bank_account_name || "";
      editablePersonField.value.bank_account_number = newField.bank_account_number || "";
      editablePersonField.value.bank = newField.bank || "";
      editablePersonField.value.bank_branch = newField.bank_branch || "";
    } else {
      editablePersonField.value = {
        bank_account_name: "",
        bank_account_number: "",
        bank: "",
        bank_branch: "",
      };
    }
  },
  { immediate: true, deep: true },
);

const handleSubmit = async () => {
  if (!props.personId) {
    console.error("Person ID is missing");
    return;
  }
  try {
    const payloadPersonFieldUpdate: Partial<PersonMeta> = {
      bank_account_name: editablePersonField.value.bank_account_name?.trim() || undefined,
      bank_account_number: editablePersonField.value.bank_account_number?.trim() || undefined,
      bank: editablePersonField.value.bank?.trim() || undefined,
      bank_branch: editablePersonField.value.bank_branch?.trim() || undefined,
    };

    const finalPersonField: PersonMeta = {
      ...(props.initialPersonField || { has_zalo: "unknown" }),
      ...payloadPersonFieldUpdate,
    };

    Object.keys(finalPersonField).forEach((key) => {
      const k = key as keyof PersonMeta;
      if (
        finalPersonField[k] === undefined &&
        (k === "bank_account_name" ||
          k === "bank_account_number" ||
          k === "bank" ||
          k === "bank_branch")
      ) {
      }
    });

    await updatePerson({
      id: props.personId,
      person_field: finalPersonField,
    });
    hide();
    emit("person-updated");
  } catch (error) {
    console.error("Error updating bank info:", error);
    // Consider adding user-facing error feedback here
  }
};

const show = (event: Event) => {
  if (props.initialPersonField) {
    editablePersonField.value.bank_account_name = props.initialPersonField.bank_account_name || "";
    editablePersonField.value.bank_account_number =
      props.initialPersonField.bank_account_number || "";
    editablePersonField.value.bank = props.initialPersonField.bank || "";
    editablePersonField.value.bank_branch = props.initialPersonField.bank_branch || "";
  } else {
    editablePersonField.value = {
      bank_account_name: "",
      bank_account_number: "",
      bank: "",
      bank_branch: "",
    };
  }
  popoverRef.value?.show(event);
};

const hide = () => {
  popoverRef.value?.hide();
};

defineExpose({ show, hide });
</script>
