<script lang="ts" setup>
import { computed, ref } from "vue";

import type { PaymentResponse, PaymentUpdateRequest } from "@/api/bcare-types-v2";
import Alert from "@/base-components/Alert/Alert.vue";
import Money from "@/base-components/Money.vue";
import useEconomy from "@/hooks/useEconomy";

import PaymentMethods from "./PaymentForm/PaymentMethods.vue";

interface Props {
  payment: PaymentResponse;
  type?: "payment" | "refund"; // Thêm prop type
}

const props = withDefaults(defineProps<Props>(), {
  type: "payment",
});

const emit = defineEmits<{
  (e: "close"): void;
  (e: "updated", paymentId: number): void;
}>();

const { updatePayment } = useEconomy();
const isLoading = ref(false);

// Chuyển đổi số tiền về dương khi hiển thị cho refund
const initialAmount = computed(() => ({
  cash: Math.abs(props.payment.cash || 0),
  creditCard: Math.abs(props.payment.credit_card || 0),
  bank: Math.abs(props.payment.bank || 0),
  mpos: Math.abs(props.payment.mpos || 0),
  momo: Math.abs(props.payment.momo || 0),
}));

const formData = ref({
  cash: initialAmount.value.cash,
  creditCard: initialAmount.value.creditCard,
  bank: initialAmount.value.bank,
  mpos: initialAmount.value.mpos,
  momo: initialAmount.value.momo,
  modified: ["cash", "credit_card", "bank", "mpos", "momo"] as Array<
    "cash" | "credit_card" | "bank" | "mpos" | "momo"
  >,
});

const totalAmount = computed(() => {
  return (
    (formData.value.cash || 0) +
    (formData.value.creditCard || 0) +
    (formData.value.bank || 0) +
    (formData.value.mpos || 0) +
    (formData.value.momo || 0)
  );
});

// Kiểm tra tổng tiền với giá trị tuyệt đối
const isValidAmount = computed(() => {
  return totalAmount.value === Math.abs(props.payment.total_amount);
});

const handlePaymentMethodChange = (
  method: "cash" | "credit_card" | "bank" | "mpos" | "momo",
  value: number,
) => {
  switch (method) {
    case "cash":
      formData.value.cash = value;
      break;
    case "credit_card":
      formData.value.creditCard = value;
      break;
    case "bank":
      formData.value.bank = value;
      break;
    case "mpos":
      formData.value.mpos = value;
      break;
    case "momo":
      formData.value.momo = value;
      break;
  }
  if (!formData.value.modified.includes(method)) {
    formData.value.modified.push(method);
  }
};

const handleSave = async () => {
  if (!isValidAmount.value) {
    return;
  }

  try {
    isLoading.value = true;
    // Chuyển đổi số tiền về âm cho refund khi gửi lên server
    const multiplier = props.type === "refund" ? -1 : 1;

    const updateData = {
      id: props.payment.id,
      cash: formData.value.cash * multiplier,
      credit_card: formData.value.creditCard * multiplier,
      bank: formData.value.bank * multiplier,
      mpos: formData.value.mpos * multiplier,
      momo: formData.value.momo * multiplier,
      modified: ["cash", "credit_card", "bank", "mpos", "momo"],
    };

    const response = await updatePayment(updateData);
    if (response) {
      emit("updated", response.id);
    }
  } catch (error) {
    console.error("Failed to update payment:", error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <div>
    <div class="mb-2 flex items-center justify-between">
      <div class="text-sm text-gray-500">
        {{
          type === "refund" ? "Chỉnh sửa phương thức hoàn phí" : "Chỉnh sửa phương thức thanh toán"
        }}
      </div>

      <div class="flex gap-2">
        <div v-if="!isValidAmount">
          <Alert variant="soft-warning">
            Tổng số tiền phải bằng
            <Money
              :amount="Math.abs(payment.total_amount)"
              :variant="type === 'refund' ? 'warning' : 'default'"
            />
          </Alert>
        </div>
        <Button label="Hủy" outlined severity="secondary" @click="emit('close')" />
        <Button :disabled="!isValidAmount" :loading="isLoading" label="Lưu" @click="handleSave" />
      </div>
    </div>

    <PaymentMethods
      v-model:bank="formData.bank"
      v-model:cash="formData.cash"
      v-model:creditCard="formData.creditCard"
      v-model:mpos="formData.mpos"
      v-model:momo="formData.momo"
      :type="type"
      @methodChange="handlePaymentMethodChange"
    />
  </div>
</template>
