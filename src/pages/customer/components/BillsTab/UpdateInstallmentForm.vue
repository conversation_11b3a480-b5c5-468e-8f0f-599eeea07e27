<script lang="ts" setup>
import { ref } from "vue";

import type { InstallmentResponse, InstallmentUpdateRequest } from "@/api/bcare-types-v2";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";

const props = defineProps<{
  installment: InstallmentResponse;
}>();

const emit = defineEmits<{
  (e: "save", updatedInstallment: InstallmentResponse): void;
  (e: "cancel"): void;
}>();

const noteInput = ref(props.installment.note || "");
const isLoading = ref(false);

// Get updateInstallment function from hook
const { updateInstallment } = useInstallmentPlan();

const handleSave = async () => {
  try {
    isLoading.value = true;

    const updateRequest: InstallmentUpdateRequest = {
      id: props.installment.id,
      note: noteInput.value,
      modified: ["note"], // Indicate which fields are modified
    };

    const updatedInstallment = await updateInstallment(updateRequest);

    if (updatedInstallment) {
      emit("save", updatedInstallment as InstallmentResponse);
    }
  } catch (error) {
    console.error("Failed to update installment:", error);
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <div class="flex items-center gap-2">
    <InputText v-model="noteInput" fluid placeholder="Ghi chú" @keyup.enter="handleSave" />
    <Button
      :loading="isLoading"
      icon="pi pi-check"
      severity="success"
      size="small"
      @click="handleSave"
    />
  </div>
</template>
