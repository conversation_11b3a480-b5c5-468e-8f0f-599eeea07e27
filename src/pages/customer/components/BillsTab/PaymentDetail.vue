<script lang="ts" setup>
import dayjs from "dayjs";
import <PERSON><PERSON> from "primevue/button";
import ConfirmPopup from "primevue/confirmpopup";
import Popover from "primevue/popover";
import Tag from "primevue/tag";
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, ref, watch } from "vue";

import { PaymentAllocationResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import { UserAvatarGroup } from "@/components/User";
import { usePermissions } from "@/composables/usePermissions";
import { usePrint } from "@/composables/usePrint";
import useEconomy from "@/hooks/useEconomy";

import AllocationEditor from "./AllocationForm/AllocationEditor.vue";
import EditPaymentForm from "./EditPaymentForm.vue";
import { usePaymentFormState } from "@/hooks/usePaymentFormState";
import State from "@/base-components/State.vue";

const props = defineProps<{
  paymentId: number;
}>();

const emit = defineEmits<{
  (e: "deleted", paymentId: number): void;
}>();

const confirm = useConfirm();
const { getPayment, deletePayment, currentPayment, updatePaymentDate } = useEconomy();
const { printPayment } = usePrint();
const { onlyAdmin } = usePermissions();

const { availableBillItems, availableInstallments } = usePaymentFormState();

const isEditing = ref(false);
const editingAllocation = ref<PaymentAllocationResponse | null>(null);
const popoverRef = ref();
const selectedDate = ref<Date | null>(null);

const selectedItemIds = computed(() => {
  if (!currentPayment.value) return [];

  return currentPayment.value.allocations
    .filter((allocation) => allocation.id !== editingAllocation.value?.id)
    .map((allocation) => {
      if (allocation.bill_item) {
        return { type: "bill_item" as const, id: allocation.bill_item.id };
      }
      if (allocation.installment) {
        return { type: "installment" as const, id: allocation.installment.id };
      }
      return null;
    })
    .filter((item): item is { type: "bill_item" | "installment"; id: number } => item !== null);
});

const fetchPaymentData = async () => {
  if (props.paymentId) {
    await getPayment({ id: props.paymentId });
  }
};

watch(
  () => props.paymentId,
  async (newId) => {
    isEditing.value = false;
    if (newId) {
      await fetchPaymentData();
    }
  },
);

onMounted(async () => {
  await fetchPaymentData();
});

const handleDeletePayment = () => {
  confirm.require({
    message: "Bạn có chắc chắn muốn xóa phiếu thu này?",
    header: "Xác nhận xóa",
    icon: "pi pi-exclamation-triangle",
    rejectProps: {
      label: "Hủy",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Xóa",
      severity: "danger",
    },
    accept: async () => {
      try {
        await deletePayment({ id: props.paymentId });
        emit("deleted", props.paymentId);
      } catch (error) {
        console.error("Error deleting payment:", error);
      }
    },
    reject: () => {
      console.log("User cancelled delete action");
    },
  });
};

const handleEditAllocation = (allocation: PaymentAllocationResponse) => {
  editingAllocation.value = allocation;
};

const handleAllocationUpdated = async (updatedAllocation: PaymentAllocationResponse) => {
  await fetchPaymentData();
  editingAllocation.value = null;
};

const getPaymentMethodAmount = (method: string) => {
  if (!currentPayment.value) return 0;
  switch (method) {
    case "cash":
      return currentPayment.value.cash;
    case "credit_card":
      return currentPayment.value.credit_card;
    case "bank":
      return currentPayment.value.bank;
    case "mpos":
      return currentPayment.value.mpos;
    case "momo":
      return currentPayment.value.momo;
    default:
      return 0;
  }
};

const getPaymentMethodLabel = (method: string) => {
  switch (method) {
    case "cash":
      return "Tiền mặt";
    case "credit_card":
      return "Thẻ tín dụng";
    case "bank":
      return "Chuyển khoản";
    case "mpos":
      return "mPOS";
    case "momo":
      return "MoMo";
    default:
      return method;
  }
};

const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

const handlePrint = () => {
  if (currentPayment.value) {
    printPayment(currentPayment.value.id);
  }
};

const handleEdit = () => {
  isEditing.value = true;
};

const handleEditComplete = async (paymentId: number) => {
  isEditing.value = false;
  await fetchPaymentData();
};

const getAllocationUserIds = (allocation: PaymentAllocationResponse) => {
  const userIds: number[] = [];

  if (allocation.user_id) {
    userIds.push(allocation.user_id);
  }

  if (allocation.bill_item?.user_id) {
    userIds.push(allocation.bill_item.user_id);
  }

  if (allocation.installment?.user_id) {
    userIds.push(allocation.installment.user_id);
  }

  return userIds;
};

const toggleDatePicker = (event: Event) => {
  popoverRef.value.toggle(event);
};

const handleDateSelect = () => {
  if (!onlyAdmin() || !currentPayment.value || !selectedDate.value) {
    popoverRef.value.hide();
    return;
  }

  updatePaymentDate(currentPayment.value, selectedDate.value)
    .then(fetchPaymentData)
    .catch((err) => console.error("Error updating date:", err));

  popoverRef.value.hide();
};
</script>

<template>
  <div v-if="currentPayment" class="rounded-lg border bg-white">
    <div class="border-b p-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">Chi tiết phiếu thu #{{ currentPayment.id }}</h3>
          <p class="mt-1 flex items-center gap-1 text-sm text-gray-500">
            Ngày tạo
            <span
              class="flex items-center"
              :class="{
                'cursor-pointer hover:text-primary': onlyAdmin(),
                'cursor-not-allowed': !onlyAdmin(),
              }"
              @click="onlyAdmin() && toggleDatePicker($event)"
              v-tooltip="'Cập nhật ngày thanh toán'"
            >
              <DateTime :time="currentPayment.payment_date" show-time />
              <i class="pi pi-calendar ml-1 text-xs"></i>
            </span>

            <Popover ref="popoverRef">
              <DatePicker
                v-model="selectedDate"
                inline
                @date-select="handleDateSelect"
                :maxDate="new Date()"
              />
            </Popover>
          </p>
        </div>
        <div class="flex items-center gap-2">
          <Tag
            :severity="currentPayment.state === 'completed' ? 'success' : 'warning'"
            :value="currentPayment.state"
          />
          <Button
            v-if="onlyAdmin()"
            aria-label="Delete"
            icon="pi pi-trash"
            rounded
            severity="danger"
            text
            @click="handleDeletePayment"
            v-tooltip="'Xóa phiếu thu'"
          />
          <Button
            v-if="currentPayment.state === 'completed'"
            aria-label="Edit"
            icon="pi pi-pencil"
            rounded
            severity="secondary"
            text
            v-tooltip="'Chỉnh sửa phương thức thanh toán'"
            @click="handleEdit"
          />
          <Button
            icon="pi pi-print"
            rounded
            severity="secondary"
            text
            @click="handlePrint"
            v-tooltip="'In phiếu thu'"
          />
        </div>
      </div>
    </div>

    <div class="p-4">
      <!-- Thông tin thanh toán -->
      <template v-if="isEditing">
        <EditPaymentForm
          :payment="currentPayment"
          @close="isEditing = false"
          @updated="handleEditComplete"
        />
      </template>
      <template v-else>
        <div class="mb-6 grid grid-cols-5 gap-4">
          <div v-for="method in ['cash', 'credit_card', 'bank', 'mpos', 'momo']" :key="method">
            <div class="rounded-lg bg-gray-50 p-3">
              <div class="text-sm text-gray-500">{{ getPaymentMethodLabel(method) }}</div>
              <div class="mt-1 text-lg font-semibold">
                <Money :amount="getPaymentMethodAmount(method)" />
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- Phân bổ thanh toán -->
      <div>
        <h4 class="mb-3 font-medium text-gray-900">Nội dung thanh toán</h4>
        <div class="space-y-2">
          <!-- Header -->
          <div
            class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 pb-2 text-xs font-medium uppercase text-gray-500"
          >
            <div class="col-span-3">Tên</div>
            <div class="col-span-1">Người liên quan</div>
            <div class="col-span-2">Ghi chú</div>
            <div class="text-right">Đơn giá</div>
            <div class="text-right">SL</div>
            <div class="text-right">Giảm giá</div>
            <div class="text-right">Thành tiền</div>
            <div class="col-span-2 text-right">Thanh toán</div>
          </div>

          <!-- Content -->
          <div
            v-for="allocation in currentPayment.allocations"
            :key="allocation.id"
            class="group grid grid-cols-12 items-center gap-3 py-2 text-sm text-gray-900"
          >
            <div class="col-span-3">
              <div class="font-medium text-primary">
                <template v-if="allocation.bill_item">
                  <AttachmentTitle :attachment="allocation.bill_item.attachment" />
                </template>
                <template v-else-if="allocation.installment">
                  {{ allocation.installment.name }}
                </template>
              </div>
              <div class="flex flex-wrap items-center gap-1 text-xs text-gray-500">
                <template v-if="allocation.bill_item">
                  Hóa đơn: #{{ allocation.bill_item.bill_id }}
                </template>
                <template v-else-if="allocation.installment">
                  {{ getInstallmentKindText(allocation.installment.kind) }}
                  #{{ allocation.installment.installment_number }}
                </template>
                <State
                  v-if="allocation.state !== 'initial'"
                  :state="allocation.state"
                  size="sm"
                  :shine="false"
                />
              </div>
            </div>
            <div class="col-span-1">
              <UserAvatarGroup
                :users="getAllocationUserIds(allocation)"
                :max-display="3"
                size="small"
              />
            </div>
            <div class="col-span-2">
              {{ allocation.bill_item?.note || allocation.installment?.note || "-" }}
            </div>
            <template v-if="allocation.bill_item">
              <div class="text-right">
                <Money :amount="allocation.bill_item.attachment.price" variant="default" />
              </div>
              <div class="text-right">
                {{ allocation.bill_item.attachment.quantity }}
              </div>
              <div class="relative flex items-center justify-end text-right">
                <span class="transition-opacity group-hover:opacity-0">
                  <Money :amount="allocation.bill_item.attachment.discount" variant="default" />
                </span>
                <div
                  class="absolute inset-0 flex items-center justify-end opacity-0 transition-opacity group-hover:opacity-100"
                  v-if="onlyAdmin()"
                >
                  <Button
                    aria-label="Edit"
                    class="size-8 text-xs"
                    icon="pi pi-pen-to-square"
                    outlined
                    severity="warn"
                    size="small"
                    @click="handleEditAllocation(allocation)"
                  />
                </div>
              </div>
              <div class="text-right">
                <Money :amount="allocation.bill_item.amount" />
              </div>
            </template>
            <template v-else>
              <div class="text-right">-</div>
              <div class="text-right">-</div>
              <div class="relative flex items-center justify-end text-right">
                <span class="transition-opacity group-hover:opacity-0">-</span>
                <div
                  class="absolute inset-0 flex items-center justify-end opacity-0 transition-opacity group-hover:opacity-100"
                  v-if="onlyAdmin()"
                >
                  <Button
                    aria-label="Edit"
                    class="size-8 text-xs"
                    icon="pi pi-pen-to-square"
                    outlined
                    severity="warn"
                    size="small"
                    @click="handleEditAllocation(allocation)"
                  />
                </div>
              </div>
              <div class="text-right">
                <Money v-if="allocation.installment" :amount="allocation.installment.amount" />
              </div>
            </template>
            <div class="col-span-2 text-right font-medium">
              <Money :amount="allocation.amount" />
            </div>
          </div>
        </div>
      </div>

      <!-- Tổng cộng -->
      <div class="mt-6 border-t pt-4">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-900">Thành tiền</span>
            <Money :amount="currentPayment.total_amount" class="text-lg font-medium" />
          </div>
          <div class="flex items-center justify-between">
            <span class="font-medium text-gray-700">Thực nhận</span>
            <Money :amount="currentPayment.total_amount" class="text-lg font-medium" />
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Allocation Form Dialog -->
    <AllocationEditor
      v-if="editingAllocation"
      :allocation="editingAllocation"
      :bill-items="availableBillItems"
      :installments="availableInstallments"
      :selected-item-ids="selectedItemIds"
      :show="!!editingAllocation"
      @close="editingAllocation = null"
      @updated="handleAllocationUpdated"
    />

    <ConfirmPopup></ConfirmPopup>
  </div>
</template>
