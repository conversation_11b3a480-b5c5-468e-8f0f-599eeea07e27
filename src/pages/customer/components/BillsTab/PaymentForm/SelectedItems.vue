<script lang="ts" setup>
import { ref } from "vue";

import type {
  BillItemResponse,
  InstallmentPlanResponse,
  InstallmentResponse,
} from "@/api/bcare-types-v2";
import Alert from "@/base-components/Alert/Alert.vue";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import State from "@/base-components/State.vue";
import { UserAvatar } from "@/components/User";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { usePermissions } from "@/composables/usePermissions";
import useBill from "@/hooks/useBill";
import UpdateBillForm from "@/pages/customer/components/BillsTab/UpdateBillForm.vue";
import UpdateInstallmentForm from "../UpdateInstallmentForm.vue";

interface AllocationItem {
  type: "bill_item" | "installment" | "installment_plan";
  bill_item?: BillItemResponse;
  installment?: InstallmentResponse;
  installment_plan?: InstallmentPlanResponse;
  amount: number;
  remaining?: number;
}

interface Props {
  allocations: AllocationItem[];
  totalAmount: number;
  totalPayableAmount: number;
  shortageAmount: number;
  isOverPaid: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "removeItem", type: "bill_item" | "installment" | "installment_plan", id: number): void;
  (e: "billItemUpdated", updatedBillItem: BillItemResponse): void;
  (e: "installmentUpdated", updatedInstallment: InstallmentResponse): void;
  (e: "billItemDeleted", id: number): void;
}>();

const editingBillItemId = ref<number | null>(null);
const editingInstallmentId = ref<number | null>(null);

const { deleteBillItem } = useBill();
const { onlyAdmin } = usePermissions();
const { confirm } = useConfirmTippy();

const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

const handleBillItemUpdated = (updatedBillItem: BillItemResponse) => {
  emit("billItemUpdated", updatedBillItem);
  editingBillItemId.value = null;
};

const handleInstallmentUpdated = (updatedInstallment: InstallmentResponse) => {
  emit("installmentUpdated", updatedInstallment);
  editingInstallmentId.value = null;
};

const handleDeleteBillItem = async (itemId: number, event: MouseEvent) => {
  confirm(event, {
    title: "Bạn có chắc chắn muốn xóa mục này?",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      try {
        await deleteBillItem({ id: itemId });
        emit("billItemDeleted", itemId);
      } catch (error) {
        console.error("Error deleting bill item:", error);
      }
    },
  });
};
</script>

<template>
  <div>
    <h4 class="mb-3 font-medium text-gray-900">Nội dung thanh toán</h4>
    <div class="space-y-2">
      <!-- Header -->
      <div
        class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 pb-2 text-xs font-medium uppercase text-gray-500"
      >
        <div class="col-span-3">Tên</div>
        <div class="col-span-1">Người thêm</div>
        <div class="col-span-2">Ghi chú</div>
        <div class="text-right">Đơn giá</div>
        <div class="text-right">SL</div>
        <div class="text-right">Giảm giá</div>
        <div class="text-right">Thành tiền</div>
        <div class="text-right">Đã trả</div>
        <div class="text-right">Thanh toán</div>
      </div>

      <!-- Empty state message -->
      <div v-if="allocations.length === 0" class="py-5 text-center text-gray-500">
        <p>Chọn sản phẩm/dịch vụ cần thanh toán từ danh sách bên dưới hoặc thêm mới</p>
        <i class="pi pi-arrow-down mt-2 animate-bounce text-2xl"></i>
      </div>

      <!-- Selected Items -->
      <div
        v-for="allocation in allocations"
        :key="`${allocation.type}-${allocation.type === 'bill_item' ? allocation.bill_item?.id : allocation.installment?.id}`"
      >
        <!-- Edit Form -->
        <UpdateBillForm
          v-if="
            allocation.type === 'bill_item' &&
            allocation.bill_item &&
            (!allocation.bill_item.allocations || allocation.bill_item.allocations.length === 0) &&
            editingBillItemId === allocation.bill_item.id
          "
          :bill-item-id="allocation.bill_item.id"
          @updated="handleBillItemUpdated"
        />

        <!-- Normal View -->
        <div v-else class="group grid grid-cols-12 items-center gap-3 text-sm text-gray-900">
          <!-- Bill Item -->
          <template v-if="allocation.type === 'bill_item' && allocation.bill_item">
            <div class="col-span-3">
              <div class="font-medium text-primary">
                <AttachmentTitle :attachment="allocation.bill_item.attachment" />
              </div>
              <div>
                <State v-if="!allocation.bill_item.bill_id" state="new" />
                <div v-else class="text-xs text-gray-500">
                  Hoá đơn: #{{ allocation.bill_item.bill_id }}
                </div>
                <DateTime :time="allocation.bill_item.created_at" class="ml-1 text-xs text-muted" />
              </div>
            </div>
            <div class="col-span-1">
              <UserAvatar :user-id="allocation.bill_item.user_id" size="small" />
            </div>
            <div class="col-span-2">{{ allocation.bill_item.note || "-" }}</div>
            <div class="text-right">
              <Money :amount="allocation.bill_item.attachment.price" variant="default" />
            </div>
            <div class="text-right">
              <span class="transition-opacity group-hover:opacity-0">
                {{ allocation.bill_item.attachment.quantity }}
              </span>
            </div>
            <div class="relative text-right">
              <span class="transition-opacity group-hover:opacity-0">
                <Money :amount="allocation.bill_item.attachment.discount" variant="default" />
              </span>
              <div
                class="absolute right-0 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <Button
                  v-if="
                    !allocation.bill_item.allocations ||
                    allocation.bill_item.allocations.length === 0
                  "
                  aria-label="Edit"
                  class="size-8 text-xs"
                  icon="pi pi-pen-to-square"
                  outlined
                  severity="warn"
                  size="small"
                  @click="editingBillItemId = allocation.bill_item!.id"
                />
                <Button
                  aria-label="Remove from payment"
                  class="size-8 text-xs"
                  outlined
                  severity="info"
                  size="small"
                  @click.stop="emit('removeItem', 'bill_item', allocation.bill_item!.id)"
                >
                  <i class="pi pi-arrow-down text-sm" />
                </Button>
                <Button
                  v-if="
                    onlyAdmin() ||
                    (!allocation.bill_item.attachment.data && !allocation.bill_item.bill_id)
                  "
                  aria-label="Delete bill item"
                  class="size-8 text-xs"
                  outlined
                  severity="danger"
                  size="small"
                  @click.stop="handleDeleteBillItem(allocation.bill_item!.id, $event)"
                >
                  <i class="pi pi-trash text-sm" />
                </Button>
              </div>
            </div>
            <div class="text-right">
              <Money :amount="allocation.bill_item.amount" />
            </div>
            <div class="text-right">
              <Money :amount="allocation.bill_item.paid_amount" variant="default" />
            </div>
          </template>

          <!-- Installment -->
          <template v-else-if="allocation.type === 'installment' && allocation.installment">
            <div class="col-span-3">
              <div class="font-medium text-primary">
                {{ allocation.installment.name }}
              </div>
              <div class="text-xs text-gray-500">
                {{ getInstallmentKindText(allocation.installment.kind) }}
                #{{ allocation.installment.installment_number }}
              </div>
            </div>
            <div class="col-span-1">
              <UserAvatar :user="allocation.installment.creator" size="small" />
            </div>
            <div class="col-span-2">
              <UpdateInstallmentForm
                v-if="editingInstallmentId === allocation.installment.id"
                :installment="allocation.installment"
                @cancel="editingInstallmentId = null"
                @save="handleInstallmentUpdated"
              />
              <div v-else>
                {{ allocation.installment.note || "-" }}
              </div>
            </div>
            <div class="text-right">-</div>
            <div class="text-right">
              <span class="transition-opacity group-hover:opacity-0">-</span>
            </div>
            <div class="relative text-right">
              <span class="transition-opacity group-hover:opacity-0">-</span>
              <div
                class="absolute right-0 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <Button
                  aria-label="Edit installment"
                  class="size-8 text-xs"
                  icon="pi pi-pen-to-square"
                  outlined
                  severity="warn"
                  size="small"
                  @click="editingInstallmentId = allocation.installment.id"
                />
                <Button
                  aria-label="Installment remove from payment"
                  class="size-8 text-xs"
                  outlined
                  severity="info"
                  size="small"
                  @click.stop="emit('removeItem', 'installment', allocation.installment.id)"
                >
                  <i class="pi pi-arrow-down text-sm" />
                </Button>
              </div>
            </div>
            <div class="text-right">
              <Money :amount="allocation.installment.amount" />
            </div>
            <div class="text-right">
              <Money :amount="allocation.installment.paid_amount" variant="default" />
            </div>
          </template>

          <!-- Payment Amount Column (Common for both types) -->
          <div class="text-right font-medium">
            <Money :amount="allocation.amount" />
            <div
              v-if="allocation.remaining && allocation.remaining > 0"
              class="text-xs text-red-500"
            >
              Còn thiếu:
              <Money :amount="allocation.remaining" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Section -->
    <div class="mt-4 border-t pt-4">
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <span class="font-medium text-gray-900">Thành tiền</span>
          <div class="text-right">
            <Money :amount="totalPayableAmount" class="text-lg font-medium" />
          </div>
        </div>
        <div class="flex items-center justify-between">
          <span class="font-medium text-gray-700">Thực nhận</span>
          <div class="text-right">
            <Money :amount="totalAmount" class="text-lg font-medium" />
          </div>
        </div>
        <div v-if="shortageAmount > 0" class="flex items-center justify-between text-danger">
          <span class="font-medium">Còn thiếu</span>
          <div class="text-right">
            <Money :amount="shortageAmount" class="font-semibold" variant="danger" />
          </div>
        </div>
        <div v-if="isOverPaid" class="mt-2">
          <Alert class="flex items-center" variant="soft-warning">
            Số tiền thực nhận đang lớn hơn tổng số tiền cần thanh toán. Vui lòng kiểm tra lại.
          </Alert>
        </div>
      </div>
    </div>
  </div>
</template>
