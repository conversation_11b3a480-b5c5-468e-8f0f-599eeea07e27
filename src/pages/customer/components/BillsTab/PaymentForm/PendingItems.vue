<script lang="ts" setup>
import { computed, defineAsyncComponent } from "vue";

import type { BillItemResponse, InstallmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import State from "@/base-components/State.vue";
import { UserAvatar } from "@/components/User";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { usePermissions } from "@/composables/usePermissions";
import useBill from "@/hooks/useBill";

import { usePaymentFormState } from "@/hooks/usePaymentFormState";
import Empty from "@/base-components/Empty";

interface Props {
  billItems: BillItemResponse[];
  installments: InstallmentResponse[];
  selectedItemIds: { type: "bill_item" | "installment" | "installment_plan"; id: number }[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "selectBillItem", item: BillItemResponse): void;
  (e: "selectInstallment", item: InstallmentResponse): void;
  (e: "refresh"): void;
}>();

const { deleteBillItem } = useBill();
const { availableBillItems } = usePaymentFormState();
const { onlyAdmin } = usePermissions();
const { confirm } = useConfirmTippy();

const isInstallmentClickable = (installmentNumber: number) => {
  const sortedInstallments = [...props.installments].sort(
    (a, b) => a.installment_number - b.installment_number,
  );

  const availableInstallments = sortedInstallments.filter((i) => {
    const isUnpaid = i.paid_amount < i.amount;
    const isNotSelected = !props.selectedItemIds.some(
      (selected) => selected.type === "installment" && selected.id === i.id,
    );
    return isUnpaid && isNotSelected;
  });

  const nextAvailableInstallment = availableInstallments[0];
  return nextAvailableInstallment?.installment_number === installmentNumber;
};

const limitedInstallments = computed(() => {
  const sortedInstallments = [...props.installments].sort(
    (a, b) => a.installment_number - b.installment_number,
  );

  const availableInstallments = sortedInstallments.filter((i) => {
    const isUnpaid = i.paid_amount < i.amount;
    const isNotSelected = !props.selectedItemIds.some(
      (selected) => selected.type === "installment" && selected.id === i.id,
    );
    return isUnpaid && isNotSelected;
  });

  return availableInstallments.slice(0, 4);
});

const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

const handleInstallmentClick = (item: InstallmentResponse) => {
  if (isInstallmentClickable(item.installment_number)) {
    emit("selectInstallment", item);
  }
};

const handleDeleteBillItem = async (itemId: number, event: MouseEvent) => {
  confirm(event, {
    title: "Bạn có chắc chắn muốn xóa mục này?",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      try {
        await deleteBillItem({ id: itemId });
        availableBillItems.value = availableBillItems.value.filter((item) => item.id !== itemId);
        emit("refresh");
      } catch (error) {
        console.error("Error deleting bill item:", error);
      }
    },
  });
};
</script>

<template>
  <div>
    <div class="border-b p-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">Danh sách chờ thanh toán</h3>
        </div>
      </div>
    </div>

    <div>
      <!-- Header -->
      <div
        class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 p-4 pb-2 text-xs font-medium uppercase text-gray-500"
      >
        <div class="col-span-3">Tên</div>
        <div class="col-span-1">Người thêm</div>
        <div class="col-span-2">Ghi chú</div>
        <div class="text-right">Đơn giá</div>
        <div class="text-right">SL</div>
        <div class="text-right">Giảm giá</div>
        <div class="text-right">Thành tiền</div>
        <div class="text-right">Đã trả</div>
        <div class="text-right">Còn lại</div>
      </div>

      <!-- Items -->
      <div class="p-2">
        <!-- Bill Items -->
        <div v-if="billItems.length === 0 && installments.length === 0" class="p-4"><Empty /></div>
        <div
          v-for="item in billItems"
          :key="`bill-${item.id}`"
          class="group grid cursor-pointer grid-cols-12 items-center gap-3 rounded px-2 py-2 text-sm hover:bg-gray-50"
          @click="emit('selectBillItem', item)"
        >
          <div class="col-span-3">
            <div class="font-medium text-primary">
              <AttachmentTitle :attachment="item.attachment" />
            </div>
            <div>
              <State v-if="!item.bill_id" state="new" />
              <div v-else class="text-xs text-gray-500">Hoá đơn: #{{ item.bill_id }}</div>
              <DateTime :time="item.created_at" class="ml-1 !text-xs text-muted" size="xs" />
            </div>
          </div>
          <div class="col-span-1">
            <UserAvatar :user="item.user" size="small" />
          </div>
          <div class="col-span-2">{{ item.note || "-" }}</div>
          <div class="text-right">
            <Money :amount="item.attachment.price" variant="default" />
          </div>
          <div class="relative text-right">
            <span
              :class="{
                'transition-opacity group-hover:opacity-0': !item.attachment.data && !item.bill_id,
              }"
            >
              {{ item.attachment.quantity }}
            </span>
            <div
              v-if="onlyAdmin() || (!item.attachment.data && !item.bill_id)"
              class="absolute inset-0 flex items-center justify-end opacity-0 transition-opacity group-hover:opacity-100"
            >
              <Button
                aria-label="Delete bill item"
                class="size-8 text-xs"
                outlined
                severity="danger"
                @click.stop="handleDeleteBillItem(item.id, $event)"
              >
                <i class="pi pi-trash text-sm" />
              </Button>
            </div>
          </div>
          <div class="text-right">
            <Money :amount="item.attachment.discount" variant="default" />
          </div>
          <div class="text-right">
            <Money :amount="item.amount" />
          </div>
          <div class="text-right">
            <Money :amount="item.paid_amount" variant="default" />
          </div>
          <div class="text-right font-medium">
            <Money :amount="item.amount - item.paid_amount" variant="warning" />
          </div>
        </div>

        <!-- Installment Items - Limited to 4 -->
        <div v-for="item in limitedInstallments" :key="`installment-${item.id}`">
          <div
            :class="[
              'relative grid grid-cols-12 items-center gap-3 rounded px-2 py-2 text-sm',
              isInstallmentClickable(item.installment_number)
                ? 'cursor-pointer hover:bg-gray-50'
                : 'cursor-not-allowed opacity-60',
            ]"
            @click="handleInstallmentClick(item)"
          >
            <div class="col-span-3">
              <div class="font-medium text-primary">
                {{ item.name }}
              </div>
              <div class="text-xs text-gray-500">
                {{ getInstallmentKindText(item.kind) }} #{{ item.installment_number }}
              </div>
            </div>
            <div class="col-span-1">
              <UserAvatar :user="item.creator" size="small" />
            </div>
            <div class="col-span-2">{{ item.note || "-" }}</div>
            <div class="text-right">-</div>
            <div class="text-right">-</div>
            <div class="text-right">-</div>
            <div class="text-right">
              <Money :amount="item.amount" />
            </div>
            <div class="text-right">
              <Money :amount="item.paid_amount" variant="default" />
            </div>
            <div class="text-right font-medium">
              <Money :amount="item.amount - item.paid_amount" variant="warning" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
