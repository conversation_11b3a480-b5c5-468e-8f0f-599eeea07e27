<script setup lang="ts">
interface Props {
  cash: number;
  creditCard: number;
  bank: number;
  mpos: number;
  momo: number;
}

export type PaymentMethodId = "cash" | "credit_card" | "bank" | "mpos" | "momo";

interface PaymentMethod {
  id: PaymentMethodId;
  label: string;
  icon: string;
  modelValue: keyof Props;
  tooltip: string;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "cash",
    label: "Tiền mặt",
    icon: "pi pi-wallet",
    modelValue: "cash",
    tooltip: "Thanh toán bằng tiền mặt",
  },
  {
    id: "credit_card",
    label: "Thẻ tín dụng",
    icon: "pi pi-credit-card",
    modelValue: "creditCard",
    tooltip: "Thanh toán bằng thẻ tín dụng",
  },
  {
    id: "bank",
    label: "Chuyển khoản",
    icon: "pi pi-send",
    modelValue: "bank",
    tooltip: "Thanh toán bằng chuyển khoản",
  },
  {
    id: "mpos",
    label: "mPOS",
    icon: "pi pi-mobile",
    modelValue: "mpos",
    tooltip: "Thanh toán bằng mPOS",
  },
  {
    id: "momo",
    label: "MoMo",
    icon: "pi pi-wallet",
    modelValue: "momo",
    tooltip: "Thanh toán bằng MoMo",
  },
];

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "update:cash", value: number): void;
  (e: "update:creditCard", value: number): void;
  (e: "update:bank", value: number): void;
  (e: "update:mpos", value: number): void;
  (e: "update:momo", value: number): void;
  (e: "methodChange", method: PaymentMethodId, value: number): void;
}>();

const handleChange = (method: PaymentMethodId, value: number) => {
  switch (method) {
    case "cash":
      emit("update:cash", value);
      break;
    case "credit_card":
      emit("update:creditCard", value);
      break;
    case "bank":
      emit("update:bank", value);
      break;
    case "mpos":
      emit("update:mpos", value);
      break;
    case "momo":
      emit("update:momo", value);
      break;
  }
  emit("methodChange", method, value);
};

const handlePayAll = (method: PaymentMethodId) => {
  const totalAmount = props.cash + props.creditCard + props.bank + props.mpos + props.momo;

  // Clear all methods first
  emit("update:cash", 0);
  emit("update:creditCard", 0);
  emit("update:bank", 0);
  emit("update:mpos", 0);
  emit("update:momo", 0);

  // Set the selected method to total amount
  switch (method) {
    case "cash":
      emit("update:cash", totalAmount);
      break;
    case "credit_card":
      emit("update:creditCard", totalAmount);
      break;
    case "bank":
      emit("update:bank", totalAmount);
      break;
    case "mpos":
      emit("update:mpos", totalAmount);
      break;
    case "momo":
      emit("update:momo", totalAmount);
      break;
  }
  emit("methodChange", method, totalAmount);
};
</script>

<template>
  <div class="mb-6 flex flex-wrap gap-4">
    <div
      v-for="method in paymentMethods"
      :key="method.id"
      class="min-w-[calc((100%/2)-0.5rem)] flex-1 rounded-lg bg-gray-50 p-3 sm:min-w-[calc((100%/3)-0.75rem)] lg:min-w-[calc((100%/5)-0.8rem)]"
    >
      <div class="flex items-center justify-between text-sm text-gray-500">
        <span>{{ method.label }}</span>
        <Button
          v-tooltip.bottom="method.tooltip"
          class="p-1 transition-transform hover:scale-110"
          text
          @click="handlePayAll(method.id)"
        >
          <i :class="[method.icon, 'text-sm']" />
        </Button>
      </div>
      <div class="mt-1">
        <InputNumber
          :modelValue="props[method.modelValue]"
          @update:modelValue="(value) => handleChange(method.id, value)"
          :min="0"
          mode="currency"
          currency="VND"
          locale="vi-VN"
          fluid
        />
      </div>
    </div>
  </div>
</template>
