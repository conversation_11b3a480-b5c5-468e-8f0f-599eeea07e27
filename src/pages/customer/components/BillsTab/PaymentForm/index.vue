<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";

import type {
  BillItemResponse,
  InstallmentResponse,
  PaymentAddRequest,
  PaymentAllocationAddRequest,
  Track,
} from "@/api/bcare-types-v2";
import { trackUpdate } from "@/api/bcare-v2";
import DateTime from "@/base-components/DateTime.vue";
import { useTrackStageBroadcast } from "@/composables/useTrackStageBroadcast";
import { config } from "@/config";
import useBill from "@/hooks/useBill";
import useEconomy from "@/hooks/useEconomy";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";
import useTrack from "@/hooks/useTrack";
import BillForm from "@/pages/customer/components/BillsTab/BillForm.vue";

import PaymentMethods, { PaymentMethodId } from "./PaymentMethods.vue";
import PendingItems from "./PendingItems.vue";
import SelectedItems from "./SelectedItems.vue";
import { usePaymentFormState } from "@/hooks/usePaymentFormState";

const props = defineProps<{
  personId: number;
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "saved", paymentId: number): void;
}>();

const {
  formData,
  selectedAllocations,
  availableBillItems,
  availableInstallments,
  totalAmount,
  totalPayableAmount,
  shortageAmount,
  isOverPaid,
  allocatePayments,
  resetState,
} = usePaymentFormState();

const { addPayment } = useEconomy();
const { getPartiallyPaidItems } = useBill();
const { getPartiallyPaidInstallments } = useInstallmentPlan();
const { broadcastPaymentDone } = useTrackStageBroadcast();
const { getActiveTrack } = useTrack();

const isLoading = ref(false);
const activeTrack = ref<Track | null>(null);

const selectedItemIds = computed(() =>
  selectedAllocations.value.map((item) => ({
    type: item.type,
    id: item.type === "bill_item" ? item.bill_item!.id : item.installment!.id,
  })),
);

onMounted(async () => {
  try {
    isLoading.value = true;
    formData.value.person_id = props.personId;

    const [billResponse, installmentResponse, track] = await Promise.all([
      getPartiallyPaidItems({ person_id: props.personId }),
      getPartiallyPaidInstallments({ person_id: props.personId }),
      getActiveTrack({ person_id: props.personId }),
    ]);

    if (billResponse?.bill_items) {
      availableBillItems.value = billResponse.bill_items;
    }
    if (installmentResponse?.installments) {
      availableInstallments.value = installmentResponse.installments;
    }
    if (track) {
      activeTrack.value = track;
    }
  } catch (error) {
    console.error("Failed to fetch data:", error);
  } finally {
    isLoading.value = false;
  }
});

const handlePaymentMethodChange = (method: PaymentMethodId, value: number) => {
  formData.value[method] = value;
  if (!formData.value.modified.includes(method)) {
    formData.value.modified.push(method);
  }
};

watch(totalAmount, () => {
  allocatePayments();
});

const handleSave = async (isSendSMS?: boolean) => {
  try {
    if (isOverPaid.value) return;
    // NOTE: Phòng khám muốn thanh toán hóa đơn 0 - tạm thời bỏ check
    if (totalAmount.value < 0) {
      throw new Error("Số tiền thanh toán phải lớn hơn 0");
    }
    if (selectedAllocations.value.length === 0) {
      throw new Error("Vui lòng chọn ít nhất một mục thanh toán");
    }

    isLoading.value = true;
    const allAllocations: PaymentAllocationAddRequest[] = [];

    selectedAllocations.value.forEach((allocation) => {
      if (allocation.type === "bill_item" && allocation.bill_item) {
        allAllocations.push({
          bill_item_id: allocation.bill_item.id,
          amount: allocation.amount,
        });
      } else if (allocation.type === "installment" && allocation.installment) {
        allAllocations.push({
          installment_id: allocation.installment.id,
          amount: allocation.amount,
        });
      }
    });

    const paymentRequest: PaymentAddRequest = {
      person_id: props.personId,
      payment_date: formData.value.payment_date,
      total_amount: totalAmount.value,
      bill_id: formData.value.bill_id,
      state: "completed",
      allocations: allAllocations,
      send_sms: isSendSMS ? 0 : 1, // 0: Gửi, 1: Không gửi
    };

    formData.value.modified.forEach((field) => {
      switch (field) {
        case "cash":
          paymentRequest.cash = formData.value.cash;
          break;
        case "credit_card":
          paymentRequest.credit_card = formData.value.credit_card;
          break;
        case "bank":
          paymentRequest.bank = formData.value.bank;
          break;
        case "mpos":
          paymentRequest.mpos = formData.value.mpos;
          break;
        case "momo":
          paymentRequest.momo = formData.value.momo;
          break;
      }
    });

    const response = await addPayment(paymentRequest);

    // Reset state sau khi lưu thành công
    resetState();

    emit("saved", response ? response.id : 0);

    if (activeTrack.value) {
      trackUpdate({
        id: activeTrack.value.id,
        stage_id: config.clinic.after_payment_stage_id,
        weight: 0,
      })
        .then(() => {
          broadcastPaymentDone(activeTrack.value!.id);
        })
        .catch((error) => {
          console.error("Failed to update track:", error);
        });
    }
  } catch (error) {
    console.error("Failed to save payment:", error);
  } finally {
    isLoading.value = false;
  }
};

const handleCancel = () => {
  emit("close");
};

const addBillItem = (billItem: BillItemResponse) => {
  if (
    selectedAllocations.value.some((a) => a.type === "bill_item" && a.bill_item?.id === billItem.id)
  ) {
    return;
  }

  selectedAllocations.value.push({
    type: "bill_item",
    bill_item: billItem,
    amount: 0,
  });

  availableBillItems.value = availableBillItems.value.filter((item) => item.id !== billItem.id);
  allocatePayments();
};

const addInstallment = (installment: InstallmentResponse) => {
  if (
    selectedAllocations.value.some(
      (a) => a.type === "installment" && a.installment?.id === installment.id,
    )
  ) {
    return;
  }

  selectedAllocations.value.push({
    type: "installment",
    installment: installment,
    amount: 0,
  });

  availableInstallments.value = availableInstallments.value.filter(
    (item) => item.id !== installment.id,
  );
  allocatePayments();
};

const removeItem = (type: "bill_item" | "installment" | "installment_plan", id: number) => {
  const allocation = selectedAllocations.value.find(
    (a) =>
      a.type === type && (type === "bill_item" ? a.bill_item?.id === id : a.installment?.id === id),
  );

  if (allocation) {
    if (type === "bill_item" && allocation.bill_item) {
      availableBillItems.value.push(allocation.bill_item);
    } else if (type === "installment" && allocation.installment) {
      availableInstallments.value.push(allocation.installment);
    }
  }

  selectedAllocations.value = selectedAllocations.value.filter(
    (a) =>
      !(
        a.type === type &&
        (type === "bill_item" ? a.bill_item?.id === id : a.installment?.id === id)
      ),
  );
  allocatePayments();
};

const handleNewBillItem = (item: BillItemResponse) => {
  addBillItem(item);
  allocatePayments();
};

const handleBillItemUpdated = (updatedBillItem: BillItemResponse) => {
  const index = selectedAllocations.value.findIndex(
    (allocation) =>
      allocation.type === "bill_item" && allocation.bill_item?.id === updatedBillItem.id,
  );
  if (index !== -1) {
    selectedAllocations.value[index].bill_item = updatedBillItem;
  }
  allocatePayments();
};

const handleInstallmentUpdated = (updatedInstallment: InstallmentResponse) => {
  const index = selectedAllocations.value.findIndex(
    (allocation) =>
      allocation.type === "installment" && allocation.installment?.id === updatedInstallment.id,
  );
  if (index !== -1 && selectedAllocations.value[index].installment) {
    selectedAllocations.value[index].installment = {
      ...selectedAllocations.value[index].installment,
      note: updatedInstallment.note,
    };
  }
  allocatePayments();
};

watch(totalPayableAmount, (newAmount) => {
  formData.value.cash = newAmount;
  formData.value.modified.push("cash");
});

const handleBillItemDeleted = (itemId: number) => {
  selectedAllocations.value = selectedAllocations.value.filter(
    (allocation) => !(allocation.type === "bill_item" && allocation.bill_item?.id === itemId),
  );
  allocatePayments();
};

const saveItems = ref([
  { label: "Lưu và không gửi SMS", icon: "pi pi-bell-slash", command: () => handleSave(false) },
]);
</script>

<template>
  <div class="space-y-4">
    <!-- Header Section -->
    <div class="rounded-lg border">
      <div class="border-b p-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold text-gray-900">Tạo phiếu thu mới</h3>
            <p class="mt-1 text-sm text-gray-500">
              Ngày tạo
              <DateTime :time="formData.payment_date" show-time />
            </p>
          </div>
          <div class="flex gap-2">
            <Button
              :loading="isLoading"
              label="Hủy"
              outlined
              severity="secondary"
              @click="handleCancel"
            />
            <Button
              label="Clear"
              outlined
              severity="info"
              @click="
                () => {
                  formData.cash = 0;
                  formData.credit_card = 0;
                  formData.bank = 0;
                  formData.mpos = 0;
                  formData.momo = 0;
                  formData.modified = ['cash', 'credit_card', 'bank', 'mpos', 'momo'];
                }
              "
            />
            <SplitButton
              :disabled="isOverPaid || isLoading"
              :loading="isLoading"
              label="Lưu"
              :model="saveItems"
              icon="pi pi-save"
              @click="handleSave(true)"
            />
          </div>
        </div>
      </div>

      <div class="p-4">
        <!-- Payment Methods Component -->
        <PaymentMethods
          v-model:bank="formData.bank"
          v-model:cash="formData.cash"
          v-model:creditCard="formData.credit_card"
          v-model:mpos="formData.mpos"
          v-model:momo="formData.momo"
          @methodChange="handlePaymentMethodChange"
        />

        <!-- Selected Items Component -->
        <SelectedItems
          :allocations="selectedAllocations"
          :is-over-paid="isOverPaid"
          :shortage-amount="shortageAmount"
          :total-amount="totalAmount"
          :total-payable-amount="totalPayableAmount"
          @billItemUpdated="handleBillItemUpdated"
          @installmentUpdated="handleInstallmentUpdated"
          @removeItem="removeItem"
          @billItemDeleted="handleBillItemDeleted"
        />
      </div>
    </div>

    <!-- Bill Form Section -->
    <div class="mt-4 rounded-lg bg-gray-50">
      <div class="p-4">
        <BillForm :isEmbedded="true" :personId="personId" @itemAdded="handleNewBillItem" />
      </div>
    </div>

    <!-- Pending Items Component -->
    <div class="rounded-lg border">
      <PendingItems
        :bill-items="availableBillItems"
        :installments="availableInstallments"
        :selected-item-ids="selectedItemIds"
        @select-bill-item="addBillItem"
        @select-installment="addInstallment"
      />
    </div>
  </div>
</template>
