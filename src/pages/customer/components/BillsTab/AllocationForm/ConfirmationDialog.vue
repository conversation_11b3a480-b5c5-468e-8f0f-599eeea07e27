<script lang="ts" setup>
import { computed, onMounted, watch } from "vue";

import type { PaymentAllocationResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Money from "@/base-components/Money.vue";

const props = defineProps<{
  visible: boolean;
  confirmationMode: "update" | "split";
  confirmationData: {
    original: PaymentAllocationResponse | null;
    updated: any;
    splitParts: any[];
  };
  isOverAllocated: boolean;
  maxAllowedAmount: number;
  loading: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "confirm"): void;
  (e: "cancel"): void;
}>();

// Khi component được mount, in ra console để debug
onMounted(() => {
  console.log("ConfirmationDialog mounted with props:", {
    visible: props.visible,
    confirmationMode: props.confirmationMode,
    isOverAllocated: props.isOverAllocated,
    maxAllowedAmount: props.maxAllowedAmount,
  });
});

// Thêm watch để theo dõi thay đổi của props.visible
watch(
  () => props.visible,
  (newValue) => {
    console.log("ConfirmationDialog - visible prop changed to:", newValue);
    // Không cần làm gì vì computed sẽ tự cập nhật
  },
  { immediate: true },
);

// Tạo computed property cho visible
const isVisible = computed({
  get: () => {
    console.log("ConfirmationDialog - isVisible getter called, returning:", props.visible);
    return props.visible;
  },
  set: (value) => {
    console.log("ConfirmationDialog - isVisible setter called with value:", value);
    emit("update:visible", value);
  },
});

// Lấy text hiển thị cho loại installment
const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

// Format số tiền (nếu cần)
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(amount);
};

const handleConfirm = () => {
  console.log("ConfirmationDialog - handleConfirm called");
  emit("confirm");
};

const handleCancel = () => {
  console.log("ConfirmationDialog - handleCancel called");
  emit("update:visible", false);
  emit("cancel");
};
</script>

<template>
  <Dialog
    :visible="isVisible"
    @update:visible="isVisible = $event"
    modal
    :style="{ width: '700px' }"
    header="Xác nhận thay đổi phân bổ"
    :closable="false"
    position="center"
  >
    <div class="mb-4 rounded-lg bg-blue-50 p-3">
      <div class="font-medium text-blue-800">Xem lại thông tin phân bổ trước khi lưu</div>
      <div class="mt-1 text-sm text-blue-700">
        Vui lòng kiểm tra kỹ thông tin dưới đây trước khi xác nhận thay đổi.
      </div>
    </div>

    <!-- Debug info - giúp kiểm tra dữ liệu -->
    <div class="mb-3 rounded bg-gray-100 p-2 text-xs" style="max-height: 100px; overflow: auto">
      <strong>Debug:</strong> Mode: {{ confirmationMode }}, IsOverAllocated: {{ isOverAllocated }},
      SplitParts: {{ confirmationData.splitParts?.length || 0 }} phần
    </div>

    <!-- Nội dung xác nhận cho chế độ update -->
    <div v-if="confirmationMode === 'update'" class="space-y-4">
      <div class="mb-4 flex gap-4">
        <div class="flex-1 rounded-lg border p-3">
          <div class="mb-2 border-b pb-2 text-sm font-semibold">Phân bổ hiện tại</div>
          <div class="space-y-2 text-sm">
            <div>
              <span class="text-gray-500">ID:</span>
              <span class="ml-2 font-medium">{{ confirmationData.original?.id }}</span>
            </div>
            <div>
              <span class="text-gray-500">Số tiền:</span>
              <span class="ml-2 font-medium">
                <Money :amount="confirmationData.original?.amount || 0" />
              </span>
            </div>
            <div>
              <span class="text-gray-500">Phân bổ cho:</span>
              <div class="ml-2 mt-1">
                <div v-if="confirmationData.original?.bill_item">
                  <span class="font-medium">Bill Item:</span>
                  <div class="ml-2 p-1">
                    <AttachmentTitle
                      :attachment="confirmationData.original?.bill_item?.attachment"
                    />
                  </div>
                </div>
                <div v-else-if="confirmationData.original?.installment">
                  <span class="font-medium">Trả góp:</span>
                  <div class="ml-2 p-1">
                    {{ confirmationData.original?.installment?.name }}
                    ({{ getInstallmentKindText(confirmationData.original?.installment?.kind) }} #{{
                      confirmationData.original?.installment?.installment_number
                    }})
                  </div>
                </div>
                <div v-else class="italic text-gray-500">Chưa phân bổ</div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-1 rounded-lg border border-green-200 bg-green-50 p-3">
          <div class="mb-2 border-b border-green-200 pb-2 text-sm font-semibold text-green-800">
            Phân bổ mới
          </div>
          <div class="space-y-2 text-sm">
            <div>
              <span class="text-gray-500">ID:</span>
              <span class="ml-2 font-medium">{{ confirmationData.original?.id }}</span>
            </div>
            <div>
              <span class="text-gray-500">Số tiền:</span>
              <span class="ml-2 font-medium">
                <Money :amount="confirmationData.original?.amount || 0" />
              </span>
            </div>
            <div>
              <span class="text-gray-500">Phân bổ cho:</span>
              <div class="ml-2 mt-1">
                <div v-if="confirmationData.updated?.bill_item">
                  <span class="font-medium">Bill Item:</span>
                  <div class="ml-2 p-1">
                    <AttachmentTitle
                      :attachment="confirmationData.updated?.bill_item?.attachment"
                    />
                  </div>
                </div>
                <div v-else-if="confirmationData.updated?.installment">
                  <span class="font-medium">Trả góp:</span>
                  <div class="ml-2 p-1">
                    {{ confirmationData.updated?.installment?.name }}
                    ({{ getInstallmentKindText(confirmationData.updated?.installment?.kind) }} #{{
                      confirmationData.updated?.installment?.installment_number
                    }})
                  </div>
                </div>
                <div v-else class="italic text-gray-500">Chưa phân bổ</div>
              </div>
            </div>
            <div v-if="confirmationData.updated?.note">
              <span class="text-gray-500">Ghi chú:</span>
              <div class="ml-2 mt-1 rounded bg-white p-1">
                {{ confirmationData.updated?.note }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cảnh báo nếu số tiền phân bổ vượt quá số tiền còn lại -->
      <div
        v-if="isOverAllocated"
        class="rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-sm text-yellow-700"
      >
        <div class="flex items-center gap-2">
          <i class="pi pi-exclamation-triangle text-yellow-500"></i>
          <span class="font-medium">Cảnh báo: Phân bổ một phần</span>
        </div>
        <div class="mt-1">
          Số tiền phân bổ (<Money :amount="confirmationData.original?.amount || 0" />) lớn hơn số
          tiền còn lại cần thanh toán (<Money :amount="maxAllowedAmount" />). Chỉ
          <Money :amount="maxAllowedAmount" class="font-medium" />
          sẽ được phân bổ.
        </div>
      </div>
    </div>

    <!-- Nội dung xác nhận cho chế độ split -->
    <div v-if="confirmationMode === 'split'" class="space-y-4">
      <div class="mb-3">
        <div class="mb-2 text-sm font-semibold">Phân bổ hiện tại:</div>
        <div class="rounded-lg border p-3">
          <div class="space-y-2">
            <div class="mb-1 flex justify-between">
              <span class="text-gray-500">ID:</span>
              <span>{{ confirmationData.original?.id }}</span>
            </div>
            <div class="mb-1 flex justify-between">
              <span class="text-gray-500">Số tiền:</span>
              <Money :amount="confirmationData.original?.amount || 0" />
            </div>
            <div class="mb-1">
              <span class="text-gray-500">Phân bổ cho:</span>
              <div class="ml-4 mt-1">
                <div v-if="confirmationData.original?.bill_item" class="text-sm">
                  <span>Bill Item:</span>
                  <div class="font-medium">
                    <AttachmentTitle
                      :attachment="confirmationData.original?.bill_item?.attachment"
                    />
                  </div>
                </div>
                <div v-else-if="confirmationData.original?.installment" class="text-sm">
                  <span>Trả góp:</span>
                  <div class="font-medium">
                    {{ confirmationData.original?.installment?.name }}
                    ({{ getInstallmentKindText(confirmationData.original?.installment?.kind) }} #{{
                      confirmationData.original?.installment?.installment_number
                    }})
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-2 text-sm font-semibold">Phân bổ mới:</div>
      <div
        v-if="confirmationData.splitParts && confirmationData.splitParts.length > 0"
        class="space-y-3"
      >
        <div
          v-for="(part, index) in confirmationData.splitParts"
          :key="index"
          :class="[
            'rounded-lg border p-3',
            part.isOriginal ? 'bg-gray-50' : 'border-green-200 bg-green-50',
          ]"
        >
          <div class="mb-1 flex justify-between">
            <span class="text-gray-500">Loại:</span>
            <span class="font-medium">
              {{ part.isOriginal ? "Phân bổ gốc (giữ lại)" : "Phân bổ mới" }}
            </span>
          </div>
          <div class="mb-1 flex justify-between">
            <span class="text-gray-500">Số tiền:</span>
            <Money :amount="part.amount || 0" />
          </div>
          <div class="mb-1">
            <span class="text-gray-500">Phân bổ cho:</span>
            <div class="ml-4 mt-1">
              <div v-if="part.bill_item" class="text-sm">
                <span>Bill Item:</span>
                <div class="font-medium">
                  <AttachmentTitle :attachment="part.bill_item?.attachment" />
                </div>
              </div>
              <div v-else-if="part.installment" class="text-sm">
                <span>Trả góp:</span>
                <div class="font-medium">
                  {{ part.installment?.name }}
                  ({{ getInstallmentKindText(part.installment?.kind) }} #{{
                    part.installment?.installment_number
                  }})
                </div>
              </div>
            </div>
          </div>
          <div v-if="part.note" class="mb-1">
            <span class="text-gray-500">Ghi chú:</span>
            <div class="ml-4 mt-1 rounded bg-white p-1 text-sm">
              {{ part.note }}
            </div>
          </div>
        </div>
      </div>
      <div v-else class="rounded-lg border border-yellow-200 bg-yellow-50 p-3 text-yellow-700">
        Không có dữ liệu về cách chia phân bổ. Vui lòng quay lại và thiết lập lại.
      </div>

      <!-- Kiểm tra tổng -->
      <div
        v-if="confirmationData.splitParts && confirmationData.splitParts.length > 0"
        class="flex justify-between rounded-lg bg-blue-50 p-3"
      >
        <span class="font-medium">Tổng số tiền phân bổ:</span>
        <span class="font-bold">
          <Money
            :amount="confirmationData.splitParts.reduce((sum, part) => sum + (part.amount || 0), 0)"
          />
        </span>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="Hủy"
          outlined
          severity="secondary"
          :disabled="loading"
          @click="handleCancel"
        />
        <Button
          label="Xác nhận và lưu"
          severity="success"
          :loading="loading"
          @click="handleConfirm"
        />
      </div>
    </template>
  </Dialog>
</template>
