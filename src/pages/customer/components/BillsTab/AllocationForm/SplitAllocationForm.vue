<script lang="ts" setup>
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';
import Button from 'primevue/button';
import InputNumber from 'primevue/inputnumber';
import InputText from 'primevue/inputtext';
import RadioButton from 'primevue/radiobutton';
import { computed, reactive,ref, watch } from 'vue';

import type { BillItemResponse, InstallmentResponse,PaymentAllocationResponse } from '@/api/bcare-types-v2';
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import Money from "@/base-components/Money.vue";

const props = defineProps<{
  allocation: PaymentAllocationResponse;
  billItems: BillItemResponse[];
  installments: InstallmentResponse[];
}>();

const emit = defineEmits<{
  (e: 'submit', data: any): void;
}>();

const keepOriginalAllocation = ref(true);

// Form cho chế độ split
const splitForm = reactive({
  originalAmount: props.allocation.amount / 2 as number,
  parts: [
    {
      amount: props.allocation.amount / 2,
      type: 'bill_item' as 'bill_item' | 'installment',
      bill_item_id: null as number | null,
      installment_id: null as number | null,
      note: ''
    }
  ]
});

// Hàm để dọn dẹp các phần không cần thiết
const cleanupEmptyParts = () => {
  // Đếm số phần đã được phân bổ
  const allocatedParts = splitForm.parts.filter(part =>
    (part.type === 'bill_item' && part.bill_item_id) ||
    (part.type === 'installment' && part.installment_id)
  );

  // Nếu có ít nhất một phần đã được phân bổ, chỉ giữ lại tối đa một phần chưa phân bổ
  if (allocatedParts.length > 0) {
    // Tìm tất cả các phần chưa phân bổ
    const unallocatedParts = splitForm.parts.filter(part =>
      !part.bill_item_id && !part.installment_id
    );

    // Nếu có nhiều hơn 1 phần chưa phân bổ, hợp nhất chúng lại
    if (unallocatedParts.length > 1) {
      // Tính tổng số tiền của các phần chưa phân bổ
      const totalUnallocatedAmount = unallocatedParts.reduce((sum, part) => sum + part.amount, 0);

      // Xóa tất cả các phần chưa phân bổ
      splitForm.parts = splitForm.parts.filter(part =>
        part.bill_item_id || part.installment_id
      );

      // Thêm một phần mới với tổng số tiền
      if (totalUnallocatedAmount > 0) {
        splitForm.parts.push({
          amount: totalUnallocatedAmount,
          type: 'bill_item',
          bill_item_id: null,
          installment_id: null,
          note: 'Số tiền dư cần phân bổ'
        });
      }
    }
  }
};

// Thêm 1 phần cho splitForm
const addSplitPart = () => {
  // Trước khi thêm phần mới, dọn dẹp các phần không cần thiết
  cleanupEmptyParts();

  // Tìm phần chưa phân bổ hiện tại
  const unallocatedPartIndex = splitForm.parts.findIndex(part =>
    !part.bill_item_id && !part.installment_id
  );

  // Nếu đã có một phần chưa phân bổ, không thêm phần mới
  if (unallocatedPartIndex !== -1) {
    console.log('Đã có một phần chưa phân bổ, sử dụng phần này thay vì tạo mới');
    return;
  }

  // Nếu chưa có phần chưa phân bổ, thêm mới
  splitForm.parts.push({
    amount: 0,
    type: 'bill_item' as 'bill_item' | 'installment',
    bill_item_id: null as number | null,
    installment_id: null as number | null,
    note: ''
  });

  recalculateSplitAmounts();
};

// Xóa 1 phần của splitForm
const removeSplitPart = (index: number) => {
  if (splitForm.parts.length <= 1) {
    console.log('Lỗi: Phải có ít nhất 1 phần');
    return;
  }

  splitForm.parts.splice(index, 1);
  recalculateSplitAmounts();
};

// Tính lại số tiền các phần
const recalculateSplitAmounts = () => {
  // Lấy tổng số tiền có thể phân bổ
  const totalAllocatableAmount = props.allocation.amount - (keepOriginalAllocation.value ? splitForm.originalAmount : 0);

  // Tổng số tiền đã phân bổ trong các phần đã chọn dịch vụ/kỳ trả góp
  const allocatedParts = splitForm.parts.filter(part =>
    (part.type === 'bill_item' && part.bill_item_id) ||
    (part.type === 'installment' && part.installment_id)
  );

  const totalAllocatedAmount = allocatedParts.reduce((sum, part) => sum + part.amount, 0);

  // Số tiền còn lại cho các phần chưa phân bổ
  const remainingAmount = totalAllocatableAmount - totalAllocatedAmount;

  // Các phần chưa phân bổ
  const unallocatedParts = splitForm.parts.filter(part =>
    !part.bill_item_id && !part.installment_id
  );

  // Nếu không có phần chưa phân bổ và vẫn còn tiền, tạo một phần mới
  if (unallocatedParts.length === 0 && remainingAmount > 0) {
    splitForm.parts.push({
      amount: remainingAmount,
      type: 'bill_item',
      bill_item_id: null,
      installment_id: null,
      note: 'Số tiền còn lại cần phân bổ'
    });
    return;
  }

  // Nếu có các phần chưa phân bổ, phân chia số tiền còn lại cho chúng
  if (unallocatedParts.length > 0) {
    // Nếu chỉ có một phần chưa phân bổ, nó sẽ nhận toàn bộ số tiền còn lại
    if (unallocatedParts.length === 1) {
      const unallocatedPartIndex = splitForm.parts.findIndex(part =>
        !part.bill_item_id && !part.installment_id
      );

      if (unallocatedPartIndex !== -1) {
        splitForm.parts[unallocatedPartIndex].amount = remainingAmount > 0 ? remainingAmount : 0;
      }
    } else {
      // Nếu có nhiều phần chưa phân bổ, gộp chúng lại thành một phần
      cleanupEmptyParts();
    }
  }
};

// Cập nhật khi thay đổi số tiền của allocation gốc
const updateOriginalAmount = () => {
  recalculateSplitAmounts();
};

// Cập nhật tự động số tiền của phần cuối cùng khi các phần khác thay đổi
const updateLastPartAmount = () => {
  // Tính lại phân bổ cho tất cả các phần
  recalculateSplitAmounts();
};

// Watch sự thay đổi của keepOriginalAllocation để cập nhật số tiền
watch(keepOriginalAllocation, () => {
  recalculateSplitAmounts();
  cleanupEmptyParts();
}, { immediate: true });

// Watch sự thay đổi của splitForm.originalAmount để cập nhật số tiền các phần
watch(() => splitForm.originalAmount, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    updateLastPartAmount();
    cleanupEmptyParts();
  }
});

// Thêm computed property để theo dõi các dịch vụ đã được chọn trong các phần
const selectedBillItemIds = computed(() => {
  const ids: number[] = [];
  splitForm.parts.forEach(part => {
    if (part.bill_item_id) {
      ids.push(part.bill_item_id);
    }
  });
  return ids;
});

// Thêm computed property để theo dõi các kỳ trả góp đã được chọn trong các phần
const selectedInstallmentIds = computed(() => {
  const ids: number[] = [];
  splitForm.parts.forEach(part => {
    if (part.installment_id) {
      ids.push(part.installment_id);
    }
  });
  return ids;
});

// Hàm để lấy danh sách bill item có sẵn cho một phần cụ thể
const getAvailableBillItemsForPart = (currentPartIndex: number) => {
  // Lọc ra các bill item có thể phân bổ
  const filtered = props.billItems.filter(billItem => {
    // Kiểm tra xem bill item có đủ số tiền còn lại để phân bổ không
    const remainingAmount = billItem.amount - billItem.paid_amount;

    // Kiểm tra xem bill item đã được chọn trong phần khác chưa
    const isSelectedInOtherPart = splitForm.parts.some((part, index) =>
      index !== currentPartIndex && part.bill_item_id === billItem.id
    );

    return remainingAmount > 0 && !isSelectedInOtherPart;
  });

  return filtered;
};

// Hàm để lấy danh sách installment có sẵn cho một phần cụ thể
const getAvailableInstallmentsForPart = (currentPartIndex: number) => {
  // Lọc ra các installment có thể phân bổ
  const filtered = props.installments.filter(installment => {
    // Kiểm tra xem installment có đủ số tiền còn lại để phân bổ không
    const remainingAmount = installment.amount - installment.paid_amount;

    // Kiểm tra xem installment đã được chọn trong phần khác chưa
    const isSelectedInOtherPart = splitForm.parts.some((part, index) =>
      index !== currentPartIndex && part.installment_id === installment.id
    );

    return remainingAmount > 0 && !isSelectedInOtherPart;
  });

  return filtered.sort((a, b) => a.installment_number - b.installment_number);
};

// Chọn bill item cho phần trong split form
const selectSplitBillItem = (index: number, item: BillItemResponse) => {
  console.log('Selecting split bill item:', item, 'for part', index);
  splitForm.parts[index].bill_item_id = item.id;
  splitForm.parts[index].installment_id = null; // Reset installment_id
  splitForm.parts[index].type = 'bill_item';

  // Kiểm tra và điều chỉnh số tiền nếu vượt quá số tiền còn lại cần thanh toán
  const remainingAmount = item.amount - item.paid_amount;

  // Chỉ tạo phần mới nếu số tiền hiện tại vượt quá số tiền còn lại cần thanh toán
  if (splitForm.parts[index].amount > remainingAmount) {
    const excessAmount = splitForm.parts[index].amount - remainingAmount;
    splitForm.parts[index].amount = remainingAmount;

    // Tạo thêm phần mới cho số tiền dư CHỈ KHI excess amount > 0
    if (excessAmount > 0) {
      // Kiểm tra xem đã có phần nào chưa được phân bổ chưa
      const unallocatedPartIndex = splitForm.parts.findIndex(part =>
        !part.bill_item_id && !part.installment_id
      );

      if (unallocatedPartIndex !== -1 && unallocatedPartIndex !== index) {
        // Nếu đã có phần chưa phân bổ, cập nhật số tiền của phần đó
        splitForm.parts[unallocatedPartIndex].amount += excessAmount;
        splitForm.parts[unallocatedPartIndex].note = 'Số tiền dư từ phân bổ trước';
      } else {
        // Nếu chưa có phần chưa phân bổ, tạo phần mới
        splitForm.parts.push({
          amount: excessAmount,
          type: 'bill_item',
          bill_item_id: null,
          installment_id: null,
          note: 'Số tiền dư từ phân bổ trước'
        });
      }
    }

    // Cập nhật lại tổng số tiền
    recalculateSplitAmounts();
    // Dọn dẹp các phần không cần thiết
    cleanupEmptyParts();
  }
};

// Chọn installment cho phần trong split form
const selectSplitInstallment = (index: number, item: InstallmentResponse) => {
  console.log('Selecting split installment:', item, 'for part', index);
  splitForm.parts[index].installment_id = item.id;
  splitForm.parts[index].bill_item_id = null; // Reset bill_item_id
  splitForm.parts[index].type = 'installment';

  // Kiểm tra và điều chỉnh số tiền nếu vượt quá số tiền còn lại cần thanh toán
  const remainingAmount = item.amount - item.paid_amount;

  // Chỉ tạo phần mới nếu số tiền hiện tại vượt quá số tiền còn lại cần thanh toán
  if (splitForm.parts[index].amount > remainingAmount) {
    const excessAmount = splitForm.parts[index].amount - remainingAmount;
    splitForm.parts[index].amount = remainingAmount;

    // Tạo thêm phần mới cho số tiền dư CHỈ KHI excess amount > 0
    if (excessAmount > 0) {
      // Kiểm tra xem đã có phần nào chưa được phân bổ chưa
      const unallocatedPartIndex = splitForm.parts.findIndex(part =>
        !part.bill_item_id && !part.installment_id
      );

      if (unallocatedPartIndex !== -1 && unallocatedPartIndex !== index) {
        // Nếu đã có phần chưa phân bổ, cập nhật số tiền của phần đó
        splitForm.parts[unallocatedPartIndex].amount += excessAmount;
        splitForm.parts[unallocatedPartIndex].note = 'Số tiền dư từ phân bổ trước';
      } else {
        // Nếu chưa có phần chưa phân bổ, tạo phần mới
        splitForm.parts.push({
          amount: excessAmount,
          type: 'bill_item',
          bill_item_id: null,
          installment_id: null,
          note: 'Số tiền dư từ phân bổ trước'
        });
      }
    }

    // Cập nhật lại tổng số tiền
    recalculateSplitAmounts();
    // Dọn dẹp các phần không cần thiết
    cleanupEmptyParts();
  }
};

// Thêm hàm kiểm tra số tiền tối đa có thể phân bổ cho từng phần
const getMaxAllowedAmountForPart = (part: typeof splitForm.parts[0]) => {
  if (part.type === 'bill_item' && part.bill_item_id) {
    const selectedItem = props.billItems.find(item => item.id === part.bill_item_id);
    if (selectedItem) {
      return selectedItem.amount - selectedItem.paid_amount;
    }
  } else if (part.type === 'installment' && part.installment_id) {
    const selectedItem = props.installments.find(item => item.id === part.installment_id);
    if (selectedItem) {
      return selectedItem.amount - selectedItem.paid_amount;
    }
  }
  return Number.MAX_SAFE_INTEGER; // Không giới hạn nếu chưa chọn
};

// Format số tiền
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount);
};

// Lấy text hiển thị cho loại installment
const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};

// Kiểm tra tổng tiền split
const isSplitAmountValid = computed(() => {
  const allocatedParts = splitForm.parts.filter(part =>
    (part.type === 'bill_item' && part.bill_item_id) ||
    (part.type === 'installment' && part.installment_id)
  );

  const totalPartAmount = allocatedParts.reduce((sum, part) => sum + part.amount, 0);
  const totalAmount = keepOriginalAllocation.value ? splitForm.originalAmount + totalPartAmount : totalPartAmount;

  return Math.abs(totalAmount - props.allocation.amount) <= 1; // Cho phép sai số 1 đồng
});

// Kiểm tra xem có ít nhất một phần hợp lệ không
const hasAtLeastOneValidPart = computed(() => {
  return splitForm.parts.some(part =>
    (part.type === 'bill_item' && part.bill_item_id) ||
    (part.type === 'installment' && part.installment_id)
  );
});

// Submit form
const handleSubmit = () => {
  // Chỉ gửi phần đã chọn dịch vụ/kỳ trả góp
  const validParts = splitForm.parts.filter(part =>
    (part.type === 'bill_item' && part.bill_item_id) ||
    (part.type === 'installment' && part.installment_id)
  );

  emit('submit', {
    keepOriginalAllocation: keepOriginalAllocation.value,
    originalAmount: splitForm.originalAmount,
    parts: validParts
  });
};

// Tự động điều chỉnh giá trị phân bổ để không vượt quá số tiền cần thanh toán
const adjustAllocationAmount = (index: number) => {
  const part = splitForm.parts[index];
  const maxAmount = getMaxAllowedAmountForPart(part);

  if (part.amount > maxAmount) {
    // Nếu số tiền phân bổ vượt quá số tiền cần thanh toán
    const excessAmount = part.amount - maxAmount;

    // Cập nhật số tiền của phần hiện tại
    part.amount = maxAmount;

    // Tìm phần chưa phân bổ hiện có
    const unallocatedPartIndex = splitForm.parts.findIndex(p =>
      !p.bill_item_id && !p.installment_id && index !== splitForm.parts.indexOf(p)
    );

    // Tạo thêm phần mới với số tiền dư
    if (excessAmount > 0) {
      if (unallocatedPartIndex !== -1) {
        // Nếu đã có phần chưa phân bổ, cộng thêm vào đó
        splitForm.parts[unallocatedPartIndex].amount += excessAmount;
      } else {
        // Nếu chưa có, tạo mới một phần
        splitForm.parts.push({
          amount: excessAmount,
          type: 'bill_item',
          bill_item_id: null,
          installment_id: null,
          note: 'Số tiền dư cần phân bổ'
        });
      }
    }

    // Dọn dẹp các phần không cần thiết
    cleanupEmptyParts();
    return true; // Đã có điều chỉnh
  }

  return false; // Không cần điều chỉnh
};

// Watch để tự động điều chỉnh số tiền khi chọn dịch vụ/trả góp
watch(() => splitForm.parts.map(p => ({ id_bill: p.bill_item_id, id_install: p.installment_id })),
  (newVal, oldVal) => {
    let hasAdjustment = false;

    splitForm.parts.forEach((part, index) => {
      if ((part.type === 'bill_item' && part.bill_item_id) ||
        (part.type === 'installment' && part.installment_id)) {
        if (adjustAllocationAmount(index)) {
          hasAdjustment = true;
        }
      }
    });

    if (hasAdjustment) {
      // Nếu có điều chỉnh, cập nhật lại tổng số tiền
      recalculateSplitAmounts();
      cleanupEmptyParts();
    }
  },
  { deep: true }
);

// Kiểm soát số lượng phần hiển thị
watch(() => splitForm.parts.length, () => {
  // Khi số lượng phần thay đổi, kiểm tra xem có cần dọn dẹp không
  cleanupEmptyParts();
});
</script>

<template>
  <div class="space-y-4">
    <div class="mb-4 p-3 bg-blue-50 rounded-lg border">
      <div class="font-medium mb-2">Chia nhỏ phân bổ #{{ allocation.id }}</div>
      <div class="text-sm text-gray-600">
        Chia nhỏ khoản tiền <Money :amount="allocation.amount" class="font-medium" /> thành nhiều phần và phân bổ cho các dịch vụ/kỳ trả góp khác nhau.
      </div>

      <!-- Thông báo nếu số tiền không khớp -->
      <div v-if="!isSplitAmountValid" class="mt-2 text-sm text-red-600 p-2 bg-red-50 rounded">
        Tổng số tiền các phần phải bằng {{ formatCurrency(allocation.amount) }}
      </div>
    </div>

    <!-- Thêm phần lựa chọn giữ allocation gốc -->
    <div class="mb-4 p-4 border rounded-lg">
      <div class="flex items-center mb-3">
        <RadioButton
          v-model="keepOriginalAllocation"
          :value="true"
          inputId="keep-original"
        />
        <label for="keep-original" class="ml-2 font-medium">
          Giữ khoảng thanh toán gốc và phân bổ phần còn lại
        </label>
      </div>

      <div v-if="keepOriginalAllocation" class="ml-6 mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">
          Số tiền giữ lại cho khoảng thanh toán gốc:
        </label>
        <InputNumber
          id="original-amount"
          v-model="splitForm.originalAmount"
          class="w-full"
          :min="0"
          :max="allocation.amount"
          mode="currency"
          currency="VND"
          locale="vi-VN"
          @update:modelValue="updateOriginalAmount"
        />
        <div class="mt-2 text-sm text-gray-600">
          <div class="flex items-center gap-1">
            <i class="pi pi-info-circle"></i>
            <span>Số tiền còn lại để phân bổ:
              <span class="font-medium">{{ formatCurrency(allocation.amount - splitForm.originalAmount) }}</span>
            </span>
          </div>
        </div>
      </div>

      <div class="flex items-center">
        <RadioButton
          v-model="keepOriginalAllocation"
          :value="false"
          inputId="replace-original"
        />
        <label for="replace-original" class="ml-2 font-medium">
          Thay thế allocation gốc bằng các phần mới
        </label>
      </div>
    </div>

    <!-- Danh sách các phần -->
    <div v-for="(part, index) in splitForm.parts" :key="index" class="border rounded-lg overflow-hidden mb-4">
      <div class="bg-gray-50 p-3 border-b flex justify-between items-center">
        <h3 class="font-medium text-gray-900">Phần {{ index + 1 }}</h3>
        <div class="flex items-center gap-2">
          <button
            v-if="splitForm.parts.length > 1"
            @click="removeSplitPart(index)"
            class="p-1 text-red-500 hover:bg-red-50 rounded"
            title="Xóa phần này"
          >
            <i class="pi pi-trash"></i>
          </button>
        </div>
      </div>

      <div class="p-4">
        <!-- Số tiền -->
        <div class="mb-4">
          <label :for="`part-amount-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Số tiền phân bổ</label>
          <InputNumber
            :id="`part-amount-${index}`"
            v-model="part.amount"
            class="w-full"
            :min="0"
            :max="allocation.amount"
            mode="currency"
            currency="VND"
            locale="vi-VN"
            @update:modelValue="updateLastPartAmount"
          />
        </div>

        <!-- Loại phân bổ -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Loại phân bổ</label>
          <div class="flex gap-4">
            <div class="flex items-center">
              <RadioButton
                v-model="part.type"
                value="bill_item"
                :inputId="`part-type-bill-${index}`"
              />
              <label :for="`part-type-bill-${index}`" class="ml-2">Dịch vụ</label>
            </div>
            <div class="flex items-center">
              <RadioButton
                v-model="part.type"
                value="installment"
                :inputId="`part-type-installment-${index}`"
              />
              <label :for="`part-type-installment-${index}`" class="ml-2">Trả góp</label>
            </div>
          </div>
        </div>

        <!-- Bill Items Selection -->
        <Accordion v-if="part.type === 'bill_item'" class="mb-4">
          <AccordionTab header="Chọn dịch vụ">
            <div v-if="selectedBillItemIds.length > 0" class="text-xs text-gray-500 mt-1 mb-2 px-3">
              <div class="flex items-center gap-1">
                <i class="pi pi-info-circle"></i>
                <span>Một số dịch vụ đã được chọn trong các phần khác và không hiển thị ở đây.</span>
              </div>
            </div>

            <div class="overflow-auto max-h-48">
              <div v-if="getAvailableBillItemsForPart(index).length === 0" class="p-4 text-center text-gray-500">
                Không có dịch vụ nào khả dụng
              </div>

              <div
                v-for="item in getAvailableBillItemsForPart(index)"
                :key="`split-bill-${index}-${item.id}`"
                :class="[
                  'flex items-center gap-3 p-3 text-sm border-b cursor-pointer hover:bg-gray-50 transition-colors',
                  part.bill_item_id === item.id ? 'bg-blue-50' : ''
                ]"
                @click="selectSplitBillItem(index, item)"
              >
                <RadioButton
                  :modelValue="part.bill_item_id"
                  :value="item.id"
                  :inputId="`split-bill-${index}-${item.id}`"
                  @click.stop
                />
                <div class="flex-1">
                  <div class="font-medium text-primary">
                    <AttachmentTitle :attachment="item.attachment" />
                  </div>
                  <div class="flex justify-between items-center mt-1">
                    <div class="text-xs text-gray-500">
                      <Money :amount="item.amount" /> | Còn lại: <Money :amount="item.amount - item.paid_amount" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionTab>
        </Accordion>

        <!-- Installments Selection -->
        <Accordion v-if="part.type === 'installment'" class="mb-4">
          <AccordionTab header="Chọn kỳ trả góp">
            <div v-if="selectedInstallmentIds.length > 0" class="text-xs text-gray-500 mt-1 mb-2 px-3">
              <div class="flex items-center gap-1">
                <i class="pi pi-info-circle"></i>
                <span>Một số kỳ trả góp đã được chọn trong các phần khác và không hiển thị ở đây.</span>
              </div>
            </div>

            <div class="overflow-auto max-h-48">
              <div v-if="getAvailableInstallmentsForPart(index).length === 0" class="p-4 text-center text-gray-500">
                Không có kỳ trả góp nào khả dụng
              </div>

              <div
                v-for="item in getAvailableInstallmentsForPart(index)"
                :key="`split-install-${index}-${item.id}`"
                :class="[
                  'flex items-center gap-3 p-3 text-sm border-b cursor-pointer hover:bg-gray-50 transition-colors',
                  part.installment_id === item.id ? 'bg-blue-50' : ''
                ]"
                @click="selectSplitInstallment(index, item)"
              >
                <RadioButton
                  :modelValue="part.installment_id"
                  :value="item.id"
                  :inputId="`split-install-${index}-${item.id}`"
                  @click.stop
                />
                <div class="flex-1">
                  <div class="font-medium text-primary">
                    {{ item.name }}
                  </div>
                  <div class="flex justify-between items-center mt-1">
                    <div class="text-xs text-gray-500">
                      {{ getInstallmentKindText(item.kind) }} #{{ item.installment_number }} |
                      <Money :amount="item.amount" /> | Còn lại: <Money :amount="item.amount - item.paid_amount" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionTab>
        </Accordion>

        <!-- Ghi chú -->
        <div class="mb-2">
          <label :for="`part-note-${index}`" class="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
          <InputText :id="`part-note-${index}`" v-model="part.note" class="w-full" placeholder="Thêm ghi chú" />
        </div>

        <!-- Thông báo nếu chưa chọn bill_item/installment -->
        <div
          v-if="(part.type === 'bill_item' && !part.bill_item_id) || (part.type === 'installment' && !part.installment_id)"
          class="text-sm text-orange-600"
        >
          Vui lòng chọn {{ part.type === 'bill_item' ? 'dịch vụ' : 'kỳ trả góp' }}
        </div>

        <!-- Thông báo nếu số tiền vượt quá số tiền còn lại cần thanh toán -->
        <div
          v-if="(part.type === 'bill_item' && part.bill_item_id && part.amount > getMaxAllowedAmountForPart(part)) ||
               (part.type === 'installment' && part.installment_id && part.amount > getMaxAllowedAmountForPart(part))"
          class="mt-2 text-sm text-orange-600 p-2 bg-orange-50 rounded"
        >
          <div class="flex items-center gap-1">
            <i class="pi pi-exclamation-triangle"></i>
            <span>Số tiền phân bổ vượt quá số tiền còn lại cần thanh toán
              (<Money :amount="getMaxAllowedAmountForPart(part)" class="font-medium" />)</span>
          </div>
          <div class="mt-1">
            Hệ thống sẽ tự động điều chỉnh số tiền và tạo thêm phần mới cho số tiền dư.
          </div>
        </div>
      </div>
    </div>

    <!-- Nút thêm phần -->
    <div class="text-center mb-4">
      <Button
        icon="pi pi-plus"
        label="Thêm phần"
        severity="secondary"
        outlined
        @click="addSplitPart"
      />
    </div>

    <!-- Nút tiếp tục -->
    <div class="flex justify-end">
      <Button
        label="Tiếp tục"
        :disabled="!isSplitAmountValid || !hasAtLeastOneValidPart"
        @click="handleSubmit"
      />
    </div>
  </div>
</template>
