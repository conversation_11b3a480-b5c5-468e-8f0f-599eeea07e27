<script lang="ts" setup>
import RadioButton from "primevue/radiobutton";

import type { BillItemResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import State from "@/base-components/State.vue";

const props = defineProps<{
  billItems: BillItemResponse[];
  selectedItemId: number | null;
}>();

const emit = defineEmits<{
  (e: "update", data: { type: string; id: number }): void;
}>();

const selectBillItem = (item: BillItemResponse) => {
  emit("update", { type: "bill_item_selected", id: item.id });
};
</script>

<template>
  <div class="max-h-80 overflow-auto">
    <div v-if="billItems.length === 0" class="p-4 text-center text-gray-500">
      Không có bill item nào kh<PERSON> dụng
    </div>

    <!-- Table Header -->
    <div
      class="grid grid-cols-6 border-b bg-gray-50 px-3 py-2 text-xs font-medium uppercase text-gray-500"
    >
      <div class="col-span-1 flex items-center justify-center">
        <i class="pi pi-check text-xs text-gray-400"></i>
      </div>
      <div class="col-span-2">Tên</div>
      <div class="text-right">Thành tiền</div>
      <div class="text-right">Đã trả</div>
      <div class="text-right">Còn lại</div>
    </div>

    <!-- Bill Items -->
    <div
      v-for="item in billItems"
      :key="`bill-${item.id}`"
      :class="[
        'grid cursor-pointer grid-cols-6 items-center gap-3 border-b px-3 py-3 text-sm transition-colors hover:bg-gray-50',
        selectedItemId === item.id ? 'bg-blue-50' : '',
      ]"
      @click="selectBillItem(item)"
    >
      <div class="col-span-1 flex justify-center">
        <RadioButton
          :modelValue="selectedItemId"
          :value="item.id"
          :inputId="`bill-item-${item.id}`"
          @click.stop
        />
      </div>
      <div class="col-span-2">
        <div class="font-medium text-primary">
          <AttachmentTitle :attachment="item.attachment" />
        </div>
        <div class="mt-1 flex items-center">
          <State v-if="!item.bill_id" state="new" class="mr-1" />
          <DateTime :time="item.created_at" class="text-xs text-gray-500" />
        </div>
      </div>
      <div class="text-right">
        <Money :amount="item.amount" />
      </div>
      <div class="text-right">
        <Money :amount="item.paid_amount" variant="default" />
      </div>
      <div class="text-right font-medium">
        <Money :amount="item.amount - item.paid_amount" variant="warning" />
      </div>
    </div>
  </div>
</template>
