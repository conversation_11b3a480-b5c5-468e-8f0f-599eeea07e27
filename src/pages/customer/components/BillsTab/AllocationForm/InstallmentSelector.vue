<script lang="ts" setup>
import RadioButton from "primevue/radiobutton";

import type { InstallmentResponse } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";

const props = defineProps<{
  installments: InstallmentResponse[];
  selectedItemId: number | null;
}>();

const emit = defineEmits<{
  (e: "update", data: { type: string; id: number }): void;
}>();

const selectInstallment = (item: InstallmentResponse) => {
  emit("update", { type: "installment_selected", id: item.id });
};

// Lấy text hiển thị cho loại installment
const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};
</script>

<template>
  <div class="max-h-80 overflow-auto">
    <div v-if="installments.length === 0" class="p-4 text-center text-gray-500">
      <PERSON><PERSON><PERSON><PERSON> c<PERSON> kỳ trả góp nào khả dụng
    </div>

    <!-- Table Header -->
    <div
      class="grid grid-cols-6 border-b bg-gray-50 px-3 py-2 text-xs font-medium uppercase text-gray-500"
    >
      <div class="col-span-1 flex items-center justify-center">
        <i class="pi pi-check text-xs text-gray-400"></i>
      </div>
      <div class="col-span-2">Tên</div>
      <div class="text-right">Thành tiền</div>
      <div class="text-right">Đã trả</div>
      <div class="text-right">Còn lại</div>
    </div>

    <!-- Installment Items -->
    <div
      v-for="item in installments"
      :key="`installment-${item.id}`"
      :class="[
        'grid cursor-pointer grid-cols-6 items-center gap-3 border-b px-3 py-3 text-sm transition-colors hover:bg-gray-50',
        selectedItemId === item.id ? 'bg-blue-50' : '',
      ]"
      @click="selectInstallment(item)"
    >
      <div class="col-span-1 flex justify-center">
        <RadioButton
          :modelValue="selectedItemId"
          :value="item.id"
          :inputId="`installment-${item.id}`"
          @click.stop
        />
      </div>
      <div class="col-span-2">
        <div class="font-medium text-primary">
          {{ item.name }}
        </div>
        <div class="mt-1 text-xs text-gray-500">
          {{ getInstallmentKindText(item.kind) }} #{{ item.installment_number }}
        </div>
      </div>
      <div class="text-right">
        <Money :amount="item.amount" />
      </div>
      <div class="text-right">
        <Money :amount="item.paid_amount" variant="default" />
      </div>
      <div class="text-right font-medium">
        <Money :amount="item.amount - item.paid_amount" variant="warning" />
      </div>
    </div>
  </div>
</template>
