<script lang="ts" setup>
import RadioButton from "primevue/radiobutton";
import { computed } from "vue";

import type { BillItemResponse, InstallmentResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";

const props = defineProps<{
  selectedBillItem: BillItemResponse | undefined;
  selectedInstallment: InstallmentResponse | undefined;
  selectedType: "bill_item" | "installment";
}>();

const emit = defineEmits<{
  (e: "updateType", type: "bill_item" | "installment"): void;
}>();

// Tạo computed property cho selectedType để xử lý two-way binding
const currentType = computed({
  get: () => props.selectedType,
  set: (value: "bill_item" | "installment") => {
    emit("updateType", value);
  },
});

// Format số tiền
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
  }).format(amount);
};

// Lấy text hiển thị cho loại installment
const getInstallmentKindText = (kind: string) => {
  switch (kind) {
    case "down_payment":
      return "Trả trước";
    case "sequence_payment":
      return "Trả góp";
    default:
      return kind;
  }
};
</script>

<template>
  <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
    <h4 class="mb-2 text-sm font-semibold text-blue-900">Dịch vụ đã chọn</h4>

    <div v-if="selectedBillItem" class="mb-3">
      <div class="flex items-center">
        <RadioButton
          v-model="currentType"
          value="bill_item"
          :inputId="`summary-bill-item-${selectedBillItem.id}`"
        />
        <label
          :for="`summary-bill-item-${selectedBillItem.id}`"
          class="ml-2 font-medium text-blue-800"
        >
          Bill Item
        </label>
      </div>

      <div class="ml-6 mt-2 rounded border border-blue-100 bg-white p-3">
        <div>
          <div class="font-medium">
            <AttachmentTitle :attachment="selectedBillItem.attachment" />
          </div>
          <div class="mt-1 flex flex-col gap-1">
            <div>Đơn giá: {{ formatCurrency(selectedBillItem.attachment.price) }}</div>
            <div>Số lượng: {{ selectedBillItem.attachment.quantity }}</div>
          </div>
          <div class="mt-2 flex justify-between">
            <div>Còn lại:</div>
            <div class="font-bold text-blue-700">
              {{ formatCurrency(selectedBillItem.amount - selectedBillItem.paid_amount) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="selectedInstallment" class="mb-3">
      <div class="flex items-center">
        <RadioButton
          v-model="currentType"
          value="installment"
          :inputId="`summary-installment-${selectedInstallment.id}`"
        />
        <label
          :for="`summary-installment-${selectedInstallment.id}`"
          class="ml-2 font-medium text-blue-800"
        >
          Trả góp
        </label>
      </div>

      <div class="ml-6 mt-2 rounded border border-blue-100 bg-white p-3">
        <div>
          <div class="font-medium">
            {{ selectedInstallment.name }}
          </div>
          <div class="mt-1 flex flex-col gap-1">
            <div>
              {{ getInstallmentKindText(selectedInstallment.kind) }} #{{
                selectedInstallment.installment_number
              }}
            </div>
            <div>Số tiền: {{ formatCurrency(selectedInstallment.amount) }}</div>
            <div>Đã trả: {{ formatCurrency(selectedInstallment.paid_amount) }}</div>
          </div>
          <div class="mt-2 flex justify-between">
            <div>Còn lại:</div>
            <div class="font-bold text-blue-700">
              {{ formatCurrency(selectedInstallment.amount - selectedInstallment.paid_amount) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!selectedBillItem && !selectedInstallment" class="py-3 text-center text-gray-500">
      Vui lòng chọn bill item hoặc kỳ trả góp
    </div>
  </div>
</template>
