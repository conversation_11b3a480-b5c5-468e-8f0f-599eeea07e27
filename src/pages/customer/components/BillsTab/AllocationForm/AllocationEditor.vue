<script lang="ts" setup>
import Button from 'primevue/button';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import { computed, onMounted,reactive, ref, watch } from 'vue';

import type {
  BillItemResponse,
  InstallmentResponse,
  PaymentAllocationAddRequest,
  PaymentAllocationDeleteRequest,
  PaymentAllocationResponse,
  PaymentAllocationUpdateRequest} from '@/api/bcare-types-v2';
import Money from "@/base-components/Money.vue";
import useEconomy from '@/hooks/useEconomy';

import BillItemSelector from './BillItemSelector.vue';
import ConfirmationDialog from './ConfirmationDialog.vue';
import InstallmentSelector from './InstallmentSelector.vue';
import SelectionSummary from './SelectionSummary.vue';
import SplitAllocationForm from './SplitAllocationForm.vue';

const props = defineProps<{
  allocation: PaymentAllocationResponse;
  billItems: BillItemResponse[];
  installments: InstallmentResponse[];
  selectedItemIds: { type: "bill_item" | "installment" | "installment_plan"; id: number }[];
  show: boolean;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'updated', allocation: PaymentAllocationResponse): void;
}>();

// Sử dụng useEconomy
const { updatePaymentAllocation, addPaymentAllocation, deletePaymentAllocation, isLoading: apiLoading } = useEconomy();

const visible = ref(props.show);
const loading = ref(false);
// Xác định loại phân bổ ban đầu dựa vào allocation hiện tại
const selectedType = ref<'bill_item' | 'installment'>(
  props.allocation.bill_item !== null && props.allocation.bill_item !== undefined ? 'bill_item' : 'installment'
);
const editMode = ref<'update' | 'split'>('update');

// Thêm các biến mới cho dialog xác nhận
const showConfirmation = ref(false);
const confirmationMode = ref<'update' | 'split'>('update');
const confirmationData = reactive({
  original: null as PaymentAllocationResponse | null,
  updated: null as any,
  splitParts: [] as any[]
});

// Debug info
const showDebugInfo = ref(false); // Thêm flag để hiển thị debug info

// Form cho chế độ update
const form = reactive({
  bill_item_id: props.allocation.bill_item?.id || null as number | null,
  installment_id: props.allocation.installment?.id || null as number | null,
  note: props.allocation.note || ''
});

// Ghi log thông tin debug khi mount component
onMounted(() => {
  console.log('Component mounted with allocation:', props.allocation);
  console.log('Initial selectedType:', selectedType.value);
  console.log('Initial form values:', form);
  console.log('Available bill items:', props.billItems);
  console.log('Available installments:', props.installments);
  console.log('Selected item IDs:', props.selectedItemIds);
});

// Watch sự thay đổi của props.show để cập nhật visible
watch(() => props.show, (newValue) => {
  visible.value = newValue;
});

// Watch sự thay đổi của props.allocation để cập nhật form
watch(() => props.allocation, (newValue) => {
  console.log('Allocation changed:', newValue);

  // Cập nhật form
  form.bill_item_id = newValue.bill_item?.id || null;
  form.installment_id = newValue.installment?.id || null;
  form.note = newValue.note || '';

  // Cập nhật loại phân bổ
  selectedType.value = newValue.bill_item !== null && newValue.bill_item !== undefined
    ? 'bill_item'
    : 'installment';

  console.log('Updated selectedType:', selectedType.value);
  console.log('Updated form:', form);
}, { immediate: true });

// Thêm computed properties để tính toán số tiền tối đa có thể phân bổ cho bill_item hoặc installment đã chọn
const maxAllowedAmount = computed(() => {
  if (selectedType.value === 'bill_item' && form.bill_item_id) {
    const selectedItem = availableBillItems.value.find(item => item.id === form.bill_item_id);
    if (selectedItem) {
      return selectedItem.amount - selectedItem.paid_amount;
    }
  } else if (selectedType.value === 'installment' && form.installment_id) {
    const selectedItem = availableInstallments.value.find(item => item.id === form.installment_id);
    if (selectedItem) {
      return selectedItem.amount - selectedItem.paid_amount;
    }
  }
  return props.allocation.amount; // Mặc định trả về toàn bộ số tiền của allocation
});

// Thêm biến để kiểm tra xem số tiền phân bổ có hợp lệ không
const isOverAllocated = computed(() => {
  return props.allocation.amount > maxAllowedAmount.value;
});

// Hàm để chuyển đổi loại phân bổ
const setSelectedType = (type: 'bill_item' | 'installment') => {
  console.log('Changing selectedType from', selectedType.value, 'to', type);

  if (type === 'bill_item') {
    form.installment_id = null;
  } else if (type === 'installment') {
    form.bill_item_id = null;
  }

  selectedType.value = type;
  console.log('Form after type change:', form);
};

// Thêm hàm để xử lý hiển thị xác nhận trước khi lưu
const showConfirmationDialog = (mode: 'update' | 'split', data: any) => {
  console.log('showConfirmationDialog called with mode:', mode, 'and data:', data);
  confirmationMode.value = mode;

  // Chuẩn bị dữ liệu xác nhận chung
  confirmationData.original = props.allocation;

  if (mode === 'update') {
    // Chuẩn bị dữ liệu xác nhận cho chế độ update
    confirmationData.updated = {
      ...props.allocation,
      bill_item: selectedBillItem.value || null,
      installment: selectedInstallment.value || null,
      bill_item_id: form.bill_item_id,
      installment_id: form.installment_id,
      note: form.note
    };
  } else if (mode === 'split') {
    // Chuẩn bị dữ liệu xác nhận cho chế độ split từ dữ liệu truyền vào
    if (data && data.splitParts) {
      confirmationData.splitParts = [...data.splitParts]; // Sử dụng spread operator để tạo bản sao mới
      console.log('Split parts assigned to confirmationData:', confirmationData.splitParts);
    } else {
      console.error('Invalid split data provided:', data);
      confirmationData.splitParts = [];
    }
  }

  console.log('confirmationData prepared:', confirmationData);
  showConfirmation.value = true;
  console.log('showConfirmation set to:', showConfirmation.value);
};

// Xử lý cập nhật allocation
const handleUpdateAllocation = async () => {
  try {
    loading.value = true;
    console.log('Updating allocation with form:', form);

    // Tạo danh sách các trường được sửa đổi
    const modified: string[] = [];

    // Kiểm tra xem bill_item_id có thay đổi không
    if (form.bill_item_id !== props.allocation.bill_item?.id) {
      modified.push('bill_item_id');
    }

    // Kiểm tra xem installment_id có thay đổi không
    if (form.installment_id !== props.allocation.installment?.id) {
      modified.push('installment_id');
    }

    // Kiểm tra xem note có thay đổi không
    if (form.note !== props.allocation.note) {
      modified.push('note');
    }

    console.log('Modified fields:', modified);

    // Tạo request payload cho API cập nhật
    const updateRequest: PaymentAllocationUpdateRequest = {
      id: props.allocation.id,
      bill_item_id: form.bill_item_id || undefined,
      installment_id: form.installment_id || undefined,
      note: form.note || undefined,
      modified: modified
    };

    console.log('Update request:', updateRequest);

    // Gọi API qua useEconomy
    const updatedAllocation = await updatePaymentAllocation(updateRequest);

    console.log('Allocation updated:', updatedAllocation);
    console.log('Thành công: Đã cập nhật phân bổ thanh toán');

    // Emit kết quả cập nhật
    if (updatedAllocation) {
      emit('updated', updatedAllocation);
    }
    emit('close');
  } catch (error) {
    console.error('Error updating allocation:', error);
    console.log('Lỗi: Không thể cập nhật phân bổ thanh toán');
  } finally {
    loading.value = false;
  }
};

// Bổ sung hàm để xử lý phân bổ một phần nếu cần
const handlePartialAllocation = async () => {
  try {
    loading.value = true;
    console.log('Updating allocation with partial amount:', maxAllowedAmount.value);

    // Tạo danh sách các trường được sửa đổi
    const modified: string[] = ['amount'];

    // Kiểm tra bill_item_id hoặc installment_id có thay đổi không
    if (form.bill_item_id !== props.allocation.bill_item?.id) {
      modified.push('bill_item_id');
    }
    if (form.installment_id !== props.allocation.installment?.id) {
      modified.push('installment_id');
    }
    if (form.note !== props.allocation.note) {
      modified.push('note');
    }

    // Tạo request payload cho API cập nhật
    const updateRequest: PaymentAllocationUpdateRequest = {
      id: props.allocation.id,
      amount: maxAllowedAmount.value, // Chỉ phân bổ tối đa số tiền còn lại của bill_item/installment
      bill_item_id: form.bill_item_id || undefined,
      installment_id: form.installment_id || undefined,
      note: form.note || undefined,
      modified: modified
    };

    console.log('Partial update request:', updateRequest);

    // Gọi API qua useEconomy
    const updatedAllocation = await updatePaymentAllocation(updateRequest);

    console.log('Allocation updated with partial amount:', updatedAllocation);

    // Nếu còn tiền thừa, có thể thông báo hoặc xử lý tiếp (tùy vào yêu cầu)
    const remainingAmount = props.allocation.amount - maxAllowedAmount.value;
    if (remainingAmount > 0) {
      console.log(`Còn ${remainingAmount} VND chưa được phân bổ`);
      // Có thể thêm logic xử lý phần tiền còn lại ở đây nếu cần
    }

    // Emit kết quả cập nhật
    if (updatedAllocation) {
      emit('updated', updatedAllocation);
    }
    emit('close');
  } catch (error) {
    console.error('Error updating allocation:', error);
    console.log('Lỗi: Không thể cập nhật phân bổ thanh toán');
  } finally {
    loading.value = false;
  }
};

// Xử lý chia nhỏ allocation
const handleSplitAllocation = async (splitData: any) => {
  try {
    loading.value = true;

    // Xử lý allocation gốc
    let updatedOriginalAllocation = null;

    if (splitData.keepOriginalAllocation) {
      // Cập nhật allocation gốc với số tiền mới
      const updateRequest: PaymentAllocationUpdateRequest = {
        id: props.allocation.id,
        amount: splitData.originalAmount,
        modified: ['amount']
      };

      updatedOriginalAllocation = await updatePaymentAllocation(updateRequest);
      console.log('Original allocation updated:', updatedOriginalAllocation);
    } else {
      // Xóa allocation hiện tại nếu không giữ lại
      const deleteRequest: PaymentAllocationDeleteRequest = {
        id: props.allocation.id
      };

      await deletePaymentAllocation(deleteRequest);
      console.log('Original allocation deleted');
    }

    // Thêm các allocation mới (chỉ phần hợp lệ)
    const newAllocations = [];
    for (const part of splitData.parts) {
      if ((part.type === 'bill_item' && part.bill_item_id) ||
        (part.type === 'installment' && part.installment_id)) {
        const addRequest: PaymentAllocationAddRequest = {
          payment_id: props.allocation.payment_id,
          amount: part.amount,
          note: part.note || undefined
        };

        if (part.type === 'bill_item' && part.bill_item_id) {
          addRequest.bill_item_id = part.bill_item_id;
        } else if (part.type === 'installment' && part.installment_id) {
          addRequest.installment_id = part.installment_id;
        }

        console.log('Adding new allocation part:', addRequest);
        const newAllocation = await addPaymentAllocation(addRequest);
        if (newAllocation) {
          newAllocations.push(newAllocation);
        }
      }
    }

    console.log('Allocations split:', newAllocations);
    console.log('Thành công: Đã chia nhỏ phân bổ thanh toán');

    // Emit kết quả cập nhật
    if (splitData.keepOriginalAllocation && updatedOriginalAllocation) {
      emit('updated', updatedOriginalAllocation);
    } else if (newAllocations.length > 0) {
      emit('updated', newAllocations[0]);
    }
    emit('close');
  } catch (error) {
    console.error('Error splitting allocation:', error);
    console.log('Lỗi: Không thể chia nhỏ phân bổ thanh toán');
  } finally {
    loading.value = false;
  }
};

// Xác nhận lưu thay đổi sau khi review
const confirmSave = async () => {
  showConfirmation.value = false;

  try {
    loading.value = true;

    if (confirmationMode.value === 'update') {
      if (isOverAllocated.value) {
        await handlePartialAllocation();
      } else {
        await handleUpdateAllocation();
      }
    } else if (confirmationMode.value === 'split') {
      // Lấy dữ liệu từ confirmationData để gửi đến handleSplitAllocation
      const splitData = {
        keepOriginalAllocation: confirmationData.splitParts.some(part => part.isOriginal),
        originalAmount: confirmationData.splitParts.find(part => part.isOriginal)?.amount || 0,
        parts: confirmationData.splitParts
          .filter(part => !part.isOriginal)
          .map(part => ({
            type: part.bill_item ? 'bill_item' : 'installment',
            bill_item_id: part.bill_item?.id || null,
            installment_id: part.installment?.id || null,
            amount: part.amount,
            note: part.note || ''
          }))
      };

      await handleSplitAllocation(splitData);
    }
  } catch (error) {
    console.error('Error saving allocation:', error);
  } finally {
    loading.value = false;
  }
};

// Hủy xác nhận
const cancelConfirmation = () => {
  showConfirmation.value = false;
};

// Hàm xử lý khi nhận sự kiện từ component con
const handleUpdateData = (data: any) => {
  if (data.type === 'bill_item_selected') {
    form.bill_item_id = data.id;
    form.installment_id = null; // Reset installment_id
    selectedType.value = 'bill_item';
  } else if (data.type === 'installment_selected') {
    form.installment_id = data.id;
    form.bill_item_id = null; // Reset bill_item_id
    selectedType.value = 'installment';
  }
};

// Xử lý sự kiện từ SplitAllocationForm
const handleSplitFormSubmit = (data: any) => {
  console.log('handleSplitFormSubmit called with data:', data);

  // Truyền dữ liệu đến showConfirmationDialog
  const splitParts = [];

  // Phần giữ lại (nếu có)
  if (data.keepOriginalAllocation) {
    splitParts.push({
      id: props.allocation.id,
      amount: data.originalAmount,
      bill_item: props.allocation.bill_item,
      installment: props.allocation.installment,
      note: props.allocation.note,
      isOriginal: true
    });
  }

  // Các phần mới
  data.parts.forEach((part: any) => {
    if ((part.type === 'bill_item' && part.bill_item_id) ||
      (part.type === 'installment' && part.installment_id)) {
      const newPart = {
        amount: part.amount,
        note: part.note,
        isOriginal: false
      };

      if (part.type === 'bill_item' && part.bill_item_id) {
        const billItem = availableBillItems.value.find(item => item.id === part.bill_item_id);
        if (billItem) {
          newPart.bill_item = billItem;
        }
      } else if (part.type === 'installment' && part.installment_id) {
        const installment = availableInstallments.value.find(item => item.id === part.installment_id);
        if (installment) {
          newPart.installment = installment;
        }
      }

      splitParts.push(newPart);
    }
  });

  console.log('splitParts prepared:', splitParts);
  if (splitParts.length === 0) {
    console.error('No valid split parts generated!');
    // Có thể hiển thị thông báo lỗi ở đây
    return;
  }

  showConfirmationDialog('split', { splitParts });
};

// Sửa lại hàm handleSubmit để xử lý trường hợp phân bổ một phần
const handleSubmit = () => {
  console.log('handleSubmit called with editMode:', editMode.value);

  if (editMode.value === 'update') {
    console.log('Calling showConfirmationDialog with mode update');
    showConfirmationDialog('update', null);
  }
};

const handleClose = () => {
  emit('close');
};

// Danh sách bill items có sẵn
const availableBillItems = computed(() => {
  // Lọc ra các bill item có thể phân bổ
  const filtered = props.billItems.filter(billItem => {
    // Kiểm tra xem bill item có đủ số tiền còn lại để phân bổ không
    const remainingAmount = billItem.amount - billItem.paid_amount;
    return remainingAmount > 0;
  });

  console.log('Available bill items:', filtered);
  return filtered;
});

// Danh sách installments có sẵn
const availableInstallments = computed(() => {
  // Lấy danh sách tất cả các allocation khác (không bao gồm allocation hiện tại)
  const otherAllocations = props.installments.flatMap(inst =>
    inst.allocations?.filter(alloc => alloc.id !== props.allocation.id) || []
  );

  console.log('Other allocations:', otherAllocations);

  // Lọc ra các installment chưa được phân bổ cho allocation khác
  // hoặc đã được phân bổ nhưng có thể phân bổ thêm
  const filtered = props.installments.filter(installment => {
    // Kiểm tra xem installment có đủ số tiền còn lại để phân bổ không
    const remainingAmount = installment.amount - installment.paid_amount;
    return remainingAmount > 0;
  });

  console.log('Available installments:', filtered);
  return filtered.sort((a, b) => a.installment_number - b.installment_number);
});

// Mục bill item đã chọn
const selectedBillItem = computed(() =>
  availableBillItems.value.find(item => item.id === form.bill_item_id)
);

// Mục installment đã chọn
const selectedInstallment = computed(() =>
  availableInstallments.value.find(item => item.id === form.installment_id)
);

// Thêm computed property để biết liệu có thể tiếp tục hay không
const canProceed = computed(() => {
  if (editMode.value === 'update') {
    // Nếu đang ở chế độ update, kiểm tra xem đã chọn bill_item hoặc installment chưa
    return (form.bill_item_id || form.installment_id);
  }
  return false;
});
</script>

<template>
  <!-- Dialog xác nhận trước khi cập nhật -->
  <ConfirmationDialog
    :visible="showConfirmation"
    @update:visible="showConfirmation = $event"
    :confirmationMode="confirmationMode"
    :confirmationData="confirmationData"
    :isOverAllocated="isOverAllocated"
    :maxAllowedAmount="maxAllowedAmount"
    :loading="loading || apiLoading"
    @confirm="confirmSave"
    @cancel="cancelConfirmation"
  />

  <Dialog
    v-model:visible="visible"
    modal
    :style="{ width: '850px' }"
    :header="`Chỉnh sửa phân bổ #${allocation.id}`"
    :closable="false"
    position="center"
  >
    <!-- Debug information - có thể bật tắt bởi người dùng-->
    <div v-if="showDebugInfo" class="mb-4 p-2 bg-gray-100 text-xs overflow-auto max-h-40 rounded">
      <div class="font-bold">Debug Info:</div>
      <div>selectedType: {{ selectedType }}</div>
      <div>form.bill_item_id: {{ form.bill_item_id }}</div>
      <div>form.installment_id: {{ form.installment_id }}</div>
      <div>availableInstallments.length: {{ availableInstallments.length }}</div>
      <div>availableBillItems.length: {{ availableBillItems.length }}</div>
      <div>allocation: {{ JSON.stringify(allocation) }}</div>
      <div>selectedItemIds: {{ JSON.stringify(selectedItemIds) }}</div>
      <div>maxAllowedAmount: {{ maxAllowedAmount }}</div>
      <div>isOverAllocated: {{ isOverAllocated }}</div>
      <div>showConfirmation: {{ showConfirmation }}</div>
      <div>confirmationMode: {{ confirmationMode }}</div>
      <div>editMode: {{ editMode }}</div>
    </div>

    <div class="mb-2 text-right">
      <button @click="showDebugInfo = !showDebugInfo" class="text-xs text-gray-500 hover:text-gray-700">
        {{ showDebugInfo ? "Ẩn" : "Hiện" }} thông tin debug
      </button>
    </div>

    <!-- Thông tin số tiền đã phân bổ -->
    <div class="mb-4 p-3 bg-gray-50 rounded-lg border flex items-center justify-between">
      <div class="text-sm text-gray-600">Số tiền đã phân bổ:</div>
      <div class="font-medium text-base">
        <Money :amount="allocation.amount" />
      </div>
    </div>

    <!-- Tabs cho chế độ chỉnh sửa -->
    <div class="mb-4 flex border-b">
      <button
        :class="[
          'py-2 px-4 border-b-2 font-medium text-sm transition-colors',
          editMode === 'update'
            ? 'border-primary text-primary'
            : 'border-transparent text-gray-500 hover:text-gray-700'
        ]"
        @click="editMode = 'update'"
      >
        Thay đổi Dịch vụ / Phí niềng
      </button>
      <button
        :class="[
          'py-2 px-4 border-b-2 font-medium text-sm transition-colors',
          editMode === 'split'
            ? 'border-primary text-primary'
            : 'border-transparent text-gray-500 hover:text-gray-700'
        ]"
        @click="editMode = 'split'"
      >
        Chia nhỏ khoảng thanh toán
      </button>
    </div>

    <!-- Thêm cảnh báo nếu số tiền phân bổ vượt quá số tiền còn lại của bill_item/installment đã chọn -->
    <div v-if="editMode === 'update' && isOverAllocated" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg mb-4 text-sm">
      <div class="flex items-center gap-2">
        <i class="pi pi-exclamation-triangle text-yellow-500"></i>
        <div class="text-yellow-700 font-medium">Cảnh báo phân bổ vượt mức</div>
      </div>
      <div class="mt-1 text-yellow-600">
        Số tiền phân bổ (<Money :amount="allocation.amount" class="font-medium" />) vượt quá số tiền còn lại cần thanh toán
        (<Money :amount="maxAllowedAmount" class="font-medium" />).
      </div>
      <div class="mt-2">
        Nếu tiếp tục, chỉ <Money :amount="maxAllowedAmount" class="font-medium" /> sẽ được phân bổ.
        Số tiền còn lại <Money :amount="allocation.amount - maxAllowedAmount" class="font-medium" /> sẽ không được phân bổ.
      </div>
    </div>

    <!-- Chế độ cập nhật -->
    <div v-if="editMode === 'update'" class="space-y-4">
      <div class="mb-4 border rounded-lg overflow-hidden">
        <div class="bg-gray-50 p-3 border-b">
          <div class="flex justify-between items-center">
            <h3 class="font-medium text-gray-900">Chọn mục phân bổ</h3>
            <div class="flex items-center gap-2">
              <button
                :class="[
                  'py-2 px-4 rounded-lg font-medium text-sm transition-colors',
                  selectedType === 'bill_item'
                    ? 'bg-primary text-white'
                    : 'text-gray-600 bg-white border hover:bg-gray-50'
                ]"
                @click="setSelectedType('bill_item')"
              >
                Dịch vụ
              </button>
              <button
                :class="[
                  'py-2 px-4 rounded-lg font-medium text-sm transition-colors',
                  selectedType === 'installment'
                    ? 'bg-primary text-white'
                    : 'text-gray-600 bg-white border hover:bg-gray-50'
                ]"
                @click="setSelectedType('installment')"
              >
                Trả góp
              </button>
            </div>
          </div>
        </div>

        <!-- Phần chọn Bill Items -->
        <BillItemSelector
          v-if="selectedType === 'bill_item'"
          :billItems="availableBillItems"
          :selectedItemId="form.bill_item_id"
          @update="handleUpdateData"
        />

        <!-- Phần chọn Installments -->
        <InstallmentSelector
          v-if="selectedType === 'installment'"
          :installments="availableInstallments"
          :selectedItemId="form.installment_id"
          @update="handleUpdateData"
        />
      </div>

      <!-- Current Selection Summary -->
      <SelectionSummary
        :selectedBillItem="selectedBillItem"
        :selectedInstallment="selectedInstallment"
        :selectedType="selectedType"
        @updateType="setSelectedType"
      />

      <!-- Thêm trường ghi chú -->
      <div class="mb-4">
        <label for="note" class="block text-sm font-medium text-gray-700 mb-1">Ghi chú</label>
        <InputText id="note" v-model="form.note" class="w-full" placeholder="Thêm ghi chú cho phân bổ này" />
      </div>
    </div>

    <!-- Chế độ chia nhỏ allocation -->
    <SplitAllocationForm
      v-if="editMode === 'split'"
      :allocation="allocation"
      :billItems="availableBillItems"
      :installments="availableInstallments"
      @submit="handleSplitFormSubmit"
    />

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button
          label="Hủy"
          outlined
          severity="secondary"
          :disabled="loading || apiLoading"
          @click="handleClose"
        />
        <Button
          v-if="editMode === 'update'"
          label="Tiếp tục"
          :loading="loading || apiLoading"
          :disabled="!canProceed"
          @click="handleSubmit"
        />
      </div>
    </template>
  </Dialog>
</template>
