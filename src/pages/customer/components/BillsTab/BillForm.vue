<script lang="ts" setup>
import { computed, reactive, ref, watch } from "vue";

import type {
  AttachmentAddRequest,
  BillItemAddRequest,
  BillItemResponse,
  Discount,
  Product,
} from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import SearchDiscount from "@/components/Discount/SearchDiscount.vue";
import SearchProduct from "@/components/Product/SearchProduct.vue";
import useAttachment from "@/hooks/useAttachment";
import useBill from "@/hooks/useBill";

const props = defineProps<{
  billId?: number;
  personId: number;
  isEmbedded?: boolean;
}>();

const emits = defineEmits<{
  (event: "saved", items: BillItemResponse[]): void;
  (event: "itemAdded", item: BillItemResponse): void;
}>();

const { addBillItem } = useBill({ useStore: false });
const { addAttachment } = useAttachment();

const items = ref<BillItemResponse[]>([]);
const isLoading = ref(false);

const productPopRef = ref();
const discountPopRef = ref();
const selectedProduct = ref<Product | null>(null);
const selectedDiscount = ref<Discount | null>(null);

interface NewItemForm {
  bill_id?: number;
  note: string;
  quantity: number;
  price: number;
  discountInput: string;
  attachment_id?: number;
}

const newItem = reactive<NewItemForm>({
  bill_id: props.billId,
  note: "",
  quantity: 1,
  price: 0,
  discountInput: "0",
});

const getDiscountAmount = computed(() => {
  const subtotal = newItem.price * newItem.quantity;
  const input = newItem.discountInput.trim();

  if (!input || input === "0") return 0;

  if (input.endsWith("%")) {
    const percentage = parseFloat(input.slice(0, -1));
    if (isNaN(percentage)) return 0;
    return (subtotal * percentage) / 100;
  } else {
    const flatAmount = parseFloat(input);
    return isNaN(flatAmount) ? 0 : flatAmount;
  }
});

const newItemTotal = computed(() => {
  if (!selectedProduct.value) return 0;
  const subtotal = newItem.price * newItem.quantity;
  return subtotal - getDiscountAmount.value;
});

const toggleProductSearch = (event: MouseEvent) => {
  productPopRef.value?.popoverRef.toggle(event);
};

const toggleDiscountSearch = (event: MouseEvent) => {
  discountPopRef.value?.popoverRef.toggle(event);
};

const selectProduct = async () => {
  if (selectedProduct.value) {
    newItem.price = selectedProduct.value.price || 0;
  }
};

const onDiscountSelected = async (discount: Discount | null) => {
  selectedDiscount.value = discount;
  if (!discount) {
    newItem.discountInput = "0";
    return;
  }

  if (discount.type === "percent") {
    newItem.discountInput = `${(discount.value * 100).toFixed(0)}%`;
  } else {
    newItem.discountInput = discount.value.toString();
  }

  try {
    // TODO: Implement your discount API call here
    // const response = await yourDiscountAPI(...)
  } catch (error) {
    console.error("Failed to apply discount:", error);
  }
};

watch(selectedProduct, (product) => {
  if (product) {
    newItem.price = product.price || 0;
  }
});

const resetForm = () => {
  Object.assign(newItem, {
    bill_id: props.billId,
    note: "",
    quantity: 1,
    price: 0,
    discountInput: "0",
  });
  selectedProduct.value = null;
  selectedDiscount.value = null;
};

const confirmAdding = async () => {
  try {
    isLoading.value = true;

    if (!selectedProduct.value) {
      throw new Error("No product selected");
    }

    const attachmentRequest: AttachmentAddRequest = {
      product_id: selectedProduct.value.id,
      person_id: props.personId,
      kind: "product",
      quantity: newItem.quantity,
      price: newItem.price,
      discount: getDiscountAmount.value,
      note: newItem.note,
    };

    const attachment = await addAttachment(attachmentRequest);

    if (!attachment?.id) {
      throw new Error("Failed to create attachment");
    }

    const billItemRequest: BillItemAddRequest = {
      bill_id: props.billId,
      attachment_id: attachment.id,
      amount: newItemTotal.value,
      note: newItem.note,
    };

    const billItem = await addBillItem(billItemRequest);

    if (billItem) {
      items.value.push(billItem);

      if (props.isEmbedded) {
        emits("itemAdded", billItem);
      } else {
        emits("saved", items.value);
      }
      resetForm();
    }
  } catch (error) {
    console.error("Failed to add bill item:", error);
  } finally {
    isLoading.value = false;
  }
};

const calculateFlatDiscount = () => {
  const input = newItem.discountInput.trim();
  if (!input.endsWith("%")) {
    const flatAmount = parseFloat(input);
    if (!isNaN(flatAmount)) {
      newItem.discountInput = (flatAmount * newItem.quantity).toString();
    }
  }
};
</script>

<template>
  <div>
    <div>
      <div class="flex items-center justify-between"></div>
    </div>

    <div>
      <!-- Header -->
      <div
        class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 pb-2 text-xs font-medium uppercase text-gray-500"
      >
        <div class="col-span-3">Tên</div>
        <div class="col-span-2">Ghi chú</div>
        <div class="col-span-2 text-right">Đơn giá</div>
        <div class="col-span-1 text-right">SL</div>
        <div class="col-span-2 text-right">Giảm giá</div>
        <div class="col-span-2 pr-14 text-right">Thành tiền</div>
      </div>

      <!-- Form Input Row -->
      <div class="grid grid-cols-12 items-center gap-3 py-2 text-sm">
        <!-- Tên sản phẩm -->
        <div class="col-span-3">
          <Button
            :label="selectedProduct?.name || 'Chọn sản phẩm/dịch vụ'"
            class="w-full justify-start rounded-md border border-dashed border-gray-300 py-2.5 font-normal hover:border-success"
            severity="secondary"
            text
            @click="toggleProductSearch($event)"
          />
        </div>

        <!-- Ghi chú -->
        <div class="col-span-2">
          <InputText v-model="newItem.note" class="w-full" placeholder="Ghi chú" />
        </div>

        <!-- Giá -->
        <div class="col-span-2">
          <InputNumber
            v-model="newItem.price"
            :min="0"
            class="w-full text-sm"
            currency="VND"
            locale="vi-VN"
            mode="currency"
            placeholder="Đơn giá"
            pt:pcInputText:root:class="w-full text-right"
          />
        </div>

        <!-- Số lượng -->
        <div class="col-span-1">
          <InputNumber
            v-model="newItem.quantity"
            :min="1"
            class="w-full text-sm"
            placeholder="SL"
            pt:pcInputText:root:class="w-full text-right pr-10"
            showButtons
          />
        </div>

        <!-- Giảm giá -->
        <div class="col-span-2">
          <InputGroup>
            <InputGroupAddon>
              <Button
                icon="pi pi-sparkles"
                severity="secondary"
                variant="text"
                @click="calculateFlatDiscount"
              />
            </InputGroupAddon>
            <InputText
              v-model="newItem.discountInput"
              class="w-full text-right"
              placeholder="Giảm giá"
            />
            <InputGroupAddon>
              <Button
                icon="pi pi-tag"
                severity="secondary"
                variant="text"
                @click="toggleDiscountSearch($event)"
              />
            </InputGroupAddon>
          </InputGroup>
        </div>

        <!-- Thành tiền và nút thêm -->
        <div class="col-span-2 flex items-center justify-end gap-4 text-right">
          <Money :amount="newItemTotal" />
          <Button
            :loading="isLoading"
            icon="pi pi-arrow-up"
            severity="success"
            size="small"
            @click="confirmAdding"
          />
        </div>
      </div>
    </div>

    <!-- Product Search Popover -->
    <SearchProduct
      ref="productPopRef"
      v-model:selectedProduct="selectedProduct"
      :defaultFilters="{ type: 'service' }"
      :multiple="false"
      @update:selectedProduct="selectProduct"
    />

    <!-- Discount Search Popover -->
    <SearchDiscount
      ref="discountPopRef"
      v-model:selectedDiscount="selectedDiscount"
      :deal-id="billId"
      :person-id="personId"
      :product-ids="[selectedProduct?.id].filter(Boolean) as number[]"
      @update:selectedDiscount="onDiscountSelected"
    />
  </div>
</template>
