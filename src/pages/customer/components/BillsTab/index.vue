<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";

import type { PaymentResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import useEconomy from "@/hooks/useEconomy";
import PaymentSkeleton from "@/pages/customer/components/BillsTab/PaymentSkeleton.vue";

import PaymentDetail from "./PaymentDetail.vue";
import PaymentForm from "./PaymentForm/index.vue";

const listboxRef = ref(null);
const props = defineProps<{
  personId: number;
}>();

const { payments, listPayments } = useEconomy();

const selectedPayment = ref<PaymentResponse | null>(null);
const searchQuery = ref("");
const showPaymentForm = ref(true);
const isLoading = ref(false);

const filteredPayments = computed(() => {
  if (!searchQuery.value) return payments.value;

  const query = searchQuery.value.toLowerCase();
  return payments.value.filter(
    (payment) =>
      payment.id.toString().includes(query) || payment.bill_id?.toString().includes(query),
  );
});

const stats = computed(() => {
  return {
    totalAmount: payments.value.reduce((sum, p) => sum + p.total_amount, 0),
    paidAmount: payments.value
      .filter((p) => p.state === "completed")
      .reduce((sum, p) => sum + p.total_amount, 0),
    depositAmount: payments.value
      .filter((p) => p.type === "deposit")
      .reduce((sum, p) => sum + p.total_amount, 0),
    debtAmount: payments.value
      .filter((p) => p.state !== "completed")
      .reduce((sum, p) => sum + p.total_amount, 0),
  };
});

const items = ref([
  {
    label: "Thêm mới",
    icon: "pi pi-plus",
    command: () => {
      showPaymentForm.value = true;
      selectedPayment.value = null;
    },
  },
]);

const handlePaymentSelect = async (payment: PaymentResponse) => {
  showPaymentForm.value = false;
  selectedPayment.value = payment;
};

const refreshPaymentList = async (newPaymentId?: number) => {
  await listPayments({
    filter: { person_id: props.personId },
    kind: "normal",
    order_by: "payment_date desc",
  });

  if (newPaymentId) {
    const newPayment = payments.value.find((p) => p.id === newPaymentId);
    if (newPayment) {
      showPaymentForm.value = false;
      selectedPayment.value = newPayment;
    }
  } else {
    selectedPayment.value = null;
  }
};

const handlePaymentDeleted = async (paymentId: number) => {
  await refreshPaymentList();
};

const handlePaymentFormClose = () => {
  showPaymentForm.value = false;
};

const getPaymentStateSeverity = (state: string) => {
  switch (state) {
    case "completed":
      return "success";
    case "pending":
      return "warning";
    case "failed":
      return "danger";
    case "draft":
      return "info";
    default:
      return "info";
  }
};

onMounted(async () => {
  try {
    isLoading.value = true;
    await listPayments({
      filter: { person_id: props.personId },
      kind: "normal",
      order_by: "payment_date desc",
    });
  } finally {
    isLoading.value = false;
  }
});
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- Menubar -->
    <div class="px-5 pb-5">
      <Menubar :model="items" class="px-1 py-1">
        <template #item="{ item, props, hasSubmenu, root }">
          <a class="flex items-center" v-bind="props.action">
            <span :class="item.icon" />
            <span>{{ item.label }}</span>
            <Badge
              v-if="item.badge"
              :class="{ 'ml-auto': !root, 'ml-2 size-5 min-w-0': root }"
              :value="item.badge"
            />
            <span
              v-if="item.shortcut"
              class="ml-auto rounded border p-1 text-xs border-surface bg-emphasis text-muted-color"
            >
              {{ item.shortcut }}
            </span>
            <i
              v-if="hasSubmenu"
              :class="[
                'pi pi-angle-down',
                { 'pi-angle-down ml-2': root, 'pi-angle-right ml-auto': !root },
              ]"
            ></i>
          </a>
        </template>
      </Menubar>
    </div>

    <!-- Main Content -->
    <div class="flex-1 overflow-hidden px-5">
      <div class="grid grid-cols-12 gap-4">
        <!-- Left Panel - Payment List -->
        <div class="col-span-12 md:col-span-2">
          <div class="rounded-lg border bg-white">
            <div class="flex items-center justify-between border-b border-gray-100 p-2">
              <h3 class="font-semibold text-gray-800">Lịch sử thanh toán</h3>
              <Badge
                :value="payments.length"
                class="size-6 rounded-full p-1 text-xs font-normal"
                severity="info"
              />
            </div>
            <div class="border-b p-2">
              <InputText
                v-model="searchQuery"
                placeholder="Tìm kiếm..."
                type="text"
                size="small"
                fluid
              />
            </div>
            <div ref="listboxRef" class="overflow-y-auto md:max-h-[40.5rem]">
              <!-- Loading State -->
              <template v-if="isLoading">
                <PaymentSkeleton v-for="n in 10" :key="n" />
              </template>

              <!-- Empty State -->
              <div v-if="!payments.length && !isLoading" class="p-4 text-center text-gray-500">
                <i class="pi pi-inbox mb-2 text-2xl"></i>
                <p>Không có thanh toán nào</p>
              </div>

              <!-- Payment List Items -->
              <div
                v-for="(payment, index) in filteredPayments"
                :key="payment.id"
                :class="{
                  'border-primary-500': selectedPayment?.id === payment.id,
                  'border-transparent': selectedPayment?.id !== payment.id,
                  'bg-gray-50': index % 2 === 0,
                }"
                class="cursor-pointer border-l-4 p-2 transition-colors hover:bg-gray-50"
                @click="handlePaymentSelect(payment)"
              >
                <div class="flex items-start justify-between gap-2">
                  <div class="min-w-0 flex-1 space-y-1">
                    <p class="truncate">
                      <Chip class="border py-0 pl-0 pr-4">
                        <span
                          class="flex size-5 items-center justify-center rounded-full bg-primary text-xs text-primary-contrast"
                        >
                          {{ filteredPayments.length - index }}
                        </span>
                        <span class="ml-2 font-medium">#{{ payment.id }}</span>
                      </Chip>
                    </p>
                    <div class="flex items-center gap-2 text-sm">
                      <span class="text-gray-500">
                        <DateTime :time="payment.payment_date" />
                      </span>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium text-gray-900">
                      <Money :amount="payment.total_amount" />
                    </div>
                    <div class="mt-1">
                      <Tag
                        :severity="getPaymentStateSeverity(payment.state)"
                        :value="payment.state"
                        class="px-1 py-0 font-medium"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Stats -->
            <div class="border-t border-gray-100 p-3">
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <div class="font-medium text-muted">Tổng số tiền</div>
                  <div class="font-semibold text-gray-900">
                    <Money :amount="stats.totalAmount" />
                  </div>
                  <div class="mt-2 font-medium text-muted">Đã trả</div>
                  <div class="font-semibold text-green-600">
                    <Money :amount="stats.paidAmount" />
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium text-muted">Cọc</div>
                  <div class="font-semibold">
                    <Money :amount="stats.depositAmount" />
                  </div>
                  <div class="mt-2 font-medium text-danger">Còn nợ</div>
                  <div class="font-semibold text-yellow-600">
                    <Money :amount="stats.debtAmount" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Panel - Payment Detail/Form -->
        <div class="col-span-12 md:col-span-10">
          <PaymentForm
            v-if="showPaymentForm"
            :personId="props.personId"
            @close="handlePaymentFormClose"
            @saved="refreshPaymentList"
          />
          <PaymentDetail
            v-else-if="selectedPayment"
            :paymentId="selectedPayment.id"
            @deleted="handlePaymentDeleted"
          />
          <div v-else class="flex h-full items-center justify-center rounded-lg bg-white shadow">
            <div class="text-center">
              <i class="pi pi-file text-4xl text-gray-400"></i>
              <p class="mt-2 text-gray-500">Chọn một thanh toán để xem chi tiết</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
