<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from "vue";

import type {
  AttachmentUpdateRequest,
  BillItemResponse,
  BillItemUpdateRequest,
  Discount,
  Product,
} from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import SearchDiscount from "@/components/Discount/SearchDiscount.vue";
import useAttachment from "@/hooks/useAttachment";
import useBill from "@/hooks/useBill";

const props = defineProps<{
  billItemId: number;
}>();

const emits = defineEmits<{
  (event: "updated", item: BillItemResponse): void;
}>();

const { getBillItem, updateBillItem } = useBill({ useStore: false });
const { updateAttachment } = useAttachment();

const isLoading = ref(false);
const currentBillItem = ref<BillItemResponse | null>(null);

const productPopRef = ref();
const discountPopRef = ref();
const selectedProduct = ref<Product | null>(null);
const selectedDiscount = ref<Discount | null>(null);

interface EditItemForm {
  note: string;
  quantity: number;
  price: number;
  discountInput: string;
}

const editItem = reactive<EditItemForm>({
  note: "",
  quantity: 1,
  price: 0,
  discountInput: "0",
});

const getDiscountAmount = computed(() => {
  const subtotal = editItem.price * editItem.quantity;
  const input = editItem.discountInput.trim();

  if (!input || input === "0") return 0;

  if (input.endsWith("%")) {
    const percentage = parseFloat(input.slice(0, -1));
    if (isNaN(percentage)) return 0;
    return (subtotal * percentage) / 100;
  } else {
    const flatAmount = parseFloat(input);
    return isNaN(flatAmount) ? 0 : flatAmount;
  }
});

const itemTotal = computed(() => {
  const subtotal = editItem.price * editItem.quantity;
  return subtotal - getDiscountAmount.value;
});

const toggleProductSearch = (event: MouseEvent) => {
  productPopRef.value?.popoverRef.toggle(event);
};

const toggleDiscountSearch = (event: MouseEvent) => {
  discountPopRef.value?.popoverRef.toggle(event);
};

const onDiscountSelected = async (discount: Discount | null) => {
  selectedDiscount.value = discount;
  if (!discount) {
    editItem.discountInput = "0";
    return;
  }

  if (discount.type === "percent") {
    editItem.discountInput = `${(discount.value * 100).toFixed(0)}%`;
  } else {
    editItem.discountInput = discount.value.toString();
  }
};

const loadBillItem = async () => {
  try {
    const billItem = await getBillItem({ id: props.billItemId });
    if (billItem) {
      currentBillItem.value = billItem;

      // Initialize form with attachment data
      const attachment = billItem.attachment;
      if (attachment) {
        selectedProduct.value = attachment.product;
        editItem.quantity = attachment.quantity || 1;
        editItem.price = attachment.price || 0;
        editItem.note = billItem.note || "";
        editItem.discountInput = attachment.discount ? attachment.discount.toString() : "0";
      }
    }
  } catch (error) {
    console.error("Failed to load bill item:", error);
  }
};

const saveChanges = async () => {
  try {
    isLoading.value = true;

    if (!currentBillItem.value?.attachment?.id) {
      throw new Error("No attachment found");
    }

    // Check modified fields for attachment
    const attachment = currentBillItem.value.attachment;
    const attachmentModified: string[] = [];

    if (editItem.quantity !== attachment.quantity) {
      attachmentModified.push("quantity");
    }
    if (editItem.price !== attachment.price) {
      attachmentModified.push("price");
    }
    if (getDiscountAmount.value !== attachment.discount) {
      attachmentModified.push("discount");
    }
    /*if (editItem.note !== attachment.note) {
      attachmentModified.push("note");
    }*/

    // Update attachment first
    const attachmentRequest: AttachmentUpdateRequest = {
      id: currentBillItem.value.attachment.id,
      quantity: editItem.quantity,
      price: editItem.price,
      discount: getDiscountAmount.value,
      //note: attachment.note, // Giữ nguyên note của attachment
      modified: attachmentModified,
    };

    await updateAttachment(attachmentRequest);

    // Check modified fields for bill item
    const billItemModified: string[] = [];
    const currentBillAmount = currentBillItem.value.amount;
    const currentBillNote = currentBillItem.value.note;

    if (itemTotal.value !== currentBillAmount) {
      billItemModified.push("amount");
    }
    if (editItem.note !== currentBillNote) {
      billItemModified.push("note");
    }

    // Then update bill item
    const billItemRequest: BillItemUpdateRequest = {
      id: props.billItemId,
      amount: itemTotal.value,
      note: editItem.note, // Sử dụng note từ form cho bill item
      modified: billItemModified,
    };

    const updatedBillItem = await updateBillItem(billItemRequest);

    if (updatedBillItem) {
      emits("updated", updatedBillItem);
    }
  } catch (error) {
    console.error("Failed to update bill item:", error);
  } finally {
    isLoading.value = false;
  }
};

const calculateFlatDiscount = () => {
  const input = editItem.discountInput.trim();
  if (!input.endsWith("%")) {
    const flatAmount = parseFloat(input);
    if (!isNaN(flatAmount)) {
      editItem.discountInput = (flatAmount * editItem.quantity).toString();
    }
  }
};

onMounted(() => {
  loadBillItem();
});
</script>

<template>
  <div>
    <div class="grid grid-cols-12 items-center gap-3 py-2 text-sm">
      <!-- Tên sản phẩm -->
      <div class="col-span-3">
        <Button
          :disabled="true"
          :label="selectedProduct?.name || 'Chọn sản phẩm/dịch vụ'"
          class="w-full justify-start rounded-md border border-dashed border-gray-300 py-2.5 font-normal"
          severity="secondary"
          text
        />
      </div>

      <!-- Ghi chú -->
      <div class="col-span-2">
        <InputText v-model="editItem.note" class="w-full" placeholder="Ghi chú" />
      </div>

      <!-- Giá -->
      <div class="col-span-2">
        <InputNumber
          v-model="editItem.price"
          :min="0"
          class="w-full text-sm"
          currency="VND"
          locale="vi-VN"
          mode="currency"
          placeholder="Đơn giá"
          pt:pcInputText:root:class="w-full text-right"
        />
      </div>

      <!-- Số lượng -->
      <div class="col-span-1">
        <InputNumber
          v-model="editItem.quantity"
          :min="1"
          class="w-full text-sm"
          placeholder="SL"
          pt:pcInputText:root:class="w-full text-right pr-11"
          showButtons
        />
      </div>

      <!-- Giảm giá -->
      <div class="col-span-2">
        <InputGroup>
          <InputGroupAddon>
            <Button
              icon="pi pi-sparkles"
              severity="secondary"
              variant="text"
              @click="calculateFlatDiscount"
            />
          </InputGroupAddon>
          <InputText
            v-model="editItem.discountInput"
            class="w-full text-right"
            placeholder="Giảm giá"
          />
          <InputGroupAddon>
            <Button
              icon="pi pi-tag"
              severity="secondary"
              variant="text"
              @click="toggleDiscountSearch($event)"
            />
          </InputGroupAddon>
        </InputGroup>
      </div>

      <!-- Thành tiền và nút lưu -->
      <div class="col-span-2 flex items-center justify-end gap-4 text-right">
        <Money :amount="itemTotal" />
        <Button
          :loading="isLoading"
          icon="pi pi-check"
          severity="success"
          size="small"
          @click="saveChanges"
        />
      </div>
    </div>

    <!-- Discount Search Popover -->
    <SearchDiscount
      v-if="currentBillItem?.attachment?.person_id"
      ref="discountPopRef"
      v-model:selectedDiscount="selectedDiscount"
      :deal-id="currentBillItem?.bill_id"
      :person-id="currentBillItem?.attachment?.person_id"
      :product-ids="[selectedProduct?.id].filter(Boolean) as number[]"
      @update:selectedDiscount="onDiscountSelected"
    />
  </div>
</template>
