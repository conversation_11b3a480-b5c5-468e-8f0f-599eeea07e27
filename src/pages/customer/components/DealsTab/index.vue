<script lang="ts" setup>
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, onUnmounted, provide, ref } from "vue";

import { DealState } from "@/api/bcare-enum";
import type { AttachmentResponse, DealResponse, DealUserResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Empty from "@/base-components/Empty";
import AttachmentTreePro from "@/components/Attachment/AttachmentTreePro.vue";
import DealPaymentProgress from "@/components/Deal/DealPaymentProgress.vue";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import EntityTags from "@/components/Tags/EntityTags.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import { usePrint } from "@/composables/usePrint";
import useBill from "@/hooks/useBill";
import useDeal from "@/hooks/useDeal";
import DealForm from "@/pages/customer/components/DealsTab/DealForm.vue";
import DealName from "@/components/Deal/DealName.vue";
import { useDealFormStore } from "@/stores/deal-form-store";

import DealPaymentHistory from "./DealPaymentHistory.vue";
import DealRatingDialog from "./DealRatingDialog.vue";
import HistoryModal from "@/components/Timeline/HistoryModal.vue";

// Type for the payload emitted by DealRatingDialog
interface RatingUpdatePayload {
  deal_user_id: number;
  category: string;
  rating: number;
  existing_rating_id?: number;
}

const props = defineProps<{
  personId: number;
}>();

const {
  deals,
  fetchDealsByPerson,
  updateDeal,
  removeDeal,
  addDealUserRating,
  updateDealUserRating,
} = useDeal({
  useStore: true,
  personId: props.personId,
});
const { addBillFromDeal } = useBill({ useStore: true });
const dealFormStore = useDealFormStore();
dealFormStore.setPersonId(props.personId);
const showDealForm = ref(false);
const dealFormRef = ref<InstanceType<typeof DealForm> | null>(null);

// Initialize the print hook
const { printDeal } = usePrint();

const confirm = useConfirm();

// Add rating dialog state
const showRatingDialog = ref(false);
const selectedDealForRating = ref<DealResponse | null>(null);

const showDealHistoryModal = ref(false);
const selectedDealIdForHistory = ref<number | null>(null);

const items = ref([
  {
    label: "Thêm mới",
    icon: "pi pi-plus",
    command: async () => {
      if (showDealForm.value) {
        dealFormStore.clearAll();
      }
      await dealFormStore.initializeDeal();
      showDealForm.value = true;
      if (dealFormRef.value) {
        dealFormRef.value.resetForm();
      }
    },
  },
]);

const visibleDealDetails = ref<Record<number, boolean>>({});

const toggleDealDetails = (dealId: number) => {
  visibleDealDetails.value[dealId] = !visibleDealDetails.value[dealId];
};

const getDealMenuItems = (deal: DealResponse) => {
  return [
    {
      label: "Chỉnh sửa",
      icon: "pi pi-pencil",
      command: async () => {
        try {
          await dealFormStore.initializeExistingDeal(deal.id);
          showDealForm.value = true;
          if (dealFormRef.value) {
            dealFormRef.value.resetForm();
            await dealFormRef.value.loadExistingInstallmentPlan();
          }
          console.log(`Deal ${deal.id} loaded for editing.`);
        } catch (error) {
          console.error(`Error initializing deal ${deal.id} for editing:`, error);
        }
      },
    },
    {
      label: "Tạo hoá đơn",
      icon: "pi pi-file",
      disabled:
        deal.state === DealState.WON ||
        deal.state === DealState.LOST ||
        deal.state === DealState.PAYING, // Disable nếu state là won, lost hoặc paying
      command: async () => {
        try {
          // Add Bill from Deal
          await addBillFromDeal({ deal_id: deal.id });
          // Update Deal state to "won"
          await updateDeal({ id: deal.id, state: DealState.WON });
          // Optionally, refetch deals to update the list
          await fetchDealsByPerson(props.personId);
          console.log(
            `Hoá đơn được tạo cho Deal ${deal.id} và trạng thái được cập nhật thành "won".`,
          );
        } catch (error) {
          console.error(`Error creating hoá đơn for Deal ${deal.id}:`, error);
        }
      },
    },
    {
      label: "In",
      icon: "pi pi-print",
      command: async () => {
        try {
          printDeal(deal.id, props.personId);
        } catch (error) {
          console.error(`Error printing Deal ${deal.id}:`, error);
        }
      },
    },
    {
      label: "Đánh giá",
      icon: "pi pi-star",
      command: () => {
        selectedDealForRating.value = deal;
        showRatingDialog.value = true;
      },
    },
    {
      label: "Lịch sử chỉnh sửa",
      icon: "pi pi-history",
      command: () => {
        selectedDealIdForHistory.value = deal.id;
        showDealHistoryModal.value = true;
      },
    },
    {
      separator: true,
    },
    {
      label: "Kết thúc deal",
      icon: "pi pi-times-circle",
      severity: "warning",
      disabled: deal.state === DealState.LOST, // Disable if already lost
      command: (event: any) => {
        confirm.require({
          target: event.originalEvent.currentTarget,
          message: "Bạn có muốn kết thúc deal này?",
          icon: "pi pi-times-circle",
          rejectProps: {
            label: "Không",
            severity: "secondary",
            outlined: true,
          },
          acceptProps: {
            label: "Xác nhận",
            severity: "danger",
          },
          accept: async () => {
            await updateDeal({ id: deal.id, state: DealState.LOST });
            await fetchDealsByPerson(props.personId);
          },
        });
      },
    },
    {
      label: "Xóa",
      icon: "pi pi-trash",
      severity: "danger",
      disabled: deal.paid_amount + deal.down_payment_received_amount > 0,
      command: (event: any) => {
        confirm.require({
          target: event.originalEvent.currentTarget,
          message: "Bạn có muốn xóa deal này?",
          icon: "pi pi-trash",
          rejectProps: {
            label: "Hủy",
            severity: "secondary",
            outlined: true,
          },
          acceptProps: {
            label: "Xóa",
            severity: "danger",
          },
          accept: () => {
            removeDeal({ id: deal.id });
          },
          reject: () => {},
        });
      },
    },
  ];
};

// Tạo computed properties để cache kết quả
const attachmentTree = computed(() => {
  if (!deals.value) return {};

  return deals.value.reduce(
    (acc, deal) => {
      if (!deal.attachments) return acc;

      // Create map for this deal's attachments
      acc[deal.id] = {
        rootAttachments: deal.attachments.filter((att) => !att.parent_id),
        childrenMap: deal.attachments.reduce(
          (childMap, att) => {
            if (att.parent_id) {
              if (!childMap[att.parent_id]) {
                childMap[att.parent_id] = [];
              }
              childMap[att.parent_id].push(att);
            }
            return childMap;
          },
          {} as Record<number, AttachmentResponse[]>,
        ),
      };

      return acc;
    },
    {} as Record<
      number,
      {
        rootAttachments: AttachmentResponse[];
        childrenMap: Record<number, AttachmentResponse[]>;
      }
    >,
  );
});

onMounted(async () => {
  await fetchDealsByPerson(props.personId);
});

onUnmounted(async () => {
  dealFormStore.clearAll();
});

const handleDealSave = async () => {
  await fetchDealsByPerson(props.personId);
  dealFormStore.clearAll(); // Clear the store if necessary
  showDealForm.value = false;
  console.log("Deal saved and form closed.");
};

// Provide reloadData function
const reloadDeals = () => fetchDealsByPerson(props.personId);
provide("reloadData", reloadDeals);

const updateDealStage = async (dealId: number, newStageId: number) => {
  try {
    await updateDeal({ id: dealId, stage_id: newStageId }); // Call API to update
    console.log(`Deal ${dealId} stage updated to ${newStageId}.`);
    // Optionally refetch if stage update doesn't automatically update the list via store reactivity
    // await fetchDealsByPerson(props.personId);
  } catch (error) {
    console.error("Error updating stage:", error);
  }
};

// Helper function to extract user IDs from deal_assignment
const getUserIdsFromDealAssignment = (dealAssignments: DealUserResponse[]) => {
  if (!dealAssignments || !Array.isArray(dealAssignments)) return [];
  return dealAssignments.map((assignment) => assignment.user_id);
};

// Add the function to handle saving ratings (Refactored)
const handleSaveRatings = async (ratingsToSave: RatingUpdatePayload[]) => {
  console.log("Received ratings to save:", ratingsToSave);
  if (!ratingsToSave || ratingsToSave.length === 0) {
    console.log("No ratings changes to save.");
    return;
  }

  try {
    const promises = ratingsToSave.map((ratingData) => {
      if (ratingData.existing_rating_id) {
        // Update existing rating
        console.log("Updating rating:", ratingData);
        return updateDealUserRating({
          id: ratingData.existing_rating_id,
          category: ratingData.category, // category might not be updatable, check API spec if needed
          rating: ratingData.rating,
        });
      } else {
        // Add new rating
        console.log("Adding rating:", ratingData);
        return addDealUserRating({
          deal_user_id: ratingData.deal_user_id,
          category: ratingData.category,
          rating: ratingData.rating,
        });
      }
    });

    await Promise.all(promises);
    console.log("Ratings saved successfully.");
    // State should be updated reactively by the store actions,
    // but explicitly refetching ensures consistency if store logic has issues
    // or if not using the store.
    await fetchDealsByPerson(props.personId);
  } catch (error) {
    console.error("Error saving ratings:", error);
    // Add user notification here if needed
  }
};
</script>

<template>
  <div>
    <ConfirmPopup />
    <div class="px-5">
      <Menubar :model="items" class="px-1 py-1">
        <template #item="{ item, props, hasSubmenu, root }">
          <a class="flex items-center" v-bind="props.action">
            <span :class="item.icon" />
            <span>{{ item.label }}</span>
            <Badge
              v-if="item.badge"
              :class="{ 'ml-auto': !root, 'ml-2 size-5 min-w-0': root }"
              :value="item.badge"
            />
            <span
              v-if="item.shortcut"
              class="ml-auto rounded border p-1 text-xs border-surface bg-emphasis text-muted-color"
              >{{ item.shortcut }}</span
            >
            <i
              v-if="hasSubmenu"
              :class="[
                'pi pi-angle-down',
                { 'pi-angle-down ml-2': root, 'pi-angle-right ml-auto': !root },
              ]"
            ></i>
          </a>
        </template>
        <template #end>
          <div class="flex items-center gap-1">
            <InputText
              class="w-32 text-sm sm:w-auto"
              placeholder="Search"
              size="small"
              type="text"
            />
            <!-- <Button icon="pi pi-print" severity="secondary" text /> -->
          </div>
        </template>
      </Menubar>
    </div>
    <TransitionGroup class="relative" name="list" tag="div">
      <div v-if="showDealForm" key="deal-form" class="card p-5 pb-0">
        <div class="relative">
          <DealForm ref="dealFormRef" :person-id="props.personId" @save="handleDealSave" />
        </div>
      </div>
      <DataView
        key="data-view"
        :paginator="true"
        :rows="10"
        :value="deals"
        class="p-5"
        dataKey="id"
      >
        <template #header>
          <div class="grid grid-cols-12 gap-2 border-b-2 py-2 font-medium">
            <div class="col-span-1">Ngày tạo</div>
            <div class="col-span-2">Tên Deal</div>
            <div class="col-span-4">Dịch vụ</div>
            <div class="col-span-5 flex items-center justify-between">
              <span>Thanh toán</span>
              <i
                class="pi pi-refresh mr-2 cursor-pointer hover:animate-spin"
                @click="fetchDealsByPerson(props.personId)"
              />
            </div>
          </div>
        </template>
        <template #list="slotProps">
          <TransitionGroup class="relative" name="deal-list" tag="div">
            <div
              v-for="(deal, index) in slotProps.items"
              :key="deal.id"
              class="grid grid-cols-12 gap-2 py-2 odd:bg-white even:bg-slate-50"
            >
              <div class="col-span-1 flex flex-col">
                <DateTime :time="deal.created_at" />
                <span class="text-xs font-medium text-slate-400">
                  <i class="pi pi-hashtag pr-1 text-xs" />{{ deal.id }}
                </span>
                <!--Deal assignments -->
                <div
                  v-if="deal.deal_assignment && deal.deal_assignment.length > 0"
                  class="ml-1.5 mt-1 flex items-center"
                >
                  <UserAvatarGroup
                    :users="getUserIdsFromDealAssignment(deal.deal_assignment)"
                    size="small"
                    animation-style="lift"
                  />
                </div>
              </div>
              <div class="col-span-2 flex flex-col">
                <DealName :name="deal.name" :state="deal.state" />
                <div class="mt-1">
                  <PipelineStageSelect
                    v-model="deal.stage_id"
                    :pipeline-id="2"
                    class="text-sm"
                    icon-class="text-xs"
                    @update:model-value="updateDealStage(deal.id, $event)"
                  />
                </div>
                <div class="mt-1.5">
                  <EntityTags
                    :entity-id="deal.id"
                    entity-type="deal"
                    :initial-tags="deal.tags || []"
                    placeholder="Thêm thẻ"
                  />
                </div>
              </div>
              <div class="col-span-4 flex flex-wrap gap-1">
                <template v-if="deal.attachments">
                  <AttachmentTreePro
                    v-for="rootAttachment in attachmentTree[deal.id]?.rootAttachments || []"
                    :key="rootAttachment.id"
                    :attachment="rootAttachment"
                    :initial-child-attachments="
                      attachmentTree[deal.id]?.childrenMap[rootAttachment.id] || []
                    "
                    mode="display"
                  />
                </template>
              </div>
              <div class="col-span-5 flex flex-wrap gap-1">
                <div class="grow">
                  <DealPaymentProgress
                    :amountPaid="deal.paid_amount"
                    :depositAmount="deal.down_payment"
                    :hideSmallAmounts="true"
                    :promotionAmount="deal.discount_amount"
                    :show-label="false"
                    :smallAmountThreshold="5"
                    :totalAmount="deal.total_plan_amount || deal.total_amount"
                    :discountUsages="deal.discount_usages"
                  />
                </div>
                <div class="flex items-start">
                  <SplitButton
                    :model="getDealMenuItems(deal)"
                    icon="pi pi-eye"
                    outlined
                    severity="secondary"
                    size="small"
                    @click="toggleDealDetails(deal.id)"
                  >
                    <template #item="{ item }">
                      <div
                        class="flex cursor-pointer items-center px-2 py-1.5"
                        :class="{
                          'text-danger': item.severity === 'danger',
                          'text-warning': item.severity === 'warning',
                        }"
                      >
                        <i :class="[item.icon, 'mr-2']"></i>
                        {{ item.label }}
                      </div>
                    </template>
                  </SplitButton>
                </div>
              </div>

              <!-- Add DealPaymentHistory after DealPaymentProgress -->
              <template v-if="visibleDealDetails[deal.id]">
                <div class="col-span-12 mt-2">
                  <DealPaymentHistory :deal-id="deal.id" />
                </div>
              </template>
            </div>
          </TransitionGroup>
        </template>
        <template #empty>
          <Empty class="mx-5 p-10" />
        </template>
      </DataView>
    </TransitionGroup>
    <DealRatingDialog
      v-model:visible="showRatingDialog"
      :deal="selectedDealForRating"
      @save="handleSaveRatings"
    />
    <HistoryModal
      v-model="showDealHistoryModal"
      entity-type="deal"
      :entity-id="selectedDealIdForHistory"
    />
  </div>
</template>

<style scoped>
.list-move,
.list-enter-active,
.list-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(0px);
}

.list-leave-active {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
