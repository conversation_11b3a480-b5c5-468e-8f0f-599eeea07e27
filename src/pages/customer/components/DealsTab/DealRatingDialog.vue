<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";

import type {
  DealResponse,
  DealUserRating,
  DealUserResponse,
  UserResponse,
} from "@/api/bcare-types-v2";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useComponentSetting } from "@/hooks/useComponentSetting";
// Import the interface from the composable
import type { RatingUpdatePayload } from "@/hooks/useDeal";

// Function to generate array of rating values
const ratingValues1to10 = Array.from({ length: 10 }, (_, i) => i + 1);

// Define the type for rating questions
type RatingQuestionsType = {
  [key: string]: {
    [key: string]: string; // role: { questionKey: questionText }
  };
};

const props = defineProps<{
  visible: boolean;
  deal: DealResponse | null;
}>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
  // Updated emit signature
  save: [ratings: RatingUpdatePayload[]];
}>();

// 1. Load rating questions setting first
const { currentSettingValue } = useComponentSetting("deal_rating_questions");

// 2. Define computed ratingQuestions based on the setting
const ratingQuestions = computed(() => {
  return (currentSettingValue.value || {}) as RatingQuestionsType;
});

// Store loaded users
const userDetails = reactive<Record<number, UserResponse>>({});

// Prepare rating values structure (deal_user_id -> questionKey -> score)
const ratingValues = ref<Record<number, Record<string, number>>>({});
// Store original ratings to detect changes (deal_user_id -> questionKey -> DealUserRating)
const originalRatings = ref<Record<number, Record<string, DealUserRating>>>({});
const averageScoreCategory = "average_score"; // Define category constant

// 3. Initialize rating values *after* ratingQuestions is defined
watch(
  () => props.deal,
  (newDeal) => {
    ratingValues.value = {};
    originalRatings.value = {};

    if (newDeal?.deal_assignment && Object.keys(ratingQuestions.value).length > 0) {
      newDeal.deal_assignment.forEach((member) => {
        const memberId = member.id;
        const memberRole = member.role;

        // Check if questions exist for the member's role. If not, we still might need to load average score.
        const questionsForRole = ratingQuestions.value[memberRole];

        // Initialize objects for this member regardless of questions existing,
        // because average_score might exist even if questions don't.
        ratingValues.value[memberId] = {};
        originalRatings.value[memberId] = {};

        // Pre-fill with existing ratings from member.ratings
        member.ratings?.forEach((rating) => {
          // Check if it's a question rating relevant to the current settings
          if (questionsForRole && questionsForRole[rating.category] !== undefined) {
            ratingValues.value[memberId][rating.category] = rating.rating;
            originalRatings.value[memberId][rating.category] = rating;
          }
          // *** ADDED: Explicitly check for and load the average score rating ***
          else if (rating.category === averageScoreCategory) {
             originalRatings.value[memberId][averageScoreCategory] = rating;
             // Do NOT put average score into ratingValues, it's calculated
          }
        });

        // Initialize any missing *question* scores with 0, only if questions exist for the role
        if (questionsForRole) {
            Object.keys(questionsForRole).forEach((questionKey) => {
                if (ratingValues.value[memberId][questionKey] === undefined) {
                ratingValues.value[memberId][questionKey] = 0;
                }
            });
        }

      });
    }
  },
  { immediate: true, deep: true },
);

// Dialog visibility
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

// Group team members by role
const teamMembersByRole = computed(() => {
  if (!props.deal?.deal_assignment) return {};

  return props.deal.deal_assignment.reduce(
    (acc, member) => {
      if (!acc[member.role]) {
        acc[member.role] = [];
      }
      acc[member.role].push(member);
      return acc;
    },
    {} as Record<string, DealUserResponse[]>,
  );
});

// Calculate average score for each member
const getMemberAverageScore = (memberId: number): number => {
  if (!ratingValues.value || !ratingValues.value[memberId]) return 0;

  const scores = Object.values(ratingValues.value[memberId]);
  if (scores.length === 0) return 0;

  // Exclude potential 'average_score' itself if it somehow got into ratingValues
  const questionScores = Object.entries(ratingValues.value[memberId])
    .filter(([key]) => key !== "average_score")
    .map(([, score]) => Number(score) || 0);

  if (questionScores.length === 0) return 0;

  const total = questionScores.reduce((sum, score) => sum + score, 0);
  // Calculate average based only on actual question scores
  return Math.round((total / questionScores.length) * 10) / 10;
};

// Update handleSubmit to include average score as a separate rating item
const handleSubmit = () => {
  const ratingsToSave: RatingUpdatePayload[] = [];

  Object.entries(ratingValues.value).forEach(([dealUserIdStr, memberRatings]) => {
    const dealUserId = parseInt(dealUserIdStr);

    // 1. Process individual question ratings (as before)
    Object.entries(memberRatings).forEach(([category, rating]) => {
      const original = originalRatings.value[dealUserId]?.[category];
      const currentRatingValue = rating ?? 0; // Treat null/undefined as 0

      if (original) {
        // Existing rating: Check if value changed
        if (original.rating !== currentRatingValue) {
          ratingsToSave.push({
            deal_user_id: dealUserId,
            category: category,
            rating: currentRatingValue,
            existing_rating_id: original.id,
          });
        }
      } else if (currentRatingValue !== 0) {
        // New rating: Only add if the value is not 0
        ratingsToSave.push({
          deal_user_id: dealUserId,
          category: category,
          rating: currentRatingValue,
        });
      }
    });

    // 2. Process average score
    const calculatedAverage = getMemberAverageScore(dealUserId);
    const originalAverageRating = originalRatings.value[dealUserId]?.[averageScoreCategory];

    if (originalAverageRating) {
      // Existing average score rating: Check if value changed
      if (originalAverageRating.rating !== calculatedAverage) {
        console.log(
          `Updating average score for ${dealUserId} from ${originalAverageRating.rating} to ${calculatedAverage}`,
        );
        ratingsToSave.push({
          deal_user_id: dealUserId,
          category: averageScoreCategory,
          rating: calculatedAverage,
          existing_rating_id: originalAverageRating.id,
        });
      }
    } else if (calculatedAverage !== 0) {
      // Only add new average if it's not 0
      // New average score rating: Add if calculated average is not 0
      console.log(`Adding average score for ${dealUserId}: ${calculatedAverage}`);
      ratingsToSave.push({
        deal_user_id: dealUserId,
        category: averageScoreCategory,
        rating: calculatedAverage,
        // No existing_rating_id means it's an add request
      });
    }
  });

  console.log("Final ratings to save (including average):", ratingsToSave);
  emit("save", ratingsToSave);
  dialogVisible.value = false;
};

// Role display names map
const roleDisplayNames: Record<string, string> = {
  consultant_doctor: "Bác sĩ tư vấn",
  doctor_assistant: "Tư vấn viên",
  treatment_doctor: "Bác sĩ điều trị",
  sale: "Nhân viên sale",
};

// Check if there are team members with rating questions
const hasRateableMembers = computed(() => {
  // Ensure ratingQuestions is available before checking
  if (!ratingQuestions.value || !teamMembersByRole.value) return false;
  return Object.keys(teamMembersByRole.value).some(
    (role) => ratingQuestions.value[role] && teamMembersByRole.value[role]?.length > 0,
  );
});
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    header="Đánh giá thành viên tư vấn"
    :closable="true"
    :dismissableMask="true"
  >
    <div class="p-fluid">
      <div v-if="!hasRateableMembers" class="p-3 text-center text-gray-500">
        Không tìm thấy thông tin bác sĩ hoặc tư vấn viên cho deal này
      </div>

      <div v-else class="space-y-6 border-b">
        <!-- Loop through each role and render its members -->
        <template v-for="(roleMembers, roleKey) in teamMembersByRole" :key="roleKey">
          <div v-if="ratingQuestions[roleKey] && roleMembers.length > 0">
            <h3
              class="mb-3 border-b border-gray-200 pb-1 text-lg font-semibold text-primary-700 dark:border-gray-700 dark:text-primary-400"
            >
              {{ roleDisplayNames[roleKey] || roleKey }}
            </h3>

            <!-- Loop through each member with this role -->
            <div v-for="member in roleMembers" :key="member.id" class="mb-4">
              <!-- User header with avatar -->
              <div class="mb-3 flex items-center gap-2">
                <UserAvatar
                  :userId="member.user_id"
                  :user="userDetails[member.user_id]"
                  size="normal"
                  showName
                  additionalClass="font-medium"
                />

                <!-- Average score display -->
                <div class="ml-auto flex items-center">
                  <span
                    class="rounded-md bg-amber-50 px-1.5 py-0.5 text-sm font-semibold text-amber-700 dark:bg-amber-900/50 dark:text-amber-300"
                  >
                    <i class="pi pi-star-fill mr-1 text-xs text-yellow-500"></i>
                    {{ getMemberAverageScore(member.id) }}/10
                  </span>
                </div>
              </div>

              <!-- Questions for this role -->
              <div class="space-y-6 pl-2">
                <template
                  v-for="(questionText, questionKey) in ratingQuestions[roleKey]"
                  :key="questionKey"
                >
                  <div>
                    <div class="mb-2 font-medium text-gray-700 dark:text-gray-300">
                      {{ questionText }}
                    </div>
                    <div class="flex flex-col gap-2">
                      <!-- Move rating buttons next to input -->
                      <!-- Responsive container: column on small, row on sm+ -->
                      <div class="flex flex-col gap-2 sm:flex-row sm:items-center">
                        <!-- Input number option -->
                        <div class="flex flex-shrink-0 items-center gap-2">
                          <!-- Added flex-shrink-0 -->
                          <InputNumber
                            v-model="ratingValues[member.id][questionKey]"
                            :inputId="`rating-input-${member.id}-${questionKey}`"
                            :min="0"
                            :max="10"
                            :step="1"
                            size="small"
                            buttonLayout="horizontal"
                            class="w-10"
                            pt:pcInputText:root:class="text-sm w-full text-success font-semibold rounded-none border-0 border-b p-0 shadow-none focus:ring-0 text-center"
                            @update:modelValue="
                              (value: number | null) => {
                                // Ensure value is stored as number, default to 0 if null
                                ratingValues[member.id][questionKey] = value ?? 0;
                              }
                            "
                            fluid
                          />
                          <span class="font-medium text-muted"> / 10</span>
                        </div>

                        <!-- Rating buttons option - now part of the same flex row -->
                        <!-- Add top margin on small, left margin on sm+ -->
                        <div class="mt-2 flex flex-wrap items-center gap-1.5 sm:ml-10 sm:mt-0">
                          <div
                            v-for="value in ratingValues1to10"
                            :key="value"
                            tabindex="-1"
                            class="rating-value flex h-9 w-9 cursor-pointer items-center justify-center rounded-full text-sm font-medium transition-colors"
                            :class="{
                              'bg-primary-500 text-white dark:bg-primary-400 dark:text-gray-900':
                                ratingValues[member.id]?.[questionKey] === value, // Added optional chaining
                              'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600':
                                ratingValues[member.id]?.[questionKey] !== value, // Added optional chaining
                            }"
                            @click="ratingValues[member.id][questionKey] = value"
                          >
                            {{ value }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <template #footer>
      <Button label="Hủy" icon="pi pi-times" outlined @click="dialogVisible = false" class="mr-2" />
      <Button
        label="Lưu đánh giá"
        icon="pi pi-check"
        @click="handleSubmit"
        :disabled="!hasRateableMembers"
        class="px-4"
      />
    </template>
  </Dialog>
</template>
