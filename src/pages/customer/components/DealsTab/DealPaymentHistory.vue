<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import type {
  AttachmentResponse,
  BillItemResponse,
  Installment,
  InstallmentPlanResponse,
  InstallmentResponse,
} from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Empty from "@/base-components/Empty/Empty.vue";
import Money from "@/base-components/Money.vue";
import useAttachment from "@/hooks/useAttachment";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";

const props = defineProps<{
  dealId: number;
}>();

const { installmentPlans, listInstallmentPlans, isLoading } = useInstallmentPlan();
const { attachments, listAttachments } = useAttachment();

const plan = computed<InstallmentPlanResponse | undefined>(
  () => installmentPlans.value[0] as InstallmentPlanResponse,
);

const sortedInstallments = computed<Installment[]>(() => {
  if (!plan.value?.installments) return [];
  return [...plan.value.installments].sort(
    (a, b) => (a.installment_number || 0) - (b.installment_number || 0),
  );
});

const getStatusSeverity = (state: string, kind?: string) => {
  if (state === "paid" && kind === "refund_payment") return "warn";
  switch (state) {
    case "paid":
      return "success";
    case "partially_paid":
      return "info";
    case "unpaid":
      return "secondary";
  }
};

const getStatusLabel = (state: string, kind?: string) => {
  if (state === "paid" && kind === "refund_payment") return "Hoàn phí";
  switch (state) {
    case "paid":
      return "Đã thanh toán";
    case "partially_paid":
      return "Đang thanh toán";
    case "unpaid":
      return "Chưa thanh toán";
  }
};

const getRemainingAmount = (installment: InstallmentResponse) => {
  if (!installment.allocations?.length) return installment.amount;
  const paidAmount = installment.allocations.reduce((sum, a) => sum + (a.amount || 0), 0);
  return installment.amount - paidAmount;
};

const getMoneyVariant = (state: string, kind?: string) => {
  if (state === "paid" && kind === "refund_payment") return "!text-orange-600";
  switch (state) {
    case "paid":
      return "!text-emerald-600";
    case "partially_paid":
      return "!text-blue-600";
    case "unpaid":
      return "!text-slate-900";
    default:
      return "!text-slate-900";
  }
};

const paymentProgress = computed(() => {
  if (!plan.value) return null;
  return {
    current: plan.value.paid_installment_count,
    total: plan.value.total_installments,
    percentage: Math.round(
      (plan.value.paid_installment_count / plan.value.total_installments) * 100,
    ),
  };
});

const hasPaymentData = computed(() => {
  return installmentPlans.value.length > 0 || attachments.value.length > 0;
});

// attachment section
const isFullyPaid = (billItem: BillItemResponse) => {
  if (!billItem?.allocations?.length) return false;
  const paidAmount = billItem.allocations.reduce((sum, a) => sum + (a.amount || 0), 0);
  return paidAmount >= billItem.amount;
};

const getRemainingAmountForBillItem = (billItem: BillItemResponse) => {
  if (!billItem.allocations?.length) return billItem.amount;
  const paidAmount = billItem.allocations.reduce((sum, a) => sum + (a.amount || 0), 0);
  return billItem.amount - paidAmount;
};

const getPaymentState = (billItem?: BillItemResponse) => {
  if (!billItem?.allocations?.length) return "unpaid";

  const paidAmount = billItem.allocations.reduce((sum, a) => sum + (a.amount || 0), 0);
  if (paidAmount >= billItem.amount) return "paid";
  if (paidAmount > 0) return "partially_paid";
  return "unpaid";
};

onMounted(async () => {
  await listInstallmentPlans({
    filter: { deal_id: props.dealId },
    page: 1,
    page_size: 1,
  });

  // Nếu không có installment plans, fetch attachments
  if (!installmentPlans.value.length) {
    await listAttachments({
      filter: {
        deal_id: props.dealId,
        // kind: "payment", // Assuming payment attachments have kind="payment"
      },
      page: 1,
      page_size: 100,
    });
  }
});
</script>

<template>
  <div class="space-y-4 p-4">
    <!-- Payment Progress -->
    <div v-if="plan" class="mx-auto w-full md:w-1/2">
      <div class="p-2">
        <div class="mb-1.5 flex items-center justify-between">
          <span class="text-xs font-medium text-slate-600">Trả góp</span>
          <span v-if="paymentProgress" class="text-sm font-medium">
            {{ paymentProgress.current }} / {{ paymentProgress.total }} kỳ
            <span class="text-xs text-slate-500">({{ paymentProgress.percentage }}%)</span>
          </span>
        </div>
        <div class="h-1.5 w-full overflow-hidden rounded-full bg-slate-100">
          <div
            class="h-full bg-emerald-500 transition-all duration-300"
            :style="{ width: `${paymentProgress?.percentage ?? 0}%` }"
          />
        </div>
      </div>
    </div>

    <!-- Installments DataView -->
    <DataView
      v-if="sortedInstallments.length"
      :value="sortedInstallments"
      :loading="isLoading"
      dataKey="installment_number"
      class="overflow-hidden rounded-md border border-slate-200"
    >
      <template #list="slotProps">
        <div class="divide-y divide-slate-200">
          <div
            v-for="(item, i) in slotProps.items"
            :key="item.installment_number"
            class="space-y-2 px-4 py-3 hover:bg-slate-50"
          >
            <!-- Main installment info -->
            <div class="grid grid-cols-4 items-center gap-4">
              <div class="font-medium">{{ i + 1 }}. {{ item.name }}</div>
              <div>
                <Money
                  :amount="Math.abs(item.amount)"
                  :custom-class="getMoneyVariant(item.state, item.kind)"
                />
              </div>
              <div class="flex items-center gap-2">
                <Tag
                  :severity="getStatusSeverity(item.state, item.kind)"
                  class="text-xs font-medium"
                  :value="getStatusLabel(item.state, item.kind)"
                />
                <span v-if="item.kind === 'down_payment'" class="text-yellow-600">
                  <Tag severity="info" class="bg-yellow-100 text-xs font-medium text-yellow-800">
                    Trả trước
                  </Tag>
                </span>
              </div>
              <div>
                <span
                  v-if="
                    (item.state === 'partially_paid' ||
                      (item.state === 'paid' && item.allocations && item.allocations.length > 1)) &&
                    item.allocations?.length
                  "
                  class="text-slate-400"
                >
                  -
                </span>
                <DateTime v-else-if="item.paid_at" :time="item.paid_at" showTime />
                <span v-else class="text-slate-400">-</span>
              </div>
            </div>

            <!-- Allocations section -->
            <div
              v-if="
                (item.state === 'partially_paid' ||
                  (item.state === 'paid' && item.allocations && item.allocations.length > 1)) &&
                item.allocations?.length
              "
              class="ml-6 space-y-2 border-l-2 border-blue-200 pl-4"
            >
              <div class="text-sm font-medium text-slate-500">Lịch sử thanh toán:</div>
              <!-- Allocation items -->
              <div class="space-y-2">
                <div
                  v-for="(allocation, idx) in item.allocations"
                  :key="idx"
                  class="grid grid-cols-4 items-center gap-4 text-sm"
                >
                  <div class="font-medium text-slate-500">Lần {{ idx + 1 }}</div>
                  <div class="text-emerald-600">
                    <Money :amount="allocation.amount" />
                  </div>
                  <div>
                    <i class="pi pi-check-circle text-emerald-600" />
                  </div>
                  <div class="text-slate-500">
                    <DateTime :time="allocation.created_at" showTime />
                  </div>
                </div>
                <!-- Remaining amount in separate row -->
                <div class="mt-2 border-t border-slate-100 pt-2 text-sm">
                  <span v-if="item.state === 'paid'" class="text-emerald-600">
                    Đã thanh toán đủ
                  </span>
                  <span v-else class="text-orange-600">
                    Còn lại: <Money :amount="getRemainingAmount(item)" variant="warning" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataView>

    <!-- Attachments DataView -->
    <DataView
      v-if="!sortedInstallments.length && attachments.length"
      :value="attachments"
      :loading="isLoading"
      dataKey="id"
      class="overflow-hidden rounded-md border border-slate-200"
    >
      <template #list="slotProps">
        <div class="divide-y divide-slate-200">
          <div
            v-for="(item, i) in slotProps.items"
            :key="item.id"
            class="space-y-2 px-4 py-3 hover:bg-slate-50"
          >
            <!-- Main payment info -->
            <div class="grid grid-cols-4 items-center gap-4">
              <div class="font-medium">{{ i + 1 }}. {{ item.title || "Thanh toán" }}</div>
              <div>
                <Money
                  :amount="Math.abs(item.bill_item?.amount || item.payment)"
                  :custom-class="getMoneyVariant(getPaymentState(item.bill_item))"
                />
              </div>
              <div class="flex items-center gap-2">
                <Tag
                  :severity="getStatusSeverity(getPaymentState(item.bill_item))"
                  class="text-xs font-medium"
                  :value="getStatusLabel(getPaymentState(item.bill_item))"
                />
              </div>
              <div>
                <span
                  v-if="item.bill_item?.allocations && item.bill_item.allocations.length > 1"
                  class="text-slate-400"
                >
                  -
                </span>
                <DateTime v-else :time="item.created_at" showTime />
              </div>
            </div>

            <!-- Allocations section -->
            <div
              v-if="item.bill_item?.allocations && item.bill_item.allocations.length > 1"
              class="ml-6 space-y-2 border-l-2 border-blue-200 pl-4"
            >
              <div class="text-sm font-medium text-slate-500">Lịch sử thanh toán:</div>
              <div class="space-y-2">
                <div
                  v-for="(allocation, idx) in item.bill_item.allocations"
                  :key="idx"
                  class="grid grid-cols-4 items-center gap-4 text-sm"
                >
                  <div class="font-medium text-slate-500">Lần {{ idx + 1 }}</div>
                  <div class="text-emerald-600">
                    <Money :amount="allocation.amount" />
                  </div>
                  <div>
                    <i class="pi pi-check-circle text-emerald-600" />
                  </div>
                  <div class="text-slate-500">
                    <DateTime :time="allocation.created_at" showTime />
                  </div>
                </div>
                <!-- Remaining amount -->
                <div class="mt-2 border-t border-slate-100 pt-2 text-sm">
                  <span v-if="isFullyPaid(item.bill_item)" class="text-emerald-600">
                    Đã thanh toán đủ
                  </span>
                  <span v-else class="text-orange-600">
                    Còn lại:
                    <Money
                      :amount="getRemainingAmountForBillItem(item.bill_item)"
                      variant="warning"
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataView>

    <Empty v-if="!hasPaymentData" message="Không có lịch sử thanh toán" />
  </div>
</template>
