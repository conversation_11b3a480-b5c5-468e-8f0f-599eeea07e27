<script setup lang="ts">
import dayjs from "dayjs";
import <PERSON>ton from "primevue/button";
import ConfirmPopup from "primevue/confirmpopup";
import Popover from "primevue/popover";
import Tag from "primevue/tag";
import { useConfirm } from "primevue/useconfirm";
import { onMounted, ref, watch } from "vue";

import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import { UserAvatar } from "@/components/User";
import { usePermissions } from "@/composables/usePermissions";
import { usePrint } from "@/composables/usePrint";
import useEconomy from "@/hooks/useEconomy";
import EditPaymentForm from "@/pages/customer/components/BillsTab/EditPaymentForm.vue";

const props = defineProps<{
  paymentId: number;
}>();

const emit = defineEmits<{
  (e: "deleted", paymentId: number): void;
}>();

const confirm = useConfirm();
const { getPayment, deletePayment, currentPayment, updatePaymentDate } = useEconomy();
const { printRefund } = usePrint();
const { onlyAdmin } = usePermissions();

const popoverRef = ref();
const selectedDate = ref<Date | null>(null);
const isEditing = ref(false);

const fetchPaymentData = async () => {
  if (props.paymentId) {
    await getPayment({ id: props.paymentId });
  }
};

watch(() => props.paymentId, fetchPaymentData);
onMounted(fetchPaymentData);

const handleDeletePayment = () => {
  confirm.require({
    message: "Bạn có chắc chắn muốn xóa phiếu hoàn phí này?",
    header: "Xác nhận xóa",
    icon: "pi pi-exclamation-triangle",
    acceptProps: { label: "Xóa", severity: "danger" },
    rejectProps: { label: "Hủy", severity: "secondary", outlined: true },
    accept: async () => {
      await deletePayment({ id: props.paymentId });
      emit("deleted", props.paymentId);
    },
  });
};

const toggleDatePicker = (event: Event) => {
  popoverRef.value.toggle(event);
};

const handleDateSelect = () => {
  if (!onlyAdmin() || !currentPayment.value || !selectedDate.value) {
    popoverRef.value.hide();
    return;
  }

  updatePaymentDate(currentPayment.value, selectedDate.value)
    .then(fetchPaymentData)
    .catch((err) => console.error("Error updating date:", err));

  popoverRef.value.hide();
};

const getPaymentMethodAmount = (method: string) => {
  if (!currentPayment.value) return 0;
  switch (method) {
    case "cash":
      return currentPayment.value.cash || 0;
    case "credit_card":
      return currentPayment.value.credit_card || 0;
    case "bank":
      return currentPayment.value.bank || 0;
    case "mpos":
      return currentPayment.value.mpos || 0;
    case "momo":
      return currentPayment.value.momo || 0;
    default:
      return 0;
  }
};

const paymentMethods = [
  { id: "cash", label: "Tiền mặt" },
  { id: "credit_card", label: "Thẻ tín dụng" },
  { id: "bank", label: "Chuyển khoản" },
  { id: "mpos", label: "mPOS" },
  { id: "momo", label: "MoMo" },
];

const handleEditComplete = () => {
  // Handle the completion of editing
  fetchPaymentData();
  isEditing.value = false;
};
</script>

<template>
  <div v-if="currentPayment" class="rounded-lg border bg-white">
    <div class="border-b p-4">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="font-semibold text-gray-900">Chi tiết hoàn phí #{{ currentPayment.id }}</h3>
          <p class="mt-1 flex items-center gap-1 text-sm text-gray-500">
            Ngày hoàn phí
            <span
              class="flex items-center"
              :class="{
                'cursor-pointer hover:text-primary': onlyAdmin(),
                'cursor-not-allowed': !onlyAdmin(),
              }"
              @click="onlyAdmin() && toggleDatePicker($event)"
              v-tooltip="
                onlyAdmin() ? 'Cập nhật ngày hoàn phí' : 'Chỉ admin mới có quyền cập nhật ngày'
              "
            >
              <DateTime :time="currentPayment.payment_date" show-time />
              <i v-if="onlyAdmin()" class="pi pi-calendar ml-1 text-xs"></i>
            </span>

            <Popover ref="popoverRef">
              <DatePicker
                v-model="selectedDate"
                inline
                @date-select="handleDateSelect"
                :maxDate="new Date()"
              />
            </Popover>
          </p>
        </div>
        <div class="flex items-center gap-2">
          <Tag severity="warn" value="Đã hoàn phí" />
          <Button
            v-if="onlyAdmin()"
            icon="pi pi-trash"
            rounded
            severity="danger"
            text
            @click="handleDeletePayment"
          />
          <Button
            v-if="onlyAdmin()"
            aria-label="Edit"
            icon="pi pi-pencil"
            rounded
            severity="warning"
            text
            v-tooltip="'Chỉnh sửa phương thức hoàn phí'"
            @click="isEditing = true"
          />
          <Button
            icon="pi pi-print"
            rounded
            severity="secondary"
            text
            @click="printRefund(currentPayment.id)"
          />
        </div>
      </div>
    </div>

    <div class="p-4">
      <!-- Thông tin hoàn phí -->
      <template v-if="isEditing">
        <EditPaymentForm
          :payment="currentPayment"
          type="refund"
          @close="isEditing = false"
          @updated="handleEditComplete"
        />
      </template>
      <template v-else>
        <div class="mb-6 flex flex-wrap gap-4">
          <div
            v-for="method in paymentMethods"
            :key="method.id"
            class="min-w-[calc((100%/2)-0.5rem)] flex-1 rounded-lg bg-gray-50 p-3 sm:min-w-[calc((100%/3)-0.75rem)] lg:min-w-[calc((100%/5)-0.8rem)]"
          >
            <div class="text-sm text-gray-500">{{ method.label }}</div>
            <div class="mt-1 text-lg font-semibold">
              <Money :amount="Math.abs(getPaymentMethodAmount(method.id))" variant="warning" />
            </div>
          </div>
        </div>
      </template>

      <!-- Chi tiết hoàn phí -->
      <div>
        <h4 class="mb-3 font-medium text-gray-900">Nội dung hoàn phí</h4>
        <div class="space-y-2">
          <!-- Header -->
          <div
            class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 pb-2 text-xs font-medium uppercase text-gray-500"
          >
            <div class="col-span-4">Tên</div>
            <div class="col-span-1">Người thêm</div>
            <div class="col-span-2 text-right">Thành tiền</div>
            <div class="col-span-2 text-right">Hoàn phí</div>
            <div class="col-span-3 text-center">Ghi chú</div>
          </div>

          <!-- Content -->
          <div v-for="allocation in currentPayment.allocations" :key="allocation.id">
            <div class="grid grid-cols-12 items-center gap-3 py-2 text-sm">
              <div class="col-span-4">
                <!-- Bill Item -->
                <div v-if="allocation.bill_item" class="font-medium text-primary">
                  <AttachmentTitle :attachment="allocation.bill_item.attachment ?? null" />
                  <div class="flex items-center gap-1 text-xs text-gray-500">
                    Hoá đơn: #{{ allocation.bill_item.bill_id }}
                    <DateTime
                      :time="allocation.bill_item.created_at"
                      class="text-muted"
                      size="xs"
                    />
                  </div>
                </div>

                <!-- Installment -->
                <div v-if="allocation.installment" class="font-medium text-primary">
                  {{ allocation.installment.name }}
                  <div class="flex items-center gap-1 text-xs text-gray-500">
                    Trả góp: #{{ allocation.installment.plan_id }}
                    <DateTime
                      :time="allocation.installment.created_at"
                      class="text-muted"
                      size="xs"
                    />
                  </div>
                </div>
              </div>

              <div class="col-span-1">
                <UserAvatar
                  :user="allocation.bill_item?.user || allocation.installment?.creator"
                  size="small"
                />
              </div>

              <div class="col-span-2 text-right">
                <div class="flex flex-col gap-1">
                  <!-- <Money :amount="allocation.bill_item?.amount || allocation.installment?.amount" /> -->
                  <Money :amount="allocation.bill_item?.amount ?? 0" v-if="allocation.bill_item" />
                  <span v-else>-</span>
                </div>
              </div>

              <div class="col-span-2 text-right font-medium">
                <Money :amount="Math.abs(allocation.amount)" variant="warning" />
              </div>

              <div class="col-span-3 text-center">
                {{ allocation.note || "-" }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tổng cộng -->
      <div class="mt-6 border-t pt-4">
        <div class="flex items-center justify-between">
          <span class="font-medium text-gray-900">Tổng tiền hoàn phí</span>
          <Money
            :amount="Math.abs(currentPayment.total_amount)"
            class="text-lg font-medium"
            variant="warning"
          />
        </div>
      </div>
    </div>
    <ConfirmPopup />
  </div>
</template>
