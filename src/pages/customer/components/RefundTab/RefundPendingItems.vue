<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import type { BillItemResponse, InstallmentPlanResponse } from "@/api/bcare-types-v2";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import { UserAvatar } from "@/components/User";
import useBill from "@/hooks/useBill";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";
import type { AllocationItem } from "@/hooks/usePaymentFormState";
import Empty from "@/base-components/Empty";

interface SelectedItemId {
  type: AllocationItem["type"];
  id: number;
}

const props = defineProps<{
  personId: number;
  selectedItemIds: SelectedItemId[];
}>();

const emit = defineEmits<{
  (e: "select-item", item: BillItemResponse): void;
  (e: "select-installment-plan", item: InstallmentPlanResponse): void;
}>();

const { billItems, getPaidItems } = useBill();
const { refundableInstallmentPlans, listRefundableInstallmentPlans } = useInstallmentPlan();
const isLoading = ref(false);
const activeView = ref<"bill_items" | "installment_plans">("bill_items");

const viewOptions = [
  { label: "Dịch vụ/Sản phẩm", value: "bill_items" },
  { label: "Trả góp", value: "installment_plans" },
];

onMounted(async () => {
  try {
    isLoading.value = true;
    await Promise.all([
      getPaidItems({
        person_id: props.personId,
        page_size: 100,
      }),
      listRefundableInstallmentPlans({
        person_id: props.personId,
      }),
    ]);
  } finally {
    isLoading.value = false;
  }
});

const availableBillItems = computed(() => {
  return billItems.value.filter(
    (item) =>
      !props.selectedItemIds.some(
        (selected) => selected.type === "bill_item" && selected.id === item.id,
      ),
  );
});

const availableInstallments = computed(() => {
  return refundableInstallmentPlans.value.filter(
    (item) =>
      !props.selectedItemIds.some(
        (selected) => selected.type === "installment_plan" && selected.id === item.id,
      ),
  );
});
</script>

<template>
  <div class="rounded-lg bg-white">
    <div class="border-b p-4">
      <div class="flex items-center justify-between">
        <h3 class="font-medium">Danh sách đã thanh toán</h3>
        <SelectButton
          v-model="activeView"
          :options="viewOptions"
          optionLabel="label"
          optionValue="value"
        />
      </div>
    </div>

    <div v-if="activeView === 'bill_items'">
      <!-- Bill Items Header -->
      <div
        class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 p-4 pb-2 text-xs font-medium uppercase text-gray-500"
      >
        <div class="col-span-4">Tên</div>
        <div class="col-span-2">Người thêm</div>
        <div class="col-span-2 text-right">Thành tiền</div>
        <div class="col-span-2 text-right">Đã thanh toán</div>
      </div>

      <!-- Bill Items List -->
      <div class="p-2">
        <div v-if="isLoading" class="p-4 text-center">
          <i class="pi pi-spin pi-spinner mr-2" />
          Đang tải...
        </div>
        <div v-else-if="!availableBillItems.length" class="p-4"><Empty /></div>
        <div
          v-for="item in availableBillItems"
          :key="item.id"
          class="group grid cursor-pointer grid-cols-12 items-center gap-3 rounded px-2 py-2 text-sm hover:bg-gray-50"
          @click="emit('select-item', item)"
        >
          <div class="col-span-4">
            <div class="font-medium text-primary">
              <AttachmentTitle :attachment="item.attachment" />
            </div>
            <div class="flex items-center gap-1 text-xs text-gray-500">
              <span>Hoá đơn: #{{ item.bill_id }}</span>
              <DateTime :time="item.created_at" class="text-muted" />
            </div>
          </div>
          <div class="col-span-2">
            <UserAvatar :user="item.user" size="small" />
          </div>
          <div class="col-span-2 text-right">
            <Money :amount="item.amount" />
          </div>
          <div class="col-span-2 text-right">
            <Money :amount="item.paid_amount" variant="default" />
          </div>
        </div>
      </div>
    </div>

    <div v-if="activeView === 'installment_plans'">
      <!-- Installments Header -->
      <div
        class="grid grid-cols-12 gap-3 border-b-2 border-gray-200 p-4 pb-2 text-xs font-medium uppercase text-gray-500"
      >
        <div class="col-span-4">Tên</div>
        <div class="col-span-2">Người thêm</div>
        <div class="col-span-2 text-right">Tổng tiền</div>
        <div class="col-span-2 text-right">Đã thanh toán</div>
        <!-- <div class="col-span-2 text-right">Có thể hoàn</div> -->
      </div>

      <!-- Installments List -->
      <div class="p-2">
        <div v-if="isLoading" class="p-4 text-center">
          <i class="pi pi-spin pi-spinner mr-2" />
          Đang tải...
        </div>
        <div v-else-if="!availableInstallments.length" class="p-4 text-center text-gray-500">
          Không có dữ liệu
        </div>
        <div
          v-for="item in availableInstallments"
          :key="item.id"
          class="group grid cursor-pointer grid-cols-12 items-center gap-3 rounded px-2 py-2 text-sm hover:bg-gray-50"
          @click="emit('select-installment-plan', item)"
        >
          <div class="col-span-4">
            <div class="font-medium text-primary">
              {{ item.name }}
            </div>
            <div class="flex items-center gap-1 text-xs text-gray-500">
              <span>Hoá đơn: #{{ item.bill_id }}</span>
              <DateTime :time="item.created_at" class="text-muted" />
            </div>
          </div>
          <div class="col-span-2">
            <UserAvatar :user="item.creator" size="small" />
          </div>
          <div class="col-span-2 text-right">
            <Money :amount="item.total_amount" />
          </div>
          <div class="col-span-2 text-right">
            <Money :amount="item.paid_amount" variant="default" />
          </div>
          <!-- <div class="col-span-2 text-right font-medium">
            <Money :amount="item.refund_amount" variant="warning" />
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
