<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from "vue";

import type {
  BillItemResponse,
  InstallmentPlanResponse,
  PaymentAddRequest,
  Track,
} from "@/api/bcare-types-v2";
import { trackUpdate } from "@/api/bcare-v2";
import DateTime from "@/base-components/DateTime.vue";
import { config } from "@/config";
import useEconomy from "@/hooks/useEconomy";
import useInstallmentPlan from "@/hooks/useInstallmentPlan";
import useTrack from "@/hooks/useTrack";
import { PaymentMethodId } from "../BillsTab/PaymentForm/PaymentMethods.vue";
import type { AllocationItem } from "@/hooks/usePaymentFormState";

import PaymentMethods from "./RefundPaymentMethods.vue";
import PendingItems from "./RefundPendingItems.vue";
import SelectedItems from "./RefundSelectedItems.vue";
import { useRefundFormState } from "@/hooks/useRefundFormState";

const props = defineProps<{
  personId: number;
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "saved", paymentId: number): void;
}>();

const { addPayment } = useEconomy();
const { getActiveTrack } = useTrack();
const {
  formData,
  selectedAllocations,
  totalAmount,
  totalPayableAmount,
  allocatePayments,
  resetState,
} = useRefundFormState();
const { addInstallment, deleteInstallment } = useInstallmentPlan();

const isLoading = ref(false);
const activeTrack = ref<Track | null>(null);

const selectedItemIds = computed(() =>
  selectedAllocations.value.map((a) => ({
    type: a.type,
    id: a.type === "bill_item" ? a.bill_item!.id : a.installment_plan!.id,
  })),
);

const isAnyItemEditing = computed(() => selectedAllocations.value.some((item) => item.isEditing));

const handleSelectItem = (item: BillItemResponse) => {
  selectedAllocations.value.push({
    type: "bill_item",
    bill_item: item,
    amount: 0,
    isEditing: true,
  } as AllocationItem);
};

const handleSelectInstallmentPlan = (item: InstallmentPlanResponse) => {
  selectedAllocations.value.push({
    type: "installment_plan",
    installment_plan: item,
    amount: 0,
    isEditing: true,
  } as AllocationItem);
};

const handleUpdateAllocation = (index: number, amount: number) => {
  selectedAllocations.value[index].amount = amount;

  formData.value.total_amount = selectedAllocations.value.reduce(
    (sum, allocation) => sum + Math.abs(allocation.amount),
    0,
  );

  formData.value.cash = formData.value.total_amount;
  formData.value.modified = ["cash"];
};

const handleRemoveItem = (index: number) => {
  selectedAllocations.value.splice(index, 1);

  const totalPaidAmount = selectedAllocations.value.reduce((total, allocation) => {
    if (allocation.type === "bill_item" && allocation.bill_item) {
      return total + allocation.bill_item.paid_amount;
    }
    return total;
  }, 0);

  formData.value.credit_card = 0;
  formData.value.bank = 0;
  formData.value.mpos = 0;
  formData.value.cash = totalPaidAmount;

  allocatePayments();
};

const handleSave = async (isSendSMS?: boolean) => {
  let createdInstallments: { allocation: any; installment: any }[] = [];

  try {
    isLoading.value = true;

    // Create installments first
    const installmentPromises = selectedAllocations.value
      .filter((a) => a.type === "installment_plan" && a.installment_plan)
      .map(async (allocation) => {
        const response = await addInstallment({
          plan_id: allocation.installment_plan!.id,
          installment_number: 0,
          amount: -Math.abs(allocation.amount),
          person_id: props.personId,
          kind: "refund_payment",
          status: 2,
          transaction_type: 1,
        });
        return {
          allocation,
          installment: response,
        };
      });

    createdInstallments = await Promise.all(installmentPromises);

    const refundRequest: PaymentAddRequest = {
      person_id: props.personId,
      payment_date: formData.value.payment_date,
      total_amount: -Math.abs(totalAmount.value),
      state: "completed",
      bill_id: 0,
      send_sms: isSendSMS ? 0 : 1, // 0: Gửi, 1: Không gửi
      allocations: selectedAllocations.value.map((allocation) => {
        if (allocation.type === "installment_plan") {
          const created = createdInstallments.find((i) => i.allocation === allocation);
          return {
            installment_id: created?.installment?.id,
            amount: -Math.abs(allocation.amount),
            note: allocation.note,
          };
        }
        return {
          bill_item_id: allocation.bill_item?.id,
          amount: -Math.abs(allocation.amount),
          note: allocation.note,
        };
      }),
    };

    formData.value.modified.forEach((field) => {
      switch (field) {
        case "cash":
          refundRequest.cash = -Math.abs(formData.value.cash);
          break;
        case "credit_card":
          refundRequest.credit_card = -Math.abs(formData.value.credit_card);
          break;
        case "bank":
          refundRequest.bank = -Math.abs(formData.value.bank);
          break;
        case "mpos":
          refundRequest.mpos = -Math.abs(formData.value.mpos);
          break;
        case "momo":
          refundRequest.momo = -Math.abs(formData.value.momo);
          break;
      }
    });

    const payment = await addPayment(refundRequest);

    if (payment) {
      await trackUpdate({
        id: activeTrack.value!.id,
        stage_id: config.clinic.after_payment_stage_id,
        weight: 0,
      });
      emit("saved", payment.id);
    }

    resetState();
    emit("close");
  } catch (error: any) {
    // Rollback created installments for both code 1003 and other errors
    await Promise.all(
      createdInstallments.map(({ installment }) => deleteInstallment({ id: installment.id })),
    );
    console.error("Failed to create refund:", error);
  } finally {
    isLoading.value = false;
  }
};

onMounted(async () => {
  const track = await getActiveTrack({ person_id: props.personId });
  activeTrack.value = track;
});

onUnmounted(() => {
  resetState();
});

const saveItems = ref([
  { label: "Lưu và không gửi SMS", icon: "pi pi-bell-slash", command: () => handleSave(false) },
]);
</script>

<template>
  <div class="space-y-4">
    <!-- Main Refund Form Card -->
    <div class="rounded-lg border bg-white">
      <div class="border-b p-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold text-gray-900">Tạo phiếu hoàn phí</h3>
            <p class="mt-1 text-sm text-gray-500">
              Ngày tạo
              <DateTime :time="formData.payment_date" show-time />
            </p>
          </div>
          <div class="flex items-center gap-2">
            <Button label="Hủy" severity="secondary" outlined text @click="emit('close')" />
            <Button
              label="Clear"
              outlined
              severity="info"
              @click="
                () => {
                  formData.cash = 0;
                  formData.credit_card = 0;
                  formData.bank = 0;
                  formData.mpos = 0;
                  formData.modified = ['cash', 'credit_card', 'bank', 'mpos'];
                }
              "
            />
            <SplitButton
              :loading="isLoading"
              label="Lưu"
              severity="warning"
              :model="saveItems"
              icon="pi pi-save"
              :disabled="isAnyItemEditing"
              @click="handleSave(true)"
            />
          </div>
        </div>
      </div>

      <div class="space-y-6 p-4">
        <!-- Payment Methods -->
        <div>
          <h4 class="mb-3 font-medium text-gray-700">Phương thức hoàn phí</h4>
          <PaymentMethods
            v-model:cash="formData.cash"
            v-model:creditCard="formData.credit_card"
            v-model:bank="formData.bank"
            v-model:mpos="formData.mpos"
            v-model:momo="formData.momo"
            :disabled="isAnyItemEditing"
            @update:modified="(field: PaymentMethodId) => formData.modified.push(field)"
          />
        </div>

        <!-- Selected Items -->
        <div>
          <h4 class="mb-3 font-medium text-gray-700">Nội dung hoàn phí</h4>
          <SelectedItems
            :allocations="selectedAllocations"
            :totalAmount="totalAmount"
            :totalPayableAmount="totalPayableAmount"
            @remove="handleRemoveItem"
            @update:allocation="handleUpdateAllocation"
          />
        </div>
      </div>
    </div>

    <!-- Pending Items Card -->
    <div class="rounded-lg border bg-white">
      <PendingItems
        :personId="props.personId"
        :selectedItemIds="selectedItemIds"
        @select-item="handleSelectItem"
        @select-installment-plan="handleSelectInstallmentPlan"
      />
    </div>
  </div>
</template>
