<script setup lang="ts">
import type { PaymentResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";

defineProps<{
  payment: PaymentResponse;
  isSelected: boolean;
}>();
</script>

<template>
  <div
    :class="{
      'border-primary-500': isSelected,
      'border-transparent': !isSelected,
    }"
    class="cursor-pointer border-l-4 p-2 transition-colors hover:bg-gray-50"
  >
    <div class="flex items-center justify-between">
      <span class="font-medium">#{{ payment.id }}</span>
      <Tag severity="warn" value="Đã hoàn" class="text-xs p-0.5 font-medium" />
    </div>
    <div class="mt-1 font-medium text-danger">
      <Money :amount="Math.abs(payment.total_amount)" variant="warning" />
    </div>
    <div class="mt-1 text-sm text-gray-500">
      <DateTime :time="payment.payment_date" />
    </div>
  </div>
</template>
