<script setup lang="ts">
interface Props {
  cash: number;
  creditCard: number;
  bank: number;
  mpos: number;
  momo: number;
  disabled?: boolean;
}

// Define the type for keys that hold payment amounts
type PaymentAmountKey = Exclude<keyof Props, "disabled">;
type PaymentMethodId = "cash" | "credit_card" | "bank" | "mpos" | "momo";

// Define the structure of items in paymentMethods
interface PaymentMethod {
  id: PaymentMethodId;
  label: string;
  icon: string;
  modelValue: PaymentAmountKey;
  tooltip: string;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: "cash",
    label: "Tiền mặt",
    icon: "pi pi-wallet",
    modelValue: "cash",
    tooltip: "Hoàn tiền mặt",
  },
  {
    id: "credit_card",
    label: "Thẻ tín dụng",
    icon: "pi pi-credit-card",
    modelValue: "creditCard",
    tooltip: "Hoàn tiền qua thẻ tín dụng",
  },
  {
    id: "bank",
    label: "<PERSON><PERSON><PERSON><PERSON> khoản",
    icon: "pi pi-send",
    modelValue: "bank",
    tooltip: "Hoàn tiền qua chuyển khoản",
  },
  {
    id: "mpos",
    label: "mPOS",
    icon: "pi pi-mobile",
    modelValue: "mpos",
    tooltip: "Hoàn tiền qua mPOS",
  },
  {
    id: "momo",
    label: "MoMo",
    icon: "pi pi-mobile",
    modelValue: "momo",
    tooltip: "Hoàn tiền qua MoMo",
  },
];

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  (e: "update:cash", value: number): void;
  (e: "update:creditCard", value: number): void;
  (e: "update:bank", value: number): void;
  (e: "update:mpos", value: number): void;
  (e: "update:momo", value: number): void;
  (e: "update:modified", field: PaymentMethodId): void;
  (e: "methodChange", method: PaymentMethodId, value: number): void;
}>();

const handleChange = (method: PaymentMethodId, value: number) => {
  switch (method) {
    case "cash":
      emit("update:cash", value);
      emit("update:modified", "cash");
      break;
    case "credit_card":
      emit("update:creditCard", value);
      emit("update:modified", "credit_card");
      break;
    case "bank":
      emit("update:bank", value);
      emit("update:modified", "bank");
      break;
    case "mpos":
      emit("update:mpos", value);
      emit("update:modified", "mpos");
      break;
    case "momo":
      emit("update:momo", value);
      emit("update:modified", "momo");
      break;
  }
  emit("methodChange", method, value);
};

const handlePayAll = (method: PaymentMethodId) => {
  const totalAmount = Math.abs(
    props.cash + props.creditCard + props.bank + props.mpos + props.momo,
  );

  // Clear all methods first
  emit("update:cash", 0);
  emit("update:creditCard", 0);
  emit("update:bank", 0);
  emit("update:mpos", 0);
  emit("update:momo", 0);

  // Set the selected method to total amount
  switch (method) {
    case "cash":
      emit("update:cash", totalAmount);
      emit("update:modified", "cash");
      break;
    case "credit_card":
      emit("update:creditCard", totalAmount);
      emit("update:modified", "credit_card");
      break;
    case "bank":
      emit("update:bank", totalAmount);
      emit("update:modified", "bank");
      break;
    case "mpos":
      emit("update:mpos", totalAmount);
      emit("update:modified", "mpos");
      break;
    case "momo":
      emit("update:momo", totalAmount);
      emit("update:modified", "momo");
      break;
  }
  emit("methodChange", method, totalAmount);
};
</script>

<template>
  <div class="mb-6 flex flex-wrap gap-4">
    <div
      v-for="method in paymentMethods"
      :key="method.id"
      class="min-w-[calc((100%/2)-0.5rem)] flex-1 rounded-lg bg-gray-50 p-3 sm:min-w-[calc((100%/3)-0.75rem)] lg:min-w-[calc((100%/5)-0.8rem)]"
    >
      <div class="flex items-center justify-between text-sm text-gray-500">
        <span>{{ method.label }}</span>
        <Button
          v-tooltip.bottom="method.tooltip"
          class="p-1 transition-transform hover:scale-110"
          text
          :disabled="props.disabled"
          @click="handlePayAll(method.id)"
        >
          <i :class="[method.icon, 'text-sm']" />
        </Button>
      </div>
      <div class="mt-1">
        <InputNumber
          :modelValue="Math.abs(props[method.modelValue])"
          @update:modelValue="(value) => handleChange(method.id, Math.abs(value ?? 0))"
          :min="0"
          mode="currency"
          currency="VND"
          locale="vi-VN"
          fluid
          :disabled="props.disabled"
        />
      </div>
    </div>
  </div>
</template>
