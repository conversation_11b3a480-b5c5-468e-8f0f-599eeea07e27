<script setup lang="ts">
import { computed, ref } from "vue";

import type { BillItemResponse } from "@/api/bcare-types-v2";
import type { AllocationItem } from "@/hooks/usePaymentFormState";

const props = defineProps<{
  show: boolean;
  billItem: BillItemResponse;
}>();

const emit = defineEmits<{
  (e: "hide"): void;
  (e: "update:show", value: boolean): void;
  (e: "confirm", allocation: AllocationItem): void;
}>();

const methods = ref({
  cash: 0,
  credit_card: 0,
  bank: 0,
  mpos: 0,
});

const refundAmount = computed(() =>
  Object.values(methods.value).reduce((sum, val) => sum + (val || 0), 0),
);

const isValid = computed(
  () => refundAmount.value > 0 && refundAmount.value <= props.billItem.paid_amount,
);

function handleConfirm() {
  if (!isValid.value) return;

  emit("confirm", {
    type: "bill_item",
    bill_item: props.billItem,
    amount: -refundAmount.value, // Negative amount for refund
  });

  methods.value = { cash: 0, credit_card: 0, bank: 0, mpos: 0 };
  emit("hide");
}
</script>

<template>
  <Dialog
    :visible="show"
    @update:visible="$emit('update:show', $event)"
    modal
    :header="`Nhập số tiền hoàn phí cho ${billItem?.attachment?.product?.name || 'Unknown'}`"
    class="w-[500px]"
  >
    <div class="space-y-4">
      <div class="mb-4">
        <div class="text-sm text-gray-600">Số tiền đã thanh toán:</div>
        <Money :amount="billItem.paid_amount" class="text-lg font-medium" />
      </div>

      <div v-for="(value, key) in methods" :key="key" class="flex items-center gap-4">
        <label class="w-32 capitalize">{{ key.replace("_", " ") }}</label>
        <InputNumber
          v-model="methods[key]"
          :max="billItem.paid_amount"
          mode="currency"
          currency="VND"
          class="w-full"
        />
      </div>

      <div class="flex justify-between font-medium">
        <span>Tổng tiền hoàn:</span>
        <Money :amount="refundAmount" variant="warning" />
      </div>

      <div v-if="refundAmount > billItem.paid_amount" class="text-danger">
        Số tiền hoàn không được lớn hơn số tiền đã thanh toán
      </div>
    </div>

    <template #footer>
      <Button label="Huỷ" outlined @click="$emit('hide')" />
      <Button label="Xác nhận" :disabled="!isValid" @click="handleConfirm" />
    </template>
  </Dialog>
</template>
