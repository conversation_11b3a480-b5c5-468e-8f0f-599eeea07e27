<script setup lang="ts">
import Alert from "@/base-components/Alert/Alert.vue";
import AttachmentTitle from "@/base-components/AttachmentTitle.vue";
import DateTime from "@/base-components/DateTime.vue";
import Money from "@/base-components/Money.vue";
import { UserAvatar } from "@/components/User";
import type { AllocationItem } from "@/hooks/usePaymentFormState";

import { useRefundFormState } from "@/hooks/useRefundFormState";

const props = defineProps<{
  allocations: AllocationItem[];
  totalAmount: number;
  totalPayableAmount: number;
}>();

const { totalPaidAmount, isOverRefunded, selectedAllocations } = useRefundFormState();

const emit = defineEmits<{
  (e: "remove", index: number): void;
  (e: "update:allocation", index: number, amount: number): void;
}>();

const toggleEdit = (allocation: AllocationItem) => {
  props.allocations.forEach((item) => {
    if (item !== allocation) {
      item.isEditing = false;
    }
  });
  allocation.isEditing = !allocation.isEditing;
};

const handleAmountUpdate = (index: number, amount: number) => {
  if (amount > 0) {
    emit("update:allocation", index, amount);
    props.allocations[index].isEditing = false;
  }
};

const handleNoteChange = (allocation: AllocationItem) => {
  const index = selectedAllocations.value.findIndex(
    (a) =>
      a.type === allocation.type &&
      ((a.type === "bill_item" && a.bill_item?.id === allocation.bill_item?.id) ||
        (a.type === "installment" && a.installment?.id === allocation.installment?.id)),
  );

  if (index !== -1) {
    selectedAllocations.value[index].note = allocation.note;
  }
};

const getAllocationMaxAmount = (allocation: AllocationItem): number => {
  switch (allocation.type) {
    case "bill_item":
      return allocation.bill_item?.paid_amount || 0;
    case "installment_plan":
      return allocation.installment_plan?.total_amount || 0;
    case "installment":
      return allocation.installment?.paid_amount || 0;
    default:
      return 0;
  }
};
</script>

<template>
  <div>
    <!-- Header -->
    <div
      class="grid grid-cols-10 gap-3 border-b-2 border-gray-200 pb-2 text-xs font-medium uppercase text-gray-500"
    >
      <div class="col-span-3">Tên</div>
      <div class="col-span-1">Người thêm</div>
      <div class="col-span-3 text-right">Thành tiền / Đã thanh toán</div>
      <div class="col-span-3 text-center">Hoàn phí</div>
    </div>

    <!-- Empty state message -->
    <div v-if="allocations.length === 0" class="py-5 text-center text-gray-500">
      <p>Chọn sản phẩm/dịch vụ cần hoàn phí từ danh sách bên dưới</p>
      <i class="pi pi-arrow-down mt-2 animate-bounce text-2xl" />
    </div>

    <!-- Selected Items -->
    <div v-for="(allocation, index) in allocations" :key="index">
      <template v-if="allocation.bill_item && allocation.type === 'bill_item'">
        <div
          :class="[
            'grid grid-cols-10 items-center gap-3 text-sm text-gray-900',
            { group: !allocation.isEditing },
          ]"
        >
          <div class="col-span-3">
            <div class="font-medium text-primary">
              <AttachmentTitle :attachment="allocation.bill_item.attachment" />
            </div>
            <div class="flex items-center gap-1 text-xs text-gray-500">
              Hoá đơn: #{{ allocation.bill_item?.bill_id }}
              <DateTime :time="allocation.bill_item?.created_at" class="text-muted" size="xs" />
            </div>
          </div>

          <div class="col-span-1">
            <UserAvatar :user="allocation.bill_item?.user" size="small" />
          </div>

          <div class="relative col-span-3 text-right">
            <span class="flex flex-col gap-1 transition-opacity group-hover:opacity-0">
              <Money :amount="allocation.bill_item?.amount || 0" />
              <div class="text-gray-500">
                Paid:
                <Money :amount="allocation.bill_item?.paid_amount || 0" variant="default" />
              </div>
            </span>
            <div
              class="absolute right-0 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
            >
              <Button
                aria-label="Remove from payment"
                class="size-8 text-xs"
                outlined
                severity="info"
                size="small"
                @click="emit('remove', index)"
              >
                <i class="pi pi-arrow-down text-sm" />
              </Button>
            </div>
          </div>

          <div class="relative col-span-3 text-center">
            <template v-if="allocation.isEditing">
              <div class="flex h-full items-center gap-2">
                <div class="flex w-full flex-col gap-2">
                  <!-- Amount input row -->
                  <div class="flex items-center gap-1">
                    <InputNumber
                      v-model="allocation.amount"
                      :max="getAllocationMaxAmount(allocation)"
                      mode="currency"
                      currency="VND"
                      locale="vi-VN"
                      fluid
                      size="small"
                      class="h-[32px]"
                      @keyup.enter="handleAmountUpdate(index, Math.abs(allocation.amount))"
                    />
                  </div>
                  <!-- Note input row -->
                  <div class="flex items-center gap-1">
                    <InputText
                      v-model="allocation.note"
                      placeholder="Ghi chú"
                      class="h-[32px] w-full text-sm"
                      size="small"
                      @update:modelValue="handleNoteChange(allocation)"
                    />
                  </div>
                </div>
                <!-- Save button -->
                <div class="flex justify-center">
                  <Button
                    aria-label="Confirm refund amount"
                    class="size-8 text-xs"
                    severity="success"
                    size="small"
                    @click="handleAmountUpdate(index, Math.abs(allocation.amount))"
                  >
                    <i class="pi pi-check text-sm" />
                  </Button>
                </div>
              </div>
            </template>
            <template v-else>
              <span class="transition-opacity group-hover:opacity-0">
                <Money :amount="Math.abs(allocation.amount)" variant="warning" />
                <div v-if="allocation.note" class="mt-1">
                  {{ allocation.note }}
                </div>
              </span>
              <div
                class="absolute right-1/2 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <Button
                  aria-label="Edit refund amount"
                  class="size-8 text-xs"
                  outlined
                  severity="warning"
                  size="small"
                  @click="toggleEdit(allocation)"
                >
                  <i class="pi pi-pencil text-sm" />
                </Button>
              </div>
            </template>
          </div>
        </div>
      </template>

      <template v-if="allocation.installment_plan && allocation.type === 'installment_plan'">
        <div
          :class="[
            'grid grid-cols-10 items-center gap-3 text-sm text-gray-900',
            { group: !allocation.isEditing },
          ]"
        >
          <div class="col-span-3">
            <div class="font-medium text-primary">
              {{ allocation.installment_plan.name }}
            </div>
            <div class="flex items-center gap-1 text-xs text-gray-500">
              Trả góp: #{{ allocation.installment_plan.id }}
              <DateTime
                :time="allocation.installment_plan.created_at"
                class="text-muted"
                size="xs"
              />
            </div>
          </div>

          <div class="col-span-1">
            <UserAvatar :user="allocation.installment_plan.creator" size="small" />
          </div>

          <div class="relative col-span-3 text-right">
            <span class="flex flex-col gap-1 transition-opacity group-hover:opacity-0">
              <Money :amount="allocation.installment_plan.total_amount" />
              <div class="text-gray-500">
                Paid: <Money :amount="allocation.installment_plan.paid_amount" variant="default" />
              </div>
            </span>
            <div
              class="absolute right-0 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
            >
              <Button
                aria-label="Remove from payment"
                class="size-8 text-xs"
                outlined
                severity="info"
                size="small"
                @click="emit('remove', index)"
              >
                <i class="pi pi-arrow-down text-sm" />
              </Button>
            </div>
          </div>

          <div class="relative col-span-3 text-center">
            <template v-if="allocation.isEditing">
              <div class="flex h-full items-center gap-2">
                <div class="flex w-full flex-col gap-2">
                  <!-- Amount input row -->
                  <div class="flex items-center gap-1">
                    <InputNumber
                      v-model="allocation.amount"
                      :max="getAllocationMaxAmount(allocation)"
                      mode="currency"
                      currency="VND"
                      locale="vi-VN"
                      fluid
                      size="small"
                      class="h-[32px]"
                      @keyup.enter="handleAmountUpdate(index, Math.abs(allocation.amount))"
                    />
                  </div>
                  <!-- Note input row -->
                  <div class="flex items-center gap-1">
                    <InputText
                      v-model="allocation.note"
                      placeholder="Ghi chú"
                      class="h-[32px] w-full text-sm"
                      size="small"
                      @update:modelValue="handleNoteChange(allocation)"
                    />
                  </div>
                </div>
                <!-- Save button -->
                <div class="flex justify-center">
                  <Button
                    aria-label="Confirm refund amount"
                    class="size-8 text-xs"
                    severity="success"
                    size="small"
                    @click="handleAmountUpdate(index, Math.abs(allocation.amount))"
                  >
                    <i class="pi pi-check text-sm" />
                  </Button>
                </div>
              </div>
            </template>
            <template v-else>
              <span class="transition-opacity group-hover:opacity-0">
                <Money :amount="Math.abs(allocation.amount)" variant="warning" />
                <div v-if="allocation.note" class="mt-1">
                  {{ allocation.note }}
                </div>
              </span>
              <div
                class="absolute right-1/2 top-1/2 z-10 flex -translate-y-1/2 items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <Button
                  aria-label="Edit refund amount"
                  class="size-8 text-xs"
                  outlined
                  severity="warning"
                  size="small"
                  @click="toggleEdit(allocation)"
                >
                  <i class="pi pi-pencil text-sm" />
                </Button>
              </div>
            </template>
          </div>
        </div>
      </template>
    </div>
  </div>

  <!-- Total Section -->
  <div class="mt-4 border-t pt-4">
    <div class="space-y-2">
      <div class="flex items-center justify-between">
        <span class="font-medium text-gray-700">Tổng tiền đã thanh toán</span>
        <div class="text-right">
          <Money :amount="totalPaidAmount" class="text-lg font-medium" />
        </div>
      </div>
      <div class="flex items-center justify-between">
        <span class="font-medium text-gray-900">Tổng tiền hoàn phí</span>
        <div class="text-right">
          <Money :amount="totalAmount" class="text-lg font-medium text-danger" variant="warning" />
        </div>
      </div>

      <div v-if="isOverRefunded" class="mt-2">
        <Alert class="flex items-center" variant="soft-warning">
          Số tiền hoàn phí lớn hơn số tiền đã thanh toán. Vui lòng kiểm tra lại.
        </Alert>
      </div>
    </div>
  </div>
</template>
