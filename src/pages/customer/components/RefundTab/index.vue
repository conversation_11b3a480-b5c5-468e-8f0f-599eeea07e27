<template>
  <div class="flex h-full flex-col">
    <div class="px-5 pb-5">
      <Menubar :model="items" class="px-1 py-1" />
    </div>

    <div class="flex-1 overflow-hidden px-5">
      <div class="grid grid-cols-12 gap-4">
        <!-- Left Panel - Refund List -->
        <div class="col-span-12 md:col-span-2">
          <div class="rounded-lg border bg-white">
            <div class="flex items-center justify-between gap-1 border-b border-gray-100 p-2">
              <h3 class="font-semibold text-gray-800">Lịch sử hoàn phí</h3>
              <Badge
                :value="filteredPayments.length"
                class="size-6 rounded-full p-1 text-xs font-normal"
                severity="info"
              />
            </div>
            <div class="border-b p-2">
              <InputText v-model="searchQuery" size="small" fluid placeholder="Tìm kiếm..." />
            </div>
            <div class="overflow-y-auto md:max-h-[35rem]">
              <RefundListItem
                v-for="(payment, index) in filteredPayments"
                :key="payment.id"
                :payment="payment"
                :is-selected="selectedPayment?.id === payment.id"
                :class="{ 'bg-gray-50': index % 2 === 0 }"
                @click="handlePaymentSelect(payment)"
              />
            </div>

            <!-- Add total refund stats -->
            <div class="border-t border-gray-100 p-3">
              <div class="font-medium text-muted">Tổng hoàn phí</div>
              <div class="font-semibold text-warning">
                <Money :amount="stats.totalRefund" variant="warning" />
              </div>
            </div>
          </div>
        </div>

        <!-- Right Panel - Refund Form/Detail -->
        <div class="col-span-12 md:col-span-10">
          <RefundForm
            v-if="showRefundForm"
            :personId="props.personId"
            @close="handleRefundFormClose"
            @saved="refreshPaymentList"
          />
          <RefundDetail
            v-else-if="selectedPayment"
            :paymentId="selectedPayment.id"
            @deleted="refreshPaymentList"
          />
          <div v-else class="flex h-full items-center justify-center rounded-lg bg-white shadow">
            <div class="text-center">
              <i class="pi pi-file text-4xl text-gray-400" />
              <p class="mt-2 text-gray-500">Chọn một phiếu hoàn phí để xem chi tiết</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import type { PaymentResponse } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import { usePrint } from "@/composables/usePrint";
import useEconomy from "@/hooks/useEconomy";

import RefundDetail from "./RefundDetail.vue";
import RefundForm from "./RefundForm.vue";
import RefundListItem from "./RefundListItem.vue";

const props = defineProps<{
  personId: number;
}>();

const { payments, listPayments } = useEconomy();
const { printPayment } = usePrint();

const selectedPayment = ref<PaymentResponse | null>(null);
const searchQuery = ref("");
const showRefundForm = ref(true);
const isLoading = ref(false);

const items = computed(() => [
  {
    label: "Tạo hoàn phí",
    icon: "pi pi-plus",
    command: () => {
      showRefundForm.value = true;
      selectedPayment.value = null;
    },
  },
]);

const filteredPayments = computed(() => {
  return payments.value.filter(
    (payment) =>
      payment.total_amount < 0 && // Only show refund payments (negative amounts)
      (!searchQuery.value || payment.id.toString().includes(searchQuery.value)),
  );
});

const handleRefundFormClose = () => {
  showRefundForm.value = false;
};

const handlePaymentSelect = (payment: PaymentResponse) => {
  showRefundForm.value = false;
  selectedPayment.value = payment;
};

const refreshPaymentList = async (newPaymentId?: number) => {
  await listPayments({
    filter: { person_id: props.personId },
    kind: "refund",
    order_by: "payment_date desc",
  });

  if (newPaymentId) {
    const newPayment = payments.value.find((p) => p.id === newPaymentId);
    if (newPayment) {
      showRefundForm.value = false;
      selectedPayment.value = newPayment;
    }
  } else {
    selectedPayment.value = null;
  }
};

const stats = computed(() => ({
  totalRefund: filteredPayments.value.reduce((sum, p) => sum + Math.abs(p.total_amount), 0),
}));

onMounted(async () => {
  try {
    isLoading.value = true;
    await listPayments({
      filter: { person_id: props.personId },
      kind: "refund",
      order_by: "payment_date desc",
    });
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped></style>
