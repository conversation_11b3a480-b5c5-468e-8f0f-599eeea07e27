<script lang="ts" setup>
import { computed, onMounted, onUnmounted, provide, readonly, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import { DealResponse, PersonAssignmentResponse, PersonResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { usePermissions } from "@/composables/usePermissions";
import { useTabRouting } from "@/composables/useTabRouting";
import useDeal from "@/hooks/useDeal";
import useToggle from "@/hooks/useToggle";
import PersonPrimaryInfo from "@/pages/customer/components/PersonPrimaryInfo.vue";
import { useCacheDataStore } from "@/stores/cache-data-store";
import { tabList } from "@/config/person-tabs.config";

import { usePaymentFormState } from "@/hooks/usePaymentFormState";
import PersonAddModal from "@/components/Person/PersonFormModal.vue";
import usePerson from "@/hooks/usePerson";

const abortController = ref(new AbortController());
const props = defineProps<{ personId: number; isDoctor?: boolean }>();
const emits = defineEmits<{ (event: "loadPerson", person: PersonResponse): void }>();
const { getPerson, currentPerson } = usePerson();

const hasHistory = ref(false);
const selectedTab = ref("");
const router = useRouter();
const route = useRoute();
const { getInitialTab } = useTabRouting();
const switchPersonDetail = ref(false);
const isShowPersonDialog = useToggle(false);
const flagGetDetail = ref(0);
const specialNote = ref("");
const medicalCondition = ref("");
const assignmentDoctor = ref<PersonAssignmentResponse>();
const assignmentCounselor = ref<PersonAssignmentResponse>();
const assignmentSale = ref<PersonAssignmentResponse>();
const assignmentCustomerCare = ref<PersonAssignmentResponse>();
const isLoading = ref(false);
const isLeftDivVisible = ref(true);
const unsubscribes: (() => void)[] = [];
const cacheDataStore = useCacheDataStore();
const { resetState: resetPaymentState } = usePaymentFormState();
const { ability } = usePermissions();

const { deals, loadDeals, personId } = useDeal({
  autoLoad: true,
  personId: props.personId,
});

const highlightedDeal = ref<DealResponse>();

const resetComponentState = () => {
  currentPerson.value = {} as PersonResponse;
  deals.value = [];
  highlightedDeal.value = undefined;
  specialNote.value = "";
  medicalCondition.value = "";
  assignmentDoctor.value = undefined;
  assignmentCounselor.value = undefined;
  assignmentSale.value = undefined;
  assignmentCustomerCare.value = undefined;
  isLoading.value = false;
  abortController.value.abort();
  abortController.value = new AbortController();
};

const loadPersonDetail = async () => {
  if (isLoading.value) return;
  isLoading.value = true;
  try {
    const id = props.personId;
    if (id) {
      await getPerson({
        id,
        full_name: "",
        phone: "",
        email: "",
      });

      if (currentPerson.value) {
        specialNote.value = currentPerson.value.person_field.special_note || "";
        medicalCondition.value = currentPerson.value.person_field.medical_condition || "";
        emits("loadPerson", currentPerson.value);
        assignmentDoctor.value = findAssignmentByRole("doctor");
        assignmentCounselor.value = findAssignmentByRole("counselor");
        assignmentSale.value = findAssignmentByRole("sale");
        assignmentCustomerCare.value = findAssignmentByRole("customer_care");
      }
    }
  } catch (error) {
    if (!abortController.value.signal.aborted) {
      console.error("Load person detail error:", error);
    }
  } finally {
    isLoading.value = false;
  }
};

provide("person", readonly(currentPerson));
provide("loadPersonDetail", loadPersonDetail);
provide("dealSelectedCallback", (deal: DealResponse) => {
  highlightedDeal.value = deal;
});
provide("dealList", deals);
provide("highlightedDeal", readonly(highlightedDeal));

const findAssignmentByRole = (role: string) => {
  return currentPerson.value?.assignment?.find(
    (item: PersonAssignmentResponse) => item.role === role,
  );
};

const toggleLeftDiv = () => {
  isLeftDivVisible.value = !isLeftDivVisible.value;
};

onMounted(async () => {
  hasHistory.value = window.history.length > 1;
  loadPersonDetail();
  selectedTab.value = getInitialTab();
  unsubscribes.push(
    watch(selectedTab, (value) => {
      router.replace({ query: { ...route.query, tab: value } });
    }),
  );
});

onUnmounted(() => {
  unsubscribes.forEach((unsubscribe) => unsubscribe());
  resetComponentState();
  cacheDataStore.clearData("invoice");
  cacheDataStore.clearData("service");
  resetPaymentState();
});

const filteredTabList = computed(() => {
  if (!ability.value) return [];
  return tabList.filter((tab) => {
    if (ability?.value?.can("manage", "all")) return true;
    return ability?.value?.can("read", tab.subject);
  });
});
</script>

<template>
  <div class="mt-5 flex flex-col gap-5 md:flex-row">
    <div
      v-if="!props.isDoctor"
      v-show="isLeftDivVisible"
      :class="[
        'hidden transition-all duration-300 ease-linear md:block md:w-[20%]',
        { 'md:hidden': switchPersonDetail },
      ]"
    >
      <PersonPrimaryInfo v-if="currentPerson && currentPerson.id" :person="currentPerson" />
    </div>
    <div class="relative flex-1">
      <div class="absolute -left-3 top-4 z-10 hidden md:block" @click="toggleLeftDiv">
        <Button aria-label="Show/Hide" class="h-6 w-6" raised rounded severity="secondary">
          <i
            :class="isLeftDivVisible ? 'pi pi-chevron-left' : 'pi pi-chevron-right'"
            class="text-sm text-muted/80"
          />
        </Button>
      </div>
      <div class="grid grid-cols-12 gap-6">
        <div class="box relative col-span-12 overflow-hidden">
          <Tabs
            :value="selectedTab"
            lazy
            @update:value="
              (value) => {
                selectedTab = value + '';
              }
            "
            scrollable
          >
            <TabList
              :pt="{
                activeBar: {
                  style: { display: 'none' },
                },
              }"
            >
              <Tab v-for="tab in filteredTabList" :key="tab.key" :value="tab.key">
                <Lucide :icon="tab.icon" class="mr-2 inline-flex h-4 w-4" />
                {{ tab.label }}
              </Tab>
            </TabList>
            <TabPanels class="px-0 py-5">
              <TabPanel v-for="tab in filteredTabList" :key="tab.key" :value="tab.key">
                <component
                  :is="tab.component"
                  :deal-id="highlightedDeal?.id"
                  :person-id="props.personId"
                  :person-name="currentPerson?.full_name"
                />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </div>
        <div class="col-span-12"></div>
      </div>
    </div>
  </div>

  <PersonAddModal
    :flag-get-detail="flagGetDetail"
    :is-open="isShowPersonDialog.isVisible.value"
    :person-id="props.personId"
    @on-close="isShowPersonDialog.hide"
    @update-list-person="loadPersonDetail"
  />
</template>
