<script setup lang="ts">
import { storeToRefs } from "pinia";
import Select from "primevue/select";
import { useConfirm } from "primevue/useconfirm";
import { computed, onUnmounted, ref, watch } from "vue";

import { EnumFileUsageType } from "@/api/bcare-enum";
import { useMedia } from "@/hooks/useMedia";
import { mediaRequest, useMediaStore } from "@/stores/media-store";

import MediaAddBtn from "./components/MediaAddBtn.vue";
import MediaContent from "./components/MediaContent.vue";

const props = defineProps<{ personId?: number; personName?: string; dealId?: number }>();

const mediaStore = useMediaStore(),
  { fetchMedia, deleteFiles, getDateOptions } = useMedia();

const confirm = useConfirm();

const examTabs = ref([
  { value: EnumFileUsageType.EXAMINATION_IMAGE, name: "Thăm khám" },
  { value: EnumFileUsageType.X_RAY_IMAGE, name: "X-<PERSON>uang" },
  { value: EnumFileUsageType.FILE_IMAGE, name: "Tập tin" },
  { value: EnumFileUsageType.PROFILE_FILE_IMAGE, name: "Trước điều trị" },
]);
const viewMode = ref<"vertical" | "horizontal">("horizontal");

const currentTab = ref(examTabs.value[0].value),
  selectedItems = ref<number[]>([]);

const _selectedDate = ref("");

const selectedDate = computed({
  get: () => _selectedDate.value || "all",
  set: (value: string) => {
    _selectedDate.value = getDateOptions.value.some((option) => option.code === value)
      ? value
      : "all";
  },
});

const handleBulkDelete = (event: Event) => {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: "Bạn có muốn xóa hết hình ảnh đã chọn?",
    icon: "pi pi-trash",
    rejectProps: {
      label: "Hủy",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Xóa",
      severity: "danger",
    },
    accept: async () => {
      await deleteFiles(selectedItems.value);
      selectedItems.value = [];
      refetchMedia();
    },
    reject: () => {},
  });
};

const dateOptions = computed(() => {
  return getDateOptions.value;
});

function refetchMedia() {
  fetchMedia({
    ...mediaRequest,
    filter: {
      search: "",
      file_id: 0,
      entity_id: props.personId ?? 0,
      entity_type: "person",
      usage_type: currentTab.value,
    },
  });
}

// Gộp watch và cleanup vào một composable
const useMediaTabCleanup = (personId: number) => {
  watch(
    () => personId,
    () => {
      selectedItems.value = [];
      _selectedDate.value = "";
      mediaStore.resetStore();
    },
    { immediate: true },
  );

  onUnmounted(() => {
    selectedItems.value = [];
    _selectedDate.value = "";
    mediaStore.resetStore();
  });
};

useMediaTabCleanup(props.personId ?? 0);
</script>

<template>
  <ConfirmPopup></ConfirmPopup>
  <div class="p-1 md:px-5">
    <Tabs v-model:value="currentTab" scrollable unstyled lazy>
      <TabList
        :pt="{
          activeBar: { style: { display: 'none !important' } },
          tabList: { style: { border: 'none' } },
        }"
        class="overflow-hidden rounded-md border border-gray-200"
      >
        <div class="flex w-full items-center !justify-between gap-2">
          <div>
            <Tab
              v-for="tab in examTabs"
              :key="tab.value"
              :value="tab.value"
              class="border-none p-2 text-sm font-normal hover:border-gray-300 hover:text-gray-700 focus:outline-none md:px-4 md:py-3"
              :class="{ 'bg-slate-100 text-primary': currentTab === tab.value }"
            >
              {{ tab.name }}
            </Tab>
          </div>
        </div>
      </TabList>

      <TabPanels unstyled>
        <TabPanel v-for="tab in examTabs" :key="tab.value" :value="tab.value" unstyled>
          <div class="flex items-center justify-end gap-2 pt-2 md:pt-5">
            <Button
              label="Xóa"
              icon="pi pi-trash"
              severity="danger"
              outlined
              v-if="selectedItems.length > 0"
              @click="handleBulkDelete($event)"
            />

            <ButtonGroup>
              <Button
                :outlined="viewMode !== 'horizontal'"
                icon="pi pi-th-large"
                @click="viewMode = 'horizontal'"
              />
              <Button
                :outlined="viewMode !== 'vertical'"
                icon="pi pi-list"
                @click="viewMode = 'vertical'"
              />
            </ButtonGroup>

            <MediaAddBtn
              :person-id="props.personId ?? 0"
              :usage-type="currentTab"
              @fetch-media="refetchMedia"
            />
          </div>

          <div class="mt-3" v-if="dateOptions.length">
            <Tabs v-model:value="selectedDate" scrollable unstyled>
              <TabList class="no-scrollbar flex w-full overflow-x-auto border-b border-gray-200">
                <Tab
                  v-for="option in dateOptions"
                  :key="option.code"
                  :value="option.code"
                  class="mr-2 whitespace-nowrap rounded-t-md px-4 py-2 text-sm font-medium transition-colors"
                  :class="[
                    selectedDate === option.code
                      ? 'border-b-2 border-primary bg-primary/10 text-primary'
                      : 'border-b-2 border-transparent hover:bg-gray-100',
                  ]"
                  @click="selectedItems = []"
                >
                  <div class="flex items-center gap-1.5">
                    <i class="pi pi-calendar-plus text-blue-500"></i>
                    <span>{{ option.code }}</span>
                  </div>
                </Tab>
              </TabList>
            </Tabs>
          </div>

          <MediaContent
            :person-id="props.personId ?? 0"
            :person-name="props.personName ?? ''"
            :usage-type="tab.value"
            :selected-date="selectedDate"
            :selected-items="selectedItems"
            :view-mode="viewMode"
            @update:selectedItems="selectedItems = $event"
          />
        </TabPanel>
      </TabPanels>
    </Tabs>
  </div>
</template>
