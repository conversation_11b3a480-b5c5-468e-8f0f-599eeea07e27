<script setup lang="ts">
import { onKeyDown, useDebounceFn } from "@vueuse/core";
import Checkbox from "primevue/checkbox";
import { useConfirm } from "primevue/useconfirm";
import { computed, onMounted, ref, watch } from "vue";
import VueEasyLightbox from "vue-easy-lightbox";

import { EnumFileUsageType } from "@/api/bcare-enum";
import Lucide from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy";
import { useImageLazyLoad } from "@/composables/useImageLazyLoad";
import { getResourceUri } from "@/utils/helper";

const props = defineProps<{
  usageType: EnumFileUsageType;
  selectedDate: string;
  selectedItems: number[];
  displayedMedia: Record<string, any[]>;
  galleriaItems: any[];
}>();

const emit = defineEmits<{
  (event: "update:selectedItems", value: number[]): void;
  (event: "delete-image", id: number, event: Event): void;
  (event: "show-image", idx: number): void;
}>();

const localSelectedItems = computed({
  get: () => props.selectedItems,
  set: (value) => {
    emit("update:selectedItems", value);
  },
});

const confirm = useConfirm();

const { vLazyLoad } = useImageLazyLoad();

const showImg = (idx: number) => {
  emit("show-image", idx);
};

function onDeleteImage(id: number, event: Event) {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: "Bạn có muốn xóa hình ảnh này?",
    icon: "pi pi-trash",
    rejectProps: {
      label: "Hủy",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Xóa",
      severity: "danger",
    },
    accept: () => {
      emit("delete-image", id, event);
    },
    reject: () => {},
  });
}

const getGalleriaIndex = (item: any, groupIndex: number) => {
  return props.galleriaItems.findIndex((i) => i.id === item.id);
};

const containerRef = ref<HTMLElement | null>(null);
const dateSectionRefs = ref<Map<string, HTMLElement>>(new Map());

const setDateSectionRef = (el: HTMLElement | null, date: string) => {
  if (el) {
    dateSectionRefs.value.set(date, el);
  }
};

const scrollToDate = (date: string) => {
  if (date === "all") return;

  const section = dateSectionRefs.value.get(date);
  if (section && containerRef.value) {
    section.scrollIntoView({ behavior: "smooth" });
  }
};

watch(
  () => props.selectedDate,
  (newDate) => {
    if (newDate && newDate !== "all") {
      scrollToDate(newDate);
    }
  },
  { immediate: true },
);
</script>

<template>
  <div
    ref="containerRef"
    class="vertical-scroll-container"
    v-if="Object.keys(displayedMedia).length > 0"
  >
    <template v-for="(group, date) in displayedMedia" :key="date">
      <div class="date-section" :ref="(el) => setDateSectionRef(el, date)">
        <div class="sticky top-0 z-10 flex items-center gap-2 border-b border-gray-200 bg-white">
          <div class="flex items-center gap-2 rounded-lg px-4 py-2">
            <i class="pi pi-calendar text-blue-500" />
            <span class="font-medium text-gray-700">{{ date }}</span>
          </div>
          <span class="text-sm text-gray-500"
            >{{ group.length }} file{{ group.length > 1 ? "s" : "" }}</span
          >
        </div>

        <div class="flex flex-wrap gap-2 py-2">
          <div
            v-for="(item, index) in group"
            :key="item.id"
            class="group relative h-60 cursor-pointer sm:h-40"
          >
            <div class="absolute left-0 top-0 z-30 ml-2 mt-1.5">
              <Checkbox v-model="localSelectedItems" :value="item.id" />
            </div>
            <img
              v-lazy-load
              :data-src="getResourceUri(item)"
              class="lazy-image h-full rounded-lg object-contain transition-transform duration-300 group-hover:scale-[1.02]"
              alt="Media item"
              @click="showImg(getGalleriaIndex(item, index))"
            />
            <Tippy
              content="Xóa hình ảnh?"
              class="absolute right-0 top-0 -mr-2 -mt-2 flex h-5 w-5 items-center justify-center rounded-full bg-danger text-white opacity-0 transition-opacity group-hover:opacity-100"
              @click="(e) => onDeleteImage(item.id, e)"
            >
              <Lucide icon="X" class="h-4 w-4" />
            </Tippy>
          </div>
        </div>
      </div>
    </template>
  </div>

  <div v-else class="flex items-center justify-center py-10">
    <div class="text-gray-500">Không có hình ảnh</div>
  </div>
</template>

<style scoped>
.vertical-scroll-container {
  height: calc(100vh - 250px);
  overflow-y: auto;
  scroll-snap-type: y proximity;
}

.date-section {
  scroll-snap-align: start;
  margin-bottom: 1.5rem;
}
</style>
