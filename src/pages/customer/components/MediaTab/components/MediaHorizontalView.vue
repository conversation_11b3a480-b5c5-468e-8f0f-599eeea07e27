<script setup lang="ts">
import Checkbox from "primevue/checkbox";
import { useConfirm } from "primevue/useconfirm";
import { computed, ref, watch } from "vue";

import { EnumFileUsageType } from "@/api/bcare-enum";
import Lucide from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy";
import { useImageLazyLoad } from "@/composables/useImageLazyLoad";
import { getResourceUri } from "@/utils/helper";

const props = defineProps<{
  usageType: EnumFileUsageType;
  selectedDate: string;
  selectedItems: number[];
  displayedMedia: Record<string, any[]>;
  galleriaItems: any[];
}>();

const emit = defineEmits<{
  (event: "update:selectedItems", value: number[]): void;
  (event: "delete-image", id: number, e: Event): void;
  (event: "show-image", idx: number): void;
}>();

const confirm = useConfirm();
const containerRef = ref<HTMLElement | null>(null);
const dateSectionRefs = ref<Map<string, HTMLElement>>(new Map());

const localSelectedItems = computed({
  get: () => props.selectedItems,
  set: (value) => {
    emit("update:selectedItems", value);
  },
});

const { vLazyLoad } = useImageLazyLoad();

const setDateSectionRef = (el: HTMLElement | null, date: string) => {
  if (el) {
    dateSectionRefs.value.set(date, el);
  }
};

const scrollToDate = (date: string) => {
  if (date === "all") return;

  const section = dateSectionRefs.value.get(date);
  if (section && containerRef.value) {
    containerRef.value.scrollTo({
      left: section.offsetLeft,
      behavior: "smooth",
    });
  }
};

// Watch for selectedDate changes to trigger scroll
watch(
  () => props.selectedDate,
  (newDate) => {
    if (newDate && newDate !== "all") {
      scrollToDate(newDate);
    }
  },
  { immediate: true },
);

function onDeleteImage(id: number, event: Event) {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: "Bạn có muốn xóa hình ảnh này?",
    icon: "pi pi-trash",
    rejectProps: {
      label: "Hủy",
      severity: "secondary",
      outlined: true,
    },
    acceptProps: {
      label: "Xóa",
      severity: "danger",
    },
    accept: () => {
      emit("delete-image", id, event);
    },
    reject: () => {},
  });
}

const getGalleriaIndex = (item: any, groupIndex: number) => {
  return props.galleriaItems.findIndex((i) => i.id === item.id);
};

const showImg = (idx: number) => {
  emit("show-image", idx);
};
</script>

<template>
  <div
    ref="containerRef"
    class="scrollbar-hide flex min-h-[120px] snap-x snap-mandatory items-start gap-2 overflow-x-auto md:gap-4"
    v-if="Object.keys(displayedMedia).length > 0"
  >
    <div
      v-for="(group, date) in displayedMedia"
      :key="date"
      :ref="(el) => setDateSectionRef(el as HTMLElement, date)"
      class="mb-2 flex min-h-[90px] w-fit min-w-[200px] max-w-[85vw] snap-start flex-col rounded-lg border border-gray-200/50 bg-gray-100/30 md:min-h-[100px] md:min-w-[236px] md:max-w-[90vw]"
    >
      <div
        class="sticky top-0 z-10 flex shrink-0 flex-wrap items-center justify-between rounded-t-lg border-b border-gray-200/80 bg-white p-1.5 md:p-2"
      >
        <div class="flex items-center gap-1 md:gap-2">
          <i class="pi pi-calendar-plus text-sm text-gray-500 md:text-base" />
          <span class="text-sm font-normal text-gray-500 md:text-base md:font-medium">{{
            date
          }}</span>
        </div>
        <span class="text-xs text-gray-500 md:text-sm">
          {{ group.length }} file{{ group.length > 1 ? "s" : "" }}
        </span>
      </div>

      <div class="flex grow flex-col gap-1.5 p-1.5 md:gap-2 md:p-2">
        <div
          v-for="(item, index) in group"
          :key="item.id"
          class="group relative min-w-[80px] max-w-[190px] cursor-pointer md:min-w-[100px] md:max-w-[220px]"
        >
          <div class="absolute left-0 top-0 z-30 ml-1 mt-1 md:ml-2 md:mt-1.5">
            <Checkbox
              v-model="localSelectedItems"
              :value="item.id"
              :pt="{ root: { class: 'scale-75 md:scale-100' } }"
            />
          </div>
          <img
            v-lazy-load
            :data-src="getResourceUri(item)"
            class="lazy-image block h-auto min-h-[60px] w-full max-w-full rounded-lg bg-gray-200 object-contain transition-transform duration-300 group-hover:scale-[1.02] md:min-h-[80px]"
            alt="Media item"
            @click="showImg(getGalleriaIndex(item, index))"
          />
          <Tippy
            content="Xóa hình ảnh?"
            class="touch:opacity-100 absolute right-0 top-0 -mr-1 -mt-1 flex h-4 w-4 items-center justify-center rounded-full bg-danger text-white opacity-0 transition-opacity group-hover:opacity-100 md:-mr-2 md:-mt-2 md:h-5 md:w-5"
            @click="(e) => onDeleteImage(item.id, e)"
          >
            <Lucide icon="X" class="h-3 w-3 md:h-4 md:w-4" />
          </Tippy>
        </div>
      </div>
    </div>
  </div>

  <div v-else class="flex min-h-[150px] items-center justify-center py-6 md:min-h-[200px] md:py-10">
    <div class="text-center text-gray-500">
      <i class="pi pi-image mb-1 text-3xl md:mb-2 md:text-4xl"></i>
      <p class="text-sm md:text-base">Không có hình ảnh nào</p>
    </div>
  </div>
</template>
