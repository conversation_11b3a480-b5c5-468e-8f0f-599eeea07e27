<script setup lang="ts">
import { useDebounceFn } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import VueEasyLightbox from "vue-easy-lightbox";

import { EnumFileUsageType } from "@/api/bcare-enum";
import { mediaRequest, useMediaStore } from "@/stores/media-store";
import { getResourceUri } from "@/utils/helper";

import MediaHorizontalView from "./MediaHorizontalView.vue";
import MediaVerticalView from "./MediaVerticalView.vue";

const props = defineProps<{
  personId: number;
  personName: string;
  usageType: EnumFileUsageType;
  selectedDate: string;
  selectedItems: number[];
  viewMode: "vertical" | "horizontal";
}>();

const emit = defineEmits<{
  (event: "update:selectedItems", value: number[]): void;
}>();

const localSelectedItems = computed({
  get: () => props.selectedItems,
  set: (value) => {
    emit("update:selectedItems", value);
  },
});

const mediaStore = useMediaStore(),
  { fetchMedia, deleteFile } = mediaStore,
  { getGroupedMedia } = storeToRefs(mediaStore);

const isShow = ref(false);
const index = ref(0);

const imgs = computed(() => {
  return galleriaItems.value.map((item) => ({
    src: getResourceUri(item),
    title: item.file.name,
  }));
});

const preloadNextImages = useDebounceFn((currentIndex: number) => {
  const nextIndex = currentIndex + 1;
  const prevIndex = currentIndex - 1;

  if (nextIndex < galleriaItems.value.length) {
    new Image().src = getResourceUri(galleriaItems.value[nextIndex]);
  }

  if (prevIndex >= 0) {
    new Image().src = getResourceUri(galleriaItems.value[prevIndex]);
  }
}, 200);

const showImg = (idx: number) => {
  index.value = idx;
  isShow.value = true;
  preloadNextImages(idx);
};

const handleHide = () => {
  isShow.value = false;
};

// actions
async function fetchMediaList() {
  await fetchMedia({
    ...mediaRequest,
    filter: {
      search: "",
      file_id: 0,
      entity_type: "person",
      entity_id: props.personId || 0,
      usage_type: props.usageType,
    },
  });
}

async function onDeleteImage(id: number) {
  await deleteFile({ id });
  await fetchMediaList();
}

onMounted(async () => {
  localSelectedItems.value = [];
  await fetchMediaList();
});

const displayedMedia = computed(() => {
  return getGroupedMedia.value;
});

const galleriaItems = computed(() => {
  return Object.values(getGroupedMedia.value).flat();
});
</script>

<template>
  <vue-easy-lightbox
    :visible="isShow"
    :imgs="imgs"
    :index="index"
    scrollDisabled
    teleport="body"
    @hide="handleHide"
  />

  <div class="mt-2">
    <MediaVerticalView
      v-if="viewMode === 'vertical'"
      :usage-type="usageType"
      :selected-date="selectedDate"
      :selected-items="localSelectedItems"
      :displayed-media="displayedMedia"
      :galleria-items="galleriaItems"
      @update:selected-items="localSelectedItems = $event"
      @delete-image="onDeleteImage"
      @show-image="showImg"
    />

    <MediaHorizontalView
      v-else
      :usage-type="usageType"
      :selected-date="selectedDate"
      :selected-items="localSelectedItems"
      :displayed-media="displayedMedia"
      :galleria-items="galleriaItems"
      @update:selected-items="localSelectedItems = $event"
      @delete-image="onDeleteImage"
      @show-image="showImg"
    />
  </div>
</template>
