<script setup lang="ts">
import Select from "primevue/select";
import { computed, onMounted, reactive, ref, watch } from "vue";

import { AppointmentReminderStatus } from "@/api/bcare-enum";
import { AppointmentListRequest, AppointmentResponse } from "@/api/bcare-types-v2";

import Paginate from "@/base-components/Paginate/Paginate.vue";
import ConsultationAppointmentModal from "@/components/Appointment/ConsultationAppointmentModal.vue";
import { TimelineModal } from "@/components/Timeline";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useAppointment } from "@/hooks/useAppointment";
import useConstant from "@/hooks/useConstant";
import useToggle from "@/hooks/useToggle";

import AppointmentItems from "./components/AppointmentItems.vue";
import ModalAppointment from "./components/ModalAppointment.vue";
import MessageModal from "@/components/Messages/MessageModal.vue";

const filterAppointmentDefault = {
  page_size: 0,
  page: 0,
  filter: {
    person_id: 0,
    doctor_id: 0,
    status: 0,
    type: 0,
  },
  from_date: "",
  to_date: "",
  order_by: "start_time DESC",
};

const perPage = ref<number>(80);
const props = defineProps<{
  personId: number;
  isZalo?: number;
  dealId?: number;
  personName?: string;
}>();

const filterParams = reactive<AppointmentListRequest>({
  ...filterAppointmentDefault,
  page: 1,
  page_size: +perPage.value,
  filter: {
    person_id: props.personId,
    status: 0,
    type: 0,
  },
});

const { fetchAppointmentList, appointmentsList, totalItems, deleteAppointment, updateAppointment } =
  useAppointment();
const { getConstants } = useConstant();
const modalSmsToggle = useToggle(false);
const modalHistoryToggle = useToggle(false);
const modalAppointmentFormToggleV2 = useToggle(false);
const modalConsultationToggle = useToggle(false);
const selectedItem = ref<AppointmentResponse>();

const menuBarItems = ref([
  {
    label: "Thêm lịch hẹn",
    icon: "pi pi-plus",
    command: () => handleAddAppointmentV2(),
  },
  {
    label: "Thêm lịch tư vấn",
    icon: "pi pi-plus",
    command: () => handleAddConsultation(),
  },
]);

const totalPages = computed(() => {
  return Math.ceil((totalItems.value ?? 0) / perPage.value);
});

const { confirm } = useConfirmTippy();

const handleDeleteAppointment = async (item: AppointmentResponse) => {
  confirm(event, {
    title: "Bạn có chắc chắn muốn xóa lịch hẹn này?",
    icon: "pi pi-exclamation-triangle",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      const success = await deleteAppointment({ id: item.id });
      if (success) {
        await fetchAppointmentList(filterParams);
      }
    },
  });
};

const handleEditAppointmentV2 = (item: AppointmentResponse) => {
  selectedItem.value = item;
  modalAppointmentFormToggleV2.show();
};

const handleAddAppointmentV2 = () => {
  selectedItem.value = undefined;
  modalAppointmentFormToggleV2.show();
};

const updateAppointmentSuccess = async () => {
  await fetchAppointmentList(filterParams);
};

const handlePageChange = async (page: number) => {
  filterParams.page = page;
  await fetchAppointmentList(filterParams);
};

watch(filterParams, () => {
  fetchAppointmentList(filterParams);
});

onMounted(async () => {
  if (props.personId) await fetchAppointmentList(filterParams);
});

const handleRemind = async (item: AppointmentResponse) => {
  confirm(event, {
    title: "Bạn có muốn gửi nhắc nhở cho lịch hẹn này?",
    icon: "pi pi-info-circle",
    acceptLabel: "Đồng ý",
    rejectLabel: "Hủy",
    onAccept: async () => {
      const success = await updateAppointment({
        ...item,
        reminder_status: AppointmentReminderStatus.REMINDED,
      });
      if (success) {
        await fetchAppointmentList(filterParams);
      }
    },
  });
};

const handleUpdateNotes = async (appointmentId: number, notes: string) => {
  const success = await updateAppointment({ id: appointmentId, notes, modified: ["notes"] });
  if (success) {
    await fetchAppointmentList(filterParams);
  }
};

const handleViewHistory = (item: AppointmentResponse) => {
  selectedItem.value = item;
  modalHistoryToggle.show();
};

const handleSendMessage = (item: AppointmentResponse) => {
  selectedItem.value = item;
  modalSmsToggle.show();
};

const handleAddConsultation = () => {
  selectedItem.value = undefined;
  modalConsultationToggle.show();
};

const handleEditConsultation = (item: AppointmentResponse) => {
  selectedItem.value = item;
  modalConsultationToggle.show();
};
</script>

<template>
  <div class="px-5">
    <Menubar :model="menuBarItems" class="px-1 py-1">
      <template #item="{ item, props: itemProps, hasSubmenu, root }">
        <a class="flex items-center" v-bind="itemProps.action">
          <span :class="item.icon" />
          <span>{{ item.label }}</span>
          <Badge
            v-if="item.badge"
            :class="{ 'ml-auto': !root, 'ml-2 size-5 min-w-0': root }"
            :value="item.badge"
          />
          <span
            v-if="item.shortcut"
            class="ml-auto rounded border border-surface-200 bg-surface-100 p-1 text-xs dark:border-surface-700 dark:bg-surface-800"
            >{{ item.shortcut }}</span
          >
          <i
            v-if="hasSubmenu"
            :class="[
              'pi pi-angle-down',
              { 'pi-angle-down ml-2': root, 'pi-angle-right ml-auto': !root },
            ]"
          ></i>
        </a>
      </template>
      <template #end>
        <div class="flex items-center gap-2">
          <Select
            v-model="filterParams.filter!.status"
            :options="[
              { value: 0, label: 'Tất cả trạng thái' },
              ...Object.entries(getConstants?.appointment_status ?? {}).map(([key, label]) => ({
                value: Number(key),
                label,
              })),
            ]"
            optionLabel="label"
            optionValue="value"
            placeholder="Tất cả trạng thái"
            class="w-48"
          />
          <Select
            v-model="filterParams.filter!.type"
            :options="[
              { value: 0, label: 'Tất cả loại hẹn' },
              ...Object.entries(getConstants?.appointment_type ?? {}).map(([key, label]) => ({
                value: Number(key),
                label,
              })),
            ]"
            optionLabel="label"
            optionValue="value"
            placeholder="Tất cả loại hẹn"
            class="w-48"
          />
        </div>
      </template>
    </Menubar>
  </div>

  <AppointmentItems
    :appointments="appointmentsList"
    :setting-configs="getConstants"
    @update-notes="handleUpdateNotes"
    @on-remind="handleRemind"
    @on-view-history="handleViewHistory"
    @on-send-message="handleSendMessage"
    @on-delete="handleDeleteAppointment"
    @on-edit-v2="handleEditAppointmentV2"
    @on-edit-consultation="handleEditConsultation"
  />

  <Paginate
    class="w-full sm:mr-auto sm:w-auto"
    :total-pages="totalPages"
    :current-page="filterParams.page ?? 1"
    @page-change="handlePageChange"
  />
  <TimelineModal
    :model-value="modalHistoryToggle.isVisible.value"
    :history="selectedItem?.history ?? []"
    :creator-id="selectedItem?.creator_id"
    :created-at="selectedItem?.created_at"
    @update:model-value="modalHistoryToggle.setValue"
  />
  <MessageModal
    :appointment="selectedItem"
    :person-id="props.personId ?? 0"
    :is-open="modalSmsToggle.isVisible.value"
    @on-close="modalSmsToggle.hide"
  />

  <ModalAppointment
    :is-open="modalAppointmentFormToggleV2.isVisible.value"
    :person-id="props.personId ?? 0"
    :selected-item="selectedItem"
    :setting-configs="getConstants"
    @update-appointment-success="updateAppointmentSuccess"
    @on-close="modalAppointmentFormToggleV2.hide"
  />

  <ConsultationAppointmentModal
    :is-open="modalConsultationToggle.isVisible.value"
    :person-id="props.personId ?? 0"
    :selected-item="selectedItem"
    @update-appointment-success="updateAppointmentSuccess"
    @on-close="modalConsultationToggle.hide"
  />
</template>

<style scoped>
.p-splitbutton-defaultbutton:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}
</style>
