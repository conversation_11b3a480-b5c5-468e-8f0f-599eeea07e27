export const extractDateTimePicker = (item: string) => {
  if (!item) {
    return {
      day: 0,
      month: 0,
      year: 0,
      hours: 0,
      minutes: 0,
    };
  }
  const [time, date] = item.split(" ");
  const [hours, minutes] = time.split(":").map(Number);
  const [day, month, year] = date.split("/").map(Number);

  return { year, month, day, hours, minutes };
};

export const parseExtraNotes = (
  extraNotes: string,
): { reminderFee: string; expectedTask: string[]; expectedTaskOther: string[] } => {
  try {
    const data = JSON.parse(extraNotes);

    if (data && typeof data === "object" && !Array.isArray(data)) {
      const { reminderFee, expectedTask, expectedTaskOther } = data;
      return {
        reminderFee: typeof reminderFee === "number" ? reminderFee.toString() : "",
        expectedTask: typeof expectedTask === "string" ? expectedTask.split(";") : [],
        expectedTaskOther:
          typeof expectedTaskOther === "string" ? expectedTaskOther.split(";") : [],
      };
    }
    return {
      reminderFee: "",
      expectedTask: [],
      expectedTaskOther: [],
    };
  } catch (error) {
    return {
      reminderFee: "",
      expectedTask: [],
      expectedTaskOther: [],
    };
  }
};

export const parseExtraNotesV2 = (
  extraNotes: string,
): { reminder_fee: string; expected_task: string[]; expected_task_other: string[] } => {
  try {
    const data = JSON.parse(extraNotes);
    return {
      reminder_fee: String(data.reminder_fee || ""),
      expected_task: (data.expected_task || "").split(";").filter(Boolean),
      expected_task_other: (data.expected_task_other || "").split(";").filter(Boolean),
    };
  } catch (error) {
    return {
      reminder_fee: "",
      expected_task: [],
      expected_task_other: [],
    };
  }
};

export function calculateTotalMinutes(startTimeStr: string, endTimeStr: string) {
  const startTime = new Date(startTimeStr).getTime();
  const endTime = new Date(endTimeStr).getTime();

  const timeDifference = endTime - startTime; // thời gian tính bằng milliseconds

  const totalMinutes = timeDifference / (1000 * 60);

  return totalMinutes;
}

export function getHoursAndMinutes(dateTimeStr: string) {
  const date = new Date(dateTimeStr);

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${hours}:${minutes}`;
}
