<script setup lang="ts">
import { Dialog } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";

const props = defineProps<{ isOpen: boolean }>();
const emits = defineEmits<{ (event: "onClose"): void }>();
</script>

<template>
  <Dialog size="lg" :open="props.isOpen" @close="emits('onClose')">
    <Dialog.Panel class="overflow-hidden">
      <Dialog.Title class="bg-white p-5">
        <h2 class="mr-auto text-base font-medium"><PERSON><PERSON>ch sử chỉnh sửa</h2>
        <a href="#" class="absolute right-0 top-0 mr-3 mt-3" @click="emits('onClose')">
          <Lucide icon="X" class="h-8 w-8 text-slate-400" />
        </a>
      </Dialog.Title>
      <Dialog.Description>
        <div
          class="relative overflow-hidden before:absolute before:inset-y-0 before:left-0 before:ml-[14px] before:w-px before:bg-slate-200/60 before:content-[''] before:dark:bg-darkmode-400"
        >
          <div
            class="relative mb-3 first:before:absolute first:before:h-1/2 first:before:w-5 first:before:bg-white first:before:content-[''] last:mb-0 last:after:absolute last:after:bottom-0 last:after:h-1/2 last:after:w-5 last:after:bg-white last:after:content-['']"
          >
            <div
              class="ml-8 px-4 py-3 before:absolute before:inset-y-0 before:left-0 before:z-10 before:my-auto before:ml-1 before:h-5 before:w-5 before:rounded-full before:bg-slate-200 before:content-[''] after:absolute after:inset-y-0 after:left-0 after:z-10 after:my-auto after:ml-[11px] after:h-1.5 after:w-1.5 after:rounded-full after:bg-slate-500 after:content-[''] before:dark:bg-darkmode-300 after:dark:bg-darkmode-200"
            >
              <a class="font-medium text-primary" href=""> Hoàn phí lộ trình </a>
              <div
                class="mt-1.5 flex flex-col gap-y-1.5 text-[0.8rem] leading-relaxed text-slate-500 sm:flex-row sm:items-center"
              >
                Mắc cài kim loại - Khó
                <span
                  class="group mr-auto flex items-center rounded-md border px-1.5 py-px text-xs font-medium sm:ml-2 sm:mr-0"
                >
                  <span class="mr-1.5 h-1.5 w-1.5 rounded-full bg-danger/80"></span>
                  <span class="-mt-px"> Hoàn phí </span>
                </span>
              </div>

              <div class="mt-1.5 text-xs text-slate-500">03-01-2023</div>
            </div>
          </div>
          <div
            class="relative mb-3 first:before:absolute first:before:h-1/2 first:before:w-5 first:before:bg-white first:before:content-[''] last:mb-0 last:after:absolute last:after:bottom-0 last:after:h-1/2 last:after:w-5 last:after:bg-white last:after:content-['']"
          >
            <div
              class="ml-8 px-4 py-3 before:absolute before:inset-y-0 before:left-0 before:z-10 before:my-auto before:ml-1 before:h-5 before:w-5 before:rounded-full before:bg-slate-200 before:content-[''] after:absolute after:inset-y-0 after:left-0 after:z-10 after:my-auto after:ml-[11px] after:h-1.5 after:w-1.5 after:rounded-full after:bg-slate-500 after:content-[''] before:dark:bg-darkmode-300 after:dark:bg-darkmode-200"
            >
              <a class="font-medium text-primary" href=""> Chỉnh sửa lộ trình </a>
              <div
                class="mt-1.5 flex flex-col gap-y-1.5 text-[0.8rem] leading-relaxed text-slate-500 sm:flex-row sm:items-center"
              >
                Mắc cài kim loại - Khó
                <span
                  class="group mr-auto flex items-center rounded-md border px-1.5 py-px text-xs font-medium sm:ml-2 sm:mr-0"
                >
                  <span class="mr-1.5 h-1.5 w-1.5 rounded-full bg-warning/80"></span>
                  <span class="-mt-px"> Đã chỉnh sửa </span>
                </span>
              </div>
              <div class="mt-1.5 text-xs text-slate-500">02-01-2023</div>
            </div>
          </div>
          <div
            class="relative mb-3 first:before:absolute first:before:h-1/2 first:before:w-5 first:before:bg-white first:before:content-[''] last:mb-0 last:after:absolute last:after:bottom-0 last:after:h-1/2 last:after:w-5 last:after:bg-white last:after:content-['']"
          >
            <div
              class="ml-8 px-4 py-3 before:absolute before:inset-y-0 before:left-0 before:z-10 before:my-auto before:ml-1 before:h-5 before:w-5 before:rounded-full before:bg-slate-200 before:content-[''] after:absolute after:inset-y-0 after:left-0 after:z-10 after:my-auto after:ml-[11px] after:h-1.5 after:w-1.5 after:rounded-full after:bg-slate-500 after:content-[''] before:dark:bg-darkmode-300 after:dark:bg-darkmode-200"
            >
              <a class="font-medium text-primary" href=""> Thêm mới lộ trình </a>
              <div
                class="mt-1.5 flex flex-col gap-y-1.5 text-[0.8rem] leading-relaxed text-slate-500 sm:flex-row sm:items-center"
              >
                Mắc cài kim loại
                <span
                  class="group mr-auto flex items-center rounded-md border px-1.5 py-px text-xs font-medium sm:ml-2 sm:mr-0"
                >
                  <span class="mr-1.5 h-1.5 w-1.5 rounded-full bg-success/80"></span>
                  <span class="-mt-px"> Đã kích hoạt </span>
                </span>
              </div>
              <div class="mt-1.5 text-xs text-slate-500">01-01-2023</div>
            </div>
          </div>
        </div>
      </Dialog.Description>
    </Dialog.Panel>
  </Dialog>
</template>

<style scoped></style>
