<script setup lang="ts">
import type { CalendarOptions, DateInput } from "@fullcalendar/core";
import interactionPlugin from "@fullcalendar/interaction";
import resourceTimeGridPlugin from "@fullcalendar/resource-timegrid";
import scrollGridPlugin from "@fullcalendar/scrollgrid";
import FullCalendar from "@fullcalendar/vue3";
import { useDateFormat } from "@vueuse/core";
import tippy, { animateFill as animateFillPlugin, roundArrow } from "tippy.js";
import { computed, createApp, ref, watch } from "vue";

import { UserAvatar } from "@/components/User";
import type { DoctorScheduleWithAppointments } from "@/hooks/useAppointment";
import "tippy.js/dist/tippy.css";
import "tippy.js/animations/shift-away.css";
import "tippy.js/dist/svg-arrow.css";
import { parseExtraNotesV2 } from "../utils";

interface Props {
  scheduleData: DoctorScheduleWithAppointments[];
  selectedDate: Date | Date[] | (Date | null)[] | null | undefined;
  isLoading?: boolean;
}

interface EmitEvents {
  (event: "onTimeSelect", value: { startTime: string; endTime: string; doctorId: number }): void;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
});
const emit = defineEmits<EmitEvents>();

const calendarRef = ref<InstanceType<typeof FullCalendar> | null>(null);

// Transform scheduleData to FullCalendar resources
const resources = computed(() => {
  return [...props.scheduleData]
    .sort((a, b) => (a.department_id || 0) - (b.department_id || 0))
    .map((doctor) => ({
      id: doctor.doctor_id.toString(),
      title: doctor.doctor_name,
      departmentId: doctor.department_id,
    }));
});

// Thêm refs để lưu trữ selection hiện tại
const currentSelection = ref<{
  start: string;
  end: string;
  resourceId: string;
} | null>(null);

// Cập nhật handleSelect
const handleSelect = (selectInfo: any) => {
  // Check if selection is within a working slot
  const isWithinWorkingSlot = events.value.some(
    (event) =>
      event.display === "background" &&
      event.resourceId === selectInfo.resource.id &&
      new Date(event.start) <= selectInfo.start &&
      new Date(event.end) >= selectInfo.end,
  );

  if (!isWithinWorkingSlot) {
    selectInfo.view.calendar.unselect();
    return;
  }

  // Lưu selection hiện tại
  currentSelection.value = {
    start: selectInfo.start.toISOString(),
    end: selectInfo.end.toISOString(),
    resourceId: selectInfo.resource.id,
  };

  emit("onTimeSelect", {
    startTime: selectInfo.start.toISOString(),
    endTime: selectInfo.end.toISOString(),
    doctorId: Number(selectInfo.resource.id),
  });
};

// Thêm method để clear selection
const clearSelection = () => {
  currentSelection.value = null;
  if (calendarRef.value) {
    calendarRef.value.getApi().unselect();
  }
};

// Expose clearSelection method nếu cần thiết
defineExpose({
  clearSelection,
});

// Thêm computed property để tính toán business hours
const businessHourRange = computed(() => {
  if (!props.scheduleData.length) {
    return {
      startTime: "07:00",
      endTime: "20:00",
    };
  }

  // Lấy tất cả các slot làm việc
  const allSlots = props.scheduleData.flatMap((doctor) => doctor.working_slots);

  // Tìm thời gian bắt đầu sớm nhất
  const startTimes = allSlots.map((slot) => new Date(slot.start_time).getTime());
  const earliestStart = new Date(Math.min(...startTimes));

  // Tìm thời gian kết thúc muộn nhất
  const endTimes = allSlots.map((slot) => new Date(slot.end_time).getTime());
  const latestEnd = new Date(Math.max(...endTimes));

  return {
    startTime: `${String(earliestStart.getHours()).padStart(2, "0")}:00`,
    endTime: `${String(latestEnd.getHours()).padStart(2, "0")}:00`,
  };
});

// Cập nhật calendarOptions
const calendarOptions = computed<CalendarOptions>(() => ({
  schedulerLicenseKey: "CC-Attribution-NonCommercial-NoDerivatives",
  plugins: [resourceTimeGridPlugin, interactionPlugin, scrollGridPlugin],
  initialView: "resourceTimeGridDay",
  initialDate: props.selectedDate instanceof Date ? props.selectedDate : new Date(),
  resources: resources.value,
  events: events.value,
  slotMinTime: businessHourRange.value.startTime,
  slotMaxTime: businessHourRange.value.endTime,
  slotDuration: "00:05:00",
  slotLabelInterval: "00:30",
  allDaySlot: false,
  selectable: true,
  selectMirror: true,
  selectMinDistance: 0,
  selectOverlap: (event: any) => event.display === "background",
  selectConstraint: {
    startTime: businessHourRange.value.startTime,
    endTime: businessHourRange.value.endTime,
    dows: [0, 1, 2, 3, 4, 5, 6],
  },
  select: handleSelect,
  headerToolbar: false,
  locale: "vi",
  nowIndicator: true,
  scrollTime: businessHourRange.value.startTime,
  height: "100%",
  expandRows: true,
  stickyHeaderDates: true,
  businessHours: {
    daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
    startTime: businessHourRange.value.startTime,
    endTime: businessHourRange.value.endTime,
  },

  slotLabelFormat: {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  },
  slotEventOverlap: false,
  snapDuration: "00:05:00",
  resourceLabelContent: (arg: any) => {
    const doctor = props.scheduleData.find((d) => d.doctor_id.toString() === arg.resource.id);
    if (!doctor) return { html: arg.resource.title };

    const workingHours = getWorkingHours(doctor.working_slots);

    return {
      html: `
        <div class="flex items-center justify-center gap-2 p-2.5 hover:bg-primary/5 transition-all w-full">
          <div class="doctor-avatar">
            <user-avatar user-id="${doctor.doctor_id}" size="small" />
          </div>
          <div class="flex flex-col items-start">
            <div class="truncate text-sm font-semibold text-gray-800">
              ${doctor.doctor_name}
            </div>
            <div class="flex items-center gap-1 text-xs text-gray-500">
              <i class="pi pi-clock text-[10px]"></i>
              ${workingHours}
            </div>
          </div>
        </div>
      `,
    };
  },
  resourceLabelDidMount: (arg: any) => {
    // Manually mount UserAvatar component
    const avatarEl = arg.el.querySelector(".doctor-avatar");
    if (avatarEl) {
      const doctor = props.scheduleData.find((d) => d.doctor_id.toString() === arg.resource.id);
      if (doctor) {
        const app = createApp(UserAvatar, {
          userId: doctor.doctor_id,
          size: "small",
        });
        app.mount(avatarEl);
      }
    }
  },
  eventDidMount: (info) => {
    if (info.event.classNames.includes("booked-appointment")) {
      tippy(info.el, {
        plugins: [animateFillPlugin],
        content: parseAppointmentTooltip(info.event.extendedProps).value,
        arrow: roundArrow,
        allowHTML: true,
        popperOptions: {
          modifiers: [
            {
              name: "preventOverflow",
              options: {
                rootBoundary: "viewport",
              },
            },
          ],
        },
        animateFill: false,
        animation: "shift-away",
      });
    }
  },
  dayMinWidth: 170,
}));

const parseAppointmentTooltip = (appointment: any) => {
  const { expected_task, expected_task_other } = parseExtraNotesV2(appointment.extra_notes);
  const tasks = [...expected_task, ...expected_task_other].filter(Boolean);
  return {
    value: `<div class="font-medium">${appointment.person?.full_name || "N/A"}</div>
      ${tasks.length ? `<div class="whitespace-pre-line">• ${tasks.join("\n• ")}</div>` : ""}`,
    escape: false,
  };
};

// Watch for date changes
watch(
  () => props.selectedDate,
  (newDate) => {
    if (calendarRef.value) {
      const calendarApi = calendarRef.value.getApi();
      if (newDate) {
        calendarApi.gotoDate(newDate as DateInput);
      }
    }
  },
);

// Cập nhật events computed
const events = computed(() => {
  const allEvents: any[] = [];

  props.scheduleData.forEach((doctor) => {
    // Add working slots as background events
    doctor.working_slots.forEach((slot) => {
      allEvents.push({
        resourceId: doctor.doctor_id.toString(),
        start: slot.start_time,
        end: slot.end_time,
        display: "background",
        backgroundColor: "#34d399",
        classNames: ["available-slot"],
      });

      // Add booked appointments
      slot.appointments.forEach((apt) => {
        allEvents.push({
          id: apt.id.toString(),
          resourceId: doctor.doctor_id.toString(),
          start: apt.start_time,
          end: apt.end_time,
          backgroundColor: "#3174AD",
          classNames: ["booked-appointment", "border-0"],
          editable: false,
          extendedProps: {
            person: apt.person?.full_name || "",
            extra_notes: apt.extra_notes,
          },
        });
      });
    });
  });

  // Thêm selection event nếu có
  if (currentSelection.value) {
    // Thêm background highlight
    allEvents.push({
      start: currentSelection.value.start,
      end: currentSelection.value.end,
      resourceId: currentSelection.value.resourceId,
      display: "background",
      backgroundColor: "#34d399",
      classNames: ["selected-slot"],
    });

    // Thêm event-like selection với thời gian
    allEvents.push({
      start: currentSelection.value.start,
      end: currentSelection.value.end,
      resourceId: currentSelection.value.resourceId,
      backgroundColor: "#3b82f6",
      classNames: ["temp-appointment", "border-0"],
      editable: false,
    });
  }

  return allEvents;
});

// Thêm helper function để tính working hours
const getWorkingHours = (workingSlots: any[]) => {
  if (!workingSlots.length) return "Không có lịch làm việc";

  const startTimes = workingSlots.map((slot) => new Date(slot.start_time).getTime());
  const endTimes = workingSlots.map((slot) => new Date(slot.end_time).getTime());

  const earliestStart = new Date(Math.min(...startTimes));
  const latestEnd = new Date(Math.max(...endTimes));

  return `${useDateFormat(earliestStart, "HH:mm").value} - ${useDateFormat(latestEnd, "HH:mm").value}`;
};
</script>

<template>
  <div class="relative h-full">
    <div
      v-if="isLoading"
      class="absolute inset-0 z-10 flex items-center justify-center bg-white/80"
    >
      <ProgressSpinner />
    </div>

    <FullCalendar ref="calendarRef" :options="calendarOptions" class="doctor-calendar" />
  </div>
</template>

<style scoped>
.doctor-calendar {
  @apply h-full overflow-x-auto;
}

:deep(.fc-timegrid-col) {
  min-width: 170px;
}

:deep(.fc) {
  @apply font-sans;
}

:deep(.fc-timegrid-slot) {
  height: 25px !important;
}

:deep(.fc-event) {
  @apply cursor-not-allowed;
}

:deep(.available-slot) {
  @apply cursor-pointer bg-emerald-400 !important;
}

:deep(.booked-appointment) {
  @apply m-[1px] bg-[#3174AD] text-white;
}

:deep(.fc-timegrid-col-events) {
  @apply mx-[5px];
}

:deep(.fc-resource-timeline-divider) {
  @apply w-[3px] bg-slate-200;
}

:deep(.fc-timegrid-now-indicator-line) {
  @apply border-primary;
}

:deep(.fc-timegrid-now-indicator-arrow) {
  @apply border-primary text-primary;
}

:deep(.temp-appointment) {
  @apply z-20 m-[1px] bg-[#3174AD] text-white;
}

:deep(.fc-resource-group) {
  @apply border-b border-primary/20;
}

:deep(.fc-resource-timeline-divider) {
  @apply hidden;
}

/* Đặt màu trắng cho khoảng thời gian không phải giờ làm việc */
:deep(.fc-timegrid-slot) {
  @apply bg-white;
}

/* Đảm bảo selected-slot vẫn hiển thị đúng màu */
:deep(.selected-slot) {
  @apply bg-emerald-400 !important;
}

/* Tippy styles */
.tippy-box {
  @apply max-w-xs rounded-lg border border-gray-200 bg-white text-gray-700 shadow-lg;
}

.tippy-box[data-animation="shift-away"][data-state="hidden"] {
  @apply opacity-0;
}

.tippy-box[data-placement^="top"] > .tippy-arrow::before {
  @apply border-t-white;
}

.tippy-content {
  @apply p-2 text-xs;
}
</style>
