<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import { useScroll } from "@vueuse/core";
import { computed, ref } from "vue";

import { UserAvatar } from "@/components/User";
import type { DoctorScheduleWithAppointments } from "@/hooks/useAppointment";
import { parseExtraNotesV2 } from "../utils";

const props = defineProps<{
  scheduleData: DoctorScheduleWithAppointments[];
  isLoading: boolean;
}>();

const emits = defineEmits<{
  (
    e: "onSlotSelect",
    payload: {
      doctorId: number;
      startTime: string;
      doctorName: string;
      departmentId?: number;
    },
  ): void;
}>();

const sortedScheduleData = computed(() => {
  if (!props.scheduleData || props.scheduleData.length === 0) return [];

  // Sắp xếp bác sĩ theo department_id
  const sortedDoctors = [...props.scheduleData]
    .sort((a, b) => (a.department_id || 0) - (b.department_id || 0))
    .map((doctor) => {
      const sortedWorkingSlots = [...doctor.working_slots].sort((a, b) => {
        return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
      });

      const workingSlotsWithSortedAppointments = sortedWorkingSlots.map((slot) => ({
        ...slot,
        appointments: [...slot.appointments].sort((a, b) => {
          return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
        }),
      }));

      return {
        ...doctor,
        working_slots: workingSlotsWithSortedAppointments,
      };
    });

  return sortedDoctors;
});

const formatTime = (isoString: string) => {
  return useDateFormat(isoString, "HH:mm").value;
};

const calculateSlotHeight = (startTime: string, endTime: string) => {
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  const diffMinutes = (end - start) / (1000 * 60);
  if (diffMinutes <= 5) return 26; // Minimum 26px (~ 3 minutes)
  return 20 + Math.max(diffMinutes * 1.5, 26); // 1.5px per minute, minimum 26px (~ 3 minutes)
};

const handleEmptySlotClick = (
  doctorId: number,
  startTime: string,
  doctorName: string,
  departmentId?: number,
) => {
  emits("onSlotSelect", {
    doctorId,
    startTime,
    doctorName,
    departmentId,
  });
};

const calculateDuration = (startTime: string, endTime: string) => {
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  return Math.round((end - start) / (1000 * 60));
};

const getDoctorWorkingHours = (workingSlots: DoctorScheduleWithAppointments["working_slots"]) => {
  if (!workingSlots.length) return "Không có lịch làm việc";

  // Get earliest start time and latest end time
  const startTimes = workingSlots.map((slot) => new Date(slot.start_time).getTime());
  const endTimes = workingSlots.map((slot) => new Date(slot.end_time).getTime());

  const earliestStart = Math.min(...startTimes);
  const latestEnd = Math.max(...endTimes);

  return `${formatTime(new Date(earliestStart).toISOString())} - ${formatTime(new Date(latestEnd).toISOString())}`;
};

const hasGapBefore = (slotStart: string, aptStart: string) => {
  const gap = calculateDuration(slotStart, aptStart);
  return gap >= 3; // Minimum 3 minutes gap
};

const hasGapAfter = (aptEnd: string, nextStart: string) => {
  const gap = calculateDuration(aptEnd, nextStart);
  return gap >= 3; // Minimum 3 minutes gap
};

const scheduleContainerRef = ref<HTMLElement | null>(null);
const { arrivedState } = useScroll(scheduleContainerRef, {
  onScroll: (e) => {
    // Chỉ xử lý wheel event (scroll Y)
    if (e instanceof WheelEvent) {
      const isScrollingUp = e.deltaY < 0;
      const isScrollingDown = e.deltaY > 0;

      if ((arrivedState.top && isScrollingUp) || (arrivedState.bottom && isScrollingDown)) {
        e.preventDefault();
      }
    }
  },
});

// Thêm helper function để parse tooltip content
const parseAppointmentTooltip = (appointment: any) => {
  const { expected_task, expected_task_other } = parseExtraNotesV2(appointment.extra_notes);
  const tasks = [...expected_task, ...expected_task_other].filter(Boolean);
  return {
    value: `<span class="font-medium">${appointment.person?.full_name || "N/A"}</span>
      ${tasks.length ? `<span class="whitespace-pre-line">• ${tasks.join("\n• ")}</span>` : ""}`,
    escape: false,
  };
};

const topScrollRef = ref<HTMLElement | null>(null);
const bottomScrollRef = ref<HTMLElement | null>(null);
let isScrolling = false;

const handleTopScroll = (e: Event) => {
  if (isScrolling) return;
  isScrolling = true;
  if (bottomScrollRef.value) {
    bottomScrollRef.value.scrollLeft = (e.target as HTMLElement).scrollLeft;
  }
  isScrolling = false;
};

const handleBottomScroll = (e: Event) => {
  if (isScrolling) return;
  isScrolling = true;
  if (topScrollRef.value) {
    topScrollRef.value.scrollLeft = (e.target as HTMLElement).scrollLeft;
  }
  isScrolling = false;
};

const handleAppointmentClick = (appointment: any) => {
  if (appointment && appointment.person && appointment.person.id) {
    const profileUrl = `/customer/profile/${appointment.person.id}?tab=appointment`;
    window.open(profileUrl, "_blank");
  } else {
    console.error("Appointment data is missing person ID:", appointment);
  }
};
</script>

<template>
  <div class="h-full space-y-2">
    <!-- Calendar Container -->
    <div class="h-full w-full overflow-hidden rounded-lg border border-gray-200 bg-white">
      <!-- Loading State -->
      <div v-if="isLoading" class="flex h-full items-center justify-center">
        <i class="pi pi-spin pi-spinner text-2xl" />
      </div>

      <!-- No Data State -->
      <div
        v-else-if="!sortedScheduleData.length"
        class="flex h-full items-center justify-center text-gray-500"
      >
        Không có lịch làm việc trong ngày này
      </div>

      <!-- Calendar Grid -->
      <div
        ref="scheduleContainerRef"
        class="relative flex h-full flex-col [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:border-2 [&::-webkit-scrollbar-thumb]:border-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar]:h-3 [&::-webkit-scrollbar]:w-3"
      >
        <!-- Top Scroll Container -->
        <div
          ref="topScrollRef"
          class="absolute left-0 right-0 top-0 z-20 flex flex-col-reverse items-start overflow-x-auto overscroll-contain bg-transparent [&::-webkit-scrollbar]:!mt-0"
          style="transform: rotateX(180deg)"
          @scroll="handleTopScroll"
        >
          <!-- Spacer div with same width as main content -->
          <div
            class="flex !h-12 items-start"
            :style="`width: ${sortedScheduleData.length * 170}px; transform: rotateX(180deg)`"
          />
        </div>

        <!-- Main Content with Bottom Scroll -->
        <div
          ref="bottomScrollRef"
          class="h-full overflow-x-auto overscroll-contain pt-0 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:border-2 [&::-webkit-scrollbar-thumb]:border-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar]:h-3 [&::-webkit-scrollbar]:w-3"
          @scroll="handleBottomScroll"
        >
          <!-- Grid Container -->
          <div
            class="grid h-full grid-flow-col"
            :style="`grid-template-columns: repeat(${sortedScheduleData.length}, minmax(170px, 1fr))`"
          >
            <!-- Doctor Column -->
            <div
              v-for="doctor in sortedScheduleData"
              :key="doctor.doctor_id"
              class="flex flex-col border-r border-gray-200 last:border-r-0"
            >
              <!-- Doctor Header -->
              <div
                class="sticky top-0 z-10 flex h-[72px] flex-col justify-center border-b border-primary/20 bg-white p-0.5 text-center shadow-sm transition-all sm:p-2.5"
              >
                <div class="flex items-center justify-center gap-2">
                  <!-- Doctor Avatar -->
                  <UserAvatar :userId="doctor.doctor_id" size="small" />

                  <div class="flex flex-col items-start">
                    <!-- Doctor Name -->
                    <div class="truncate text-sm font-semibold text-gray-800">
                      {{ doctor.doctor_name }}
                    </div>

                    <!-- Working Hours -->
                    <div class="flex items-center gap-1 text-xs text-gray-500">
                      <i class="pi pi-clock text-[10px]"></i>
                      {{ getDoctorWorkingHours(doctor.working_slots) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Doctor Schedule -->
              <div class="flex-1 overflow-hidden">
                <div
                  v-for="(slot, slotIndex) in doctor.working_slots"
                  :key="slot.start_time"
                  class="relative border-b border-gray-100"
                  :style="{
                    minHeight: `${Math.max(calculateSlotHeight(slot.start_time, slot.end_time), 40)}px`,
                  }"
                >
                  <!-- Shift Start Indicator -->
                  <div
                    class="flex items-center gap-1 border-y border-red-100 bg-red-50 px-2 py-1 text-xs font-medium text-red-700"
                  >
                    <i class="pi pi-arrow-right text-[10px]"></i>
                    {{ formatTime(slot.start_time) }}
                  </div>

                  <!-- Working Slot Content -->
                  <div
                    class="border-b border-gray-100 p-1"
                    :style="{
                      minHeight: `${Math.max(calculateSlotHeight(slot.start_time, slot.end_time), 40)}px`,
                    }"
                  >
                    <!-- Available Time Slots within Working Hours -->
                    <template v-if="slot.appointments.length">
                      <!-- Existing Appointments -->
                      <div
                        v-for="(apt, index) in slot.appointments"
                        :key="apt.id"
                        class="relative mb-1 last:mb-0"
                      >
                        <!-- Available Slot Before First Appointment -->
                        <div
                          v-if="index === 0 && hasGapBefore(slot.start_time, apt.start_time)"
                          @click="
                            handleEmptySlotClick(
                              doctor.doctor_id,
                              slot.start_time,
                              doctor.doctor_name,
                              doctor.department_id,
                            )
                          "
                          class="group mb-1 min-h-[20px] cursor-pointer rounded-md border-2 border-dashed border-emerald-600 bg-emerald-50 p-2 transition-all hover:scale-[1.02] hover:border-solid hover:border-emerald-600 hover:bg-emerald-100 hover:shadow-md"
                          :style="{
                            height: `${calculateSlotHeight(slot.start_time, apt.start_time)}px`,
                          }"
                        >
                          <div class="flex h-full flex-col justify-center">
                            <div class="flex items-center gap-1.5">
                              <div
                                class="flex h-5 w-5 items-center justify-center rounded-full bg-emerald-200 text-emerald-700 group-hover:bg-emerald-300"
                              >
                                <i class="pi pi-clock text-[10px]"></i>
                              </div>
                              <div class="text-xs font-medium text-emerald-800">
                                {{ formatTime(slot.start_time) }} - {{ formatTime(apt.start_time) }}
                                <span class="ml-1 text-emerald-700"
                                  >({{ calculateDuration(slot.start_time, apt.start_time) }}p)</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Appointment -->
                        <div
                          class="flex cursor-pointer items-center rounded border bg-[#3174AD] px-2 py-1 text-white opacity-90"
                          v-tooltip.left="parseAppointmentTooltip(apt)"
                          @click="handleAppointmentClick(apt)"
                        >
                          <div class="text-xs">
                            {{ formatTime(apt.start_time) }} - {{ formatTime(apt.end_time) }}
                            <span class="ml-1"
                              >({{ calculateDuration(apt.start_time, apt.end_time) }}p)</span
                            >
                          </div>
                        </div>

                        <!-- Available Slot Between Appointments -->
                        <div
                          v-if="
                            hasGapAfter(
                              apt.end_time,
                              slot.appointments[index + 1]?.start_time || slot.end_time,
                            )
                          "
                          @click="
                            handleEmptySlotClick(
                              doctor.doctor_id,
                              apt.end_time,
                              doctor.doctor_name,
                              doctor.department_id,
                            )
                          "
                          class="group mt-1 min-h-[20px] cursor-pointer rounded-md border-2 border-dashed border-emerald-600 bg-emerald-50 p-2 transition-all hover:scale-[1.02] hover:border-solid hover:border-emerald-600 hover:bg-emerald-100 hover:shadow-md"
                          :style="{
                            height: `${calculateSlotHeight(apt.end_time, slot.appointments[index + 1]?.start_time || slot.end_time)}px`,
                          }"
                        >
                          <div class="flex h-full flex-col justify-center">
                            <div class="flex items-center gap-1.5">
                              <div
                                class="flex h-5 w-5 items-center justify-center rounded-full bg-emerald-200 text-emerald-700 group-hover:bg-emerald-300"
                              >
                                <i class="pi pi-clock text-[10px]"></i>
                              </div>
                              <div class="text-xs font-medium text-emerald-800">
                                {{ formatTime(apt.end_time) }} -
                                {{
                                  formatTime(
                                    slot.appointments[index + 1]?.start_time || slot.end_time,
                                  )
                                }}
                                <span class="ml-1 text-emerald-700"
                                  >({{
                                    calculateDuration(
                                      apt.end_time,
                                      slot.appointments[index + 1]?.start_time || slot.end_time,
                                    )
                                  }}p)</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>

                    <!-- Completely Empty Working Slot -->
                    <div
                      v-else
                      @click="
                        handleEmptySlotClick(
                          doctor.doctor_id,
                          slot.start_time,
                          doctor.doctor_name,
                          doctor.department_id,
                        )
                      "
                      class="group min-h-[20px] cursor-pointer rounded-md border-2 border-dashed border-emerald-600 bg-emerald-50 p-2 transition-all hover:scale-[1.02] hover:border-solid hover:border-emerald-600 hover:bg-emerald-100 hover:shadow-md"
                      :style="{
                        height: `${calculateSlotHeight(slot.start_time, slot.end_time)}px`,
                      }"
                    >
                      <div class="flex h-full flex-col justify-center">
                        <div class="flex items-center gap-1.5">
                          <div
                            class="flex h-5 w-5 items-center justify-center rounded-full bg-emerald-200 text-emerald-700 group-hover:bg-emerald-300"
                          >
                            <i class="pi pi-clock text-[10px]"></i>
                          </div>
                          <div class="text-xs font-medium text-emerald-800">
                            {{ formatTime(slot.start_time) }} - {{ formatTime(slot.end_time) }}
                            <span class="ml-1 text-emerald-700"
                              >({{ calculateDuration(slot.start_time, slot.end_time) }}p)</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Shift End Indicator -->
                  <div
                    class="flex items-center gap-1 border-y border-red-100 bg-red-50 px-2 py-1 text-xs font-medium text-red-700"
                  >
                    <i class="pi pi-arrow-left text-[10px]"></i>
                    {{ formatTime(slot.end_time) }}
                  </div>

                  <!-- Break Time Indicator (if not last slot) -->
                  <div
                    v-if="slotIndex < doctor.working_slots.length - 1"
                    class="mt-1 flex items-center justify-center gap-2 bg-gray-50 px-2 py-1.5 text-xs text-gray-600"
                  >
                    <div class="flex items-center gap-1">
                      <i class="pi pi-clock text-[10px]"></i>
                      {{ formatTime(slot.end_time) }}
                    </div>
                    <div class="font-medium text-gray-400">|</div>
                    <div class="flex items-center gap-1">
                      <i class="pi pi-clock text-[10px]"></i>
                      {{ formatTime(doctor.working_slots[slotIndex + 1].start_time) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
