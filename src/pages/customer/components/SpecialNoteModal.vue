<template>
  <Popover ref="popoverRef" :pt="{ root: { class: 'surface-0 shadow-md' } }">
    <div class="min-w-[400px] p-0">
      <div class="flex items-center gap-2">
        <FormField :label="title" :icon="icon" size="lg" :icon-color="iconColor" />
      </div>

      <Textarea
        v-if="type === 'pancake_link'"
        auto-resize
        rows="3"
        fluid
        v-model="personField.pancake_link"
      />

      <Textarea
        v-else-if="type === 'special_note'"
        auto-resize
        rows="3"
        fluid
        v-model="personField.special_note"
      />

      <Textarea
        v-else-if="type === 'medical_condition'"
        auto-resize
        rows="3"
        fluid
        v-model="personField.medical_condition"
      />

      <div class="mt-2 flex w-full items-center justify-end gap-2">
        <Button label="Hủy" @click="hide" outlined severity="secondary" />
        <Button
          label="Xóa"
          @click="handleDelete"
          outlined
          severity="danger"
          v-if="!!personField[type]"
        />
        <Button label="Lưu" severity="primary" @click="handleSubmit" />
      </div>
    </div>
  </Popover>
</template>

<script setup lang="ts">
import Button from "primevue/button";
import Popover from "primevue/popover";
import { computed, ref } from "vue";

import { PersonMeta } from "@/api/bcare-types-v2";
import { FormField } from "@/components/Form";
import usePerson from "@/hooks/usePerson";

const props = defineProps<{
  personField: PersonMeta & { id: number };
  type: "medical_condition" | "special_note" | "pancake_link";
}>();

const emit = defineEmits<{
  (e: "person-updated"): void;
}>();

const { updatePerson } = usePerson();
const popoverRef = ref();

const title = computed(() => {
  switch (props.type) {
    case "medical_condition":
      return "Bệnh lý";
    case "special_note":
      return "Ghi chú đặc biệt";
    case "pancake_link":
      return "Link chat";
  }
});

const icon = computed(() => {
  switch (props.type) {
    case "medical_condition":
      return "pi pi-clipboard";
    case "special_note":
      return "pi pi-exclamation-triangle";
    case "pancake_link":
      return "pi pi-link";
  }
});

const iconColor = computed(() => {
  switch (props.type) {
    case "medical_condition":
      return "yellow-500";
    case "special_note":
      return "red-500";
    case "pancake_link":
      return "blue-500";
  }
});

const handleSubmit = async () => {
  await updatePerson({
    id: props.personField.id,
    person_field: props.personField,
  });
  hide();
  emit("person-updated");
};

const handleDelete = async () => {
  const updatedPersonField = { ...props.personField };

  // Reset field based on type
  if (props.type === "special_note") {
    updatedPersonField.special_note = "";
  } else if (props.type === "medical_condition") {
    updatedPersonField.medical_condition = "";
  } else if (props.type === "pancake_link") {
    updatedPersonField.pancake_link = "";
  }

  await updatePerson({
    id: props.personField.id,
    person_field: updatedPersonField,
  });
  hide();
  emit("person-updated");
};

const show = (event: Event) => {
  popoverRef.value?.show(event);
};

const hide = () => {
  popoverRef.value?.hide();
};

defineExpose({ show, hide });
</script>
