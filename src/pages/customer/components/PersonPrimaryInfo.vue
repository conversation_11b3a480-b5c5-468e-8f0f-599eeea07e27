<script lang="ts" setup>
import { computed, inject, ref, provide } from "vue";

import { DepartmentAssignment } from "@/api/bcare-enum";
import { DealResponse, PersonResponse } from "@/api/bcare-types-v2";
import Alert from "@/base-components/Alert/Alert.vue";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import { PersonCard } from "@/components/Person/";
import UserAssignPromax from "@/components/User/UserAssignPromax.vue";
import { useModalControl } from "@/composables/useModalControl";
import { usePermissions } from "@/composables/usePermissions";
import useLocation from "@/hooks/useLocation";
import usePerson from "@/hooks/usePerson";
import useTerm from "@/hooks/useTerm";
import PersonFormModal from "@/components/Person/PersonFormModal.vue";
import { useCallInfoStore } from "@/stores/call-info";
import { formatShortDate, formatShortDateTime } from "@/utils/time-helper";
import { copyToClipboard } from "@/utils/helper";
import Fieldset from "primevue/fieldset";
import EntityTags from "@/components/Tags/EntityTags.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";

import PersonSubmissionPopup from "@/components/Person/PersonSubmissionPopup.vue";
import PersonDeals from "@/components/Person/PersonDeals.vue";
import SpecialNoteModal from "./SpecialNoteModal.vue";
import BankInfo from "./BankInfo.vue";
import PersonSourceUrlPopup from "@/components/Person/PersonSourceUrlPopup.vue";

const loadPersonDetail =
  inject<() => Promise<void>>("loadPersonDetail") ||
  (async () => {
    console.error("loadPersonDetail is not available in PersonPrimaryInfo");
    // Implement actual loading logic or ensure it's provided by *its* parent
  });

const dealList = inject<DealResponse[]>("dealList") || [];
const showMedicalText = ref(false);

type AssignmentRole = "doctor" | "counselor" | "sale" | "customer_care";

const { getProvinceById, getDistrictById, getWardById } = useLocation();

const { getTermNameById } = useTerm();

const callInfoStore = useCallInfoStore();

const props = defineProps<{
  person: PersonResponse;
}>();

const { modalRef: personFormModalRef, openModal: openPersonFormModal } = useModalControl();

const { addPersonAssignment, deletePersonAssignment } = usePerson();

const { canViewPhone } = usePermissions();

// Provide the reload function for EntityTags
provide("reloadData", loadPersonDetail);

// Modify getAssignmentValues to handle both multiple and single assignments
const getAssignmentValues = (role: AssignmentRole, singleValue = false) =>
  computed({
    get: () => {
      const assignments = props.person.assignment?.filter((a) => a.role === role) ?? [];
      const userIds = assignments.map((a) => a.user_id);
      return singleValue ? userIds[0] || undefined : userIds;
    },
    set: async (newValue: number | number[] | undefined) => {
      try {
        const currentAssignments = props.person.assignment?.filter((a) => a.role === role) ?? [];

        if (singleValue) {
          // Handle single assignment (like sale)
          const newUserId = newValue as number | undefined;
          const currentUserId = currentAssignments[0]?.user_id;

          // If same value, do nothing
          if (newUserId === currentUserId) return;

          // Delete existing assignment if any
          if (currentUserId) {
            const assignmentToDelete = currentAssignments[0];
            await deletePersonAssignment({ id: assignmentToDelete.id });
          }

          // Add new assignment if provided
          if (newUserId) {
            await addPersonAssignment({
              person_id: props.person.id,
              user_id: newUserId,
              role,
            });
          }
        } else {
          // Handle multiple assignments (like doctor, counselor, etc.)
          const newUserIds = newValue as number[];
          const currentUserIds = currentAssignments.map((a) => a.user_id);
          const userIdsToAdd = newUserIds.filter((id) => !currentUserIds.includes(id));
          const assignmentsToDelete = currentAssignments.filter(
            (a) => !newUserIds.includes(a.user_id),
          );

          const addPromises = userIdsToAdd.map((userId) =>
            addPersonAssignment({
              person_id: props.person.id,
              user_id: userId,
              role,
            }),
          );

          const deletePromises = assignmentsToDelete.map((assignment) =>
            deletePersonAssignment({ id: assignment.id }),
          );

          await Promise.all([...addPromises, ...deletePromises]);
        }

        loadPersonDetail();
      } catch (error) {
        console.error(`Error updating ${role} assignments:`, error);
      }
    },
  });

// Then use it like this
const doctorIds = getAssignmentValues("doctor", true);
const counselorIds = getAssignmentValues("counselor", true);
const saleIds = getAssignmentValues("sale", true);
const customerCareIds = getAssignmentValues("customer_care", true);

const noteType = ref<"medical_condition" | "special_note" | "pancake_link">("special_note");
const specialNoteModalRef = ref();
const bankInfoEditorRef = ref();

const openNoteModal = (
  event: Event,
  type: "medical_condition" | "special_note" | "pancake_link",
) => {
  noteType.value = type;
  specialNoteModalRef.value?.show(event);
};

const showPersonField = computed(() => {
  const personField = props.person.person_field;
  return personField?.description || personField?.special_note || personField?.medical_condition;
});

const handleCallPerson = (isSecondaryPhone = false) => {
  if (!props.person) return;

  const phone = isSecondaryPhone ? props.person.person_field?.secondary_phone : props.person.phone;

  if (!phone) return;

  callInfoStore.showWithPerson({
    ...props.person,
    phone,
  });
};

const copyAllBankInfo = () => {
  const pf = props.person.person_field;
  if (!pf) {
    return;
  }
  const lines = [];
  if (pf.bank_account_name) {
    lines.push(`Tên tài khoản: ${pf.bank_account_name}`);
  }
  if (pf.bank_account_number) {
    lines.push(`Số tài khoản: ${pf.bank_account_number}`);
  }
  if (pf.bank) {
    lines.push(`Ngân hàng: ${pf.bank}`);
  }
  if (pf.bank_branch) {
    lines.push(`Chi nhánh: ${pf.bank_branch}`);
  }

  if (lines.length > 0) {
    const textToCopy = lines.join("\n");
    copyToClipboard(textToCopy);
  }
};

const openBankInfoEditor = (event: Event) => {
  bankInfoEditorRef.value?.show(event);
};
</script>
<template>
  <div class="box mt-5 pb-3 md:mt-0">
    <div class="relative flex items-center justify-between gap-10 p-3">
      <span v-if="person.id">
        <PersonCard :person="person" bottom-info="info_tags" show-gender size="large" />
      </span>

      <Menu>
        <Menu.Button class="block h-5 w-5" href="#" tag="a">
          <Lucide class="h-5 w-5 text-slate-500" icon="MoreHorizontal" />
        </Menu.Button>
        <Menu.Items class="-mt-0.5 w-52 pt-2 shadow-slate-400/60" placement="bottom-start">
          <Menu.Item @click="openPersonFormModal">
            <Lucide class="mr-2 h-4 w-4" icon="Edit" />
            Chỉnh sửa
          </Menu.Item>
          <Menu.Item @click="(e) => openNoteModal(e, 'special_note')">
            <Lucide class="mr-2 h-4 w-4" icon="FileText" />
            Ghi chú đặc biệt
          </Menu.Item>
          <Menu.Item @click="(e) => openNoteModal(e, 'medical_condition')">
            <Lucide class="mr-2 h-4 w-4" icon="Clipboard" />
            Bệnh lý
          </Menu.Item>
        </Menu.Items>
      </Menu>
    </div>

    <div
      v-if="showPersonField"
      class="space-y-2 border-t border-slate-200/60 p-3 dark:border-darkmode-400"
    >
      <Alert
        v-if="person.person_field?.special_note"
        v-tooltip="'Ghi chú đặc biệt'"
        class="flex cursor-pointer items-center"
        variant="outline-danger"
        @click="openNoteModal($event, 'special_note')"
      >
        <span class="absolute -right-1.5 -top-1.5 flex h-3 w-3">
          <span
            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-500 opacity-75"
          ></span>
          <span class="relative inline-flex h-3 w-3 rounded-full bg-red-600"></span>
        </span>
        {{ person.person_field?.special_note }}
      </Alert>

      <Alert
        v-if="person.person_field?.medical_condition"
        v-tooltip="'Bệnh lý'"
        class="group relative flex cursor-pointer items-center"
        variant="outline-warning"
        @click="openNoteModal($event, 'medical_condition')"
      >
        <span class="absolute -right-1.5 -top-1.5 flex h-3 w-3">
          <span
            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-orange-500 opacity-75"
          ></span>
          <span class="relative inline-flex h-3 w-3 rounded-full bg-orange-600"></span>
        </span>
        <!-- Text content with blur -->
        <span
          :class="[showMedicalText ? 'blur-none' : 'blur-[4px]']"
          class="transition-all duration-200"
        >
          {{ person.person_field?.medical_condition }}
        </span>
        <!-- Eye icon -->
        <i
          :class="[showMedicalText ? 'pi-eye-slash' : 'pi-eye']"
          class="pi absolute right-2 cursor-pointer text-slate-500 transition-all hover:text-slate-700"
          @click.stop="showMedicalText = !showMedicalText"
        ></i>
      </Alert>

      <Alert
        v-if="person.person_field?.description"
        v-tooltip="'Ghi chú'"
        class="flex items-center"
        variant="outline-primary"
      >
        <span class="absolute -right-1.5 -top-1.5 flex h-3 w-3">
          <span
            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-primary/90 opacity-75"
          ></span>
          <span class="relative inline-flex h-3 w-3 rounded-full bg-primary"></span>
        </span>
        {{ person.person_field?.description }}
      </Alert>
    </div>

    <div class="space-y-4 border-t border-slate-200/60 p-3 dark:border-darkmode-400">
      <div class="flex items-center" v-if="canViewPhone()">
        <Lucide class="mr-2 h-4 w-4" icon="PhoneCall" />
        <span class="mr-2 whitespace-nowrap font-medium">Số chính:</span>
        <div class="fixed-hotline group flex items-center hover:text-primary hover:underline">
          {{ person.phone }}
          <span
            class="ml-3 hidden h-5 w-5 cursor-pointer items-center justify-center rounded-full bg-success text-white shadow-md transition group-hover:flex"
            @click="handleCallPerson(false)"
          >
            <Lucide class="h-3 w-3 -rotate-90" icon="Phone" />
          </span>
        </div>
      </div>
      <div class="flex items-center" v-if="canViewPhone()">
        <Lucide class="mr-2 h-4 w-4" icon="PhoneCall" />
        <span class="mr-2 whitespace-nowrap font-medium">Số phụ:</span>
        <div class="fixed-hotline group flex items-center hover:text-primary hover:underline">
          {{ person.person_field?.secondary_phone }}
          <span
            class="ml-3 hidden h-5 w-5 cursor-pointer items-center justify-center rounded-full bg-success text-white shadow-md transition group-hover:flex"
            @click="handleCallPerson(true)"
          >
            <Lucide class="h-3 w-3 -rotate-90" icon="Phone" />
          </span>
        </div>
      </div>
      <div class="flex items-center">
        <Lucide class="mr-2 h-4 w-4" icon="CalendarRange" />
        <span class="mr-2 whitespace-nowrap font-medium">Ngày sinh:</span>
        {{ formatShortDate(person.date_of_birth) }}
      </div>
      <div class="flex items-center">
        <Lucide class="mr-2 h-4 w-4" icon="Share2" />
        <span class="mr-2 whitespace-nowrap font-medium">Zalo:</span>
        {{
          person.person_field.has_zalo === "yes"
            ? "Có sử dụng"
            : person.person_field.has_zalo === "no"
              ? "Không sử dụng"
              : "Không xác định"
        }}
      </div>
      <div class="flex flex-wrap items-center">
        <Lucide class="mr-2 h-4 w-4" icon="MapPin" />
        <span class="mr-2 whitespace-nowrap font-medium">Địa chỉ:</span>
        <span v-if="person.address_number" class="mr-1">{{ person.address_number }},</span>
        <span v-if="person.ward_id" class="mr-1">{{ getWardById(person.ward_id)?.name }}, </span>
        <span v-if="person.district_id" class="mr-1"
          >{{ getDistrictById(person.district_id)?.name }},
        </span>
        <span v-if="person.province_id">{{ getProvinceById(person.province_id)?.name }} </span>
      </div>

      <div class="flex flex-wrap items-center">
        <Lucide class="mr-2 h-4 w-4" icon="MessageCircle" />
        <span class="mr-2 whitespace-nowrap font-medium">Link chat:</span>
        <div class="group flex items-center gap-2">
          <template v-if="person.person_field?.pancake_link">
            <a
              :href="person.person_field.pancake_link"
              target="_blank"
              class="inline-flex cursor-pointer items-center gap-2 text-gray-700 decoration-1 underline-offset-2 hover:underline hover:decoration-dotted"
            >
              Mở
              <i class="pi pi-external-link text-sm" />
            </a>
            <i
              v-tooltip="'Chỉnh sửa link chat'"
              class="pi pi-pencil cursor-pointer text-sm"
              @click="openNoteModal($event, 'pancake_link')"
            />
          </template>
          <span
            v-else
            class="flex cursor-pointer items-center gap-1 text-slate-400 hover:text-primary"
            @click="openNoteModal($event, 'pancake_link')"
          >
            Thêm link
            <i class="pi pi-link text-sm" />
          </span>
        </div>
      </div>
    </div>

    <!-- START: Updated Tags Section -->
    <Fieldset
      :pt="{
        root: {
          class:
            'border-t border-b-0 border-l-0 border-r-0 border-slate-200/60 dark:border-darkmode-400 rounded-none',
        },
        toggleButton: { class: 'p-1 bg-transparent w-full justify-start' },
        legendLabel: { class: 'font-light text-sm' },
      }"
      :toggleable="true"
      class="p-3"
      legend="Gắn thẻ"
    >
      <EntityTags
        entity-type="person"
        :entity-id="person.id"
        :initial-tags="person.tags"
        placeholder="Thêm thẻ"
      />
    </Fieldset>
    <!-- END: Updated Tags Section -->

    <PersonDeals :deal-list="dealList" />

    <Fieldset
      :pt="{
        root: {
          class:
            'border-t border-b-0 border-l-0 border-r-0 border-slate-200/60 dark:border-darkmode-400 rounded-none',
        },
        toggleButton: { class: 'p-1 bg-transparent w-full justify-start' },
        legendLabel: { class: 'font-light text-sm' },
      }"
      class="p-3"
      legend="Chi tiết"
      toggleable
    >
      <div class="space-y-4">
        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Calendar" />
            <span class="font-medium">Nghề nghiệp:</span>
          </div>
          <span class="flex-1">{{ getTermNameById("nghe_nghiep", person.job_id) }}</span>
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Mail" />
            <span class="font-medium">Email:</span>
          </div>
          <a class="flex-1 text-primary underline decoration-dotted" href="">{{ person.email }}</a>
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Framer" />
            <span class="font-medium">Loại điều trị:</span>
          </div>
          <span class="flex-1">
            {{ getTermNameById("loai_dieu_tri", person.person_field?.treatment_id || 0) }}
          </span>
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Users" />
            <span class="font-medium">Bác sĩ:</span>
          </div>
          <UserAssignPromax
            v-model="doctorIds"
            :department-id="DepartmentAssignment.DOCTOR"
            class="min-w-[200px] flex-1"
            global-unique-name="doctor"
            :multiple="false"
            placeholder="Chọn bác sĩ"
          />
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Users" />
            <span class="font-medium">Trợ lý BS:</span>
          </div>
          <UserAssignPromax
            v-model="counselorIds"
            :department-id="DepartmentAssignment.ASSISTANT"
            class="min-w-[200px] flex-1"
            global-unique-name="assistant"
            :multiple="false"
            placeholder="Chọn trợ lý BS"
          />
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Headphones" />
            <span class="font-medium">Telesales:</span>
          </div>

          <UserAssignPromax
            v-model="saleIds"
            :department-id="DepartmentAssignment.TELESALES"
            class="min-w-[200px] flex-1"
            global-unique-name="telesale"
            :multiple="false"
            placeholder="Chọn telesales"
          />
        </div>

        <div class="flex min-w-0 flex-wrap items-center gap-2">
          <div class="flex shrink-0 items-center">
            <Lucide class="mr-2 h-4 w-4" icon="Headphones" />
            <span class="font-medium">CSKH:</span>
          </div>
          <UserAssignPromax
            v-model="customerCareIds"
            :department-id="DepartmentAssignment.CS"
            class="min-w-[200px] flex-1"
            global-unique-name="cskh"
            :multiple="false"
            placeholder="Chọn CSKH"
          />
        </div>
      </div>
    </Fieldset>

    <Fieldset
      :pt="{
        root: {
          class:
            'border-t border-b-0 border-l-0 border-r-0 border-slate-200/60 dark:border-darkmode-400 rounded-none',
        },
        toggleButton: { class: 'p-1 bg-transparent w-full justify-start' },
        legendLabel: { class: 'font-light text-sm' },
      }"
      :toggleable="true"
      class="p-3"
      legend="Thông tin khác"
    >
      <div class="flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="UserPlus" />
        <span class="whitespace-nowrap font-medium">Người tạo:</span>
        <UserAvatar v-if="person.user_id" :user-id="person.user_id" size="small" show-name />
      </div>
      <div class="mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="Globe2" />
        <span class="whitespace-nowrap font-medium">Nguồn:</span>
        {{ getTermNameById("nguon", person.source_id) }}
      </div>
      <div class="mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="Calendar" />
        <span class="whitespace-nowrap font-medium">Ngày tạo:</span>
        {{ formatShortDateTime(person.created_at) }}
      </div>
      <div
        v-if="person.person_field?.form_source || person.person_field?.form_position"
        class="mt-5 flex flex-wrap items-center gap-2"
      >
        <Lucide class="h-4 w-4 font-bold" icon="FileText" />
        <span class="whitespace-nowrap font-medium">Form:</span>
        <span>
          {{ person.person_field?.form_source }}
          {{ person.person_field?.form_source && person.person_field?.form_position ? " | " : "" }}
          {{ person.person_field?.form_position }}
        </span>
      </div>

      <div
        v-if="person.person_field?.source_channel || person.person_field?.landing_page_url"
        class="mt-5 flex flex-wrap items-center gap-2"
      >
        <Lucide class="h-4 w-4 font-bold" icon="Link" />
        <span class="whitespace-nowrap font-medium">Nguồn & URL:</span>
        <PersonSourceUrlPopup
          :url="person.person_field?.landing_page_url ?? ''"
          :source-channel="person.person_field?.source_channel ?? ''"
        />
      </div>

      <div v-if="person.form_submissions?.length" class="mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="FileText" />
        <span class="whitespace-nowrap font-medium">Submission:</span>
        <PersonSubmissionPopup :submissions="person.form_submissions" />
      </div>
    </Fieldset>

    <Fieldset
      :pt="{
        root: {
          class:
            'border-t border-b-0 border-l-0 border-r-0 border-slate-200/60 dark:border-darkmode-400 rounded-none',
        },
        toggleButton: { class: 'p-1 bg-transparent w-full justify-start' },
        legendLabel: { class: 'font-light text-sm' },
      }"
      :toggleable="true"
      class="p-3"
    >
      <template #legend>
        <div class="flex w-full items-center justify-between gap-1 py-1">
          <span class="text-sm font-light">Thông tin ngân hàng</span>
          <i
            v-tooltip.top="'Chỉnh sửa'"
            class="pi pi-pencil cursor-pointer rounded p-1 text-sm text-slate-600 hover:bg-slate-200/60 hover:text-primary dark:text-slate-400 dark:hover:bg-darkmode-600"
            @click.stop="openBankInfoEditor($event)"
          ></i>
          <i
            v-tooltip.top="'Copy'"
            class="pi pi-copy cursor-pointer rounded p-1 text-sm text-slate-600 hover:bg-slate-200/60 hover:text-primary dark:text-slate-400 dark:hover:bg-darkmode-600"
            @click.stop="copyAllBankInfo"
          ></i>
        </div>
      </template>
      <div class="group flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="UserX" />
        <span class="whitespace-nowrap font-medium">Tên tài khoản:</span>
        <span v-if="person.person_field?.bank_account_name" class="flex items-center">
          {{ person.person_field.bank_account_name }}
          <i
            v-tooltip.top="'Copy'"
            class="pi pi-copy ml-2 cursor-pointer text-xs text-slate-500 opacity-0 transition-opacity group-hover:opacity-100 dark:text-slate-400"
            @click="copyToClipboard(person.person_field.bank_account_name!)"
          ></i>
        </span>
        <i
          v-else
          v-tooltip.top="'Chưa có thông tin'"
          class="pi pi-minus text-xs text-slate-500"
        ></i>
      </div>
      <div class="group mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="CreditCard" />
        <span class="whitespace-nowrap font-medium">Số tài khoản:</span>
        <span v-if="person.person_field?.bank_account_number" class="flex items-center">
          {{ person.person_field.bank_account_number }}
          <i
            v-tooltip.top="'Copy'"
            class="pi pi-copy ml-2 cursor-pointer text-xs text-slate-500 opacity-0 transition-opacity group-hover:opacity-100 dark:text-slate-400"
            @click="copyToClipboard(person.person_field.bank_account_number!)"
          ></i>
        </span>
        <i
          v-else
          v-tooltip.top="'Chưa có thông tin'"
          class="pi pi-minus text-xs text-slate-500"
        ></i>
      </div>
      <div class="group mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="Landmark" />
        <span class="whitespace-nowrap font-medium">Ngân hàng:</span>
        <span v-if="person.person_field?.bank" class="flex items-center">
          {{ person.person_field.bank }}
          <i
            v-tooltip.top="'Copy'"
            class="pi pi-copy ml-2 cursor-pointer text-xs text-slate-500 opacity-0 transition-opacity group-hover:opacity-100 dark:text-slate-400"
            @click="copyToClipboard(person.person_field.bank!)"
          ></i>
        </span>
        <i
          v-else
          v-tooltip.top="'Chưa có thông tin'"
          class="pi pi-minus text-xs text-slate-500"
        ></i>
      </div>
      <div class="group mt-5 flex flex-wrap items-center gap-2">
        <Lucide class="h-4 w-4 font-bold" icon="Building" />
        <span class="whitespace-nowrap font-medium">Chi nhánh:</span>
        <span v-if="person.person_field?.bank_branch" class="flex items-center">
          {{ person.person_field.bank_branch }}
          <i
            v-tooltip.top="'Copy'"
            class="pi pi-copy ml-2 cursor-pointer text-xs text-slate-500 opacity-0 transition-opacity group-hover:opacity-100 dark:text-slate-400"
            @click="copyToClipboard(person.person_field.bank_branch!)"
          ></i>
        </span>
        <i
          v-else
          v-tooltip.top="'Chưa có thông tin'"
          class="pi pi-minus text-xs text-slate-500"
        ></i>
      </div>
    </Fieldset>
  </div>

  <PersonFormModal
    ref="personFormModalRef"
    :person-id="person.id ?? 0"
    @person-updated="loadPersonDetail"
  />
  <SpecialNoteModal
    ref="specialNoteModalRef"
    :person-field="{
      id: person.id ?? 0,
      ...person.person_field,
    }"
    :type="noteType"
    @person-updated="loadPersonDetail"
  />
  <BankInfo
    ref="bankInfoEditorRef"
    :person-id="person.id ?? 0"
    :initial-person-field="person.person_field"
    @person-updated="loadPersonDetail"
  />
</template>
