<script setup lang="ts">
import { watch } from "vue";
import { useRouter } from "vue-router";

import { useModalCustomerStore } from "@/stores/modal-customer-store";

import ContentCustomer from "./components/ContentCustomer.vue";

const modalStore = useModalCustomerStore();
const router = useRouter();

watch(
  () => modalStore.visible,
  (newValue) => {
    if (!newValue) {
      const currentRoute = router.currentRoute.value;
      if (Object.keys(currentRoute.query).length > 0) {
        router.replace({
          path: currentRoute.path,
          query: {},
        });
      }
      modalStore.closeModal();
    }
  },
);
</script>

<template>
  <Dialog
    v-model:visible="modalStore.visible"
    modal
    destroyOnHide
    :pt="{
      header: 'py-1 px-3 border-b bg-white rounded-t-lg',
      title: 'text-base font-medium',
      content: 'bg-slate-100 py-2',
    }"
    closeOnEscape
    @hide="modalStore.closeModal"
    class="w-[99%] bg-slate-100"
    style="height: 99vh"
    header="Chi tiết Khách hàng"
    dismissableMask
    maximizable
    blockScroll
  >
    <ContentCustomer
      v-if="modalStore.personId"
      :key="modalStore.personId"
      :person-id="Number(modalStore.personId)"
      @load-person="modalStore.setPerson"
    />
    <Message class="mt-2" v-else severity="error"
      >Khách hàng không khả dụng, vui lòng chọn lại</Message
    >
  </Dialog>
</template>
