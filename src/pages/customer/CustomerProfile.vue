<script setup lang="ts">
import { onMounted, ref, defineAsyncComponent } from "vue";
import { useRoute, useRouter } from "vue-router";

import { PersonResponse } from "@/api/bcare-types-v2";
import { TopBackButton } from "@/components";

import ContentCustomer from "./components/ContentCustomer.vue";
import PersonCard from "@/components/Person/PersonCard.vue";
import Lucide from "@/base-components/Lucide";

const PersonPrimaryInfo = defineAsyncComponent(() => import("./components/PersonPrimaryInfo.vue"));

const route = useRoute();
const hasHistory = ref(false);
const router = useRouter();
const personItem = ref<PersonResponse>();
const isPersonDrawerVisible = ref(false);

onMounted(async () => {
  hasHistory.value = window.history.length > 1;
});

const reloadPerson = () => {
  console.log("Reloading person details potentially needed");
};
</script>

<template>
  <div class="intro-y mt-0 p-4 md:mt-5">
    <div class="flex items-center justify-between md:mb-4">
      <TopBackButton title="Thông tin khách hàng" @click="router.go(-1)" class="hidden md:flex" />
      <div
        v-if="personItem"
        class="flex w-full cursor-pointer items-center justify-between rounded-lg bg-white p-3 shadow-sm hover:bg-gray-50 dark:bg-darkmode-600 dark:hover:bg-darkmode-500 md:hidden"
        @click="isPersonDrawerVisible = true"
      >
        <PersonCard
          :person="personItem"
          class="flex-1"
          :show-phone="false"
          :show-code="true"
          size="small"
        >
          <template #bottom-info>
            <span class="mt-0.5 text-xs text-gray-500 dark:text-slate-400">Xem chi tiết</span>
          </template>
        </PersonCard>
        <Lucide icon="ChevronRight" class="h-5 w-5 text-gray-400" />
      </div>
    </div>

    <ContentCustomer :person-id="+route.params.id" @load-person="(item) => (personItem = item)" />

    <Drawer
      v-model:visible="isPersonDrawerVisible"
      position="left"
      class="w-full max-w-md md:hidden"
      style="width: 85vw"
    >
      <template #header>
        <h2 class="text-lg font-medium">Thông tin chi tiết</h2>
      </template>
      <PersonPrimaryInfo
        v-if="personItem && isPersonDrawerVisible"
        :person="personItem"
        @person-updated="
          reloadPerson();
          isPersonDrawerVisible = false;
        "
        class="!box-shadow-none !mt-0 !border-none"
      />
    </Drawer>
  </div>
</template>
