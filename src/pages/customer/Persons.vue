<script lang="ts" setup>
import { FilterMatchMode } from "@primevue/core/api";
import { useDateFormat } from "@vueuse/core";
import MultiSelect from "primevue/multiselect";
import { computed, nextTick, onMounted, ref } from "vue";

import { FilterOperator, FilterSqlFunction } from "@/api/bcare-enum";
import { Filter, PersonResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Term from "@/base-components/Term/Term.vue";
import { ColumnDefinition, DataTable, FilterType } from "@/components/DataTable";
import { DateTimeInfo, NoteInfo, TermInfo } from "@/components/InfoText";
import { PersonCard } from "@/components/Person/";
import PipelineStageSelect from "@/components/PipelineStageSelect.vue";
import PopSetting from "@/components/Settings/PopSetting.vue";
import { UserAvatar } from "@/components/User";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import { useModalControl } from "@/composables/useModalControl";
import { usePermissions } from "@/composables/usePermissions";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import useLocation from "@/hooks/useLocation";
import usePerson from "@/hooks/usePerson";
import { usePersonQuery } from "@/hooks/usePersonQuery";
import useTerm from "@/hooks/useTerm";
import useUser from "@/hooks/useUser";
import PersonAddModal from "@/components/Person/PersonFormModal.vue";
import { useSettingGlobalStore } from "@/stores/setting-global-store";
import { handleDownload } from "@/utils/helper";
import { useModalCustomerStore } from "@/stores/modal-customer-store";

const { modalRef: personFormModalRef, openModal: openPersonFormModal } = useModalControl();
const { onlyAdmin } = usePermissions();

const { getProvinceById, getDistrictById, getWardById } = useLocation();
const { getBundleTerms } = useTerm();

const { deletePerson } = usePerson();
const { persons, total, fetchPersons, isLoading } = usePersonQuery();

const settingGlobalStore = useSettingGlobalStore();
const personSettings = computed(() => settingGlobalStore.getSettingByKey("person"));
const { confirm } = useConfirmTippy();

const personId = ref(0);
const showActions = ref({ edit: true, delete: true });

const fetchType = ref<{ name: string; value: string; icon: string } | null>(null);
const fetchTypeOps = ref([
  { name: "Tất cả", value: "all", icon: "pi pi-users text-blue-500" },
  { name: "Khách hàng mới", value: "new", icon: "pi pi-sparkles text-yellow-500" },
  { name: "Khách hàng sinh nhật hôm nay", value: "birthday", icon: "pi pi-gift text-pink-500" },
  {
    name: "Khách hàng đến khám hôm nay",
    value: "appointment",
    icon: "pi pi-clock text-green-500",
  },
]);

// Local pagination state
const page = ref(1);
const rowsPerPage = ref(10);
const rowsPerPageOptions = [10, 20, 50, 100];

// Function to reset pagination
const resetPagination = () => {
  page.value = 1;
  // Keep rowsPerPage as is
};

const { users, getUsersByDepartmentIds } = useUser({ autoLoad: true });

const departmentUsers = computed(() =>
  users.value.length > 0 ? getUsersByDepartmentIds([10]) : [],
);

const HEADER_PERSON = computed(() => [
  { key: "id", name: "STT", isCheck: true },
  {
    key: "full_name",
    name: "Thông tin KH",
    isCheck: true,
    filterType: "text",
    filterPlaceholder: "Tên KH & Mã hồ sơ & SĐT",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  { key: "email", name: "Email", isCheck: false },
  { key: "date_of_birth", name: "Ngày sinh", isCheck: true },
  { key: "job_id", name: "Nghề nghiệp", isCheck: false },
  { key: "person_field.description", name: "Mô tả", isCheck: false },
  { key: "address_number", name: "Địa chỉ", isCheck: false },
  {
    key: "source_id",
    name: "Nguồn khách hàng",
    isCheck: true,
    filterType: "custom",
    // filterPlaceholder: "Chọn nguồn KH",
    // filterMatchMode: FilterMatchMode.CONTAINS,
    // filterOptions:
    //   getBundleTerms("nguon")?.map((term) => ({
    //     title: term.name,
    //     value: term.id,
    //   })) || [],
  },
  {
    key: "sale.name",
    name: "Sale phụ trách",
    isCheck: true,
    filterType: "select",
    filterPlaceholder: "Chọn sale",
    filterMatchMode: FilterMatchMode.CONTAINS,
    filterOptions:
      departmentUsers.value.map((user) => ({
        title: user.name,
        value: user.id,
      })) || [],
  },
  {
    key: "stage_name",
    name: "Stage",
    isCheck: true,
    filterType: "custom",
    filterPlaceholder: "Chọn stage",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    key: "person_field.treatment_id",
    name: "Loại điều trị",
    isCheck: true,
  },
  {
    key: "person_field.treatment_status_id",
    name: "Trạng thái điều trị",
    isCheck: true,
  },
  {
    key: "created_at",
    name: "Ngày tạo",
    isCheck: true,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày",
  },
  {
    key: "person_field.form_source",
    name: "Thông tin form",
    isCheck: false,
  },
  {
    key: "person_field.source_channel",
    name: "Nguồn & URL",
    isCheck: false,
  },
]);

const visibleColumns = computed(() => {
  const displayColumnsGroup = personSettings.value.find(
    (setting) => setting.type === "group" && setting.field_name === "display",
  );
  const displayColumns = displayColumnsGroup?.children?.find(
    (setting) => setting.field_name === "display_columns",
  );
  return (
    displayColumns?.options?.filter((option) => option.value).map((option) => option.field_name) ||
    []
  );
});

const columns = computed<ColumnDefinition<any>[]>(() =>
  HEADER_PERSON.value
    .filter((column) => visibleColumns.value.includes(column.key))
    .map((column) => ({
      field: column.key as keyof PersonResponse,
      header: column.name,
      sortable: false,
      showFilterMenu: false,
      filterType: column.filterType as FilterType | undefined,
      filterPlaceholder: column.filterPlaceholder,
      filterMatchMode: column.filterMatchMode,
      filterOptions: column.filterOptions as any,
    })),
);

const filterConfigs = {
  full_name: {
    field: "full_name",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  source_id: {
    field: "source_id",
    valueTransform: (v: string | string[] | undefined) => {
      if (v === undefined) return null;

      // Handle single selection
      if (typeof v === "string") {
        return `${v}`;
      }

      // Handle multiple selection
      if (Array.isArray(v) && v.length > 0) {
        const sourceIds = v.join(",");
        return `(${sourceIds})`;
      }

      return null;
    },
    operator: FilterOperator.IN,
  },
  created_at: {
    field: "created_at",
    isDateRange: true,
  },
  "sale.name": {
    field: "sale_id",
    operator: FilterOperator.EQ,
    valueTransform: (v: string) => `${v}`,
  },
  stage_name: {
    field: "stage_id",
    valueTransform: (v: number | number[] | undefined) => {
      if (v === undefined) return null;

      // Handle single selection mode
      if (typeof v === "number") {
        if (isNaN(v)) return null;

        return {
          logic: "OR",
          conditions: [
            {
              field: "stage_id",
              operator: FilterOperator.EQ,
              value: v.toString(),
            },
            {
              field: "stage_parent_id",
              operator: FilterOperator.EQ,
              value: v.toString(),
            },
          ],
        };
      }

      // Handle multiple selection mode
      if (Array.isArray(v) && v.length > 0) {
        const stageIds = v.join(",");

        return {
          logic: "OR",
          conditions: [
            {
              field: "stage_id",
              operator: FilterOperator.IN,
              value: `(${stageIds})`,
            },
            {
              field: "stage_parent_id",
              operator: FilterOperator.IN,
              value: `(${stageIds})`,
            },
          ],
        };
      }

      return null;
    },
  },
};

const baseFilters: Filter[] = [
  {
    field: "status",
    operator: FilterOperator.NEQ,
    value: "-1",
  },
];

const { filters, currentFilterPayload } = useFilterQuery((appliedFilters) => {
  // Reset pagination when filters change
  resetPagination();
  loadPersons();
}, filterConfigs);

const modalCustomerStore = useModalCustomerStore();

const handleRowClick = (data: any) => {
  // Open the modal instead of navigating
  modalCustomerStore.openModal(data.id.toString());
};

const handlePageChange = (event: { first: number; rows: number }) => {
  page.value = Math.floor(event.first / event.rows) + 1;
  rowsPerPage.value = event.rows;
  loadPersons(false);
};

const loadPersons = (getCount: boolean = true) => {
  const month = useDateFormat(new Date(), "MM").value;
  const day = useDateFormat(new Date(), "DD").value;

  const processedFilters =
    currentFilterPayload.value.filters
      ?.map((filter: Filter) => {
        if (filter.field === "full_name") {
          return {
            logic: "OR",
            conditions: [
              {
                field: "full_name",
                operator: FilterOperator.LIKE,
                value: filter.value,
              },
              {
                field: "phone",
                operator: FilterOperator.LIKE,
                value: filter.value,
              },
              {
                field: "person_code",
                operator: FilterOperator.LIKE,
                value: filter.value,
              },
            ],
          };
        }
        if (filter.field === "stage_id" && typeof filter.value === "object") {
          return filter.value;
        }
        return filter;
      })
      .filter(Boolean) || [];

  const typeFilters: Record<string, Filter[]> = {
    all: [],
    new: [
      {
        field: "created_at",
        operator: FilterOperator.TODAY,
      },
    ],
    birthday: [
      {
        field: "date_of_birth",
        operator: FilterOperator.EQ,
        value: month,
        function: FilterSqlFunction.MONTH,
      },
      {
        field: "date_of_birth",
        operator: FilterOperator.EQ,
        value: day,
        function: FilterSqlFunction.DAY,
      },
    ],
    appointment: [
      {
        field: "updated_at",
        operator: FilterOperator.TODAY,
      },
    ],
  };
  const key = fetchType.value?.value ?? "all";
  const filters = typeFilters[key];
  fetchPersons(
    {
      offset: (page.value - 1) * rowsPerPage.value,
      limit: rowsPerPage.value,
      ...currentFilterPayload.value,
      filters: [...baseFilters, ...processedFilters, ...filters],
    },
    getCount,
  );
};

const handleDelete = async (data: PersonResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa khách hàng",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deletePerson({ id: data.id, full_name: data.full_name });
      loadPersons();
    },
  });
};

const handleOpenPersonFormModal = async (data?: PersonResponse) => {
  personId.value = data?.id ?? 0;
  await nextTick();
  openPersonFormModal();
};

// Export functionality
const isExporting = ref(false);

const handleExport = async () => {
  if (isExporting.value) return;
  isExporting.value = true;

  try {
    const res = await fetchPersons(currentFilterPayload.value, false, true);
    if (!res.data?.result?.file_url) throw new Error("No file URL returned");

    // Lấy ngày từ filter
    const filters = currentFilterPayload.value?.filters || [];
    const fromDate = new Date(
      filters
        .find((f: Filter) => f.operator === FilterOperator.GTE)
        ?.value.match(/time\((.*?)Z\)/)?.[1] || "",
    );
    const toDate = new Date(
      filters
        .find((f: Filter) => f.operator === FilterOperator.LT)
        ?.value.match(/time\((.*?)Z\)/)?.[1] || "",
    );

    // Trừ 1 ngày từ toDate vì nó là exclusive
    toDate.setDate(toDate.getDate() - 1);

    // Format ngày thành dd_MM
    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, "0")}_${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}`;
    };

    // Tạo string ngày cho tên file
    const dateStr =
      fromDate.getTime() === toDate.getTime()
        ? formatDate(fromDate)
        : `${formatDate(fromDate)}-${formatDate(toDate)}`;

    // Tạo tên file với format ngày
    const filename = `danh_sach_khach_hang${dateStr}.xlsx`;

    await handleDownload(res.data.result.file_url, filename);
  } finally {
    isExporting.value = false;
  }
};

onMounted(() => {
  loadPersons();
});
</script>

<template>
  <PersonAddModal
    ref="personFormModalRef"
    :person-id="personId"
    @person-updated="loadPersons()"
    @person-added="loadPersons()"
  />
  <div class="mt-5">
    <DataTable
      title="Danh sách khách hàng"
      :columns="columns"
      :data="persons"
      :loading="isLoading"
      :total-records="total"
      paginator
      @page="handlePageChange"
      @row-click="handleRowClick"
      v-model:filters="filters"
      selection-mode="single"
      clickable
      :show-actions="showActions"
      @on-delete="handleDelete"
      @on-edit="handleOpenPersonFormModal"
      size="small"
      :rows="rowsPerPage"
      :rows-per-page-options="rowsPerPageOptions"
    >
      <template #left-header>
        <Select
          v-model="fetchType"
          :options="fetchTypeOps"
          optionLabel="name"
          class="w-full"
          @change="loadPersons()"
        >
          <template #value="slotProps">
            <div class="flex items-center gap-2">
              <i :class="slotProps.value?.icon ?? 'pi pi-users text-blue-500'" />
              <div class="text-base font-medium">{{ slotProps.value?.name ?? "Tất cả" }}</div>
              <span class="text-base font-medium">({{ total }})</span>
            </div>
          </template>

          <template #option="slotProps">
            <div class="flex items-center gap-2">
              <i :class="slotProps.option.icon" />
              <div>{{ slotProps.option.name }}</div>
            </div>
          </template>
        </Select>
      </template>

      <template #right-header>
        <Button
          v-if="onlyAdmin()"
          icon="pi pi-file-excel"
          @click="handleExport"
          :loading="isExporting"
          :disabled="isExporting"
          v-tooltip="'Xuất excel'"
          class="h-9"
        />
        <PopSetting class="h-9" title="Tuỳ chỉnh" setting-key="person" />
        <Button
          icon="pi pi-user-plus"
          class="h-9 w-32 font-medium"
          @click="handleOpenPersonFormModal()"
        >
          Thêm mới
        </Button>
      </template>

      <template #full_name="{ data }">
        <PersonCard :person="data" show-gender show-code submit-type="new-tab" />
      </template>

      <template #sale.name="{ data }">
        <div class="flex items-center gap-2">
          <UserAvatar :name="data.sale?.name" v-if="data.sale" />
          <span class="text-sm font-medium">{{ data.sale?.name ?? "-" }}</span>
        </div>
      </template>

      <template #stage_name="{ data }">
        <Term collection-key="Tailwind300" size="sm" variant="soft" v-if="data.stage_name">
          {{ data.stage_name }}
        </Term>
        <span class="text-sm font-medium" v-else>-</span>
      </template>

      <template #date_of_birth="{ data }">
        <DateTimeInfo :date="data.date_of_birth" />
      </template>

      <template #job_id="{ data }">
        <TermInfo termType="nghe_nghiep" :termId="data.job_id" />
      </template>

      <template #source_id="{ data }">
        <TermInfo termType="nguon" :termId="data.source_id" />
      </template>

      <template #address_number="{ data }">
        <AddressInfo
          :addressNumber="data.address_number"
          :ward="getWardById(data.ward_id)?.name"
          :district="getDistrictById(data.district_id)?.name"
          :province="getProvinceById(data.province_id)?.name"
        />
      </template>

      <template #person_field.description="{ data }">
        <NoteInfo :notes="data.person_field.description" mode="text" />
      </template>

      <template #person_field.treatment_id="{ data }">
        <TermInfo termType="loai_dieu_tri" :termId="data.person_field.treatment_id ?? 0" />
      </template>

      <template #person_field.treatment_status_id="{ data }">
        <TermInfo
          termType="trang_thai_dieu_tri"
          :termId="data.person_field.treatment_status_id ?? 0"
        />
      </template>

      <template #created_at="{ data }">
        <DateTime :time="data.created_at" show-time />
      </template>

      <template #stage_name.filter="{ filterModel, filterCallback }">
        <PipelineStageSelect
          v-model="filterModel.value"
          :placeholder="'Chọn stage'"
          :pipeline-id="2"
          selectionMode="multiple"
          @update:modelValue="filterCallback()"
        />
      </template>

      <template #source_id.filter="{ filterModel, filterCallback }">
        <MultiSelect
          v-model="filterModel.value"
          :options="getBundleTerms('nguon')"
          optionValue="id"
          optionLabel="name"
          placeholder="Chọn nguồn KH"
          @change="filterCallback()"
          display="chip"
          :maxSelectedLabels="1"
          filter
          fluid
        />
      </template>

      <template #person_field.form_source="{ data }">
        <div
          v-if="data.person_field?.form_source || data.person_field?.form_position"
          class="flex flex-col gap-1 text-xs text-gray-500"
        >
          <span>{{ data.person_field?.form_source ?? "-" }}</span>
          <span>{{ data.person_field?.form_position ?? "-" }}</span>
        </div>
        <span v-else class="text-sm">-</span>
      </template>

      <template #person_field.source_channel="{ data }">
        <div
          v-if="data.person_field?.source_channel || data.person_field?.landing_page_url"
          class="flex flex-col gap-1 text-xs text-gray-500"
        >
          <span>{{ data.person_field?.source_channel ?? "-" }}</span>
          <a
            v-if="data.person_field?.landing_page_url"
            :href="data.person_field.landing_page_url"
            target="_blank"
            @click.stop
            class="text-blue-500 hover:text-blue-700 hover:underline"
          >
            {{ data.person_field.landing_page_url }}
          </a>
          <span v-else>-</span>
        </div>
        <span v-else class="text-sm">-</span>
      </template>
    </DataTable>
  </div>
</template>
