import { reactive, toRefs } from "vue";

import { adminLogs } from "@/api/bcare";
import { LogsRequest, LogsResponse } from "@/api/bcare-types";

export default function useLog() {
  const state = reactive<{
    logs: LogsResponse;
  }>({
    logs: {} as LogsResponse,
  });
  const getListLog = async (request: LogsRequest) => {
    try {
      const response = await adminLogs(request);
      if (response.code === 0) {
        state.logs = response.data || ({} as LogsResponse);
      }
      return true;
    } catch (error) {
      return false;
    }
  };
  return {
    ...toRefs(state),
    getListLog,
  };
}
