<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";

import { LogEnum } from "@/api/bcare-enum";
import Button from "@/base-components/Button";
import { FormCheck, FormInput, FormLabel, FormSelect } from "@/base-components/Form";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import useLog from "@/pages/log/config/use-log";
import { LOG_TYPE, LOG_TYPES, logPayload } from "@/pages/log/constants";
import { parseDateTimeLogs } from "@/utils/helper";

const { logs, getListLog } = useLog();
const logList = ref<any>();
const selectedFilterLog = ref<number | null>(LOG_TYPES[0].id);
const perPage = ref<number>(100);
const currentPage = ref<number>(1);
const truncateIds = ref<number[]>([]);

const totalPages = computed(() => {
  return Math.ceil(logList?.value?.total || 1 / perPage.value);
});
const logsPayload = reactive({ ...logPayload, page: currentPage.value, page_size: +perPage.value });
const loadLogs = async (typeLog: string) => {
  logsPayload.type = typeLog;
  await getListLog(logsPayload);

  if (logs.value?.logs) {
    if (typeLog !== LOG_TYPE.APP) {
      try {
        logList.value = JSON.parse(logs.value.logs);
      } catch (error) {
        console.error("Error parsing logs:", error);
        logList.value = [];
      }
    } else if (logs.value.logs === "null") {
      logList.value = [];
    } else {
      logList.value = logs.value.logs
        .split(",")
        .map((logString: string) => logString.replace(/\\n|\\"|\\|[\[\]]/g, ""));
    }
  } else {
    logList.value = [];
  }
};

const shouldTruncate = (id: number) => {
  return truncateIds.value.includes(id);
};

const toggleTruncate = (id: number) => {
  if (truncateIds.value.includes(id)) {
    truncateIds.value = truncateIds.value.filter((item) => item !== id);
  } else {
    truncateIds.value.push(id);
  }
};
const reversedLogList = computed(() => {
  return Array.isArray(logList.value) ? [...logList.value].reverse() : [];
});
watch(currentPage, async () => {
  logsPayload.page = currentPage.value;
  await loadLogs(logsPayload.type);
});
onMounted(() => {
  loadLogs(LOG_TYPE.APP);
});
</script>

<template>
  <div class="mt-8 grid grid-cols-12 gap-6">
    <div class="col-span-12 lg:col-span-3 2xl:col-span-2">
      <h2 class="intro-y mr-auto mt-2 text-lg font-medium">Logs</h2>
      <!-- BEGIN: Inbox Menu -->
      <div class="intro-y box mt-6 bg-primary p-5">
        <Button
          type="button"
          class="mt-1 w-full bg-white text-slate-600 dark:border-darkmode-300 dark:bg-darkmode-300 dark:text-slate-300"
        >
          <Lucide icon="Edit3" class="mr-2 h-4 w-4" />
          Compose
        </Button>
        <div class="mt-6 border-t border-white/10 pt-4 text-white dark:border-darkmode-400">
          <template v-for="type in LOG_TYPES" :key="type.id">
            <div
              :class="[
                'mt-2 flex cursor-pointer items-center truncate rounded-md px-3 py-2 first:mt-0 hover:bg-white/10 hover:dark:bg-darkmode-700',
                {
                  'bg-white/10 font-medium dark:bg-darkmode-700': selectedFilterLog === type.id,
                },
              ]"
              @click="
                () => {
                  selectedFilterLog = type.id;
                  currentPage = 1;
                  loadLogs(type.value);
                }
              "
            >
              <div
                :class="[
                  'mr-3 h-2 w-2 rounded-full',
                  {
                    'bg-blue-500': type.value === LOG_TYPE.APP,
                    'bg-success': type.value === LOG_TYPE.ACCESS,
                    'bg-slate-600': type.value === LOG_TYPE.STAT,
                    'bg-danger': type.value === LOG_TYPE.ERROR,
                    'bg-warning': type.value === LOG_TYPE.SLOW,
                    'bg-black': type.value === LOG_TYPE.SEVERE,
                  },
                ]"
              ></div>
              {{ type.name }}
            </div>
          </template>
        </div>
        <div class="mt-4 border-t border-white/10 pt-6 text-white dark:border-darkmode-400">
          <a
            href=""
            class="flex items-center rounded-md bg-white/10 px-3 py-2 font-medium dark:bg-darkmode-700"
          >
            <Lucide icon="Mail" class="mr-2 h-4 w-4" />
            Inbox
          </a>
          <a href="" class="mt-2 flex items-center rounded-md px-3 py-2">
            <Lucide icon="Star" class="mr-2 h-4 w-4" />
            Marked
          </a>
          <a href="" class="mt-2 flex items-center rounded-md px-3 py-2">
            <Lucide icon="Inbox" class="mr-2 h-4 w-4" />
            Draft
          </a>
          <a href="" class="mt-2 flex items-center rounded-md px-3 py-2">
            <Lucide icon="Send" class="mr-2 h-4 w-4" />
            Sent
          </a>
          <a href="" class="mt-2 flex items-center rounded-md px-3 py-2">
            <Lucide icon="Trash" class="mr-2 h-4 w-4" />
            Trash
          </a>
        </div>
      </div>
      <!-- END: Inbox Menu -->
    </div>
    <div class="col-span-12 lg:col-span-9 2xl:col-span-10">
      <!-- BEGIN: Inbox Filter -->
      <div class="intro-y flex flex-col-reverse items-center sm:flex-row">
        <div class="relative mr-auto mt-3 w-full sm:mt-0 sm:w-auto">
          <Lucide
            icon="Search"
            class="absolute inset-y-0 left-0 z-10 my-auto ml-3 h-4 w-4 text-slate-500"
          />
          <FormInput type="text" class="!box w-full px-10 sm:w-64" placeholder="Search mail" />
          <Menu class="absolute inset-y-0 right-0 mr-3 flex items-center">
            <Menu.Button as="a" role="button" class="block h-4 w-4" href="#">
              <Lucide icon="ChevronDown" class="h-4 w-4 cursor-pointer text-slate-500" />
            </Menu.Button>
            <Menu.Items placement="bottom-start" class="-ml-[228px] -mt-0.5 w-[478px] pt-2">
              <div class="grid grid-cols-12 gap-4 gap-y-3 p-3">
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-1" class="text-xs"> From</FormLabel>
                  <FormInput
                    id="input-filter-1"
                    type="text"
                    class="flex-1"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-2" class="text-xs"> To</FormLabel>
                  <FormInput
                    id="input-filter-2"
                    type="text"
                    class="flex-1"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-3" class="text-xs"> Subject</FormLabel>
                  <FormInput
                    id="input-filter-3"
                    type="text"
                    class="flex-1"
                    placeholder="Important Meeting"
                  />
                </div>
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-4" class="text-xs"> Has the Words</FormLabel>
                  <FormInput
                    id="input-filter-4"
                    type="text"
                    class="flex-1"
                    placeholder="Job, Work, Documentation"
                  />
                </div>
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-5" class="text-xs"> Doesn't Have</FormLabel>
                  <FormInput
                    id="input-filter-5"
                    type="text"
                    class="flex-1"
                    placeholder="Job, Work, Documentation"
                  />
                </div>
                <div class="col-span-6">
                  <FormLabel html-for="input-filter-6" class="text-xs"> Size</FormLabel>
                  <FormSelect id="input-filter-6" class="flex-1">
                    <option>10</option>
                    <option>25</option>
                    <option>35</option>
                    <option>50</option>
                  </FormSelect>
                </div>
                <div class="col-span-12 mt-3 flex items-center">
                  <Button variant="secondary" class="ml-auto w-32"> Create Filter</Button>
                  <Button variant="primary" class="ml-2 w-32"> Search</Button>
                </div>
              </div>
            </Menu.Items>
          </Menu>
        </div>
        <div class="flex w-full sm:w-auto">
          <Button variant="primary" class="mr-2 shadow-md"> Start a Video Call</Button>
          <Menu>
            <Menu.Button :as="Button" class="box px-2">
              <span class="flex h-5 w-5 items-center justify-center">
                <Lucide icon="Plus" class="h-4 w-4" />
              </span>
            </Menu.Button>
            <Menu.Items class="w-40">
              <Menu.Item>
                <Lucide icon="User" class="mr-2 h-4 w-4" />
                Contacts
              </Menu.Item>
              <Menu.Item>
                <Lucide icon="Settings" class="mr-2 h-4 w-4" />
                Settings
              </Menu.Item>
            </Menu.Items>
          </Menu>
        </div>
      </div>
      <!-- END: Inbox Filter -->
      <!-- BEGIN: Inbox Content -->
      <div class="intro-y box mt-5">
        <div
          class="flex flex-col-reverse border-b border-slate-200/60 p-5 text-slate-500 sm:flex-row"
        >
          <div
            class="-mx-5 mt-3 flex items-center border-t border-slate-200/60 px-5 pt-5 sm:mx-0 sm:mt-0 sm:border-0 sm:px-0 sm:pt-0"
          >
            <FormCheck.Input class="border-slate-400 checked:border-primary" type="checkbox" />
            <Menu class="ml-1">
              <Menu.Button class="block h-5 w-5" href="#">
                <Lucide icon="ChevronDown" class="h-5 w-5" />
              </Menu.Button>
              <Menu.Items placement="bottom-start" class="w-32 text-slate-800 dark:text-slate-300">
                <Menu.Item>All</Menu.Item>
                <Menu.Item>None</Menu.Item>
                <Menu.Item>Read</Menu.Item>
                <Menu.Item>Unread</Menu.Item>
                <Menu.Item>Starred</Menu.Item>
                <Menu.Item>Unstarred</Menu.Item>
              </Menu.Items>
            </Menu>
            <a
              class="ml-5 flex h-5 w-5 cursor-pointer items-center justify-center hover:text-primary"
              @click="() => getListLog(logsPayload)"
            >
              <Lucide icon="RefreshCw" class="h-4 w-4" />
            </a>
            <a href="#" class="ml-5 flex h-5 w-5 items-center justify-center">
              <Lucide icon="MoreHorizontal" class="h-4 w-4" />
            </a>
          </div>
          <div class="flex items-center sm:ml-auto">
            <div class="">{{ currentPage }} - {{ perPage }}</div>
            <a href="#" class="ml-5 flex h-5 w-5 items-center justify-center">
              <Lucide icon="ChevronLeft" class="h-4 w-4" />
            </a>
            <a
              class="ml-5 flex h-5 w-5 cursor-pointer items-center justify-center"
              @click="
                () => {
                  if (totalPages > currentPage) {
                    currentPage++;
                  }
                }
              "
            >
              <Lucide icon="ChevronRight" class="h-4 w-4" />
            </a>
            <a
              class="ml-5 flex h-5 w-5 cursor-pointer items-center justify-center"
              @click="
                () => {
                  currentPage--;
                }
              "
            >
              <Lucide icon="Settings" class="h-4 w-4" />
            </a>
          </div>
        </div>
        <div v-if="logsPayload.type === LOG_TYPE.APP" class="overflow-x-auto sm:overflow-x-visible">
          <div v-for="(log, key) in reversedLogList" :key="key" class="intro-y">
            <div
              :class="[
                'inline-block transform cursor-pointer  border-b border-slate-200/60 transition duration-200 ease-in-out dark:border-darkmode-400 sm:block',
                'hover:relative hover:z-20 hover:scale-[1.02] hover:rounded hover:border-0 hover:shadow-md',
                'bg-white text-slate-800 dark:bg-darkmode-600 dark:text-slate-300',
              ]"
              @click="toggleTruncate(key)"
            >
              <div class="flex px-5 py-3">
                <div class="w-26 mr-5 flex flex-none items-center">
                  <FormCheck.Input
                    class="flex-none border-slate-400 checked:border-primary"
                    type="checkbox"
                  />
                  <a
                    href="#"
                    class="ml-4 flex h-5 w-5 flex-none items-center justify-center text-slate-400"
                  >
                    <Lucide icon="Star" class="h-4 w-4" />
                  </a>
                  <a
                    href="#"
                    class="ml-2 flex h-5 w-5 flex-none items-center justify-center text-slate-400"
                  >
                    <Lucide icon="Bookmark" class="h-4 w-4" />
                  </a>
                </div>

                <div
                  :class="[
                    'w-64 sm:w-auto',
                    { truncate: !shouldTruncate(key) },
                    { '': shouldTruncate(key) },
                  ]"
                >
                  {{ log }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="logList && logList.length > 0 && logsPayload.type !== LOG_TYPE.APP"
          class="overflow-x-auto sm:overflow-x-visible"
        >
          <div v-for="(log, key) in reversedLogList" :key="key" class="intro-y">
            <div
              :class="[
                'inline-block transform cursor-pointer  border-b border-slate-200/60 transition duration-200 ease-in-out dark:border-darkmode-400 sm:block',
                'hover:relative hover:z-20 hover:scale-[1.02] hover:rounded hover:border-0 hover:shadow-md',
                'bg-white text-slate-800 dark:bg-darkmode-600 dark:text-slate-300',
              ]"
              @click="toggleTruncate(key)"
            >
              <div class="flex px-5 py-3">
                <div class="mr-5 flex w-72 flex-none items-center">
                  <FormCheck.Input
                    class="flex-none border-slate-400 checked:border-primary"
                    type="checkbox"
                  />
                  <a
                    href="#"
                    class="ml-4 flex h-5 w-5 flex-none items-center justify-center text-slate-400"
                  >
                    <Lucide icon="Star" class="h-4 w-4" />
                  </a>
                  <a
                    href="#"
                    class="ml-2 flex h-5 w-5 flex-none items-center justify-center text-slate-400"
                  >
                    <Lucide icon="Bookmark" class="h-4 w-4" />
                  </a>
                  <div
                    :class="[
                      'w-20 pl-3 font-bold sm:w-auto ',
                      {
                        'text-blue-500': log.level === LogEnum.APP,
                        'text-success': log.level === LogEnum.INFO,
                        'text-slate-600': log.level === LogEnum.STAT,
                        'text-danger': log.level === LogEnum.ERROR,
                        'text-warning': log.level === LogEnum.SLOW,
                        'text-black': log.level === LogEnum.SEVERE,
                      },
                    ]"
                  >
                    {{ log.level }}
                  </div>
                  <div class="ml-3 truncate whitespace-nowrap font-[600]">
                    {{ parseDateTimeLogs(log["@timestamp"]) }}
                  </div>
                </div>

                <div
                  :class="[
                    'w-64  sm:w-auto',
                    { truncate: !shouldTruncate(key) },
                    { '': shouldTruncate(key) },
                  ]"
                >
                  {{ log.content }}
                </div>
                <div :class="['ml-auto whitespace-nowrap pl-10']">
                  {{ log.duration }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="flex flex-col items-center p-5 text-center text-slate-500 sm:flex-row sm:text-left"
        >
          <div>4.41 GB (25%) of 17 GB used Manage</div>
          <div class="mt-2 sm:ml-auto sm:mt-0">Last account activity: 36 minutes ago</div>
        </div>
      </div>
      <!-- END: Inbox Content -->
    </div>
  </div>
</template>
