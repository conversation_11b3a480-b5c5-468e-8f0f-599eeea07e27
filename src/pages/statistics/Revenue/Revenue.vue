<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { useDateFormat } from "@vueuse/core";
import dayjs from "dayjs";
import { computed, onMounted, ref, watch } from "vue";

import { FilterOperator } from "@/api/bcare-enum";
import { Filter } from "@/api/bcare-types-v2";
import Money from "@/base-components/Money.vue";
import { ColumnDefinition, DataTable } from "@/components/DataTable";
import DateTimeInfo from "@/components/InfoText/DateTimeInfo.vue";
import PersonCard from "@/components/Person/PersonCard.vue";
import { UserAvatar } from "@/components/User";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { useRevenueQuery } from "@/hooks/useRevenueQuery";
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import { handleDownload } from "@/utils/helper";
import { extractDateRange, getDateStringForFilename } from "@/utils/time-helper";

// State
const showFilter = ref(false);
const currentPage = ref(1);

// Function to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Thêm computed để lấy range date từ dateFilter
const selectedDateRange = computed(() => {
  const filters = currentFilterPayload.value?.filters;
  return extractDateRange(filters, "created_at");
});

// Query setup
const { revenues, total, fetchRevenues, isLoading, aggregations, fetchAggregations } =
  useRevenueQuery();
const modalStore = useModalCustomerStore();

// Filter configurations
const filterConfigs = {
  full_name: {
    field: "full_name",
    operator: FilterOperator.LIKE,
  },
  doctor_name: {
    field: "doctor_name",
    operator: FilterOperator.LIKE,
  },
  product_names: {
    field: "product_names",
    operator: FilterOperator.LIKE,
  },
  group_names: {
    field: "group_names",
    operator: FilterOperator.LIKE,
  },
  created_at: {
    field: "created_at",
    isDateRange: true,
  },
};

// Column definitions
const columns = computed((): ColumnDefinition<any>[] => [
  {
    field: "created_at",
    header: "Ngày tạo",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày",
  },
  {
    field: "full_name",
    header: "Khách hàng",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm khách hàng",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    field: "doctor_name",
    header: "Bác sĩ",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm bác sĩ",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    field: "product_names",
    header: "Sản phẩm",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm sản phẩm",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { minWidth: "250px" },
  },
  {
    field: "group_names",
    header: "Nhóm",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm nhóm",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { minWidth: "200px" },
  },
  {
    field: "income",
    header: "Thu",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "expense",
    header: "Chi",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "total_amount",
    header: "Tổng tiền",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "cash",
    header: "Tiền mặt",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "bank",
    header: "Chuyển khoản",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "credit_card",
    header: "Thẻ tín dụng",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "mpos",
    header: "MPOS",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "momo",
    header: "Momo",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "product_amount",
    header: "Sản phẩm",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "general_service_amount",
    header: "Dịch vụ tổng quát",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "orthodontic_amount",
    header: "Chỉnh nha",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "implant_amount",
    header: "Implant",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "gxtn_amount",
    header: "Gxtn",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "veneer_amount",
    header: "Veneer",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "other_amount",
    header: "Khác",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
]);

// Setup filter query
const { filters, currentFilterPayload } = useFilterQuery(() => {
  // Reset pagination when filters change
  resetPagination();
  loadData();
}, filterConfigs);

// Add watcher for created_at filter
watch(
  () => filters.value.created_at?.value,
  (newValue) => {
    if (!newValue || newValue.length === 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filters.value.created_at = { value: [today, today] };
    }
  },
);

// Methods
const loadData = async (getCount: boolean = true) => {
  const pageToLoad = currentPage.value;
  const queryPayload = {
    offset: (pageToLoad - 1) * 10,
    limit: 10,
    ...currentFilterPayload.value,
  };
  await fetchAggregations(currentFilterPayload.value);
  await fetchRevenues(queryPayload, getCount);
};

const handlePageChange = (event: { first: number; rows: number }) => {
  currentPage.value = Math.floor(event.first / event.rows) + 1;
  loadData(false);
};

const handleOpenModal = (personId: number) => {
  modalStore.openModal(personId.toString());
};

// Add loading state
const isExporting = ref(false);

const handleExport = async (detailed: boolean = false) => {
  if (isExporting.value) return;
  isExporting.value = true;

  try {
    const res = await fetchRevenues(currentFilterPayload.value, false, true, detailed);
    if (!res.data?.result?.file_url) throw new Error("No file URL returned");

    const dateStr = getDateStringForFilename(currentFilterPayload.value?.filters, "created_at");
    const filename = `doanh_thu${detailed ? "_chi_tiet" : ""}_${dateStr}.xlsx`;
    await handleDownload(res.data.result.file_url, filename);
  } finally {
    isExporting.value = false;
  }
};

// Initial load
onMounted(async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  filters.value = {
    ...filters.value,
    created_at: { value: [today, today] },
  };
});
</script>

<template>
  <div class="mt-5 flex flex-wrap gap-5">
    <!-- Main Content -->
    <div class="min-w-0 flex-1">
      <DataTable
        title="Thống kê doanh thu"
        :columns="columns"
        :data="revenues"
        :loading="isLoading"
        :total-records="total"
        paginator
        :rows="10"
        v-model:filters="filters"
        @page="handlePageChange"
        size="small"
      >
        <!-- Thêm left-header template -->
        <template #left-header>
          <div class="flex items-center gap-2">
            <span class="text-base font-medium">Thống kê doanh thu ({{ total }})</span>
            <template v-if="selectedDateRange">
              <div class="flex items-center gap-1">
                <i class="pi pi-calendar text-yellow-500" />
                <span class="text-sm text-slate-600">
                  {{ selectedDateRange.isOneDay ? `Ngày ` : `Từ ngày ` }}
                  <span class="font-medium">{{ selectedDateRange.fromFormatted }}</span>
                  {{ !selectedDateRange.isOneDay ? ` đến ` : `` }}
                  <span v-if="!selectedDateRange.isOneDay" class="font-medium">
                    {{ selectedDateRange.toFormatted }}
                  </span>
                </span>
              </div>
            </template>
          </div>
        </template>

        <template #right-header>
          <SplitButton
            icon="pi pi-file-excel"
            @click="() => handleExport()"
            :loading="isExporting"
            :disabled="isExporting"
            :model="[
              {
                label: 'Doanh thu chi tiết',
                icon: 'pi pi-file-excel',
                command: () => handleExport(true),
              },
            ]"
            v-tooltip.focus="'Xuất file doanh thu'"
          />
        </template>

        <!-- Person code slot -->
        <template #full_name="{ data }">
          <PersonCard
            v-if="data.person"
            :person="data.person"
            size="small"
            @submitName="() => handleOpenModal(data.person.id)"
          />
          <template v-else>-</template>
        </template>

        <template #doctor_name="{ data }">
          <template v-if="data.doctor_name">
            <div class="flex items-center gap-1">
              <UserAvatar :user="data.doctor" size="small" />
              <span class="whitespace-nowrap text-xs font-medium">
                {{ data.doctor?.name }}
              </span>
            </div>
          </template>
          <template v-else>-</template>
        </template>

        <!-- Product names slot -->
        <template #product_names="{ data }">
          <template v-if="data.product_names">
            <span class="font-medium">{{ data.product_names }}</span>
          </template>
          <template v-else>-</template>
        </template>

        <!-- Group names slot -->
        <template #group_names="{ data }">
          <template v-if="data.group_names">
            <span class="font-medium">{{ data.group_names }}</span>
          </template>
          <template v-else>-</template>
        </template>

        <!-- Date slot -->
        <template #created_at="{ data }">
          <DateTimeInfo :date="data.created_at" format="DD/MM/YYYY HH:mm" size="small" />
        </template>

        <!-- Money slots -->
        <template #income="{ data }">
          <div class="text-right">
            <Money
              :amount="Math.abs(data.income || 0)"
              :variant="data.income >= 0 ? 'success' : 'danger'"
            />
          </div>
        </template>
        <template #expense="{ data }">
          <div class="text-right">
            <Money
              :amount="Math.abs(data.expense || 0)"
              :variant="data.expense >= 0 ? 'success' : 'danger'"
            />
          </div>
        </template>
        <template #total_amount="{ data }">
          <div class="text-right">
            <Money
              :amount="Math.abs(data.total_amount || 0)"
              :variant="data.total_amount >= 0 ? 'success' : 'danger'"
            />
          </div>
        </template>

        <template #cash="{ data }">
          <div class="text-right">
            <template v-if="data.cash">
              <Money
                :amount="Math.abs(data.cash)"
                :variant="data.cash >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <template #bank="{ data }">
          <div class="text-right">
            <template v-if="data.bank">
              <Money
                :amount="Math.abs(data.bank)"
                :variant="data.bank >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <template #credit_card="{ data }">
          <div class="text-right">
            <template v-if="data.credit_card">
              <Money
                :amount="Math.abs(data.credit_card)"
                :variant="data.credit_card >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <template #mpos="{ data }">
          <div class="text-right">
            <template v-if="data.mpos">
              <Money
                :amount="Math.abs(data.mpos)"
                :variant="data.mpos >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Product amount slot -->
        <template #product_amount="{ data }">
          <div class="text-right">
            <template v-if="data.product_amount">
              <Money
                :amount="Math.abs(data.product_amount)"
                :variant="data.product_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- General service amount slot -->
        <template #general_service_amount="{ data }">
          <div class="text-right">
            <template v-if="data.general_service_amount">
              <Money
                :amount="Math.abs(data.general_service_amount)"
                :variant="data.general_service_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Orthodontic amount slot -->
        <template #orthodontic_amount="{ data }">
          <div class="text-right">
            <template v-if="data.orthodontic_amount">
              <Money
                :amount="Math.abs(data.orthodontic_amount)"
                :variant="data.orthodontic_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Implant amount slot -->
        <template #implant_amount="{ data }">
          <div class="text-right">
            <template v-if="data.implant_amount">
              <Money
                :amount="Math.abs(data.implant_amount)"
                :variant="data.implant_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Gxtn amount slot -->
        <template #gxtn_amount="{ data }">
          <div class="text-right">
            <template v-if="data.gxtn_amount">
              <Money
                :amount="Math.abs(data.gxtn_amount)"
                :variant="data.gxtn_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Veneer amount slot -->
        <template #veneer_amount="{ data }">
          <div class="text-right">
            <template v-if="data.veneer_amount">
              <Money
                :amount="Math.abs(data.veneer_amount)"
                :variant="data.veneer_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Other amount slot -->
        <template #other_amount="{ data }">
          <div class="text-right">
            <template v-if="data.other_amount">
              <Money
                :amount="Math.abs(data.other_amount)"
                :variant="data.other_amount >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <!-- Add filter slots for money columns -->
        <template #income.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_income || 0" variant="success" class="font-bold" />
          </div>
        </template>
        <template #expense.filter>
          <div class="text-right">
            <Money
              :amount="Math.abs(aggregations?.total_expense || 0)"
              variant="danger"
              class="font-bold"
            />
          </div>
        </template>
        <template #total_amount.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_revenue || 0" variant="success" class="font-bold" />
          </div>
        </template>

        <template #cash.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_cash || 0" variant="success" class="font-bold" />
          </div>
        </template>

        <template #bank.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_bank || 0" variant="success" class="font-bold" />
          </div>
        </template>

        <template #credit_card.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_credit_card || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #mpos.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_mpos || 0" variant="success" class="font-bold" />
          </div>
        </template>

        <template #momo="{ data }">
          <div class="text-right">
            <template v-if="data.momo">
              <Money
                :amount="Math.abs(data.momo)"
                :variant="data.momo >= 0 ? 'success' : 'danger'"
              />
            </template>
            <template v-else>
              <Money :amount="0" variant="default" />
            </template>
          </div>
        </template>

        <template #momo.filter>
          <div class="text-right">
            <Money :amount="aggregations?.total_momo || 0" variant="success" class="font-bold" />
          </div>
        </template>

        <!-- Optional: Add more aggregation info if needed -->
        <!-- Add filter slots for aggregation -->
        <template #product_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_product_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #general_service_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_general_service_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>
        <template #orthodontic_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_orthodontic_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #implant_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_implant_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #gxtn_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_gxtn_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #veneer_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_veneer_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>

        <template #other_amount.filter>
          <div class="text-right">
            <Money
              :amount="aggregations?.total_other_amount || 0"
              variant="success"
              class="font-bold"
            />
          </div>
        </template>
      </DataTable>
    </div>
  </div>
</template>
