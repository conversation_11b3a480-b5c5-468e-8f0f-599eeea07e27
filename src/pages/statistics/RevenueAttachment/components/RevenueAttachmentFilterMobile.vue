<script setup lang="ts">
import { computed } from "vue";
import dayjs from "dayjs";
import { RevenueAttachmentFilterState } from "@/hooks/useRevenueAttachmentFilter";

const props = defineProps<{
  filterState: RevenueAttachmentFilterState;
  visible: boolean;
  searchQuery: string;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "apply-filters"): void;
  (e: "clear-filters"): void;
  (e: "update:searchQuery", value: string): void;
}>();

const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const searchQueryModel = computed({
  get: () => props.searchQuery,
  set: (value) => emit("update:searchQuery", value),
});

// Handle date changes with separate date pickers
const handleDateChange = (
  currentValue: Date | Date[] | (Date | null)[] | null | undefined,
  target: "startDate" | "endDate"
) => {
  let newDate: Date | null = null;

  if (currentValue instanceof Date) {
    newDate = currentValue;
  } else if (Array.isArray(currentValue) && currentValue[0] instanceof Date) {
    newDate = currentValue[0]; // Take the first date if it's an array
  }

  if (target === "startDate") {
    // If start date is after end date, adjust end date
    if (newDate && props.filterState.endDate && dayjs(newDate).isAfter(dayjs(props.filterState.endDate))) {
      props.filterState.endDate = dayjs(newDate).endOf("day").toDate();
    }
    props.filterState.startDate = newDate ? dayjs(newDate).startOf("day").toDate() : null;
  } else {
    // If end date is before start date, adjust start date
    if (newDate && props.filterState.startDate && dayjs(newDate).isBefore(dayjs(props.filterState.startDate))) {
      props.filterState.startDate = dayjs(newDate).startOf("day").toDate();
    }
    props.filterState.endDate = newDate ? dayjs(newDate).endOf("day").toDate() : null;
  }
};

const applyFilters = () => {
  emit("apply-filters");
};

const clearFilters = () => {
  emit("clear-filters");
};
</script>

<template>
  <Drawer
    v-model:visible="drawerVisible"
    position="bottom"
    header="Bộ lọc báo cáo điều trị"
    :pt="{ content: { class: 'p-4' } }"
    blockScroll
  >
    <!-- From date filter -->
    <div class="mb-4">
      <label for="fromDateDrawer" class="mb-1 block text-sm font-medium text-slate-700">Từ ngày</label>
      <DatePicker
        id="fromDateDrawer"
        :modelValue="props.filterState.startDate instanceof Date ? props.filterState.startDate : null"
        @update:modelValue="(value) => handleDateChange(value, 'startDate')"
        dateFormat="dd/mm/yy"
        placeholder="Từ ngày"
        class="w-full"
        :pt="{ input: { class: 'w-full border rounded' } }"
      />
    </div>

    <!-- To date filter -->
    <div class="mb-4">
      <label for="toDateDrawer" class="mb-1 block text-sm font-medium text-slate-700">Đến ngày</label>
      <DatePicker
        id="toDateDrawer"
        :modelValue="props.filterState.endDate instanceof Date ? props.filterState.endDate : null"
        @update:modelValue="(value) => handleDateChange(value, 'endDate')"
        dateFormat="dd/mm/yy"
        placeholder="Đến ngày"
        class="w-full"
        :pt="{ input: { class: 'w-full border rounded' } }"
        :minDate="props.filterState.startDate instanceof Date ? props.filterState.startDate : undefined"
      />
    </div>

    <!-- Customer name filter -->
    <div class="mb-4">
      <label class="mb-1 block text-sm font-medium text-slate-700">Tên khách hàng</label>
      <InputText v-model="searchQueryModel" placeholder="Nhập tên khách hàng" class="w-full" />
    </div>

    <!-- Product title filter -->
    <div class="mb-4">
      <label class="mb-1 block text-sm font-medium text-slate-700">Tên sản phẩm</label>
      <InputText
        v-model="filterState.serviceName"
        placeholder="Nhập tên sản phẩm"
        class="w-full"
      />
    </div>

    <template #footer>
      <Button
        label="Xóa bộ lọc"
        severity="secondary"
        outlined
        @click="clearFilters"
        class="flex-1"
      />
      <Button
        label="Áp dụng"
        @click="applyFilters"
        class="flex-1"
        severity="primary"
      />
    </template>
  </Drawer>
</template>
