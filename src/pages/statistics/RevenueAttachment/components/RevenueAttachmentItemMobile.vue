<script setup lang="ts">
import Money from "@/base-components/Money.vue";
import DateTime from "@/base-components/DateTime.vue";
import { PersonCard } from "@/components/Person";
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import { AttachmentOperationReportRecord, PersonResponse } from "@/api/bcare-types-v2";
import { CommonStatus } from "@/api/bcare-enum";
import AttachmentTreeLite from "@/components/Attachment/AttachmentTreeLite.vue";

defineProps<{
  item: AttachmentOperationReportRecord & { id: number };
}>();

const modalCustomerStore = useModalCustomerStore();

const handleOpenModal = (personId: number) => {
  modalCustomerStore.openModal(personId.toString());
};
</script>

<template>
  <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
    <!-- Customer info -->
    <div class="border-b border-gray-100 pb-2">
      <div class="flex items-center gap-2">
        <PersonCard
          v-if="item.person"
          :person="item.person as PersonResponse"
          @submitName="() => handleOpenModal(item.person.id)"
          size="small"
        />
        <template v-else>-</template>
      </div>
    </div>

    <!-- Header with date and ID -->
    <div class="flex items-center justify-between pt-2">
      <div class="flex w-full items-center justify-between gap-2">
        <DateTime :time="item.treatment_date" size="xs" />
        <span class="text-xs font-medium text-slate-400">
          <i class="pi pi-hashtag pr-1 text-xs" />{{ item.id }}
        </span>
      </div>
    </div>

    <!-- Attachment Tree -->
    <div class="py-2">
      <AttachmentTreeLite
        :productTitle="item.product_title || '-'"
        :operation="item.operation"
        :status="CommonStatus.ACTIVE"
      />
    </div>

    <!-- Price info -->
    <div class="border-t border-gray-100 pt-2">
      <div class="flex items-center justify-between">
        <span class="text-xs text-gray-600">Doanh thu:</span>
        <Money
          :amount="Math.abs(item.price || 0)"
          :variant="item.price >= 0 ? 'success' : 'danger'"
          size="small"
        />
      </div>
    </div>
  </div>
</template>
