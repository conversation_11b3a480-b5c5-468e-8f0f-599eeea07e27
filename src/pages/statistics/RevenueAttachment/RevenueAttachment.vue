<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, defineAsyncComponent, onMounted, ref, watch } from "vue";

import { FilterOperator } from "@/api/bcare-enum";
import Money from "@/base-components/Money.vue";
import { ColumnDefinition, DataTable } from "@/components/DataTable";
import DateTimeInfo from "@/components/InfoText/DateTimeInfo.vue";
import { PersonCard } from "@/components/Person";
import { UserAvatar } from "@/components/User";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { useRevenueAttachmentQuery } from "@/hooks/useRevenueAttachmentQuery";
import { useModalCustomerStore } from "@/stores/modal-customer-store";
import { handleDownload } from "@/utils/helper";
import { extractDateRange, getDateStringForFilename } from "@/utils/time-helper";
import { useResponsive } from "@/composables/useResponsive";

// Import mobile component
const RevenueAttachmentMobile = defineAsyncComponent(() => import("./RevenueAttachmentMobile.vue"));

// Check if on mobile
const { isMobile, isTablet } = useResponsive();

// State
const showFilter = ref(false);
const currentPage = ref(1);

// Query setup
const { operations, total, fetchOperations, isLoading } = useRevenueAttachmentQuery();
const modalStore = useModalCustomerStore();

// Filter configurations
const filterConfigs = {
  full_name: {
    field: "full_name",
    operator: FilterOperator.LIKE,
  },
  person_code: {
    field: "person_code",
    operator: FilterOperator.LIKE,
  },
  product_title: {
    field: "product_title",
    operator: FilterOperator.LIKE,
  },
  operation: {
    field: "operation",
    operator: FilterOperator.LIKE,
  },
  doctor_name: {
    field: "doctor_name",
    operator: FilterOperator.LIKE,
  },
  treatment_date: {
    field: "treatment_date",
    isDateRange: true,
  },
  buy_date: {
    field: "buy_date",
    isDateRange: true,
  },
};

// Column definitions
const columns = computed((): ColumnDefinition<any>[] => [
  {
    field: "treatment_date",
    header: "Ngày điều trị",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày",
  },
  {
    field: "full_name",
    header: "Bệnh nhân",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm bệnh nhân",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    field: "person_code",
    header: "Mã hồ sơ",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm mã hồ sơ",
  },
  {
    field: "product_title",
    header: "Sản phẩm/Dịch vụ",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm sản phẩm",
    style: { minWidth: "200px" },
  },
  {
    field: "operation",
    header: "Thao tác",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm thao tác",
  },
  {
    field: "price",
    header: "Doanh thu",
    sortable: true,
    showFilterMenu: showFilter.value,
  },
  {
    field: "doctor_name",
    header: "Bác sĩ",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm bác sĩ",
  },
  {
    field: "buy_date",
    header: "Ngày mua",
    sortable: true,
    showFilterMenu: showFilter.value,
    filterType: "dateRange",
    filterPlaceholder: "Chọn ngày",
  },
]);

// Function to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Setup filter query
const { filters, currentFilterPayload } = useFilterQuery(() => {
  // Reset pagination when filters change
  resetPagination();
  loadData();
}, filterConfigs);

// Methods
const loadData = async (getCount: boolean = true) => {
  const pageToLoad = currentPage.value;
  const queryPayload = {
    offset: (pageToLoad - 1) * 10,
    limit: 10,
    ...currentFilterPayload.value,
  };
  await fetchOperations(queryPayload, getCount);
};

const handlePageChange = (event: { first: number; rows: number }) => {
  currentPage.value = Math.floor(event.first / event.rows) + 1;
  loadData(false);
};

const handleOpenModal = (personId: number) => {
  modalStore.openModal(personId.toString());
};

// Export functionality
const isExporting = ref(false);

// Thêm computed để hiển thị date range
const selectedDateRange = computed(() => {
  const filters = currentFilterPayload.value?.filters;
  return extractDateRange(filters, "treatment_date");
});

const handleExport = async () => {
  if (isExporting.value) return;
  isExporting.value = true;

  try {
    const res = await fetchOperations(currentFilterPayload.value, false, true);
    if (!res.data?.result?.file_url) throw new Error("No file URL returned");

    const dateStr = getDateStringForFilename(currentFilterPayload.value?.filters, "treatment_date");
    const filename = `bao_cao_dieu_tri_${dateStr}.xlsx`;
    await handleDownload(res.data.result.file_url, filename);
  } finally {
    isExporting.value = false;
  }
};

// Initial load
onMounted(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  filters.value = {
    ...filters.value,
    treatment_date: { value: [today, today] },
  };
});

// Add watcher for created_at filter
watch(
  () => filters.value.created_at?.value,
  (newValue) => {
    if (!newValue || newValue.length === 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      filters.value.created_at = { value: [today, today] };
    }
  },
);
</script>

<template>
  <!-- Render mobile component on mobile devices -->
  <RevenueAttachmentMobile v-if="isMobile || isTablet" />

  <!-- Render desktop component on desktop devices -->
  <div v-else class="mt-5 flex flex-wrap gap-5">
    <div class="min-w-0 flex-1">
      <DataTable
        title="Báo cáo điều trị"
        :columns="columns"
        :data="operations"
        :loading="isLoading"
        :total-records="total"
        paginator
        :rows="10"
        v-model:filters="filters"
        @page="handlePageChange"
        size="small"
      >
        <template #right-header>
          <Button
            icon="pi pi-file-excel"
            @click="handleExport"
            :loading="isExporting"
            :disabled="isExporting"
            v-tooltip="'Xuất excel'"
          />
        </template>

        <template #left-header>
          <div class="flex items-center gap-2">
            <span class="text-base font-medium">Báo cáo điều trị ({{ total }})</span>
            <template v-if="selectedDateRange">
              <div class="flex items-center gap-1">
                <i class="pi pi-calendar text-yellow-500" />
                <span class="text-sm text-slate-600">
                  {{ selectedDateRange.isOneDay ? `Ngày ` : `Từ ngày ` }}
                  <span class="font-medium">{{ selectedDateRange.fromFormatted }}</span>
                  {{ !selectedDateRange.isOneDay ? ` đến ` : `` }}
                  <span v-if="!selectedDateRange.isOneDay" class="font-medium">
                    {{ selectedDateRange.toFormatted }}
                  </span>
                </span>
              </div>
            </template>
          </div>
        </template>

        <!-- Custom column templates -->
        <template #treatment_date="{ data }">
          <DateTimeInfo :date="data.treatment_date" format="DD/MM/YYYY" size="small" />
        </template>

        <!-- Person code slot -->
        <template #full_name="{ data }">
          <PersonCard
            v-if="data.person"
            :person="data.person"
            size="small"
            @submitName="() => handleOpenModal(data.person.id)"
          />
          <template v-else>-</template>
        </template>

        <template #price="{ data }">
          <div class="text-right">
            <Money
              :amount="Math.abs(data.price || 0)"
              :variant="data.price >= 0 ? 'success' : 'danger'"
            />
          </div>
        </template>

        <template #product_title="{ data }">
          <template v-if="data.product_title">
            <span class="font-medium">{{ data.product_title }}</span>
          </template>
          <template v-else>-</template>
        </template>

        <template #operation="{ data }">
          <template v-if="data.operation">
            <span class="font-medium">{{ data.operation }}</span>
          </template>
          <template v-else>-</template>
        </template>

        <template #buy_date="{ data }">
          <DateTimeInfo :date="data.buy_date" format="DD/MM/YYYY" size="small" />
        </template>

        <template #doctor_name="{ data }">
          <template v-if="data.doctor_name">
            <div class="flex items-center gap-1">
              <UserAvatar :name="data.doctor_name" size="small" />
              <span class="whitespace-nowrap text-xs font-medium">
                {{ data.doctor_name }}
              </span>
            </div>
          </template>
          <template v-else>-</template>
        </template>
      </DataTable>
    </div>
  </div>
</template>
