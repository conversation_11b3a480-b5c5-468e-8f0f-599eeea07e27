<script setup lang="ts">
import { onMounted, ref, watch, computed } from "vue";
import dayjs from "dayjs";
import { useRevenueAttachmentFilter } from "@/hooks/useRevenueAttachmentFilter";
import { attachmentQueryOperation } from "@/api/bcare-v2";
import { handleDownload } from "@/utils/helper";
import { getDateStringForFilename } from "@/utils/time-helper";
import Empty from "@/base-components/Empty";
import { AttachmentDynamicQuery } from "@/api/bcare-types-v2";
import { FilterOperator } from "@/api/bcare-enum";
import { useInfiniteScroll } from "@vueuse/core";
import RevenueAttachmentItemMobile from "./components/RevenueAttachmentItemMobile.vue";
import RevenueAttachmentFilterMobile from "./components/RevenueAttachmentFilterMobile.vue";

// Initialize with a larger page size
const PAGE_SIZE = 50;

// Store accumulated data locally to avoid duplicate API calls
const accumulatedData = ref<any[]>([]);

const {
  revenueData,
  total,
  isLoading,
  error,
  filterState,
  fetchFilteredData,
  resetFiltersAndRefetch,
  searchQuery,
  underlyingDynamicQuery,
  activeFiltersCount,
} = useRevenueAttachmentFilter();

// Create a computed property to display either the accumulated data or the original data
const displayData = computed(() => {
  return accumulatedData.value.length > 0 ? accumulatedData.value : revenueData.value;
});

// Computed property for displaying the total count
const displayCount = computed(() => {
  return total.value > 0 ? total.value : displayData.value?.length || 0;
});

const isFilterDrawerOpen = ref(false);
const isExporting = ref(false);
const isLoadingMore = ref(false);
const hasMoreData = ref(true);
const currentPage = ref(1);
const isInitialLoad = ref(true);
const isDataFetching = ref(false); // Track if we're currently fetching data

// Fetch data with proper pagination and accumulation
const fetchData = async (page = 1, append = false) => {
  // Prevent duplicate API calls
  if (
    isDataFetching.value ||
    (isLoading.value && !isLoadingMore.value) ||
    (!append && isLoadingMore.value)
  ) {
    return;
  }

  isDataFetching.value = true;

  if (append) {
    isLoadingMore.value = true;
  } else {
    // Reset state for new filter
    currentPage.value = 1;
    hasMoreData.value = true;
    // Clear accumulated data when fetching new data (not appending)
    accumulatedData.value = [];
  }

  try {
    // Only fetch if we need more data
    if (append && accumulatedData.value.length >= total.value) {
      hasMoreData.value = false;
      return;
    }

    await fetchFilteredData({
      page: page,
      pageSize: PAGE_SIZE,
    });

    // After data is fetched, update our accumulated data
    if (append && accumulatedData.value.length > 0) {
      // Append new data to existing accumulated data
      accumulatedData.value = [...accumulatedData.value, ...revenueData.value];
    } else {
      // Set accumulated data to the new data
      accumulatedData.value = [...revenueData.value];
    }

    // Check if we've reached the end of the data
    if (accumulatedData.value.length >= total.value) {
      hasMoreData.value = false;
    }
  } catch (error) {
    console.error("Error loading data:", error);
  } finally {
    isLoadingMore.value = false;
    isInitialLoad.value = false;
    isDataFetching.value = false;
  }
};

// Infinite scroll implementation - only loads more data
const loadMoreData = async () => {
  // Prevent loading more if already loading, no more data, or initial loading
  if (isLoadingMore.value || !hasMoreData.value || isLoading.value || isDataFetching.value) return;

  // Check if we already have enough data locally
  if (accumulatedData.value.length >= total.value) {
    hasMoreData.value = false;
    return;
  }

  currentPage.value += 1;
  await fetchData(currentPage.value, true);
};

// Reference for infinite scroll
const infiniteScrollEl = ref<HTMLElement | null>(null);

// Use VueUse's useInfiniteScroll with debounce to prevent multiple calls
useInfiniteScroll(
  infiniteScrollEl,
  () => {
    if (
      !isLoading.value &&
      hasMoreData.value &&
      !isLoadingMore.value &&
      !isInitialLoad.value &&
      !isDataFetching.value
    ) {
      loadMoreData();
    }
  },
  {
    distance: 10,
    throttle: 800, // Increase throttle to reduce API calls
  },
);

// Watch for filter changes to prevent duplicate API calls
watch(
  () => filterState.startDate,
  () => {
    if (!isInitialLoad.value) return; // Skip during initial load

    // Set end date if only start date is set
    if (filterState.startDate && !filterState.endDate) {
      filterState.endDate = dayjs(filterState.startDate).endOf("day").toDate();
    }
  },
);

watch(
  () => filterState.endDate,
  () => {
    if (!isInitialLoad.value) return; // Skip during initial load

    // Set start date if only end date is set
    if (filterState.endDate && !filterState.startDate) {
      filterState.startDate = dayjs(filterState.endDate).startOf("day").toDate();
    }
  },
);

onMounted(() => {
  // Reset state
  accumulatedData.value = [];
  currentPage.value = 1;
  hasMoreData.value = true;
  isInitialLoad.value = true;

  // Set initial date filter to today
  const todayStart = dayjs().startOf("day").toDate();
  const todayEnd = dayjs().endOf("day").toDate();

  // Set filter state without triggering watches
  if (!filterState.startDate) {
    filterState.startDate = todayStart;
  }

  if (!filterState.endDate) {
    filterState.endDate = todayEnd;
  }

  // Initial data fetch - wait a bit to ensure component is fully mounted
  setTimeout(() => {
    fetchData(1, false);
  }, 0);
});

const handleExport = async () => {
  if (isExporting.value) return;
  isExporting.value = true;
  try {
    // Create a copy of the query with the required table field
    const exportQuery: AttachmentDynamicQuery = {
      table: "attachment_operation_report_view", // Ensure table is specified
      filters: underlyingDynamicQuery.filters || [],
      limit: undefined,
      offset: undefined,
      export: true,
      sort: underlyingDynamicQuery.sort,
      group_by: underlyingDynamicQuery.group_by,
      aggregations: underlyingDynamicQuery.aggregations,
      joins: underlyingDynamicQuery.joins,
      selects: underlyingDynamicQuery.selects,
    };

    const res = await attachmentQueryOperation(exportQuery);
    if (!res.data?.result?.file_url) throw new Error("Không tìm thấy URL file để xuất");

    // Create date filters for filename
    const dateFiltersForFilename: any[] = [];
    if (filterState.startDate) {
      dateFiltersForFilename.push({
        field: "treatment_date",
        operator: FilterOperator.GTE,
        value: dayjs(filterState.startDate).format("YYYY-MM-DD"),
      });
    }
    if (filterState.endDate) {
      dateFiltersForFilename.push({
        field: "treatment_date",
        operator: FilterOperator.LTE,
        value: dayjs(filterState.endDate).format("YYYY-MM-DD"),
      });
    }

    const dateStr = getDateStringForFilename(dateFiltersForFilename, "treatment_date");
    const filename = `bao_cao_dieu_tri_${dateStr || "tat_ca_ngay"}.xlsx`;
    await handleDownload(res.data.result.file_url, filename);
  } catch (err) {
    console.error("Xuất file thất bại:", err);
  } finally {
    isExporting.value = false;
  }
};

const applyFilters = () => {
  // Prevent duplicate API calls
  if (isDataFetching.value) return;

  // Close filter drawer
  isFilterDrawerOpen.value = false;

  // Ensure we have date filters (default to today if not set)
  if (!filterState.startDate) {
    filterState.startDate = dayjs().startOf("day").toDate();
  }

  if (!filterState.endDate) {
    filterState.endDate = dayjs().endOf("day").toDate();
  }

  // Reset accumulated data and pagination state
  accumulatedData.value = [];
  currentPage.value = 1;
  hasMoreData.value = true;

  // Fetch data with new filters
  fetchData(1, false);
};

const clearFilters = async () => {
  // Prevent duplicate API calls
  if (isDataFetching.value) return;

  // Reset filters using the hook's function but skip the fetch
  // This will set the date to today as implemented in the hook
  await resetFiltersAndRefetch(undefined, true);

  // Close filter drawer
  isFilterDrawerOpen.value = false;

  // Reset accumulated data and pagination state
  accumulatedData.value = [];
  currentPage.value = 1;
  hasMoreData.value = true;

  // Manually fetch data to have more control
  fetchData(1, false);
};
</script>

<template>
  <div class="p-4">
    <!-- Header with title and filter button -->
    <div class="mb-4 flex items-center justify-between">
      <h3 class="border-l-4 border-blue-500 py-1 pl-2 text-sm font-semibold text-slate-800">
        Dịch vụ điều trị <span v-if="displayCount > 0">({{ displayCount }})</span>
      </h3>
      <div class="flex gap-2">
        <Button
          icon="pi pi-filter"
          @click="isFilterDrawerOpen = true"
          :badge="activeFiltersCount > 0 ? activeFiltersCount.toString() : undefined"
          rounded
          aria-label="Bộ lọc"
        />
        <Button
          icon="pi pi-file-excel"
          @click="handleExport"
          :loading="isExporting"
          :disabled="isExporting"
          rounded
          aria-label="Xuất excel"
        />
      </div>
    </div>

    <!-- Filter drawer component -->
    <RevenueAttachmentFilterMobile
      v-model:visible="isFilterDrawerOpen"
      :filter-state="filterState"
      :search-query="searchQuery"
      @update:search-query="(val) => (searchQuery = val)"
      @apply-filters="applyFilters"
      @clear-filters="clearFilters"
    />

    <!-- Loading state -->
    <div v-if="isLoading && !isLoadingMore" class="flex items-center justify-center py-6">
      <i class="pi pi-spin pi-spinner text-xl text-primary"></i>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="py-4 text-center text-sm text-red-500">
      <p>Đã có lỗi xảy ra khi tải dữ liệu.</p>
    </div>

    <!-- Empty state -->
    <div v-else-if="!displayData || displayData.length === 0" class="py-4">
      <Empty message="Không có dữ liệu" />
    </div>

    <!-- Data view with infinite scroll -->
    <div v-else>
      <div class="flex flex-col gap-3">
        <RevenueAttachmentItemMobile v-for="item in displayData" :key="item.id" :item="item" />

        <!-- Loading more indicator -->
        <div v-if="isLoadingMore" class="flex items-center justify-center py-3">
          <i class="pi pi-spin pi-spinner text-sm text-primary"></i>
        </div>

        <!-- Infinite scroll target for VueUse's useInfiniteScroll -->
        <div v-if="hasMoreData && !isLoadingMore" ref="infiniteScrollEl" class="h-4 w-full"></div>

        <!-- End of list message -->
        <div v-if="!hasMoreData" class="py-3 text-center text-xs text-slate-500">
          Đã hiển thị tất cả dữ liệu
        </div>
      </div>
    </div>
  </div>
</template>
