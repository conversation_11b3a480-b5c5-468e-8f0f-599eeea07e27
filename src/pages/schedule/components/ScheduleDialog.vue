<script setup lang="ts">
import dayjs from "dayjs";
import { computed, ref } from "vue";

import <PERSON>Field from "@/components/Form/FormField.vue";
import { UserAvatar } from "@/components/User";
import useSchedule from "@/hooks/useSchedule";
import useUser from "@/hooks/useUser";

const props = defineProps<{
  mode: "add" | "edit";
  scheduleId?: number;
  start: string;
  end: string;
  userId?: number;
}>();

const emit = defineEmits<{
  (e: "close", response: { success: boolean }): void;
}>();

// Hooks
const { getScheduleById, addSchedule, deleteSchedule } = useSchedule({ autoLoad: false });
const { getUserById } = useUser();

// State

// Computed
const schedule = computed(() => {
  if (props.mode === "edit" && props.scheduleId) {
    return getScheduleById(props.scheduleId);
  }
  return null;
});

const formattedStart = computed(() => {
  if (props.mode === "edit" && schedule.value) {
    return dayjs(schedule.value.start_time).format("DD/MM/YYYY HH:mm");
  }
  return dayjs(props.start).format("DD/MM/YYYY HH:mm");
});

const formattedEnd = computed(() => {
  if (props.mode === "edit" && schedule.value) {
    return dayjs(schedule.value.end_time).format("DD/MM/YYYY HH:mm");
  }
  return dayjs(props.end).format("DD/MM/YYYY HH:mm");
});

const userName = computed(() => {
  if (props.mode === "edit" && props.userId) {
    return getUserById(props.userId)?.name || "Không xác định";
  }
  return props.userId ? getUserById(props.userId)?.name || "Không xác định" : "Không xác định";
});

// Handlers
const handleSubmit = async () => {
  try {
    if (props.mode === "add" && props.userId) {
      await addSchedule({
        user_id: props.userId,
        start_time: props.start,
        end_time: props.end,
      });
    }
    emit("close", { success: true });
  } catch (error) {}
};

const handleClose = () => {
  emit("close", { success: false });
};

const handleDelete = async () => {
  if (!props.scheduleId) return;

  try {
    await deleteSchedule({ id: props.scheduleId });
    emit("close", { success: true });
  } catch (error) {
    // Handle error
  }
};
</script>

<template>
  <div class="flex flex-col gap-4">
    <!-- Form Content -->
    <div class="flex flex-col gap-4">
      <FormField label="Nhân viên" icon="pi pi-user" size="md">
        <div class="flex items-center gap-2">
          <UserAvatar :user-id="userId" size="small" />
          <span class="text-gray-700">{{ userName }}</span>
        </div>
      </FormField>

      <FormField label="Thời gian bắt đầu" icon="pi pi-calendar" size="md">
        <span class="text-gray-700">{{ formattedStart }}</span>
      </FormField>

      <FormField label="Thời gian kết thúc" icon="pi pi-calendar" size="md">
        <span class="text-gray-700">{{ formattedEnd }}</span>
      </FormField>
    </div>

    <!-- Footer -->
    <div class="flex justify-end gap-2">
      <!-- Right side - Cancel & Confirm buttons -->
      <div class="flex items-center gap-2">
        <Button label="Hủy" icon="pi pi-times" severity="secondary" outlined @click="handleClose" />
        <Button v-if="mode === 'add'" label="Xác nhận" icon="pi pi-save" @click="handleSubmit" />
        <Button
          v-if="mode === 'edit'"
          label="Xóa"
          icon="pi pi-trash"
          severity="danger"
          @click="handleDelete"
        />
      </div>
    </div>
  </div>
</template>
