<script setup lang="ts">
import { useWindowSize } from "@vueuse/core";
import { computed, nextTick, onMounted, ref, watch } from "vue";

import type { UserResponse } from "@/api/bcare-types-v2";
import type { UniversalSetting } from "@/api/extend-types";
import HighlightText from "@/base-components/HighlightText.vue";
import DynamicSetting from "@/components/Settings/DynamicSetting.vue";
import { UserAvatar } from "@/components/User";
import { useComponentSetting } from "@/hooks/useComponentSetting";
import useDepartment from "@/hooks/useDepartment";
import { fuzzySearch } from "@/utils/string";

interface ScheduleFilterProps {
  userId?: number;
  users: UserResponse[];
}

const props = withDefaults(defineProps<ScheduleFilterProps>(), {
  userId: undefined,
  users: () => [],
});

const emit = defineEmits<{
  (e: "update:userId", value: number | undefined): void;
  (e: "filter-change"): void;
}>();

// Settings configuration
const settingName = "schedule_filter_settings";
const settingsSchema: UniversalSetting[] = [
  {
    type: "multi-number",
    label: "Phòng ban",
    field_name: "department_ids",
    value: null,
  },
];

// Get departments and settings
const { departments } = useDepartment({ autoLoad: true });
const { currentSettingValue, refreshCurrentSetting } = useComponentSetting(settingName);

// Search query
const searchQuery = ref("");

// Filter users based on department settings and search query
const filteredUsers = computed(() => {
  const selectedDepts = currentSettingValue.value?.department_ids || [];
  const query = searchQuery.value.trim();

  return props.users.filter((user) => {
    // First check department filter
    if (selectedDepts.length && !selectedDepts.includes(user.department_id)) {
      return false;
    }

    // Then apply search filter if query exists
    if (query) {
      return fuzzySearch(user.name, query);
    }

    return true;
  });
});

// Group filtered users by department
const groupedUserOptions = computed(() => {
  return departments.value
    .sort((a, b) => a.name.localeCompare(b.name))
    .map((dept) => ({
      label: dept.name,
      code: dept.id.toString(),
      items: filteredUsers.value
        .filter((user) => user.department_id === dept.id)
        .map((user) => ({
          label: user.name,
          value: user.id,
          user,
        })),
    }))
    .filter((group) => group.items.length > 0);
});

// Handle user selection with v-model
const selectedUser = computed({
  get: () => props.userId,
  set: (value: number | undefined) => {
    emit("update:userId", value);
    emit("filter-change");
  },
});

const { height: windowHeight } = useWindowSize();

onMounted(async () => {
  // Wait for settings and users to be loaded
  await nextTick();

  // Only proceed if we don't have a selected user yet
  if (!props.userId) {
    // Wait for departments and groupedUserOptions to be computed
    await nextTick();

    // Get first group and its first user
    const firstGroup = groupedUserOptions.value[0];
    if (firstGroup?.items?.length > 0) {
      const firstUser = firstGroup.items[0];
      selectedUser.value = firstUser.value;
      emit("update:userId", firstUser.value);
      emit("filter-change");
    }
  }
});
</script>

<template>
  <div class="h-full rounded-lg bg-white px-2 py-2.5 shadow-sm">
    <div class="flex h-full flex-col">
      <div class="sticky top-0 z-10 bg-white">
        <InputText v-model="searchQuery" placeholder="Tìm kiếm theo tên..." class="mb-2 w-full" />
        <DynamicSetting
          :settingName="settingName"
          :settingsSchema="settingsSchema"
          title="Cài đặt phòng ban"
          @popover-close="refreshCurrentSetting"
        />
      </div>
      <div class="flex-1 overflow-auto" :style="{ maxHeight: `${windowHeight - 300}px` }">
        <!-- Empty State -->
        <div
          v-if="groupedUserOptions.length === 0"
          class="flex min-w-[250px] flex-col items-center justify-center py-8 text-center"
        >
          <i class="pi pi-search mb-2 text-2xl text-gray-400" />
          <p class="text-sm text-gray-500">
            {{
              searchQuery ? `Không tìm thấy kết quả cho "${searchQuery}"` : "Không có người dùng"
            }}
          </p>
        </div>

        <!-- User List -->
        <template v-else>
          <div v-for="group in groupedUserOptions" :key="group.code" class="mb-2">
            <!-- Department header -->
            <div
              class="sticky top-0 z-10 flex items-center gap-2 rounded bg-gray-50 p-1.5 font-medium text-gray-700"
            >
              <i class="pi pi-folder-open" />
              <span class="truncate">{{ group.label }}</span>
            </div>

            <!-- Users list -->
            <div class="mt-1 space-y-1 p-0.5">
              <div
                v-for="user in group.items"
                :key="user.value"
                :class="{
                  'bg-soft text-primary ring-1 ring-highlight': selectedUser === user.value,
                }"
                class="group flex cursor-pointer items-center gap-3 rounded p-1.5 transition duration-100 ease-in-out hover:bg-slate-100 dark:hover:bg-darkmode-400"
                @click="selectedUser = user.value"
              >
                <UserAvatar :user="user.user" size="small" />
                <div class="min-w-0 flex-1">
                  <HighlightText :highlight="searchQuery" :text="user.label" class="truncate" />
                </div>
                <div class="text-gray-600 dark:text-gray-400">
                  <div
                    :class="{
                      'bg-primary': selectedUser === user.value,
                      invisible: selectedUser !== user.value,
                    }"
                    class="flex h-3 w-3 items-center justify-center rounded-full border-4 border-primary bg-white"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
