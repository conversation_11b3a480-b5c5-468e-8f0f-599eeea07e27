<script setup lang="ts">
import type { EventClickArg, EventDropArg } from "@fullcalendar/core";
import dayGridPlugin from "@fullcalendar/daygrid";
import type { EventResizeDoneArg } from "@fullcalendar/interaction";
import interactionPlugin from "@fullcalendar/interaction";
import listPlugin from "@fullcalendar/list";
import timeGridPlugin from "@fullcalendar/timegrid";
import FullCalendar from "@fullcalendar/vue3";
import dayjs from "dayjs";
import { useConfirm } from "primevue/useconfirm";
import { computed, ref } from "vue";

import useSchedule from "@/hooks/useSchedule";
import useUser from "@/hooks/useUser";

import ScheduleDialog from "./components/ScheduleDialog.vue";
import ScheduleFilter from "./components/ScheduleFilter.vue";

// Initialize hooks
const { calendarEvents, getInitialView, updateSchedule, fetchWorkSchedules, isLoading } =
  useSchedule({ autoLoad: false });
const { users } = useUser({ autoLoad: true });
const confirm = useConfirm();

// State
const calendarRef = ref();
const selectedUserId = ref<number>();
const dialogVisible = ref(false);
const dialogData = ref<{
  mode: "add" | "edit";
  start: string;
  end: string;
  scheduleId: number | null;
}>({ mode: "add", start: "", end: "", scheduleId: null });

// Calendar options
const calendarOptions = computed(() => ({
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin],
  initialView: getInitialView.value,
  firstDay: 1,
  headerToolbar: {
    left: "prev,next today",
    center: "title",
    right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
  },
  buttonText: {
    today: "Hôm nay",
    month: "Tháng",
    week: "Tuần",
    day: "Ngày",
    list: "Danh sách",
  },
  views: {
    dayGridMonth: {
      type: "dayGridMonth",
      buttonText: "Tháng",
    },
    timeGridWeek: {
      type: "timeGridWeek",
      buttonText: "Tuần",
    },
    timeGridDay: {
      type: "timeGridDay",
      buttonText: "Ngày",
    },
    listWeek: {
      type: "listWeek",
      buttonText: "Danh sách",
    },
  },
  events: isLoading.value ? [] : calendarEvents.value,
  editable: !!selectedUserId.value,
  selectable: !!selectedUserId.value,
  selectMirror: true,
  dayMaxEvents: true,
  weekends: true,
  select: handleDateSelect,
  eventClick: handleEventClick,
  eventDrop: handleEventDrop,
  locale: "vi",
  slotMinTime: "07:00:00",
  slotMaxTime: "21:00:00",
  allDaySlot: false,
  slotDuration: "00:30:00",
  expandRows: true,
  stickyHeaderDates: true,
  nowIndicator: true,
  eventTimeFormat: {
    hour: "2-digit" as const,
    minute: "2-digit" as const,
    hour12: false,
  },
  slotLabelFormat: {
    hour: "2-digit" as const,
    minute: "2-digit" as const,
    hour12: false,
  },
  selectConstraint: {
    start: new Date(),
  },
  eventConstraint: {
    start: new Date(),
  },
  height: "auto",
  width: "auto",
  eventResize: handleEventResize,
  eventDurationEditable: true,
  slotEventOverlap: false,
  snapDuration: "00:15:00",
}));

// Handlers
const handleDateSelect = (selectInfo: { start: Date; end: Date }) => {
  if (!selectedUserId.value) {
    // Có thể thêm thông báo yêu cầu chọn nhân viên trước
    return;
  }

  dialogData.value = {
    mode: "add",
    start: selectInfo.start.toISOString(),
    end: selectInfo.end.toISOString(),
    scheduleId: null,
  };
  dialogVisible.value = true;
};

const handleEventClick = (clickInfo: EventClickArg) => {
  dialogData.value = {
    mode: "edit",
    start: "",
    end: "",
    scheduleId: Number(clickInfo.event.id),
  };
  dialogVisible.value = true;
};

const handleEventDrop = (dropInfo: EventDropArg) => {
  const scheduleId = Number(dropInfo.event.id);

  confirm.require({
    message: "Bạn có chắc chắn muốn thay đổi thời gian này?",
    header: "Xác nhận thay đổi",
    icon: "pi pi-exclamation-triangle",
    accept: async () => {
      try {
        await updateSchedule({
          id: scheduleId,
          start_time: dropInfo.event.startStr,
          end_time: dropInfo.event.endStr,
          modified: ["start_time", "end_time"],
        });
      } catch (error) {
        dropInfo.revert();
      }
    },
    reject: () => {
      dropInfo.revert();
    },
    onHide: () => {
      dropInfo.revert();
    },
  });
};

const handleEventResize = (resizeInfo: EventResizeDoneArg) => {
  const scheduleId = Number(resizeInfo.event.id);

  confirm.require({
    message: "Bạn có chắc chắn muốn thay đổi thời gian này?",
    header: "Xác nhận thay đổi",
    icon: "pi pi-exclamation-triangle",
    accept: async () => {
      try {
        await updateSchedule({
          id: scheduleId,
          start_time: resizeInfo.event.startStr,
          end_time: resizeInfo.event.endStr,
          modified: ["start_time", "end_time"],
        });
      } catch (error) {
        resizeInfo.revert();
      }
    },
    reject: () => {
      resizeInfo.revert();
    },
    onHide: () => {
      resizeInfo.revert();
    },
  });
};

const handleFilterChange = async () => {
  const today = dayjs();
  await fetchWorkSchedules({
    from: today.startOf("month").toISOString(),
    to: today.add(3, "month").endOf("month").toISOString(),
    user_id: selectedUserId.value,
  });
};

const handleDialogClose = (response: { success: boolean }) => {
  dialogVisible.value = false;
};
</script>

<template>
  <div class="mt-5 flex gap-2 overflow-hidden">
    <!-- Sidebar for user filter -->
    <ScheduleFilter
      class="w-3/10 p-1"
      v-model:userId="selectedUserId"
      :users="users"
      @filter-change="handleFilterChange"
    />
    <!-- Calendar -->
    <div class="w-7/10 relative overflow-x-auto rounded-lg bg-white p-4 shadow-sm">
      <div
        v-if="isLoading"
        class="absolute inset-0 z-10 flex items-center justify-center bg-white/80"
      >
        <ProgressSpinner />
      </div>
      <FullCalendar ref="calendarRef" :options="calendarOptions" class="calendar-container" />
    </div>
  </div>

  <!-- Dialog -->
  <Dialog
    v-model:visible="dialogVisible"
    header="Lịch làm việc"
    :modal="true"
    :closable="true"
    class="w-full max-w-lg"
  >
    <ScheduleDialog
      :mode="dialogData.mode"
      :start="dialogData.start"
      :end="dialogData.end"
      :scheduleId="dialogData.scheduleId ?? undefined"
      :userId="selectedUserId"
      @close="handleDialogClose"
    />
  </Dialog>

  <!-- Confirm -->
  <ConfirmDialog>
    <template #message="slotProps">
      <div class="flex items-center gap-2">
        <i :class="slotProps.message.icon" class="text-yellow-500" />
        <span>{{ slotProps.message.message }}</span>
      </div>
    </template>
  </ConfirmDialog>
</template>

<style scoped>
/* Calendar Container Base Styles */
:deep(.calendar-container) {
  @apply h-full min-w-[768px] font-sans;

  /* Toolbar Styles */
  @apply [&_.fc-toolbar]:mb-4 
         [&_.fc-toolbar]:flex-wrap 
         [&_.fc-toolbar]:gap-4;

  /* Button Styles */
  @apply [&_.fc-button]:border 
         [&_.fc-button]:border-gray-300 
         [&_.fc-button]:bg-transparent 
         [&_.fc-button]:text-gray-700 
         [&_.fc-button]:transition-colors 
         [&_.fc-button]:duration-200;

  /* Button Hover States */
  @apply [&_.fc-button:hover]:border-primary 
         [&_.fc-button:hover]:bg-primary 
         [&_.fc-button:hover]:text-white;

  /* Button Active States */
  @apply [&_.fc-button-active]:!border-primary 
         [&_.fc-button-active]:!bg-primary 
         [&_.fc-button-active]:!text-white;

  /* Active/Selected State - đảm bảo luôn có bg primary khi được chọn */
  @apply [&_.fc-button-active]:!border-primary 
         [&_.fc-button-active]:!bg-primary 
         [&_.fc-button-active]:!text-white;

  /* Đảm bảo active state không bị override bởi các styles khác */
  @apply [&_.fc-button-active:hover]:!border-primary 
         [&_.fc-button-active:hover]:!bg-primary 
         [&_.fc-button-active:hover]:!text-white;

  /* Button Focus States */
  @apply [&_.fc-button-primary:focus]:shadow-none 
         [&_.fc-button:focus]:shadow-none;

  /* Disabled Button States */
  @apply [&_.fc-button:disabled]:cursor-not-allowed 
         [&_.fc-button:disabled]:opacity-50;

  /* Event Styles */
  @apply [&_.fc-event]:cursor-pointer 
         [&_.fc-event]:border-0 
         [&_.fc-event]:shadow-sm 
         [&_.fc-event]:transition-opacity 
         [&_.fc-event]:duration-200;

  @apply [&_.fc-event:hover]:opacity-80;

  /* Event Content Styles */
  @apply [&_.fc-event-main]:p-0;
  @apply [&_.fc-event-title]:font-medium;
  @apply [&_.fc-event-time]:font-normal 
         [&_.fc-event-time]:opacity-90;

  /* Toolbar Button Styles */
  @apply [&_.fc-toolbar-button]:border
         [&_.fc-toolbar-button]:border-gray-300
         [&_.fc-toolbar-button]:bg-transparent
         [&_.fc-toolbar-button]:text-gray-900
         [&_.fc-toolbar-button]:transition-all
         [&_.fc-toolbar-button]:duration-200;

  /* Toolbar Button Hover States */
  @apply [&_.fc-toolbar-button:hover]:border-primary
         [&_.fc-toolbar-button:hover]:bg-primary
         [&_.fc-toolbar-button:hover]:text-white;

  /* Toolbar Button Active/Selected States */
  @apply [&_.fc-toolbar-button.fc-button-active]:border-primary
         [&_.fc-toolbar-button.fc-button-active]:bg-primary
         [&_.fc-toolbar-button.fc-button-active]:text-white;
}

/* Responsive Styles */
@media (max-width: 640px) {
  .calendar-container {
    @apply [&_.fc-toolbar]:flex-col 
           [&_.fc-toolbar]:items-stretch;

    @apply [&_.fc-toolbar-chunk]:flex 
           [&_.fc-toolbar-chunk]:justify-center;
  }
}
</style>
