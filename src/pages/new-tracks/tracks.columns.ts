import { FilterOperator } from "@/api/bcare-enum";
import type { NewTrackReportResponse, User } from "@/api/bcare-types-v2";
import type { ColumnDefinition } from "@/components/DataTable";
import type { FilterConfig } from "@/hooks/useFilterQuery"; // Import FilterConfig

// Column Definitions for Tracks DataTable
export const trackColumns: ColumnDefinition<NewTrackReportResponse>[] = [
  {
    field: "begin",
    header: "Ngày tạo",
    sortable: true,
    showFilterMenu: false,
    filterType: "dateRange",
    filterPlaceholder: "<PERSON><PERSON>n ngày",
    style: { width: "14%", minWidth: "140px" },
  },
  {
    field: "person.full_name",
    header: "Khách hàng",
    sortable: false,
    filterType: "text",
    showFilterMenu: false,
    filterPlaceholder: "<PERSON><PERSON><PERSON> ki<PERSON>m kh<PERSON>ch hàng",
    filterMatchMode: "contains",
    style: { width: "15%", minWidth: "150px" },
  },
  {
    field: "deal_id",
    header: "Deal",
    sortable: false,
    showFilterMenu: false,
    style: { width: "12%", minWidth: "120px" },
  },
  {
    field: "deal_stage_id",
    header: "Stage",
    sortable: false,
    filterType: "custom",
    showFilterMenu: false,
    filterField: "deal_stage_id",
    style: { width: "16%", minWidth: "160px" },
    filterMatchMode: "contains",
  },
  {
    field: "deal.tags",
    header: "Thẻ",
    sortable: false,
    filterType: "custom",
    showFilterMenu: false,
    filterField: "deal.tags",
    style: { width: "18%", minWidth: "180px" },
  },
  {
    field: "user_id",
    header: "Người phụ trách",
    sortable: false,
    filterType: "custom",
    showFilterMenu: false,
    style: { width: "25%", minWidth: "250px" },
  },
];

// Filter Configurations for Tracks DataTable
export const trackFilterConfigs: Record<string, FilterConfig> = {
  begin: {
    field: "begin",
    isDateRange: true,
  },
  "person.full_name": {
    field: "full_name",
    valueTransform: (v: string | undefined) => {
      if (!v) return null;
      return {
        logic: "OR",
        conditions: [
          { field: "full_name", operator: FilterOperator.LIKE, value: v },
          { field: "person_phone", operator: FilterOperator.LIKE, value: v },
          { field: "person_code", operator: FilterOperator.LIKE, value: v },
        ],
      };
    },
  },
  deal_stage_id: {
    field: "deal_stage_id",
    valueTransform: (v: number[] | undefined) => {
      if (!Array.isArray(v) || v.length === 0) return null;
      const stageIds = v.join(",");
      const valueString = `(${stageIds})`;
      return {
        logic: "OR",
        conditions: [
          { field: "deal_stage_id", operator: FilterOperator.IN, value: valueString },
          { field: "deal_stage_parent_id", operator: FilterOperator.IN, value: valueString },
        ],
      };
    },
  },
  "deal.tags": {
    field: "deal_tag_ids",
    operator: FilterOperator.JSON_CONTAINS_ANY,
    valueTransform: (v: number[] | undefined | null) => {
      if (!Array.isArray(v) || v.length === 0) {
        return null;
      }
      return JSON.stringify(v);
    },
  },
  user_id: {
    field: "deal_assignment_user_ids",
    operator: FilterOperator.JSON_CONTAINS_ANY,
    valueTransform: (v: User[] | undefined | null) => {
      if (!Array.isArray(v) || v.length === 0) {
        return null;
      }
      const userIds = v.map((user) => user.id);
      const userIdsString = `(${userIds.join(",")})`;
      //   return JSON.stringify(userIds);
      return {
        logic: "OR",
        conditions: [
          { field: "sale_user_id", operator: FilterOperator.IN, value: userIdsString },
          {
            field: "deal_assignment_user_ids",
            operator: FilterOperator.JSON_CONTAINS_ANY,
            value: `[${userIds}]`,
          },
        ],
      };
    },
  },
};
