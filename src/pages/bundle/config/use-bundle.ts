import { reactive, toRefs } from "vue";

import { bundleGet, bundleList } from "@/api/bcare";
import { BundleGetRequest, BundleListRequest, BundleResponse } from "@/api/bcare-types";

export default function useBundle() {
  const state = reactive({
    bundleItem: {} as BundleResponse,
    bundleList: [] as any,
    infoPage: {} as any,
  });

  const fetchBundleList = async (request: BundleListRequest) => {
    try {
      const response = await bundleList(request);
      if (response.code === 0) {
        state.bundleList = response.data?.bundles;
        state.infoPage.total_item = response.data?.total;
        state.infoPage.total_page = response.data?.total_page;
      }
      return response.data?.bundles;
    } catch (error) {
      return [];
    }
  };
  const fetchBundle = async (request: BundleGetRequest) => {
    try {
      const response = (await bundleGet(request)) as any;
      if (response.code === 0) {
        state.bundleItem = response.data;
      }
    } catch (error) {}
  };
  return {
    ...toRefs(state),
    fetchBundleList,
    fetchBundle,
  };
}
