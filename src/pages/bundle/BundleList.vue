<script setup lang="ts">
import { useVuelidate } from "@vuelidate/core";
import { helpers, required } from "@vuelidate/validators";
import _ from "lodash";
import { computed, onMounted, reactive, ref, toRefs, watch } from "vue";
import draggable from "vuedraggable";

import { BundleListRequest } from "@/api/bcare-types";
import Button from "@/base-components/Button";
import { FormInput, FormLabel } from "@/base-components/Form";
import { Slideover } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Table from "@/base-components/Table";
import useBundle from "@/pages/bundle/config/use-bundle";

const { bundleList, bundleItem, fetchBundleList, fetchBundle } = useBundle();

const title = ref("Thêm nhóm phân loại");
const bundleSlideover = ref(false);
const drag = ref(false);
const setBundleSlideover = (value: boolean) => {
  bundleSlideover.value = value;
};
const perPage = ref<number>(10);
// const currentPage = ref(1);
// const totalPages = computed(() => {
//   return Math.ceil(infoPage.value.total_item / perPage.value);
// });
const formData = reactive({
  id: 0,
  machine_name: "",
  name: "",
  type: "",
  description: "",
});
const newData = {
  id: 0,
  machine_name: "",
  name: "",
  type: "",
  description: "",
};
const rules = {
  machine_name: {},
  name: {
    required: helpers.withMessage("Tên không được để trống", required),
  },
  type: {},
  description: {},
};
const filterParams = reactive<BundleListRequest>({
  page_size: 10,
  page: 1,
  filter: {
    name: "",
    type: "",
    status: 0,
  },
  order_by: "",
});
const dragOptions = computed(() => {
  console.log("drag option");
  return {
    animation: 200,
    group: "pipeline",
    disabled: false,
    ghostClass: "ghost",
  };
});
const log = function () {
  console.log(bundleList);
};
const loadList = async () => {
  filterParams.page = 1;
  filterParams.page_size = +perPage.value;
  await fetchBundleList(filterParams);
};
onMounted(async () => {
  await loadList();
});
// const handlePageChange = async (page: number) => {
//   currentPage.value = page;
//   filterParams.page = page;
//   await fetchBundleList(filterParams);
// };
const handleSearchBundle = (val: string) => {
  filterParams.filter.name = val;
  filterParams.page = 1;
  debouncedSearch();
};
const handlePerPageChange = async (newPerPage: number) => {
  filterParams.page_size = +newPerPage;
  await fetchBundleList(filterParams);
};
const debouncedSearch = _.debounce(async () => {
  await fetchBundleList(filterParams);
}, 500);
const handleEditBundle = async (bundleId: number) => {
  if (bundleId) {
    title.value = "Chỉnh sửa nhóm phân loại";
    setBundleSlideover(true);
    const params = { id: bundleId };
    await fetchBundle(params);
    Object.assign(formData, bundleItem.value);
  }
};
const validate = useVuelidate(rules, toRefs(formData));
const onSubmitBundle = async () => {
  validate.value.$touch();
  if (validate.value.$invalid) {
    return false;
  }
};
const handleAddBundle = async () => {
  setBundleSlideover(true);
  Object.assign(formData, newData);
};
watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});
</script>

<template>
  <h2 class="intro-y mt-10 text-lg font-medium">Nhóm phân loại</h2>
  <div class="mt-5 grid grid-cols-12 gap-6">
    <div class="intro-y col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
      <Button variant="primary" class="mr-2 shadow-md" @click="handleAddBundle">
        Thêm nhóm phân loại
      </Button>

      <div class="mx-auto hidden text-slate-500 md:block"></div>
      <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
        <div class="relative w-56 text-slate-500">
          <FormInput
            type="text"
            class="!box w-56 pr-10"
            placeholder="Tìm kiếm"
            @update:model-value="handleSearchBundle"
          />
          <Lucide icon="Search" class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4" />
        </div>
      </div>
    </div>
    <div class="intro-y col-span-12 overflow-auto lg:overflow-visible">
      <div class="box p-5">
        <Table striped>
          <Table.Thead>
            <Table.Tr>
              <Table.Th class="whitespace-nowrap"> </Table.Th>
              <Table.Th class="whitespace-nowrap"> Tên </Table.Th>
              <Table.Th class="whitespace-nowrap"> Mô tả </Table.Th>
              <Table.Th class="whitespace-nowrap"> Trạng thái </Table.Th>
              <Table.Th class="whitespace-nowrap text-center"> </Table.Th>
            </Table.Tr>
          </Table.Thead>
          <draggable
            v-bind="dragOptions"
            v-model="bundleList"
            class="list-group"
            :component-data="{
              tag: 'tbody',
              type: 'transition-group',
              name: !drag ? 'flip-list' : null,
            }"
            tag="tbody"
            group="buldle"
            item-key="id"
            @start="drag = true"
            @change="log"
            @end="drag = false"
          >
            <template #item="{ element }">
              <Table.Tr class="list-group-item cursor-move">
                <Table.Td class="w-10">
                  <Lucide icon="Move" class="h-4 w-4" />
                </Table.Td>
                <Table.Td>
                  <div class="font-bold font-medium">
                    {{ element.name }}
                  </div>
                </Table.Td>
                <Table.Td>
                  {{ element.description }}
                </Table.Td>
                <Table.Td class="w-40">
                  <div
                    :class="[
                      'flex items-center',
                      { 'text-success': element.status === 1 },
                      { 'text-danger': element.status === 0 },
                    ]"
                  >
                    <div class="flex items-center justify-center">
                      <Lucide icon="CheckSquare" class="mr-2 h-4 w-4" />
                      {{ element.status === 1 ? "Đã kích hoạt" : "Đã khóa" }}
                    </div>
                  </div>
                </Table.Td>
                <Table.Td
                  class="relative w-56 before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 before:dark:bg-darkmode-400"
                >
                  <div class="flex items-center justify-center">
                    <a
                      class="mr-3 flex items-center"
                      href="#"
                      @click="handleEditBundle(element.id)"
                    >
                      <Lucide icon="CheckSquare" class="mr-1 h-4 w-4" />
                      Sửa
                    </a>
                    <a class="flex items-center text-danger" href="#">
                      <Lucide icon="Trash2" class="mr-1 h-4 w-4" /> Xóa
                    </a>
                  </div>
                </Table.Td>
              </Table.Tr>
            </template>
          </draggable>
        </Table>
      </div>
    </div>
    <Slideover
      size="xl"
      :open="bundleSlideover"
      @close="
        () => {
          setBundleSlideover(false);
        }
      "
    >
      <Slideover.Panel>
        <Slideover.Title class="p-5">
          <h2 class="mr-auto text-base font-medium">
            {{ title }}
          </h2>
        </Slideover.Title>
        <Slideover.Description class="bg-slate-100">
          <form class="validate-form intro-y col-span-12" @submit.prevent="onSubmitBundle">
            <div class="box">
              <div class="p-5">
                <div class="mt-3 2xl:mt-0">
                  <FormLabel html-for="bundel-name"
                    >Tên <span class="text-danger">*</span></FormLabel
                  >
                  <FormInput id="bundel-name" v-model="formData.name" type="text" />
                </div>
                <div class="mt-3">
                  <FormLabel html-for="bundel-description">Mô tả</FormLabel>
                  <FormInput id="bundle-description" v-model="formData.description" type="text" />
                </div>
              </div>
            </div>
          </form>
        </Slideover.Description>
        <Slideover.Footer>
          <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
        </Slideover.Footer>
      </Slideover.Panel>
    </Slideover>
  </div>
</template>
<style>
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
