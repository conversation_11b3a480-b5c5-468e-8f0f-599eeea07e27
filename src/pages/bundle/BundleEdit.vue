<script setup lang="ts">
import Button from "@/base-components/Button";
import FormInput from "@/base-components/Form/FormInput.vue";
import FormLabel from "@/base-components/Form/FormLabel.vue";
import { Tab } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide/Lucide.vue";
import FieldList from "@/pages/bundle/components/FieldList.vue";
import ManageFormDisplay from "@/pages/bundle/components/ManageFormDisplay.vue";
import TermList from "@/pages/bundle/components/TermList.vue";
</script>
<template>
  <h2 class="intro-y mt-10 text-lg font-medium">Tên nhóm</h2>
  <div class="mt-5 grid grid-cols-12 gap-6">
    <div class="col-span-12">
      <Tab.Group class="box intro-y w-full">
        <Tab.List
          class="flex-col border-transparent bg-slate-200 dark:border-transparent dark:bg-darkmode-800 sm:flex-row"
        >
          <Tab v-slot="{ selected }" :full-width="false">
            <Tab.Button
              :class="[
                'flex w-full items-center justify-center px-0 py-0 py-4 text-slate-500 sm:w-40',
                {
                  'hover:border-transparent hover:bg-transparent hover:text-slate-600 hover:dark:bg-transparent hover:dark:text-slate-300':
                    !selected,
                },
                {
                  'border-transparent text-primary dark:border-x-transparent dark:border-t-transparent dark:bg-darkmode-600 dark:text-white':
                    selected,
                },
              ]"
              as="button"
            >
              <Lucide icon="FileText" class="mr-2 h-4 w-4" />
              Term vocabulary
            </Tab.Button>
          </Tab>
          <Tab v-slot="{ selected }" :full-width="false">
            <Tab.Button
              :class="[
                'flex w-full items-center justify-center px-0 py-0 py-4 text-slate-500 sm:w-40',
                {
                  'hover:border-transparent hover:bg-transparent hover:text-slate-600 hover:dark:bg-transparent hover:dark:text-slate-300':
                    !selected,
                },
                {
                  'border-transparent text-primary dark:border-x-transparent dark:border-t-transparent dark:bg-darkmode-600 dark:text-white':
                    selected,
                },
              ]"
              as="button"
            >
              <Lucide icon="Settings" class="mr-2 h-4 w-4" /> Edit Vocabulary
            </Tab.Button>
          </Tab>
          <Tab v-slot="{ selected }" :full-width="false">
            <Tab.Button
              :class="[
                'flex w-full items-center justify-center px-0 py-0 py-4 text-slate-500 sm:w-40',
                {
                  'hover:border-transparent hover:bg-transparent hover:text-slate-600 hover:dark:bg-transparent hover:dark:text-slate-300':
                    !selected,
                },
                {
                  'border-transparent text-primary dark:border-x-transparent dark:border-t-transparent dark:bg-darkmode-600 dark:text-white':
                    selected,
                },
              ]"
              as="button"
            >
              <Lucide icon="AlignLeft" class="mr-2 h-4 w-4" />
              Danh sách Field
            </Tab.Button>
          </Tab>
          <Tab v-slot="{ selected }" :full-width="false">
            <Tab.Button
              :class="[
                'flex w-full items-center justify-center px-0 py-0 py-4 text-slate-500 sm:w-40',
                {
                  'hover:border-transparent hover:bg-transparent hover:text-slate-600 hover:dark:bg-transparent hover:dark:text-slate-300':
                    !selected,
                },
                {
                  'border-transparent text-primary dark:border-x-transparent dark:border-t-transparent dark:bg-darkmode-600 dark:text-white':
                    selected,
                },
              ]"
              as="button"
            >
              <Lucide icon="Sliders" class="mr-2 h-4 w-4" />
              Quản lý hiển thị
            </Tab.Button>
          </Tab>
        </Tab.List>
        <Tab.Panels class="p-5">
          <Tab.Panel class="leading-relaxed">
            <TermList />
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <form class="validate-form">
              <div class="intro-y box p-5">
                <div class="2xl:mt-0">
                  <FormLabel html-for="vocabulary-name"
                    >Tên <span class="text-danger">*</span></FormLabel
                  >
                  <FormInput id="vocabulary-name" type="text" />
                </div>
                <div class="mt-3">
                  <FormLabel html-for="vocabulary-description">Mô tả</FormLabel>
                  <FormInput id="vocabulary-name" type="text" />
                </div>
                <div class="mt-5">
                  <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
                </div>
              </div>
            </form>
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <FieldList />
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <ManageFormDisplay />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  </div>
</template>
