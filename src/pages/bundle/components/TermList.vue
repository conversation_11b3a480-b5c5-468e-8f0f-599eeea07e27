<script setup lang="ts">
import { computed, ref } from "vue";
import draggable from "vuedraggable";

import Button from "@/base-components/Button";
import { FormInput, FormLabel, FormTextarea } from "@/base-components/Form";
import { Slideover } from "@/base-components/Headless";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Table from "@/base-components/Table";

const termSlideover = ref(false);
const setTermSlideover = (value: boolean) => {
  termSlideover.value = value;
};

const taxonomy = ref([
  {
    id: 1,
    name: "<PERSON><PERSON> mục",
  },
  {
    id: 2,
    name: "Sản phẩm",
  },
  {
    id: 3,
    name: "<PERSON> tứ<PERSON>",
  },
  {
    id: 4,
    name: "Sự kiện",
  },
  {
    id: 5,
    name: "Hướng dẫn",
  },
  {
    id: 6,
    name: "Câu hỏi thường gặp",
  },
  {
    id: 7,
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 8,
    name: "<PERSON><PERSON><PERSON> hệ",
  },
  {
    id: 9,
    name: "<PERSON><PERSON><PERSON> <PERSON> và phản hồi",
  },
  {
    id: 10,
    name: "<PERSON><PERSON> chúng tôi",
  },
]);
const drag = ref(false);
const dragOptions = computed(() => {
  console.log("drag option");
  return {
    animation: 200,
    group: "pipeline",
    disabled: false,
    ghostClass: "ghost",
  };
});
const log = function () {
  console.log(taxonomy);
};
</script>
<template>
  <Button
    variant="primary"
    class="intro-y shadow-md"
    @click="
      (event: MouseEvent) => {
        event.preventDefault();
        setTermSlideover(true);
      }
    "
  >
    Thêm thành phần
  </Button>
  <div class="intro-y mt-2 overflow-auto lg:overflow-visible">
    <Table striped>
      <Table.Thead>
        <Table.Tr>
          <Table.Th class="whitespace-nowrap"> Tiêu đề </Table.Th>
          <Table.Th class="whitespace-nowrap"> Mô tả </Table.Th>
          <Table.Th class="whitespace-nowrap"> Trạng thái </Table.Th>
          <Table.Th class="whitespace-nowrap"></Table.Th>
        </Table.Tr>
      </Table.Thead>
      <draggable
        v-bind="dragOptions"
        v-model="taxonomy"
        class="list-group"
        :component-data="{
          tag: 'tbody',
          type: 'transition-group',
          name: !drag ? 'flip-list' : null,
        }"
        tag="tbody"
        group="term"
        item-key="id"
        @start="drag = true"
        @end="drag = false"
        @change="log"
      >
        <template #item="{ element }">
          <Table.Tr class="list-group-item cursor-move">
            <Table.Td class="whitespace-nowrap">
              <div class="flex items-center">
                <Lucide icon="Move" class="mr-5" />
                {{ element.name }}
              </div>
            </Table.Td>
            <Table.Td class="whitespace-nowrap"> Đây là đoạn mô tả </Table.Td>
            <Table.Td class="whitespace-nowrap">
              <div class="flex items-center text-success">
                <Lucide icon="CheckSquare" class="mr-2 h-4 w-4" />
                Đã kích hoạt
              </div>
            </Table.Td>
            <Table.Td class="whitespace-nowrap">
              <div class="flex items-center">
                <Menu class="ml-auto">
                  <Menu.Button :as="Button" variant="outline-secondary" class="font-normal">
                    Thao tác
                    <Lucide icon="ChevronDown" class="ml-2 h-4 w-4" />
                  </Menu.Button>
                  <Menu.Items class="w-40">
                    <Menu.Item>
                      <Lucide icon="Settings" class="mr-2 h-4 w-4" /> Chỉnh sửa
                    </Menu.Item>
                    <Menu.Item class="text-danger">
                      <Lucide icon="Delete" class="mr-2 h-4 w-4" />
                      Xóa
                    </Menu.Item>
                  </Menu.Items>
                </Menu>
              </div>
            </Table.Td>
          </Table.Tr>
        </template>
      </draggable>
    </Table>
  </div>
  <Slideover
    size="xl"
    :open="termSlideover"
    @close="
      () => {
        setTermSlideover(false);
      }
    "
  >
    <Slideover.Panel>
      <Slideover.Title class="p-5">
        <h2 class="mr-auto text-base font-medium">Thêm thành phần</h2>
      </Slideover.Title>
      <Slideover.Description class="bg-slate-100">
        <form class="validate-form intro-y col-span-12">
          <div class="box">
            <div class="p-5">
              <div class="mt-3 2xl:mt-0">
                <FormLabel html-for="term-name">Tên <span class="text-danger">*</span></FormLabel>
                <FormInput id="term-name" type="text" />
              </div>
              <div class="mt-3">
                <FormLabel html-for="term-description">Mô tả</FormLabel>
                <FormTextarea id="term-description" type="text" />
              </div>
              <div class="mt-3">
                <FormLabel html-for="term-alias">Url alias</FormLabel>
                <FormInput id="term-alias" type="text" />
              </div>
            </div>
          </div>
        </form>
      </Slideover.Description>
      <Slideover.Footer>
        <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
      </Slideover.Footer>
    </Slideover.Panel>
  </Slideover>
</template>
<style>
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
</style>
