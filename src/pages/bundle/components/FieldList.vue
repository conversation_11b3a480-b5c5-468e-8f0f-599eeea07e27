<script setup lang="ts">
import _ from "lodash";

import Button from "@/base-components/Button";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Table from "@/base-components/Table";
import fakerData from "@/utils/faker";
</script>

<template>
  <Button variant="primary" class="intro-y shadow-md">
    <router-link :to="{ name: 'top-menu-field-create' }"> Thêm field </router-link>
  </Button>
  <div class="intro-y mt-2 overflow-auto lg:overflow-visible">
    <Table striped>
      <Table.Thead>
        <Table.Tr>
          <Table.Th class="whitespace-nowrap"> Label </Table.Th>
          <Table.Th class="whitespace-nowrap"> Machine name </Table.Th>

          <Table.Th class="whitespace-nowrap"> Loại trường </Table.Th>
          <Table.Th class="whitespace-nowrap text-center"> </Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        <Table.Tr
          v-for="(faker, fakerKey) in _.take(fakerData, 10)"
          :key="fakerKey"
          class="zoom-in intro-x"
        >
          <Table.Td class="whitespace-nowrap">
            <div class="mr-3 flex items-center" href="#">
              {{ faker.taxonomy[0].name }}
            </div>
          </Table.Td>
          <Table.Td class="whitespace-nowrap"> field_example </Table.Td>
          <Table.Td class="whitespace-nowrap"> text </Table.Td>
          <Table.Td class="whitespace-nowrap">
            <div class="flex items-center">
              <Menu class="ml-auto">
                <Menu.Button :as="Button" variant="outline-secondary" class="font-normal">
                  Thao tác
                  <Lucide icon="ChevronDown" class="ml-2 h-4 w-4" />
                </Menu.Button>
                <Menu.Items class="w-40">
                  <Menu.Item> <Lucide icon="Settings" class="mr-2 h-4 w-4" /> Chỉnh sửa </Menu.Item>
                  <Menu.Item class="text-danger">
                    <Lucide icon="Delete" class="mr-2 h-4 w-4" />
                    Xóa
                  </Menu.Item>
                </Menu.Items>
              </Menu>
            </div>
          </Table.Td>
        </Table.Tr>
      </Table.Tbody>
    </Table>
  </div>
</template>
