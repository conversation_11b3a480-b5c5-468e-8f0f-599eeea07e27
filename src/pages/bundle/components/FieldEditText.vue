<script setup lang="ts">
import Button from "@/base-components/Button/Button.vue";
import { FormCheck, FormInline, FormInput, FormLabel, FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
</script>
<template>
  <form action="">
    <div class="intro-y box mt-5 p-5">
      <div class="rounded-md border border-slate-200/60 p-5 dark:border-darkmode-400">
        <div
          class="flex items-center border-b border-slate-200/60 pb-5 text-base font-medium dark:border-darkmode-400"
        >
          <Lucide icon="ChevronDown" class="mr-2 h-4 w-4" /> Thông tin trường
        </div>
        <div class="mt-5">
          <FormInline class="mt-5 flex-col items-start pt-5 first:mt-0 first:pt-0 xl:flex-row">
            <FormLabel class="xl:!mr-10 xl:w-64">
              <div class="text-left">
                <div class="flex items-center">
                  <div class="font-medium">Văn bản trợ giúp</div>
                </div>
                <div class="mt-3 text-xs leading-relaxed text-slate-500">
                  <div>Huớng dẫn hiển thị cho người dùng bên dưới trường.</div>
                </div>
              </div>
            </FormLabel>
            <div class="mt-3 w-full flex-1 xl:mt-0">
              <div class="flex flex-col sm:flex-row">
                <FormTextarea type="text" placeholder="" />
              </div>
            </div>
          </FormInline>

          <FormInline class="mt-5 flex-col items-start pt-5 first:mt-0 first:pt-0 xl:flex-row">
            <FormLabel class="xl:!mr-10 xl:w-64">
              <div class="text-left">
                <div class="flex items-center">
                  <div class="font-medium">Trường bắt buộc</div>
                </div>
                <div class="mt-3 text-xs leading-relaxed text-slate-500">
                  <div>Chọn nếu field không được để trống</div>
                </div>
              </div>
            </FormLabel>
            <div class="mt-3 w-full flex-1 xl:mt-0">
              <div class="flex flex-col sm:flex-row">
                <FormCheck class="mt-2">
                  <FormCheck.Input id="checkbox-switch-1" type="checkbox" value="" />
                  <FormCheck.Label html-for="checkbox-switch-1"> Required field </FormCheck.Label>
                </FormCheck>
              </div>
            </div>
          </FormInline>
        </div>
      </div>
    </div>
    <div class="intro-y box mt-5 p-5">
      <div class="rounded-md border border-slate-200/60 p-5 dark:border-darkmode-400">
        <div
          class="flex items-center border-b border-slate-200/60 pb-5 text-base font-medium dark:border-darkmode-400"
        >
          <Lucide icon="ChevronDown" class="mr-2 h-4 w-4" /> Giá trị mặc định
        </div>
        <div class="mt-5">
          <FormInline class="mt-5 flex-col items-start pt-5 first:mt-0 first:pt-0 xl:flex-row">
            <FormLabel class="xl:!mr-10 xl:w-64">
              <div class="text-left">
                <div class="flex items-center">
                  <div class="font-medium">Tiêu đề trường</div>
                </div>
                <div class="mt-3 text-xs leading-relaxed text-slate-500">
                  <div>Giá trị mặc định cho trường này, được sử dụng khi tạo nội dung mới.</div>
                </div>
              </div>
            </FormLabel>
            <div class="mt-3 w-full flex-1 xl:mt-0">
              <div class="flex flex-col sm:flex-row">
                <FormInput type="text" placeholder="" />
              </div>
            </div>
          </FormInline>
        </div>
      </div>
    </div>
    <div class="intro-y mt-5">
      <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
    </div>
  </form>
</template>
