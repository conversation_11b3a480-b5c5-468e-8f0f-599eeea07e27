<script setup lang="ts">
import { ref } from "vue";

import Button from "@/base-components/Button";
import { FormInput, FormLabel, FormSelect, FormTextarea } from "@/base-components/Form";
import { Slideover } from "@/base-components/Headless";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Table from "@/base-components/Table";

const formSlideover = ref(false);
const setFormSlideover = (value: boolean) => {
  formSlideover.value = value;
};
const fields = [
  {
    id: 1,
    name: "Name",
  },
  {
    id: 2,
    name: "Description",
  },
  {
    id: 3,
    name: "URL alias",
  },
];
</script>
<template>
  <form>
    <div class="intro-y overflow-auto lg:overflow-visible">
      <Table striped>
        <Table.Thead>
          <Table.Tr>
            <Table.Th class="whitespace-nowrap"> Tiêu đề </Table.Th>
            <Table.Th class="whitespace-nowrap"> Parent </Table.Th>
            <Table.Th class="whitespace-nowrap"> Region </Table.Th>
            <Table.Th class="whitespace-nowrap"></Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <Table.Tr v-for="field in fields" class="zoom-in intro-x">
            <Table.Td class="whitespace-nowrap">
              <div class="mr-3 flex items-center" href="#">
                {{ field.name }}
              </div>
            </Table.Td>
            <Table.Td class="whitespace-nowrap">
              <FormSelect>
                <option value="none" disabled>None</option>
                <option value="female">Nội dung</option>
                <option value="unknown">Ẩn</option>
              </FormSelect>
            </Table.Td>
            <Table.Td class="whitespace-nowrap">
              <FormSelect>
                <option value="female">Nội dung</option>
                <option value="unknown">Vô hiệu hóa</option>
              </FormSelect>
            </Table.Td>
            <Table.Td class="whitespace-nowrap">
              <div class="flex items-center">
                <Menu class="ml-auto">
                  <Menu.Button :as="Button" variant="outline-secondary" class="font-normal">
                    Thao tác
                    <Lucide icon="ChevronDown" class="ml-2 h-4 w-4" />
                  </Menu.Button>
                  <Menu.Items class="w-40">
                    <Menu.Item
                      @click="
                        (event: MouseEvent) => {
                          event.preventDefault();
                          setFormSlideover(true);
                        }
                      "
                    >
                      <Lucide icon="Settings" class="mr-2 h-4 w-4" /> Chỉnh sửa
                    </Menu.Item>
                  </Menu.Items>
                </Menu>
              </div>
            </Table.Td>
          </Table.Tr>
        </Table.Tbody>
      </Table>
    </div>
    <div class="mt-5">
      <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
    </div>
  </form>
  <Slideover
    size="xl"
    :open="formSlideover"
    @close="
      () => {
        setFormSlideover(false);
      }
    "
  >
    <Slideover.Panel>
      <Slideover.Title class="p-5">
        <h2 class="mr-auto text-base font-medium">Chỉnh sửa</h2>
      </Slideover.Title>
      <Slideover.Description class="bg-slate-100">
        <form class="validate-form intro-y col-span-12">
          <div class="box">
            <div class="p-5">
              <div class="mt-3 2xl:mt-0">
                <FormLabel html-for="term-name">Tên <span class="text-danger">*</span></FormLabel>
                <FormInput id="term-name" type="text" />
              </div>
              <div class="mt-3">
                <FormLabel html-for="term-description">Mô tả</FormLabel>
                <FormTextarea id="term-description" type="text" />
              </div>
              <div class="mt-3">
                <FormLabel html-for="term-alias">Url alias</FormLabel>
                <FormInput id="term-alias" type="text" />
              </div>
            </div>
          </div>
        </form>
      </Slideover.Description>
      <Slideover.Footer>
        <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
      </Slideover.Footer>
    </Slideover.Panel>
  </Slideover>
</template>
