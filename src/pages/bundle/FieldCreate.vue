<script setup lang="ts">
import Button from "@/base-components/Button/Button.vue";
import { FormInput, FormLabel } from "@/base-components/Form";
import { Tab } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import FieldEditBoolean from "@/pages/bundle/components/FieldEditBoolean.vue";
import FieldEditEmail from "@/pages/bundle/components/FieldEditEmail.vue";
import FieldEditInteger from "@/pages/bundle/components/FieldEditInteger.vue";
import FieldEditText from "@/pages/bundle/components/FieldEditText.vue";
</script>

<template>
  <div class="intro-y mt-8 flex items-center">
    <h2 class="mr-auto text-lg font-medium">Tạo Field</h2>
  </div>
  <Tab.Group>
    <div class="grid grid-cols-12 gap-6">
      <!-- BEGIN: FAQ Menu -->
      <div class="intro-y col-span-12 lg:col-span-4 xl:col-span-3">
        <div class="box intro-y mt-5">
          <div class="px-4 pb-3 pt-5">
            <Tab.List variant="boxed-tabs" class="flex-col">
              <Tab class="w-full">
                <Tab.Button class="flex w-full items-center px-4 py-2 shadow-none" as="button">
                  <Lucide icon="Activity" class="mr-2 h-4 w-4" />
                  <div class="truncate">Number (Integer)</div>
                </Tab.Button>
              </Tab>
              <Tab class="w-full">
                <Tab.Button class="mt-1 flex w-full items-center px-4 py-2 shadow-none" as="button">
                  <Lucide icon="Box" class="mr-2 h-4 w-4" />
                  <div class="truncate">Text</div>
                </Tab.Button>
              </Tab>
              <Tab v-slot="{ selected }" class="w-full" :selected="true">
                <Tab.Button
                  selected
                  class="mt-1 flex w-full items-center px-4 py-2 shadow-none"
                  as="button"
                >
                  <Lucide icon="Box" class="mr-2 h-4 w-4" />
                  <div class="truncate">Boolean</div>
                </Tab.Button>
              </Tab>
              <Tab class="w-full">
                <Tab.Button class="mt-1 flex w-full items-center px-4 py-2 shadow-none" as="button">
                  <Lucide icon="AtSign" class="mr-2 h-4 w-4" />
                  <div class="truncate">Email</div>
                </Tab.Button>
              </Tab>
            </Tab.List>
          </div>
        </div>
        <div class="box intro-y mt-5 p-5">
          <form action="">
            <div class="">
              <FormLabel
                html-for="title-field"
                class="flex w-full flex-col font-medium sm:flex-row"
              >
                Label
                <span class="text-danger"> * </span>
              </FormLabel>
              <FormInput id="title-field" type="text" />
            </div>
            <div class="mt-3">
              <FormLabel
                html-for="title-field"
                class="flex w-full flex-col font-medium sm:flex-row"
              >
                Số lượng giá trị cho phép
                <span class="text-danger"> * </span>
              </FormLabel>
              <FormInput id="title-field" type="text" />
              <!--            <div class="mt-2 text-xs leading-relaxed text-slate-500"><div> Ví dụ 1 sản phẩm về mặt lý thuyết có thể có vô số tags </div></div>-->
            </div>
            <div class="mt-5">
              <Button variant="primary" type="submit" class="mr-auto w-20"> Lưu </Button>
            </div>
          </form>
        </div>
      </div>
      <div class="intro-y col-span-12 lg:col-span-8 xl:col-span-9">
        <Tab.Panels class="mt-5">
          <Tab.Panel class="leading-relaxed">
            <FieldEditInteger />
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <FieldEditText />
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <FieldEditBoolean />
          </Tab.Panel>
          <Tab.Panel class="leading-relaxed">
            <FieldEditEmail />
          </Tab.Panel>
        </Tab.Panels>
      </div>
    </div>
  </Tab.Group>
</template>
