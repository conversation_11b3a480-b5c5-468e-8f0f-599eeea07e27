import { TaskPriorityEnum } from "@/api/bcare-enum";
import {
  Person,
  PersonDynamicQuery,
  TaskAssignmentResponse,
  TaskDynamicQuery,
  TaskNoteResponse,
  UserShort,
} from "@/api/bcare-types";
import { TaskListRequest } from "@/api/bcare-types-v2";
import { Icon } from "@/base-components/Lucide/Lucide.vue";

export interface UserTask {
  user_id: number;
  user_name: string;
  type: string;
}

export const TASK_STATUS = [
  { id: 2, name: "<PERSON><PERSON>i tạo" },
  { id: 3, name: "<PERSON>ang thực hiện" },
  { id: 4, name: "<PERSON><PERSON> xác nhận" },
  { id: 5, name: "<PERSON><PERSON><PERSON> thành" },
];
export const TASK_TYPE = [
  { id: 1, name: "<PERSON>" },
  { id: 2, name: "Trung bình" },
  { id: 3, name: "Thấp" },
];
export const TASK_VIEW_TYPES = {
  ALL: "all",
  PRIMARY: "primary",
  CREATOR: "creator",
  CONTRIBUTOR: "contributor",
  SLOW_PROCESS: "slowProcess",
  COMPLETE: "complete",
  PRIORITY_LOW: "priority_low",
  PRIORITY_MEDIUM: "priority_medium",
  PRIORITY_HIGH: "priority_high",
} as const;

export type TaskViewType = (typeof TASK_VIEW_TYPES)[keyof typeof TASK_VIEW_TYPES];

// Move filter definitions here
export const TASK_FILTERS = [
  { type: TASK_VIEW_TYPES.ALL, icon: "ListOrdered", name: "Tất cả" },
  { type: TASK_VIEW_TYPES.PRIMARY, icon: "Mail", name: "Của tôi" },
  { type: TASK_VIEW_TYPES.CREATOR, icon: "Send", name: "Đã giao" },
  { type: TASK_VIEW_TYPES.CONTRIBUTOR, icon: "Star", name: "Liên quan" },
  { type: TASK_VIEW_TYPES.SLOW_PROCESS, icon: "FileX", name: "Chậm tiến độ" },
  { type: TASK_VIEW_TYPES.COMPLETE, icon: "FileCheck", name: "Hoàn thành" },
] as const;

export const PRIORITY_FILTERS = [
  { type: TASK_VIEW_TYPES.PRIORITY_LOW, value: TaskPriorityEnum.LOW, name: "Thấp" },
  { type: TASK_VIEW_TYPES.PRIORITY_MEDIUM, value: TaskPriorityEnum.MEDIUM, name: "Trung bình" },
  { type: TASK_VIEW_TYPES.PRIORITY_HIGH, value: TaskPriorityEnum.HIGH, name: "Cao" },
] as const;

export const taskAddPayload = {
  id: "",
  title: "",
  note: "",
  start_date: "",
  due_date: "",
  department_id: "",
  type: 0,
  primary_user_id: 0,
  notes: [] as TaskNoteResponse[],
  priority: TaskPriorityEnum.MEDIUM,
  creator: {} as UserShort,
  assignment: [] as TaskAssignmentResponse[],
  person: {} as Person,
  parent_id: 0,
  person_id: 0,
  deal_id: 0,
  status: 0,
  appointment_id: 0,
};

export const filterTasksDefault: TaskListRequest = {
  page_size: 10,
  page: 1,
  filter: {},
  primary_id: 0,
  contributor_id: 0,
  slow_process: false,
  order_by: "start_date DESC",
};

export const assignmentTaskPayload = {
  task_id: 0,
  user_id: 0,
  role: "",
};

export const taskDynamicQuery: TaskDynamicQuery = {
  table: "task",
  selects: [],
  filters: [
    {
      conditions: [],
      field: "status",
      logic: "AND",
      operator: "GT",
      value: "0",
    },
  ],
  sort: [
    {
      field: "start_date",
      order: "DESC",
    },
  ],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  person: "",
  contributor: "",
  creator: "",
  primary: "",
};
export const personDynamicQuery: PersonDynamicQuery = {
  table: "person",
  selects: [],
  filters: [],
  sort: [],
  group_by: [],
  aggregations: [],
  limit: 10,
  offset: 0,
  creator: "", // tên, id
  sale: "", // tên, id
  doctor_id: "",
  search: "",
};
