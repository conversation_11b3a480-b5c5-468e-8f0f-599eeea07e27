<template>
  <Dialog
    v-model:visible="visible"
    :style="{ width: '50vw' }"
    :modal="true"
    closable
    dismissableMask
    :header="'Chỉnh sửa thời gian'"
  >
    <div class="space-y-4">
      <div class="flex flex-wrap gap-4">
        <FormField label="<PERSON><PERSON><PERSON> bắt đầu" icon="pi pi-calendar-plus" class="flex-1">
          <DatePicker
            v-model="startDate"
            showTime
            hourFormat="24"
            placeholder="Bắt đầu"
            date-format="dd/mm/yy"
            fluid
            :minDate="new Date()"
            @update:model-value="handleStartDateUpdate"
          />
        </FormField>
        <FormField label="Ngày kết thúc" icon="pi pi-calendar-times" class="flex-1">
          <DatePicker
            v-model="dueDate"
            showTime
            hourFormat="24"
            placeholder="Kết thúc"
            date-format="dd/mm/yy"
            fluid
            :minDate="new Date()"
            @update:model-value="handleDueDateUpdate"
          />
          <small class="text-red-500" v-if="errors.due_date">
            {{ errors.due_date }}
          </small>
        </FormField>
      </div>
    </div>

    <template #footer>
      <Button label="Hủy" severity="danger" icon="pi pi-times" @click="close" outlined />
      <Button label="Cập nhật" icon="pi pi-save" @click="updateDeadline" autofocus />
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import Button from "primevue/button";
import Dialog from "primevue/dialog";
import { ref } from "vue";

import { FormField } from "@/components/Form";
import { useDatePicker } from "@/composables/useDatePicker";
import { rules, useFormValidation } from "@/composables/useFormValidation";
import { useNotiStore } from "@/stores/notification";
import { useTaskStore } from "@/stores/task-store";

const props = defineProps<{
  taskIds: number[];
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "updated"): void;
}>();

const visible = ref(false);

const formData = ref({
  start_date: formatTodayWithHour(9),
  due_date: formatTodayWithHour(21),
});

const { dateValue: startDate, handleDateUpdate: handleStartDateUpdate } = useDatePicker(
  () => formData.value.start_date,
  (value) => (formData.value.start_date = value ?? ""),
);

const { dateValue: dueDate, handleDateUpdate: handleDueDateUpdate } = useDatePicker(
  () => formData.value.due_date,
  (value) => (formData.value.due_date = value ?? ""),
);

const taskStore = useTaskStore();
const { success } = useNotiStore();

const { errors, clearErrors } = useFormValidation({});

function formatTodayWithHour(hour: number) {
  return useDateFormat(new Date().setHours(hour - 7, 0, 0, 0), "YYYY-MM-DDTHH:mm:ss.SSSZ").value;
}

const open = () => {
  visible.value = true;
};

const close = () => {
  visible.value = false;
  clearErrors();
  formData.value = {
    start_date: formatTodayWithHour(9),
    due_date: formatTodayWithHour(21),
  };
  emit("close");
};

const updateDeadline = async () => {
  const dateValidation = rules.dateComparison(
    new Date(startDate.value || ""),
    new Date(dueDate.value || ""),
    "Thời gian kết thúc phải sau thời gian bắt đầu",
  );

  if (!dateValidation.validate()) {
    errors.value.due_date = dateValidation.message;
    return;
  }

  try {
    await taskStore.bulkUpdateTasks({
      id_list: props.taskIds,
      start_date: startDate.value as unknown as string,
      due_date: dueDate.value as unknown as string,
      end_date: dueDate.value as unknown as string,
    });
    success({ message: "Cập nhật deadline thành công" });
    emit("updated");
    close();
  } catch (error) {
    console.error("Failed to update deadline:", error);
  }
};

defineExpose({ open });
</script>
