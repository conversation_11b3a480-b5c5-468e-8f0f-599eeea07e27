<template>
  <div class="flex h-full flex-col">
    <!-- Scrollable Message List -->
    <div class="flex-grow overflow-y-auto overflow-x-hidden bg-gray-100 p-4">
      <!-- Combined Timeline Section -->
      <div v-if="combinedActivities.length > 0" class="space-y-4">
        <template v-for="activity in combinedActivities" :key="activity.timestamp">
          <!-- History Item -->
          <template v-if="activity.type === 'history'">
            <HistoryTimeline :history="[activity]" used-at-component="task" />
          </template>

          <!-- Message Item -->
          <template v-else>
            <div class="flex gap-2.5">
              <UserAvatar
                :user="{
                  ...activity.creator,
                  id: activity.user_id,
                  username: activity.creator.username,
                  name: activity.creator.name,
                }"
                class="h-10 w-10 flex-shrink-0"
              />
              <div class="flex-grow">
                <h5 class="pb-1 text-sm font-semibold leading-snug text-gray-900">
                  {{ activity.creator.name }}
                </h5>
                <div class="max-w-full">
                  <div
                    class="inline-block max-w-full rounded-3xl rounded-tl-none bg-white px-3.5 py-2"
                  >
                    <div
                      class="prose relative w-full max-w-none break-words text-sm prose-p:m-0 prose-ol:my-0.5 prose-ul:my-0.5 prose-ul:ps-7 prose-li:m-0 prose-img:my-1 prose-img:max-w-1/2 prose-img:rounded-md"
                      v-lightbox
                    >
                      {{ activity.body }}
                    </div>
                  </div>
                  <div class="mt-1 text-right">
                    <h6 class="py-1 text-xs font-normal leading-4 text-gray-500">
                      {{ formatDate(activity.created_at) }}
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </template>
      </div>

      <!-- Empty State -->
      <div v-else class="flex h-full flex-col items-center justify-center text-center">
        <div class="mb-2 text-6xl text-blue-300">
          <i class="pi pi-comments"></i>
        </div>
        <p class="text-sm text-gray-500">Chưa có hoạt động nào!</p>
      </div>
    </div>

    <!-- Fixed Message Input Form -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white p-2 sm:p-3">
      <div class="flex w-full items-center gap-2">
        <InputText
          v-model="newMessage"
          placeholder="Nhập nội dung..."
          @keyup.enter="handleSendMessage"
          class="min-w-0 flex-grow px-2 py-1 text-sm sm:px-3 sm:py-2 sm:text-base"
        />
        <Button
          type="submit"
          icon="pi pi-send"
          class="p-button-rounded h-8 w-8 flex-shrink-0 sm:h-10 sm:w-10"
          @click="handleSendMessage"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import { computed, onMounted, ref, watch } from "vue";

import {
  HistoryEntry,
  TaskNoteAddRequest,
  TaskNoteListRequest,
  TaskNoteResponse,
} from "@/api/bcare-types-v2";
import HistoryTimeline from "@/components/Timeline/HistoryTimeline.vue";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useTaskStore } from "@/stores/task-store";

const props = defineProps<{
  taskId?: number;
  history?: HistoryEntry[];
  creatorId?: number;
  createdAt?: string;
}>();

const emit = defineEmits<{
  (e: "messageSent"): void;
}>();

const taskStore = useTaskStore();
const messages = ref<TaskNoteResponse[]>([]);
const newMessage = ref("");

const fetchMessages = async () => {
  if (!props.taskId) return;

  const query: TaskNoteListRequest = {
    page_size: 100,
    filter: {
      task_id: props.taskId,
    },
  };

  try {
    const response = await taskStore.listTaskNotes(query);
    messages.value = response?.notes || [];
  } catch (error) {
    console.error("Failed to fetch task notes:", error);
  }
};

const handleSendMessage = async () => {
  if (!props.taskId || !newMessage.value.trim()) {
    return;
  }

  const request: TaskNoteAddRequest = {
    task_id: props.taskId,
    body: newMessage.value.trim(),
  };

  try {
    await taskStore.addTaskNote(request);
    newMessage.value = "";
    emit("messageSent");
    await fetchMessages(); // Refresh the messages list
  } catch (error) {
    console.error("Failed to add note:", error);
  }
};

const formatDate = (date: string) => {
  return useDateFormat(new Date(date), "HH:mm DD/MM/YYYY").value;
};

const combinedActivities = computed(() => {
  const activities = [
    // Transform history entries
    ...(props.history?.map((entry) => ({
      ...entry,
      type: "history" as const,
      timestamp: entry.timestamp,
    })) || []),
    // Transform messages
    ...(messages.value.map((message) => ({
      ...message,
      type: "message" as const,
      timestamp: message.created_at,
    })) || []),
  ];

  // Add creation event if creatorId and createdAt exist
  if (props.creatorId && props.createdAt) {
    activities.push({
      type: "history" as const,
      timestamp: props.createdAt,
      user_id: props.creatorId,
      changes: [
        {
          field: "created_at",
          old_value: "",
          new_value: "",
        },
      ],
    });
  }

  return activities.sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
  );
});

onMounted(fetchMessages);

watch(() => props.taskId, fetchMessages);
</script>
