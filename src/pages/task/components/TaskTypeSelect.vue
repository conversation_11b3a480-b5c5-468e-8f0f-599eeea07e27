<template>
  <div class="relative w-full">
    <div v-if="!isCustomMode">
      <Select
        v-model="selectedOption"
        :options="options"
        optionLabel="label"
        optionValue="value"
        placeholder="Chọn loại công việc"
        class="w-full"
        @change="handleOptionChange"
      />
    </div>
    <div v-else class="flex w-full gap-2">
      <InputText
        v-model="customValue"
        class="flex-1"
        placeholder="Nhập loại công việc tùy chỉnh"
        @input="updateModelValue"
      />
      <Button
        icon="pi pi-times"
        @click="exitCustomMode"
        aria-label="Hủy"
        v-tooltip.top="'Hủy'"
        severity="danger"
        rounded
        text
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const options = [
  { label: "Gọi điện", value: "goi_dien" },
  { label: "Nhắn tin", value: "nhan_tin" },
  { label: "Theo dõi", value: "theo_doi" },
  { label: "Tùy chỉnh", value: "custom" },
];

const isCustomMode = ref(false);
const selectedOption = ref(determineInitialOption(props.modelValue));
const customValue = ref(isCustomValue(props.modelValue) ? props.modelValue : "");

function determineInitialOption(value: string): string {
  if (!value) return "";

  const predefinedValues = ["goi_dien", "nhan_tin", "theo_doi"];
  return predefinedValues.includes(value) ? value : "custom";
}

function isCustomValue(value: string): boolean {
  const predefinedValues = ["goi_dien", "nhan_tin", "theo_doi"];
  return Boolean(value) && !predefinedValues.includes(value);
}

function handleOptionChange() {
  if (selectedOption.value === "custom") {
    isCustomMode.value = true;
  } else {
    emit("update:modelValue", selectedOption.value);
  }
}

function updateModelValue() {
  emit("update:modelValue", customValue.value);
}

function exitCustomMode() {
  isCustomMode.value = false;
  selectedOption.value = "";
  emit("update:modelValue", "");
}

// Initialize custom mode if needed
watch(
  () => selectedOption.value,
  (newValue) => {
    if (newValue === "custom") {
      isCustomMode.value = true;
    }
  },
  { immediate: true },
);

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (isCustomValue(newValue)) {
      selectedOption.value = "custom";
      customValue.value = newValue;
      isCustomMode.value = true;
    } else {
      selectedOption.value = newValue;
      isCustomMode.value = false;
    }
  },
);
</script>
