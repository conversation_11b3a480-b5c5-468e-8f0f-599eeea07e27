<template>
  <Tag v-if="type" :value="label" severity="secondary" class="text-xs" />
</template>

<script setup lang="ts">
import Tag from "primevue/tag";
import { computed } from "vue";

const props = defineProps<{
  type: string;
}>();

const typeMap = {
  goi_dien: "Gọi điện",
  nhan_tin: "Nhắn tin",
  theo_doi: "Theo dõi",
};

const label = computed(() => {
  return typeMap[props.type as keyof typeof typeMap] || props.type;
});
</script>
