<template>
  <div class="col-span-6">
    <Tabs :modelValue="activeTabLocal" @update:modelValue="$emit('update:activeTab', $event)">
      <TabList>
        <Tab value="0">Nội dung</Tab>
        <Tab value="1">Chuỗi công việc</Tab>
      </TabList>
      <TabPanels class="!px-0 py-4">
        <TabPanel value="0">
          <Textarea
            :modelValue="props.formData.note"
            @update:modelValue="$emit('update:note', $event)"
            rows="5"
            class="w-full"
            autoResize
            placeholder="Nhập nội dung công việc"
          />
        </TabPanel>

        <TabPanel value="1">
          <div class="grid grid-cols-6 gap-2">
            <!-- Recurrence -->
            <div class="col-span-2 flex items-center gap-2">
              <Lucide icon="Repeat" class="h-4 w-4 text-orange-500" />
              <label for="recurrence" class="text-sm font-medium text-gray-700">Lặp lại</label>
            </div>
            <div class="col-span-4 flex items-stretch gap-1">
              <InputText
                v-model="prompt"
                class="flex-grow"
                placeholder="Vd: Mỗi thứ 2 lúc 10:00 sáng"
              />
              <Button
                icon="pi pi-sparkles"
                @click="generateCron"
                :loading="isLoading"
                class="h-full"
                :disabled="isLoading"
              />
            </div>

            <div class="col-span-2 flex items-center gap-2">
              <Lucide icon="Info" class="h-4 w-4 text-blue-500" />
              <label for="recurrence" class="text-sm font-medium text-gray-700">Kết quả</label>
            </div>

            <div class="col-span-4">
              <div v-if="cronExpression">
                <pre class="overflow-x-auto rounded-md bg-gray-100 p-4">{{ cronExpression }}</pre>
              </div>
            </div>
          </div>
        </TabPanel>
      </TabPanels>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import Tabs from "primevue/tabs";
import Textarea from "primevue/textarea";
import { ref } from "vue";

import Lucide from "@/base-components/Lucide";
import useMind from "@/hooks/useMind";

const prompt = ref(""),
  cronExpression = ref("");

const { isLoading, error, getCron } = useMind();

const props = defineProps({
  activeTab: {
    type: String,
    default: "0",
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
});

const activeTabLocal = ref(props.activeTab);

async function generateCron() {
  try {
    const response = await getCron({ prompt: prompt.value });
    if (response && response.expression) {
      cronExpression.value = response.expression;
    } else {
      cronExpression.value = "Không nhận được kết quả hợp lệ";
    }
  } catch (err) {
    console.error("Lỗi khi gọi API:", err);
  }
}

defineEmits(["update:activeTab", "update:note"]);
</script>
