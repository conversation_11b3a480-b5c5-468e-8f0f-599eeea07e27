<template>
  <div class="group relative">
    <div
      v-show="!isEditing"
      :class="[
        'flex cursor-pointer items-center rounded-lg transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700',
        { 'border dark:border-gray-700': displayNextOccur.length > 0 },
      ]"
      @click="startEditing"
    >
      <div v-if="displayNextOccur.length > 0" class="flex w-full flex-col items-start p-2 text-sm">
        <div class="mb-1 flex w-full items-center justify-between">
          <div class="flex items-center">
            <i class="pi pi-calendar mr-2 text-gray-500" />
            <span class="font-semibold text-success">Đ<PERSON> thiết lập lịch nhắc lại công việc:</span>
            <Button
              v-tooltip.bottom="'Xóa lịch lặp lại'"
              class="p-button-rounded p-button-text p-button-sm ml-2 hidden h-5 w-5 !text-red-500 hover:!bg-red-100 group-hover:inline-flex"
              icon="pi pi-times"
              @click.stop="removeCronExpression"
            />
          </div>
        </div>
        <div v-for="(value, index) in displayNextOccur" :key="index" class="flex items-center">
          <span class="mr-2">{{ index + 1 }}.</span>
          <span class="first-letter:capitalize">{{ value }}</span>
        </div>
        <span>...</span>
      </div>
      <div v-else class="flex items-center text-gray-500">
        <div
          class="flex h-[30px] w-[30px] items-center justify-center rounded-full border border-dashed border-gray-300 text-xs font-medium"
        >
          <i class="pi pi-calendar text-gray-400" />
        </div>
        <span class="ml-2 text-sm text-gray-400">{{ placeholder }}</span>
      </div>
    </div>

    <div v-show="isEditing" class="flex flex-col gap-2">
      <div class="flex w-full gap-2">
        <InputText
          ref="inputRef"
          v-model="promptModel"
          class="flex-grow"
          placeholder="Ví dụ: Mỗi thứ 2 lúc 10:00 sáng"
          @blur="onInputBlur"
        />
        <Button
          v-tooltip.bottom="'Tạo lịch lặp lại'"
          :loading="isLoadingCron"
          class="flex-shrink-0"
          icon="pi pi-sparkles"
          @click.stop="generateCron"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useFocus, useVModel } from "@vueuse/core";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import { computed, nextTick, ref } from "vue";

import useMind from "@/hooks/useMind";
import { formatFriendlyDateTime, getNextCronOccurrences } from "@/utils/time-helper";

const props = withDefaults(
  defineProps<{
    modelValue: string | undefined;
    placeholder?: string;
  }>(),
  {
    placeholder: "Thêm lịch lặp lại",
  },
);

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const isEditing = ref(false);
const promptModel = ref("");
const cronExpression = useVModel(props, "modelValue", emit);
const inputRef = ref<HTMLInputElement | null>(null);
const { focused } = useFocus(inputRef, {
  initialValue: false,
});

const { isLoading: isLoadingCron, error: errorCron, getCron } = useMind();

const displayNextOccur = computed(() => {
  if (cronExpression.value) {
    const nextOccurrences = getNextCronOccurrences(cronExpression.value, 3);
    return nextOccurrences.map((date) => formatFriendlyDateTime(date.toISOString()));
  }
  return [];
});

async function startEditing() {
  isEditing.value = true;
  promptModel.value = "";
  await nextTick();
  focused.value = true;
}

function onInputBlur(event: FocusEvent) {
  if (
    !(event.relatedTarget && (event.relatedTarget as HTMLElement).classList.contains("p-button"))
  ) {
    if (!isLoadingCron.value) {
      isEditing.value = false;
      promptModel.value = "";
    }
  }
}

async function generateCron() {
  if (!promptModel.value.trim()) return;

  const response = await getCron({ prompt: promptModel.value });
  if (response && response.expression) {
    cronExpression.value = response.expression;
    isEditing.value = false;
    promptModel.value = "";
  }
}

function removeCronExpression(event: Event) {
  event.stopPropagation();
  cronExpression.value = "";
}
</script>
