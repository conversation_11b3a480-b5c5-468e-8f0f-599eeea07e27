<template>
  <div>
    <div class="flex space-x-3 divide-x divide-solid">
      <div class="flex flex-wrap gap-2">
        <!-- Task Filters -->
        <template v-for="filter in TASK_FILTERS" :key="filter.type">
          <button
            :class="[
              'inline-flex cursor-pointer items-center justify-center rounded-md border px-3 py-2 font-medium shadow-sm transition duration-200 disabled:cursor-not-allowed disabled:opacity-70 [&:hover:not(:disabled)]:border-opacity-90 [&:hover:not(:disabled)]:bg-opacity-90 [&:not(button)]:text-center',
              selectedFilterType === filter.type
                ? filter.type === TASK_VIEW_TYPES.SLOW_PROCESS
                  ? 'border-red-500 bg-red-500 text-white'
                  : filter.type === TASK_VIEW_TYPES.COMPLETE
                    ? 'border-blue-500 bg-blue-500 text-white'
                    : 'border-primary bg-primary text-white'
                : 'border-slate-300 text-slate-700 hover:bg-slate-100 dark:border-darkmode-300 dark:text-slate-300 dark:hover:bg-darkmode-400',
            ]"
            @click="() => handleSelectFilter(filter.type)"
          >
            <Lucide :icon="filter.icon" class="mr-2 h-4 w-4" />
            <span>{{ filter.name }}</span>
          </button>
        </template>
      </div>
      <div class="flex flex-wrap gap-2 pl-3">
        <!-- Priority Filters -->
        <template v-for="filter in PRIORITY_FILTERS" :key="filter.type">
          <button
            :class="[
              'inline-flex cursor-pointer items-center justify-center rounded-md border px-3 py-2 font-medium shadow-sm transition duration-200 disabled:cursor-not-allowed disabled:opacity-70 [&:hover:not(:disabled)]:border-opacity-90 [&:hover:not(:disabled)]:bg-opacity-90 [&:not(button)]:text-center',
              selectedFilterType === filter.type
                ? 'border-primary bg-primary text-white'
                : 'border-slate-300 text-slate-700 hover:bg-slate-100 dark:border-darkmode-300 dark:text-slate-300 dark:hover:bg-darkmode-400',
            ]"
            @click="() => handleSelectFilter(filter.type)"
          >
            <div class="flex items-center">
              <i
                :class="[
                  'pi',
                  'pi-flag-fill',
                  {
                    'text-blue-500': filter.value === TaskPriorityEnum.LOW,
                    'text-yellow-500': filter.value === TaskPriorityEnum.MEDIUM,
                    'text-red-500': filter.value === TaskPriorityEnum.HIGH,
                  },
                ]"
              />
            </div>
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";

import { TaskPriorityEnum } from "@/api/bcare-enum";
import Lucide from "@/base-components/Lucide";
import { useTaskStore } from "@/stores/task-store-v2";
import { PRIORITY_FILTERS, TASK_FILTERS, TASK_VIEW_TYPES, type TaskViewType } from "../constants";

// Setup
const taskStore = useTaskStore();

interface Props {
  modelValue?: TaskViewType;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:modelValue": [value: TaskViewType];
  "reload-data": [];
  "filter-changed": [value: TaskViewType];
}>();

// Selected filter type
const selectedFilterType = computed<TaskViewType>({
  get: () => props.modelValue || TASK_VIEW_TYPES.ALL,
  set: (value: TaskViewType) => emit("update:modelValue", value),
});

// Handle filter selection
const handleSelectFilter = (filterType: TaskViewType): void => {
  if (selectedFilterType.value !== filterType) {
    selectedFilterType.value = filterType;
    emit("filter-changed", filterType);
  }
};

// Initialize on mount - không tự động emit filter-changed nếu đã có modelValue
onMounted(() => {
  if (!props.modelValue) {
    selectedFilterType.value = TASK_VIEW_TYPES.ALL;
    // Chỉ emit khi cần thiết để tránh gọi API trùng lặp
    if (taskStore.tasks.length === 0) {
      emit("filter-changed", TASK_VIEW_TYPES.ALL);
    }
  }
});
</script>
