<script setup lang="ts">
import { ref } from "vue";

import { casbinRoleAdd } from "@/api/bcare";
import Button from "@/base-components/Button";
import FormInput from "@/base-components/Form/FormInput.vue";
import FormSelect from "@/base-components/Form/FormSelect.vue";
import { Dialog } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
const props = defineProps<{
  isOpen: boolean;
}>();
const emits = defineEmits<{
  (event: "close"): void;
  (event: "role-added"): void;
}>();

//API
const roleParent = ref("");
const roleName = ref("");
const roleDescription = ref("");
const addRole = async () => {
  try {
    await casbinRoleAdd({
      name: roleName.value,
      description: roleDescription.value,
      parent: roleParent.value,
    });

    emits("role-added");
    emits("close");
    resetForm();
  } catch (error) {
    console.error("Error adding role:", error);
  }
};

const resetForm = () => {
  roleName.value = "";
  roleDescription.value = "";
  roleParent.value = "";
};
</script>
<template>
  <Dialog size="lg" :open="props.isOpen" @close="emits('close')">
    <Dialog.Panel class="overflow-hidden bg-slate-100">
      <Dialog.Title class="bg-white p-5">
        <h2 class="mr-auto text-base font-medium">Thêm mới role</h2>
        <a href="#" class="absolute right-0 top-0 mr-3 mt-3" @click="emits('close')">
          <Lucide icon="X" class="h-8 w-8 text-slate-400" />
        </a>
      </Dialog.Title>
      <Dialog.Description class="bg-white">
        <div class="mb-3 flex items-center">
          <div class="mr-2 w-24 text-right">
            <span class="text-danger">*</span>
            Kế thừa
          </div>
          <div class="flex-1">
            <FormSelect v-model="roleParent" disabled>
              <option value="0"></option>
            </FormSelect>
          </div>
        </div>
        <div class="mb-3 flex items-center">
          <div class="mr-2 w-24 text-right">
            <span class="text-danger">*</span>
            Tên vai trò
          </div>
          <div class="flex-1">
            <FormInput v-model="roleName" type="text" />
          </div>
        </div>
        <div class="flex items-center">
          <div class="mr-2 w-24 text-right">Mô tả</div>
          <div class="flex-1">
            <FormInput v-model="roleDescription" type="text" />
          </div>
        </div>
      </Dialog.Description>
      <Dialog.Footer class="bg-white">
        <Button variant="outline-secondary" type="button" class="mr-1 w-24" @click="emits('close')">
          Hủy
        </Button>
        <Button variant="primary" type="button" class="w-24" @click="addRole"> Lưu </Button>
      </Dialog.Footer>
    </Dialog.Panel>
  </Dialog>
</template>
