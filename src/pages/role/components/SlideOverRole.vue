<script setup lang="ts">
import { ref } from "vue";

import Button from "@/base-components/Button";
import { FormCheck, FormInput } from "@/base-components/Form";
import { Slideover } from "@/base-components/Headless";
import { Disclosure } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
const checkTasks = ref<number[]>([]);
const props = defineProps<{
  isOpen: boolean;
}>();
const emits = defineEmits<{
  (event: "close"): void;
}>();
const fakeRole = [
  {
    key: 3,
    title: "Quản trị viên cấp cao",
    isChecked: false,
    items: [
      {
        key: 4,
        title: "Quản lý vai trò",
        isChecked: true,
      },
      {
        key: 9,
        title: "Chi tiết từ điển",
        isChecked: true,
      },
      {
        key: 5,
        title: "Quản lý menu",
        isChecked: true,
      },
    ],
  },
  {
    key: 6,
    title: "Quản lý API",
    isChecked: true,
    items: [],
  },
  {
    key: 7,
    title: "Quản lý người dùng",
    isChecked: true,
    items: [],
  },
  {
    key: 8,
    title: "Quản lý từ điển",
    isChecked: true,
    items: [],
  },
  {
    key: 10,
    title: "Lịch sử hoạt động",
    isChecked: false,
    items: [],
  },
];
</script>
<template>
  <Slideover :open="props.isOpen" @close="emits('close')">
    <!-- BEGIN: Slide Over Header -->
    <Slideover.Panel :not-backdrop="false">
      <a
        class="absolute left-0 right-auto top-0 -ml-12 mt-4"
        href="#"
        @click="
          (event: MouseEvent) => {
            event.preventDefault();
            emits('close');
          }
        "
      >
        <Lucide icon="X" class="h-8 w-8 text-slate-400" />
      </a>
      <Slideover.Title class="relative">
        <h2 class="mr-auto text-base font-medium">Gán quyền</h2>
        <FormInput placeholder="Tìm kiếm" class="w-[50%]" />
      </Slideover.Title>
      <!-- END: Slide Over Header -->
      <!-- BEGIN: Slide Over Body -->
      <Slideover.Description class="scrollbar-hidden p-3">
        <Disclosure.Group class="mt-5">
          <template v-for="(item, key) in fakeRole" :key="key">
            <Disclosure
              v-slot="{ open }"
              class="mb-2 rounded-md border-none p-0"
              :default-open="false"
            >
              <div class="flex items-center">
                <Disclosure.Button
                  :class="[
                    'my-0 rounded-md p-0 hover:bg-slate-200',
                    { 'pl-6': item.items && item.items.length === 0 },
                  ]"
                  @click.stop
                >
                  <div class="flex items-center p-1 font-medium md:items-center">
                    <Lucide
                      v-if="item.items && item.items.length > 0"
                      icon="ChevronDown"
                      :class="[
                        'mr-2 h-4 w-4 font-bold transition',
                        { 'rotate-180 transform': open },
                      ]"
                    />
                    <div class="flex-1 hover:bg-slate-200">
                      <FormCheck class="mr-4">
                        <FormCheck.Input
                          :id="`${key + 1}`"
                          class="border-slate-400 checked:border-primary"
                          type="checkbox"
                          @click.stop
                          @change="
                            (e: Event) => {
                              if ((e.target as HTMLInputElement).checked) {
                                checkTasks = item.items.map((item) => item.key);
                              } else checkTasks = [];
                            }
                          "
                        />
                        <FormCheck.Label :for="`${key + 1}`" class="flex-1" @click.stop>
                          {{ item.title }}
                        </FormCheck.Label>
                      </FormCheck>
                    </div>
                  </div>
                </Disclosure.Button>
              </div>
              <Disclosure.Panel
                v-if="item.items && item.items.length > 0"
                :class="['leading-none', { 'margin-custom': open }]"
              >
                <div
                  v-for="(childItem, childKey) in item.items"
                  :key="childKey"
                  class="mb-2 font-medium last:mb-0 md:items-center"
                >
                  <div class="rounded-md pl-12 hover:bg-slate-200">
                    <FormCheck class="mr-4 py-1">
                      <FormCheck.Input
                        :id="'child' + `${childKey + 1}`"
                        v-model="checkTasks"
                        class="border-slate-400 checked:border-primary"
                        type="checkbox"
                        :value="childItem.key"
                      />
                      <FormCheck.Label
                        :for="'child' + `${childKey + 1}`"
                        class="flex-1"
                        @click.stop
                      >
                        {{ childItem.title }}
                      </FormCheck.Label>
                    </FormCheck>
                  </div>
                </div>
              </Disclosure.Panel>
            </Disclosure>
          </template>
        </Disclosure.Group>
      </Slideover.Description>
      <!-- END: Slide Over Body -->
      <!-- BEGIN: Slide Over Footer -->
      <Slideover.Footer>
        <Button variant="primary" type="button" class="w-20"> Lưu </Button>
      </Slideover.Footer>
    </Slideover.Panel>
    <!-- END: Slide Over Footer -->
  </Slideover>
</template>
<style scoped>
.margin-custom {
  margin-top: 0.25rem !important;
}
</style>
