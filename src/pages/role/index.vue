<script setup lang="ts">
import { capitalize, onMounted, ref } from "vue";

import { casbinRoleList, Role } from "@/api/bcare";
import Button from "@/base-components/Button/Button.vue";
import { FormInput, FormSelect } from "@/base-components/Form";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Pagination from "@/base-components/Pagination";
import Table from "@/base-components/Table";
import useToggle from "@/hooks/useToggle";
import FormRole from "@/pages/role/components/FormRole.vue";
import SlideOverRole from "@/pages/role/components/SlideOverRole.vue";

const slideOverRoleToggle = useToggle(false);
const formRoleToggle = useToggle(false);

const handleShowSlideOver = () => {
  slideOverRoleToggle.show();
};
const handleShowFormRole = () => {
  formRoleToggle.show();
};

const roleList = ref<Role[]>([]);

//Fetch data
onMounted(async () => {
  await loadList();
});

const loadList = async () => {
  await fetchRoleList();
};

const fetchRoleList = async () => {
  try {
    const response = await casbinRoleList();
    if (response.code === 0 && Array.isArray(response.data)) {
      roleList.value = response.data.slice();
    } else {
      roleList.value = [];
    }
  } catch (error) {
    console.log("role list fetch error");
  }
};
</script>
<template>
  <h2 class="intro-y mt-10 text-lg font-medium">Danh sách vai trò</h2>
  <div class="mt-5 grid grid-cols-12 gap-6">
    <div class="intro-y col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
      <Button variant="primary" class="mr-2 shadow-md" @click="handleShowFormRole">
        Thêm mới
      </Button>
      <Menu>
        <Menu.Button :as="Button" class="!box px-2">
          <span class="flex h-5 w-5 items-center justify-center">
            <Lucide icon="Plus" class="h-4 w-4" />
          </span>
        </Menu.Button>
        <Menu.Items class="w-40">
          <Menu.Item> <Lucide icon="Printer" class="mr-2 h-4 w-4" /> Print </Menu.Item>
          <Menu.Item> <Lucide icon="FileText" class="mr-2 h-4 w-4" /> Export to Excel </Menu.Item>
          <Menu.Item> <Lucide icon="FileText" class="mr-2 h-4 w-4" /> Export to PDF </Menu.Item>
        </Menu.Items>
      </Menu>
      <div class="mx-auto hidden text-slate-500 md:block">Showing 1 to 10 of 150 entries</div>
      <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
        <div class="relative w-56 text-slate-500">
          <FormInput type="text" class="!box w-56 pr-10" placeholder="Tìm kiếm" />
          <Lucide icon="Search" class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4" />
        </div>
      </div>
    </div>
    <!-- BEGIN: Data List -->
    <div class="intro-y col-span-12 overflow-auto lg:overflow-visible">
      <Table class="-mt-2 border-separate border-spacing-y-[10px]">
        <Table.Thead>
          <Table.Tr>
            <Table.Th class="whitespace-nowrap border-b-0 uppercase"> ID </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 uppercase"> Tên vai trò </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center uppercase">
              Kế thừa
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <template v-for="(role, key) in roleList" :key="key">
            <Table.Tr class="intro-x hover:shadow-md">
              <Table.Td
                class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <span class="whitespace-nowrap">
                  {{ key + 1 }}
                </span>
              </Table.Td>
              <Table.Td
                class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <a href="" class="whitespace-nowrap font-medium">
                  {{ capitalize(role.name) }}
                </a>
                <div class="mt-0.5 whitespace-nowrap text-xs text-slate-500">
                  {{ role.description }}
                </div>
              </Table.Td>
              <Table.Td
                class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <a href="" class="whitespace-nowrap font-medium">
                  {{ capitalize(role.parent ?? "") }}
                </a>
              </Table.Td>
              <Table.Td
                class="relative w-[380px] border-b-0 bg-white py-0 shadow-[20px_3px_20px_#0000000b] before:absolute before:inset-y-0 before:left-0 before:my-auto before:block before:h-8 before:w-px before:bg-slate-200 first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600 before:dark:bg-darkmode-400"
              >
                <div class="flex items-center justify-end">
                  <div
                    class="mr-3 flex cursor-pointer items-center"
                    href="#"
                    @click="
                      (event: MouseEvent) => {
                        event.preventDefault();
                        handleShowFormRole();
                      }
                    "
                  >
                    <Lucide icon="Plus" class="mr-1 h-4 w-4" />
                    Sub role
                  </div>
                  <div class="mr-3 flex cursor-pointer items-center" @click="handleShowSlideOver">
                    <Lucide icon="Cog" class="mr-1 h-4 w-4" />
                    Gán quyền
                  </div>
                  <div class="mr-3 flex cursor-pointer items-center" @click="handleShowFormRole">
                    <Lucide icon="CheckSquare" class="mr-1 h-4 w-4" />
                    Chỉnh sửa
                  </div>
                  <div class="flex cursor-pointer items-center text-danger">
                    <Lucide icon="Trash2" class="mr-1 h-4 w-4" /> Xóa
                  </div>
                </div>
              </Table.Td>
            </Table.Tr>
          </template>
        </Table.Tbody>
      </Table>
    </div>
    <!-- END: Data List -->
    <!-- BEGIN: Pagination -->
    <div class="intro-y col-span-12 flex flex-wrap items-center sm:flex-row sm:flex-nowrap">
      <Pagination class="w-full sm:mr-auto sm:w-auto">
        <Pagination.Link>
          <Lucide icon="ChevronsLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>1</Pagination.Link>
        <Pagination.Link active>2</Pagination.Link>
        <Pagination.Link>3</Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronRight" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronsRight" class="h-4 w-4" />
        </Pagination.Link>
      </Pagination>
      <FormSelect class="!box mt-3 w-20 sm:mt-0">
        <option>10</option>
        <option>25</option>
        <option>35</option>
        <option>50</option>
      </FormSelect>
    </div>
    <!-- END: Pagination -->
  </div>

  <FormRole
    :is-open="formRoleToggle.isVisible.value"
    @close="formRoleToggle.hide"
    @role-added="loadList"
  />
  <SlideOverRole :is-open="slideOverRoleToggle.isVisible.value" @close="slideOverRoleToggle.hide" />
</template>
