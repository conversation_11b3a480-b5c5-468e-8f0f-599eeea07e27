<script setup lang="ts">
import Column from "primevue/column";
import DataTable from "primevue/datatable";
import { onMounted, ref } from "vue";

import { casbinPolicyUpdate, casbinSync, PolicyUpdateRequest } from "@/api/bcare";
import { FormInput } from "@/base-components/Form";

const permissions = ref<PolicyUpdateRequest[]>([]);
// const timeOut: Record<string, ReturnType<typeof setTimeout>> = {};

const getList = async () => {
  const res = await casbinSync();
  try {
    if (res.data) {
      const dataParse = JSON.parse(res.data.perm).p;
      if (Array.isArray(dataParse)) {
        permissions.value = dataParse.map(([type, name, operation, expr]) => {
          return {
            type: type || "",
            name: name || "",
            operation: operation || "",
            expr: expr === "true" ? "true" : "false",
          };
        });
      }
    }
  } catch (error) {
    permissions.value = [];
  }
};

const onCellEditComplete = (event: any) => {
  const { data } = event;
  casbinPolicyUpdate(data);
};

// const handleChange = (item: PolicyUpdateRequest, idx: number, isDebounce = true) => {
//   if (isDebounce && timeOut[idx]) {
//     clearTimeout(timeOut[idx]);
//     delete timeOut[idx];
//   }
//   if (isDebounce)
//     timeOut[idx] = setTimeout(() => {
//       casbinPolicyUpdate(item);
//     }, 300);
//   else casbinPolicyUpdate(item);
// };

const columns = ref([
  { field: "type", header: "Rule type" },
  { field: "name", header: "Role name" },
  { field: "operation", header: "Operation" },
  { field: "expr", header: "Expr" },
]);

onMounted(() => {
  getList();
});
</script>

<template>
  <h2 class="mt-10 text-lg font-medium">Permission list</h2>
  <div class="box mt-6 overflow-x-auto bg-white">
    <DataTable
      :value="permissions"
      edit-mode="cell"
      :pt="{
        table: { style: 'min-width: 50rem' },
        column: {
          bodycell: ({ state }: any) => ({
            class: [{ 'pt-0 pb-0': state['d_editing'] }],
          }),
        },
      }"
      @cell-edit-complete="onCellEditComplete"
    >
      <Column
        v-for="col of columns"
        :key="col.field"
        :field="col.field"
        :header="col.header"
        :style="{ width: col.field === 'expr' ? '40%' : col.field === 'type' ? '10%' : '20%' }"
      >
        <template #body="{ data, field }">
          {{ data[field] }}
        </template>
        <template #editor="{ data, field }">
          <template v-if="field === 'expr'">
            <FormInput v-model="data[field]" autofocus />
          </template>
          <template v-else>
            {{ data[field] }}
          </template>
        </template>
      </Column>
    </DataTable>
  </div>
</template>

<style scoped></style>
