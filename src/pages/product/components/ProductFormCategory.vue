<template>
  <div class="space-y-4">
    <div class="flex flex-col gap-4 md:flex-row">
      <FormField label="Loại sản phẩm" icon="pi pi-tag" class="flex-1">
        <Select
          v-model="formData.type"
          :options="[...PRODUCT_TYPE_OPTIONS]"
          optionLabel="name"
          optionValue="value"
          placeholder="Chọn loại sản phẩm"
          class="w-full"
          filter
        />
      </FormField>

      <FormField label="Danh mục" icon="pi pi-folder" class="flex-1">
        <Select
          v-model="formData.category_id"
          :options="categoryOptions"
          optionLabel="name"
          optionValue="id"
          placeholder="Chọn danh mục"
          class="w-full"
          :class="{ 'p-invalid': errors.category_id }"
          filter
        />
        <small class="p-error">{{ errors.category_id }}</small>
      </FormField>
    </div>

    <FormField label="Nhóm" icon="pi pi-users">
      <Select
        v-model="formData.group_id"
        :options="groupOptions"
        optionLabel="name"
        optionValue="id"
        placeholder="Chọn nhóm"
        class="w-full"
        filter
      />
    </FormField>

    <FormField label="Quy trình" icon="pi pi-cog">
      <MultiSelect
        v-model="selectedOperations"
        :options="operationOptions"
        optionLabel="name"
        optionValue="id"
        display="chip"
        placeholder="Chọn quy trình"
        :maxSelectedLabels="5"
        filter
        class="w-full"
        @change="updateFormDataOperations"
      />
    </FormField>
  </div>
</template>

<script setup lang="ts">
import MultiSelect from "primevue/multiselect";
import Select from "primevue/select";
import { computed, ref, watch } from "vue";

import type { OperationResponse, ProductAddRequest } from "@/api/bcare-types-v2";
import { PRODUCT_TYPE_OPTIONS } from "@/api/product";
import FormField from "@/components/Form/FormField.vue";
import useOperation from "@/hooks/useOperation";
import useTerm from "@/hooks/useTerm";

interface Props {
  formData: ProductAddRequest;
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();

const { getBundleTerms } = useTerm();
const { getAllOperations } = useOperation();

const categoryOptions = computed(() => {
  return getBundleTerms("danh_muc_sp").map((term) => ({
    id: term.id,
    name: term.name,
  }));
});

const groupOptions = computed(() => {
  return getBundleTerms("nhom_sp").map((term) => ({
    id: term.id,
    name: term.name,
  }));
});

// Track selected operations
const selectedOperations = ref<number[]>([]);

// Get all operations for the dropdown
const operationOptions = computed(() =>
  getAllOperations.value.map(({ id, name }) => ({ id, name })),
);

// Watch for existing operations in formData and set initial selection
watch(
  () => props.formData.operations,
  (newOps) => {
    selectedOperations.value = newOps?.map((op) => op.operation_id) || [];
  },
  { immediate: true },
);

// Update formData when operations selection changes
const updateFormDataOperations = () => {
  props.formData.operations = selectedOperations.value.map((id) => ({
    operation_id: id,
  }));
};
</script>
