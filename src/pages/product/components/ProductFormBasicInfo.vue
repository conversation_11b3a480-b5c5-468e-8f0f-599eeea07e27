<template>
  <div class="space-y-4">
    <FormField label="Tên sản phẩm" icon="pi pi-box">
      <InputText
        v-model="formData.name"
        placeholder="Nhập tên sản phẩm"
        class="w-full"
        :class="{ 'p-invalid': errors.name }"
      />
      <small class="p-error">{{ errors.name }}</small>
    </FormField>

    <div class="flex flex-col gap-4 sm:flex-row">
      <FormField label="Mã sản phẩm" icon="pi pi-hashtag" class="flex-1">
        <InputText
          v-model="formData.code"
          placeholder="Nhập mã sản phẩm"
          class="w-full"
          :class="{ 'p-invalid': errors.code }"
        />
        <small class="p-error">{{ errors.code }}</small>
      </FormField>

      <FormField label="SKU" icon="pi pi-tag" class="flex-1">
        <InputText v-model="formData.sku" placeholder="Nhập SKU" class="w-full" />
      </FormField>

      <FormField label="Loại sản phẩm" icon="pi pi-tags" class="flex-1">
        <SelectButton
          v-model="formData.collection"
          :options="collectionOptions"
          optionLabel="name"
          optionValue="value"
          multiple
          class="w-full"
        >
          <template #option="{ option }">
            <div class="flex items-center gap-2">
              <i :class="[option.icon, 'text-blue-500']"></i>
              <span class="font-medium">{{ option.name }}</span>
            </div>
          </template>
        </SelectButton>
      </FormField>
    </div>

    <FormField label="Mô tả" icon="pi pi-align-left">
      <Textarea
        v-model="formData.description"
        rows="3"
        placeholder="Nhập mô tả sản phẩm"
        class="w-full"
      />
    </FormField>

    <FormField label="Trạng thái" icon="pi pi-check-circle">
      <SelectButton
        v-model="formData.status"
        :options="statusOptions"
        optionLabel="label"
        optionValue="value"
        dataKey="value"
      >
        <template #option="{ option }">
          <div class="flex items-center gap-2">
            <i :class="[option.icon, option.value === 2 ? 'text-green-500' : 'text-gray-500']"></i>
            <span :class="[option.value === 2 ? 'text-green-600' : 'text-gray-600', 'font-medium']">
              {{ option.label }}
            </span>
          </div>
        </template>
      </SelectButton>
    </FormField>
  </div>
</template>

<script setup lang="ts">
import InputText from "primevue/inputtext";
import SelectButton from "primevue/selectbutton";
import Textarea from "primevue/textarea";

import type { ProductAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: ProductAddRequest;
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: ProductAddRequest): void;
}>();

const statusOptions = [
  {
    label: "Active",
    value: 2,
    icon: "pi pi-check-circle",
  },
  {
    label: "Inactive",
    value: 1,
    icon: "pi pi-ban",
  },
];

const collectionOptions = [
  {
    name: "Chính",
    value: "primary",
    icon: "pi pi-star-fill",
  },
  {
    name: "Phụ",
    value: "secondary",
    icon: "pi pi-star",
  },
];
</script>
