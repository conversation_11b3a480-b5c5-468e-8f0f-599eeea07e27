<template>
  <div class="space-y-2">
    <div class="flex flex-col gap-2 md:flex-row">
      <FormField label="Giá" icon="pi pi-dollar" class="w-full md:w-1/2">
        <InputNumber
          v-model="formData.price"
          :min="0"
          mode="currency"
          currency="VND"
          locale="vi-VN"
          class="w-full"
          :class="{ 'p-invalid': errors.price }"
        />
        <small class="p-error">{{ errors.price }}</small>
      </FormField>

      <div class="flex flex-col gap-2 sm:flex-row">
        <FormField label="Đơn vị tính" icon="pi pi-box" class="flex-1">
          <Select
            v-model="formData.unit_id"
            :options="unitOptions"
            optionLabel="name"
            optionValue="id"
            placeholder="Chọn đơn vị tính"
            :class="{ 'p-invalid': errors.unit_id }"
            filter
            class="flex h-full w-full items-center"
          />
          <small class="p-error">{{ errors.unit_id }}</small>
        </FormField>

        <FormField label="Số lượng" icon="pi pi-sort-numeric-up" class="flex-1">
          <InputNumber
            v-model="formData.quantity"
            :min="0"
            :step="1"
            showButtons
            class="w-full"
            placeholder="Nhập số lượng"
          />
        </FormField>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import InputNumber from "primevue/inputnumber";
import Select from "primevue/select";
import { computed } from "vue";

import type { ProductAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";
import useTerm from "@/hooks/useTerm";

interface Props {
  formData: ProductAddRequest;
  errors: Partial<Record<string, string>>;
}

defineProps<Props>();

const { getBundleTerms } = useTerm();

const unitOptions = computed(() => {
  return getBundleTerms("don_vi").map((term) => ({
    id: term.id,
    name: term.name,
  }));
});
</script>
