<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, onMounted, ref, watch } from "vue";

import { CallKind, FilterOperator } from "@/api/bcare-enum";
import { Call, CallResponse, Filter } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import { ColumnDefinition, DataTable } from "@/components/DataTable";
import { DateTimeInfo, NoteInfo } from "@/components/InfoText";
import { PersonCard, PersonPhone } from "@/components/Person";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useCall } from "@/hooks/useCall";
import { useCallQuery } from "@/hooks/useCallQuery";
import { useFilterQuery } from "@/hooks/useFilterQuery";
import { formatTimer } from "@/utils/helper";

// Stores & Hooks
const {
  calls,
  total,
  dynamicQuery,
  fetchCalls,
  isLoading,
  stateStats,
  fetchStateStats,
  rowsPerPage,
} = useCallQuery();
const { updateCallFeedback } = useCall();

// States
const showFilter = ref(false);
const dateFilter = ref<Filter[]>([]);
const currentCallState = ref<CallState>("all");
const currentPage = ref(1);
const first = ref(0);

// Define CallState type
type CallState = "inbound" | "outbound" | "miss_inbound" | "miss_outbound" | "all";

// Add this near other type definitions
const CALL_STATES = {
  INBOUND_ANSWERED: "inbound_answered",
  INBOUND_MISSED: "inbound_missed",
  OUTBOUND_ANSWERED: "outbound_answered",
  OUTBOUND_MISSED: "outbound_missed",
  UNDEFINED: "undefined",
} as const;

const getStateConfig = (direction: string, state: string) => {
  const isInbound = direction === "inbound";
  const callLabel = isInbound ? "Cuộc gọi đến" : "Cuộc gọi đi";

  const configs: Record<
    (typeof CALL_STATES)[keyof typeof CALL_STATES],
    {
      icon: "PhoneIncoming" | "PhoneMissed" | "PhoneOutgoing";
      color: string;
      label: string;
      subLabel: string;
    }
  > = {
    [CALL_STATES.INBOUND_ANSWERED]: {
      icon: "PhoneIncoming",
      color: "text-success",
      label: callLabel,
      subLabel: "Đã trả lời",
    },
    [CALL_STATES.INBOUND_MISSED]: {
      icon: "PhoneMissed",
      color: "text-danger",
      label: callLabel,
      subLabel: "Nhỡ",
    },
    [CALL_STATES.OUTBOUND_ANSWERED]: {
      icon: "PhoneOutgoing",
      color: "text-info",
      label: callLabel,
      subLabel: "Đã kết nối",
    },
    [CALL_STATES.OUTBOUND_MISSED]: {
      icon: "PhoneMissed",
      color: "text-danger",
      label: callLabel,
      subLabel: "Không trả lời",
    },
    [CALL_STATES.UNDEFINED]: {
      icon: isInbound ? "PhoneIncoming" : "PhoneOutgoing",
      color: isInbound ? "text-success" : "text-info",
      label: callLabel,
      subLabel: "Chưa xác định",
    },
  };

  return configs[state as keyof typeof configs] || configs[CALL_STATES.UNDEFINED];
};

// Stats cards configuration
const statsCards = computed(() => [
  {
    icon: "PhoneCall",
    total: stateStats.value?.inbound ?? 0,
    percentage: 33,
    label: "Tổng gọi đến",
    isActive: currentCallState.value === "inbound",
    tooltipContent: "33% Higher than last month",
    state: "inbound" as const,
  },
  {
    icon: "PhoneOutgoing" as const,
    total: stateStats.value?.outbound ?? 0,
    percentage: -2,
    label: "Tổng gọi đi",
    isActive: currentCallState.value === "outbound",
    tooltipContent: "2% Lower than last month",
    state: "outbound" as const,
  },
  {
    icon: "PhoneMissed" as const,
    total: stateStats.value?.miss_inbound ?? 0,
    percentage: 12,
    label: "Tổng gọi đến nhỡ",
    isActive: currentCallState.value === "miss_inbound",
    tooltipContent: "12% Higher than last month",
    state: "miss_inbound" as const,
  },
  {
    icon: "PhoneMissed" as const,
    total: stateStats.value?.miss_outbound ?? 0,
    percentage: 22,
    label: "Tổng gọi đi nhỡ",
    isActive: currentCallState.value === "miss_outbound",
    tooltipContent: "22% Higher than last month",
    state: "miss_outbound" as const,
  },
]);

// Add after currentPage declaration
// Function to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
  dynamicQuery.offset = 0;
  first.value = 0;
};

// Handlers
const handlePageChange = (event: { first: number; rows: number }) => {
  first.value = event.first;
  dynamicQuery.offset = event.first;
  dynamicQuery.limit = event.rows;
  currentPage.value = Math.floor(event.first / event.rows) + 1;
  loadCalls();
};

const mergeFilters = (baseFilters: any[], newFilters: any[]) => {
  const statusFilter = baseFilters.find((f) => f.field === "status");
  const otherFilters = newFilters.filter((f) => f.field !== "status");
  return [statusFilter, ...otherFilters].filter(Boolean);
};

// Thêm biến để lưu trữ tổng số cuộc gọi
const totalAllCalls = ref(0);

// Sửa lại hàm loadCalls để cập nhật totalAllCalls khi cần
const loadCalls = async (getCount: boolean = true) => {
  const queryParams = {
    ...dynamicQuery,
    ...currentFilterPayload.value,
    filters: mergeFilters(dynamicQuery.filters || [], [
      ...(currentFilterPayload.value?.filters || []),
      ...dateFilter.value,
    ]),
    state: currentCallState.value !== "all" ? currentCallState.value : undefined,
  };

  const kindFilterValue = filters.value.kind?.value;
  if (kindFilterValue === CallKind.UNDEFINED.value) {
    queryParams.filters = queryParams.filters.filter(
      (filter) =>
        !(filter.field === "kind" && filter.operator === FilterOperator.EQ && filter.value === ""),
    );
    // Thêm filter ISNULL vào filters
    queryParams.filters.push({
      field: "kind",
      operator: FilterOperator.ISNULL,
      logic: "AND",
    });
  }

  await fetchCalls(queryParams, getCount);

  // Nếu đang xem tất cả cuộc gọi, cập nhật tổng số
  if (currentCallState.value === "all" && getCount) {
    totalAllCalls.value = total.value;
  }
};

const handleUpdateFeedback = async (call: Call, field: string = "rating") => {
  await updateCallFeedback({
    id: call.id,
    [field]: call[field as keyof Call],
    modified: [field],
  });
  loadCalls(false); // Don't need to get count when updating feedback
};

const handleUpdateNotes = async (call: Call, newNotes: string) => {
  await updateCallFeedback({
    id: call.id,
    feedback: newNotes,
    modified: ["feedback"],
  });
  loadCalls(false);
};

// Tạo mảng options cho dropdown
const callKindOptions = Object.values(CallKind);

// Khi cập nhật
const handleUpdateKind = async (call: Call, newKind: any) => {
  await updateCallFeedback({
    id: call.id,
    kind: newKind,
    modified: ["kind"],
  });
  loadCalls(false);
};

// Column definitions
const filterConfigs = {
  "user.name": {
    field: "creator",
    isPayload: true,
    operator: FilterOperator.LIKE,
  },
  "person.full_name": {
    field: "person",
    isPayload: true,
    operator: FilterOperator.LIKE,
  },
  start_time: {
    field: "start_time",
    operator: FilterOperator.GTE,
    isDateRange: true,
  },
  kind: {
    field: "kind",
    operator: FilterOperator.EQ,
    valueTransform: (value: string) => {
      if (value !== CallKind.UNDEFINED.value) {
        return value;
      }
      return "";
    },
  },
};

// Sửa options cho select kind
const kindOptions = [
  { title: "Tất cả", value: "" },
  { title: "Đã xử lý", value: CallKind.PROCESSED.value },
  { title: "Không nghe máy", value: CallKind.UNHEARD.value },
  { title: "Thuê bao", value: CallKind.RENTAL.value },
  { title: "Test tổng đài", value: CallKind.TEST.value },
  { title: "Chưa xử lý", value: CallKind.UNDEFINED.value },
];

const columns = computed<ColumnDefinition<CallResponse>[]>(() => [
  {
    field: "person.full_name",
    header: "Khách hàng",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm kiếm khách hàng",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    field: "user.name",
    header: "Người thực hiện",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "text",
    filterPlaceholder: "Tìm kiếm người thực hiện",
    filterMatchMode: FilterMatchMode.CONTAINS,
  },
  {
    field: "state",
    header: "Trạng thái",
    sortable: false,
    showFilterMenu: false,
  },
  {
    field: "start_time",
    header: "Thời gian",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "dateRange",
    filterPlaceholder: "Chọn thời gian",
  },
  {
    field: "feedback",
    header: "Ghi chú",
    sortable: false,
    showFilterMenu: false,
  },
  {
    field: "rating",
    header: "Xếp hạng",
    sortable: false,
    showFilterMenu: false,
  },
  {
    field: "recording_file",
    header: "Ghi âm",
    sortable: false,
    showFilterMenu: false,
  },
  {
    field: "kind",
    header: "Trạng thái",
    sortable: false,
    showFilterMenu: showFilter.value,
    filterType: "select",
    filterOptions: kindOptions,
    filterPlaceholder: "Chọn trạng thái",
  },
]);

// Modify useFilterQuery callback
const { filters, currentFilterPayload } = useFilterQuery(() => {
  // Reset pagination when filters change
  resetPagination();
  loadCalls();
}, filterConfigs);

// Thêm computed property cho options của SelectButton
const selectButtonOptions = computed(() => [
  {
    label: "Tất cả",
    state: "all",
    icon: "Phone" as const,
    total: totalAllCalls.value,
  },
  ...statsCards.value,
]);

watch(currentCallState, () => {
  resetPagination();
  loadCalls();
});

// Thêm hàm để xác định severity của Badge dựa trên kind
const getKindSeverity = (kind: any): string => {
  if (!kind) return "secondary";

  switch (kind.value || kind) {
    case "PROCESSED":
      return "success";
    case "UNHEARD":
      return "warning";
    case "RENTAL":
      return "info";
    case "TEST":
      return "secondary";
    default:
      return "secondary";
  }
};

// Thêm hàm để tìm label từ value
const findKindLabel = (value: string): string => {
  const option = Object.values(CallKind).find((kind) => kind.value === value);
  return option?.label || "";
};

onMounted(async () => {
  await fetchStateStats();
  loadCalls();
});
</script>

<template>
  <div class="intro-y">
    <!-- Main Content -->
    <div class="mt-5">
      <DataTable
        title="Danh sách cuộc gọi"
        :columns="columns"
        :data="calls"
        :loading="isLoading"
        :total-records="total"
        paginator
        :rows="rowsPerPage"
        :rows-per-page-options="[10, 20, 50, 100]"
        :first="first"
        v-model:filters="filters"
        @page="handlePageChange"
        size="small"
        :page="currentPage"
      >
        <template #left-header>
          <div class="flex flex-col gap-2">
            <!-- <span class="text-base font-medium">Danh sách cuộc gọi ({{ total }})</span> -->
            <div>
              <SelectButton
                v-model="currentCallState"
                :options="selectButtonOptions"
                optionLabel="label"
                optionValue="state"
                size="small"
                class="call-filter-buttons"
              >
                <template #option="slotProps">
                  <div class="flex items-center gap-1">
                    <Lucide :icon="slotProps.option.icon" class="h-4 w-4" />
                    <span>{{ slotProps.option.label }}</span>
                    <Badge
                      :value="slotProps.option.total"
                      :severity="currentCallState === slotProps.option.state ? 'info' : 'secondary'"
                    />
                  </div>
                </template>
              </SelectButton>
            </div>
          </div>
        </template>

        <template #user.name="{ data }">
          <div class="flex items-center gap-2">
            <UserAvatar :user="data.user" />
            <div class="flex flex-col">
              <span class="font-medium">{{ data.user?.name }}</span>
              <span class="text-xs text-slate-500">
                Line: {{ data.direction === "inbound" ? data.destination : data.source }}
              </span>
            </div>
          </div>
        </template>

        <template #person.full_name="{ data }">
          <PersonCard
            :person="data.person"
            submit-type="new-tab"
            :query-params="{ tab: 'appointment' }"
          >
            <template #bottom-info>
              <PersonPhone
                :person="{
                  ...data.person,
                  phone: data.direction === 'inbound' ? data.source : data.destination,
                }"
              />
            </template>
          </PersonCard>
        </template>

        <template #state="{ data }">
          <div class="flex items-center gap-1">
            <div
              :class="[
                'flex h-9 w-9 items-center justify-center rounded-full',
                getStateConfig(data.direction, data.state).color.replace('text-', 'bg-'),
                'bg-opacity-20',
              ]"
            >
              <Lucide
                :icon="getStateConfig(data.direction, data.state).icon"
                :class="['h-5 w-5', getStateConfig(data.direction, data.state).color]"
              />
            </div>
            <div class="flex flex-col">
              <span class="font-medium">
                {{ getStateConfig(data.direction, data.state).label }}
              </span>
              <span class="text-xs text-slate-500">
                {{ getStateConfig(data.direction, data.state).subLabel }}
              </span>
            </div>
          </div>
        </template>

        <template #start_time="{ data }">
          <div class="flex flex-col">
            <span><DateTimeInfo :date="data.start_time" format="DD/MM/YYYY, HH:mm" /></span>
            <span class="mt-0.5 flex items-center text-xs text-slate-500">
              <i class="pi pi-clock mr-1 text-xs" />
              {{ formatTimer(+data.duration) }}
            </span>
          </div>
        </template>

        <template #feedback="{ data }">
          <NoteInfo
            :notes="data.feedback"
            mode="tag"
            @update:notes="(newFeedback) => handleUpdateNotes(data, newFeedback)"
            :editable="true"
          />
        </template>

        <template #rating="{ data }">
          <div class="flex">
            <Lucide
              v-for="star in [1, 2, 3, 4, 5]"
              :key="star"
              icon="Star"
              :class="['mr-2 h-5 w-5 cursor-pointer', { 'text-warning': star <= data.rating }]"
              @click="
                () => {
                  data.rating = star;
                  handleUpdateFeedback(data);
                }
              "
            />
          </div>
        </template>

        <template #recording_file="{ data }">
          <div v-if="!data.recording_file" class="flex items-center gap-2">
            <Lucide icon="MicOff" class="h-5 w-5 text-slate-500" />
            Không có bản ghi âm
          </div>
          <div v-else class="flex items-center gap-2">
            <audio controls :src="data.recording_file" class="h-10" />
          </div>
        </template>

        <template #kind="{ data }">
          <div class="flex items-center">
            <Select
              v-model="data.kind"
              :options="callKindOptions"
              optionLabel="label"
              optionValue="value"
              placeholder="Chọn trạng thái"
              @change="() => handleUpdateKind(data, data.kind)"
            >
              <template #value="slotProps">
                <div v-if="slotProps.value" class="flex items-center gap-2">
                  <Badge
                    :value="findKindLabel(slotProps.value) || 'Chọn trạng thái'"
                    :severity="getKindSeverity(slotProps.value)"
                  />
                </div>
                <span v-else>
                  {{ slotProps.placeholder }}
                </span>
              </template>
              <template #option="slotProps">
                <div class="flex items-center gap-2">
                  <Badge
                    :value="slotProps.option.label"
                    :severity="getKindSeverity(slotProps.option.value)"
                  />
                </div>
              </template>
            </Select>
          </div>
        </template>
      </DataTable>
    </div>
  </div>
</template>
