import { AppointmentDynamicQuery } from "@/api/bcare-types";
import { parseExtraNotes } from "@/pages/customer/components/AppointmentTab/utils";

export const PUNCTUALLY = 3;
export const ReminderStatus = {
  REMINDED: 1,
  NOT_REMINDED: 0,
};
export const filterAppointmentDefault = {
  page_size: 0,
  page: 0,
  filter: {
    person_id: 0,
    doctor_id: 0,
    status: 0,
    type: 0,
  },
  from_date: "",
  to_date: "",
  order_by: "",
};
export const appointmentQueryDefault = {
  table: "appointment",
  selects: [],
  filters: [
    {
      logic: "AND",
      conditions: [],
    },
  ],
  group_by: [],
  aggregations: [],
  sort: [
    {
      field: "created_at",
      order: "DESC",
    },
  ],
  limit: 0,
  offset: 0,
};
export const defaultAppointmentDynamicQuery: AppointmentDynamicQuery = {
  table: "appointment",
  selects: [],
  filters: undefined,
  group_by: [],
  aggregations: [],
  sort: [
    {
      field: "start_time",
      order: "ASC",
    },
  ],
  limit: 10,
  offset: 0,
  doctor: "",
  person: "",
  task: "",
  note: "",
  arrived: "both",
};

export const getExpectedTask = (expectedTask: string) => {
  const extraNotes = parseExtraNotes(expectedTask);
  return extraNotes.expectedTask;
};
