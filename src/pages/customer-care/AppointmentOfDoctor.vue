<script setup lang="ts">
import Appointments from "@/pages/customer-care/Appointments.vue";
import { useResponsive } from "@/composables/useResponsive";
import AppointmentTabMobile from "@/pages/persons/components/mobile/tabs/AppointmentTabMobile.vue";

const { isTablet, isMobile } = useResponsive();
</script>

<template>
  <AppointmentTabMobile v-if="isTablet || isMobile" :isMainPage="true" />
  <Appointments v-else hasDoctor="yes" title="Lịch hẹn của tôi" :useDoctor="true" />
</template>
