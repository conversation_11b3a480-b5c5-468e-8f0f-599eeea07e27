<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { useDateFormat } from "@vueuse/core";
import { computed, defineAsyncComponent,ref } from "vue";

import { FilterOperator } from "@/api/bcare-enum";
import { AppointmentResponse } from "@/api/bcare-types-v2";
import Lucide from "@/base-components/Lucide";
import Term from "@/base-components/Term/Term.vue";
import { ColumnDefinition } from "@/components/DataTable";
import { UserAvatar } from "@/components/User";
import useConstant from "@/hooks/useConstant";
import useTerm from "@/hooks/useTerm";
import useUser from "@/hooks/useUser";

import Appointments from "./Appointments.vue";

// Tải StatusChangePopover không đồng bộ
const StatusChangePopover = defineAsyncComponent(
  () => import("@/components/Appointment/StatusChangePopover.vue"),
);

// Get constants
const { getConstants } = useConstant();

// Get user data
const { getUserById, users, getUsersByDepartmentIds } = useUser({ autoLoad: true });

// Get term data for source
const { getBundleTerms } = useTerm();

// Get users by department (assuming department ID 10 is for sales/creators)
const departmentUsers = computed(() =>
  users.value.length > 0 ? getUsersByDepartmentIds([10]) : [],
);

// Date formatters
const timeFormat = (date: string) => useDateFormat(date, "HH:mm").value;
const dateFormat = (date: string) => useDateFormat(date, "DD/MM/YYYY").value;

// Reference to the Appointments component
const appointmentsComponent = ref<InstanceType<typeof Appointments> | null>(null);

// Define custom columns for offline appointments
const customColumns = computed(
  () =>
    [
      {
        field: "person.full_name",
        header: "Khách hàng",
        sortable: false,
        showFilterMenu: false,
        filterType: "text",
        filterPlaceholder: "Tìm kiếm khách hàng",
        filterMatchMode: FilterMatchMode.CONTAINS,
      },
      {
        field: "person.source_id",
        header: "Nguồn khách hàng",
        sortable: false,
        showFilterMenu: false,
        filterType: "select",
        filterPlaceholder: "Chọn nguồn KH",
        filterMatchMode: FilterMatchMode.CONTAINS,
        filterOptions:
          getBundleTerms("nguon")?.map((term) => ({
            title: term.name,
            value: term.id,
          })) || [],
      },
      {
        field: "creator_id",
        header: "Người tạo",
        sortable: false,
        showFilterMenu: false,
        filterType: "select",
        filterPlaceholder: "Chọn người tạo",
        filterMatchMode: FilterMatchMode.CONTAINS,
        filterOptions:
          departmentUsers.value.map((user) => ({
            title: user.name,
            value: user.id,
          })) || [],
      },
      {
        field: "appointment_date",
        header: "Ngày hẹn",
        sortable: false,
        showFilterMenu: false,
      },
      {
        field: "appointment_time",
        header: "Giờ hẹn",
        sortable: false,
        showFilterMenu: false,
      },
      {
        field: "notes",
        header: "Ghi chú",
        sortable: false,
        showFilterMenu: false,
        filterType: "text",
        filterPlaceholder: "Tìm kiếm ghi chú",
        filterMatchMode: FilterMatchMode.CONTAINS,
      },

      {
        field: "status",
        header: "Trạng thái",
        sortable: false,
        showFilterMenu: false,
        filterType: "select",
        filterPlaceholder: "Chọn trạng thái",
        filterMatchMode: FilterMatchMode.EQUALS,
        filterOptions: [
          { title: "Huỷ hẹn", value: "1" },
          { title: "Hẹn mới", value: "2" },
          { title: "Đúng hẹn", value: "3" },
          { title: "Trễ hẹn", value: "4" },
          { title: "Đổi hẹn", value: "5" },
          { title: "Phát sinh", value: "6" },
        ],
      },
    ] as ColumnDefinition<AppointmentResponse>[],
);

// Define custom filter configurations
const customFilterConfigs = {
  "person.full_name": {
    field: "person",
    isPayload: true,
  },
  "person.source_id": {
    field: "source_id",
    operator: FilterOperator.EQ,
    isPayload: true,
    valueTransform: (v: number | string | undefined) => {
      if (v === undefined || v === "") {
        return undefined;
      }
      return Number(v);
    },
  },
  creator_id: {
    field: "creator_id",
    operator: FilterOperator.EQ,
    valueTransform: (v: string | number | undefined) => {
      if (v === undefined || v === "") {
        return undefined;
      }
      return `${v}`;
    },
  },
  status: {
    field: "status",
    operator: FilterOperator.EQ,
  },
  notes: {
    field: "note",
    operator: FilterOperator.LIKE,
    isPayload: true,
  },
};

// Handle status change
const handleStatusChanged = () => {
  if (appointmentsComponent.value) {
    appointmentsComponent.value.loadAppointments(true);
  }
};
</script>

<template>
  <Appointments
    ref="appointmentsComponent"
    hasDoctor="no"
    title="Lịch hẹn lý thuyết"
    :customColumns="customColumns"
    :customFilterConfigs="customFilterConfigs"
  >
    <!-- Custom template for appointment date -->
    <template #appointment_date="{ data }">
      <div class="flex items-center">
        <Lucide icon="CalendarDays" class="mr-1 h-4 w-4 text-warning" />
        <span class="text-sm">
          {{ dateFormat(data.end_time) }}
        </span>
      </div>
    </template>

    <!-- Custom template for appointment time -->
    <template #appointment_time="{ data }">
      <div class="flex items-center whitespace-nowrap">
        <Lucide icon="Clock" class="mr-1 h-4 w-4 text-yellow-500" />
        <span class="text-sm">
          {{ timeFormat(data.start_time) }} - {{ timeFormat(data.end_time) }}
        </span>
      </div>
    </template>

    <!-- Custom template for person source_id -->
    <template #person.source_id="{ data }">
      <div class="flex items-center">
        <Term collection-key="Tailwind300" size="sm" variant="soft" v-if="data.person?.source_id">
          {{
            getBundleTerms("nguon")?.find((term) => term.id === data.person?.source_id)?.name || "-"
          }}
        </Term>
        <span class="text-sm" v-else>-</span>
      </div>
    </template>

    <!-- Custom template for creator_id -->
    <template #creator_id="{ data }">
      <div class="flex items-center gap-2">
        <UserAvatar :userId="data.creator_id" size="small" />
        <span class="text-sm font-medium">{{ getUserById(data.creator_id)?.name || "-" }}</span>
      </div>
    </template>

    <!-- Custom template for status using the reusable component -->
    <template #status="{ data }">
      <StatusChangePopover
        :appointment="data"
        :statusLabels="getConstants?.appointment_status || {}"
        @status-changed="handleStatusChanged"
      />
    </template>
  </Appointments>
</template>
