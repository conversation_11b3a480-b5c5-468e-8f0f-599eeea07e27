<script setup lang="ts">
import { useDateFormat } from "@vueuse/core";
import DatePicker from "primevue/datepicker";
import { computed, ref, watch } from "vue";

import { Filter } from "@/api/bcare-types-v2";

interface Props {
  field?: string;
  initialRange?: [Date, Date];
}

const props = withDefaults(defineProps<Props>(), {
  field: "start_time",
  initialRange: () => [new Date(), new Date()],
});

const emit = defineEmits<{
  (event: "update:filters", filters: Filter[]): void;
}>();

const dateRange = ref<Date[]>(props.initialRange);

const dateFilters = computed<Filter[]>(() => {
  // Nếu không có giá trị nào
  if (!dateRange.value?.[0]) return [];

  const fromDate = new Date(dateRange.value[0]);
  // Nếu chỉ chọn 1 ngày hoặc ngày thứ 2 chưa được chọn
  const toDate = dateRange.value[1] ? new Date(dateRange.value[1]) : new Date(fromDate);

  // Set time cho fromDate là 00:00:00
  fromDate.setHours(0, 0, 0, 0);
  
  // Set time cho toDate là 23:59:59.999
  toDate.setHours(23, 59, 59, 999);

  // Format dates using useDateFormat
  const fromISOString = useDateFormat(fromDate, "YYYY-MM-DDTHH:mm:ssZ").value;
  const toISOString = useDateFormat(toDate, "YYYY-MM-DDTHH:mm:ssZ").value;

  return [
    {
      logic: "AND",
      field: props.field,
      operator: "GTE",
      value: `time(${fromISOString})`,
    },
    {
      logic: "AND",
      field: props.field,
      operator: "LT",
      value: `time(${toISOString})`,
    },
  ];
});

watch(
  dateFilters,
  (newFilters) => {
    emit("update:filters", newFilters);
  },
  { immediate: true },
);
</script>

<template>
  <div class="w-full">
    <DatePicker
      v-model="dateRange"
      selectionMode="range"
      :inline="true"
      :showTime="false"
      :showButtonBar="false"
      fluid
      :panelStyle="{ padding: '0px', margin: '0px', overflow: 'hidden', width: '100%' }"
      class="w-full [&_.p-datepicker-calendar]:!w-full [&_.p-datepicker]:!w-full [&_.p-datepicker_table]:!w-full [&_td]:!p-0.5 [&_th]:!p-0.5"
      selectOtherMonths
    />
  </div>
</template>
