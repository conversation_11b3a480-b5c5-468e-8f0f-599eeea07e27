<script setup lang="ts">
import { defineAsyncComponent, watch, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { useBreakpoints, breakpointsTailwind } from "@vueuse/core";

import LoadingIcon from "@/base-components/LoadingIcon";
import Empty from "@/base-components/Empty";
import usePerson from "@/hooks/usePerson";

const CustomerProfile = defineAsyncComponent(() => import("@/pages/customer/CustomerProfile.vue"));
const PersonProfileMobile = defineAsyncComponent(
  () => import("./components/mobile/PersonProfileMobile.vue"),
);

const route = useRoute();

const breakpoints = useBreakpoints(breakpointsTailwind);
const isDesktop = breakpoints.greaterOrEqual("lg");

const { currentPerson, isLoading, error, getPerson } = usePerson();

const numericPersonId = computed(() => {
  const idFromRoute = route.params.id;
  if (idFromRoute) {
    const id = parseInt(Array.isArray(idFromRoute) ? idFromRoute[0] : idFromRoute, 10);
    return isNaN(id) ? null : id;
  }
  return null;
});

const loadPersonData = async () => {
  if (numericPersonId.value !== null && !isDesktop.value) {
    await getPerson({ id: numericPersonId.value });
  }
};

onMounted(loadPersonData);

watch([numericPersonId, isDesktop], loadPersonData);
</script>

<template>
  <div v-if="isDesktop">
    <Suspense>
      <CustomerProfile />
      <template #fallback>
        <div class="flex h-[50vh] items-center justify-center">
          <LoadingIcon icon="three-dots" class="h-12 w-12" />
        </div>
      </template>
    </Suspense>
  </div>
  <div v-else>
    <div v-if="isLoading" class="flex h-[50vh] items-center justify-center">
      <LoadingIcon icon="three-dots" class="h-12 w-12" />
    </div>
    <div v-else-if="error">
      <Empty
        :message="
          (error as any)?.message || error || 'Đã có lỗi xảy ra khi tải dữ liệu người dùng.'
        "
      />
    </div>
    <Suspense v-else-if="currentPerson">
      <PersonProfileMobile :person="currentPerson" />
      <template #fallback>
        <div class="flex h-[50vh] items-center justify-center">
          <LoadingIcon icon="three-dots" class="h-12 w-12" />
        </div>
      </template>
    </Suspense>
    <div v-else>
      <Empty message="Không có dữ liệu người dùng hoặc ID không hợp lệ." />
    </div>
  </div>
</template>
