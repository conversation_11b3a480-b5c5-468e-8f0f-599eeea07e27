<script setup lang="ts">
import { mobilePersonTabs } from "@/config/person-tabs.config";
import { ref, PropType, computed } from "vue";

const props = defineProps({
  personId: {
    type: Number as PropType<number | null | undefined>,
    default: null,
  },
  initialTabKey: {
    type: String as PropType<string | null>,
    default: null,
  },
});

const activeTab = ref<string>(
  props.initialTabKey || (mobilePersonTabs.length > 0 ? mobilePersonTabs[0].key : ""),
);

const tabs = computed(() => {
  return mobilePersonTabs.map((tab) => ({
    ...tab,
    isActive: tab.key === activeTab.value,
  }));
});
</script>

<template>
  <Tabs v-model:value="activeTab" scrollable lazy class="bg-white">
    <TabList>
      <Tab v-for="tab in tabs" :key="tab.key" :value="tab.key" class="p-3">
        <div class="flex items-center gap-2">
          <i :class="[tab.icon, 'text-xl']" />
          <span v-if="tab.isActive">{{ tab.label }}</span>
        </div>
      </Tab>
    </TabList>

    <TabPanels class="!px-0 pt-1">
      <TabPanel v-for="tabConfig in mobilePersonTabs" :key="tabConfig.key" :value="tabConfig.key">
        <component :is="tabConfig.component" :person-id="personId" />
      </TabPanel>
    </TabPanels>
  </Tabs>
</template>
