<script setup lang="ts">
import { ref, PropType, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";
import Button from "primevue/button";
import Menu from "primevue/menu";
import ScrollToTop from "@/base-components/ScrollToTop";

import type { PersonResponse } from "@/api/bcare-types-v2";
import Empty from "@/base-components/Empty";
import { mobilePersonTabs as personTabsConfig } from "@/config/person-tabs.config";
import PersonTabsMobile from "./PersonTabsMobile.vue";

const PersonPrimaryInfo = defineAsyncComponent(
  () => import("@/pages/customer/components/PersonPrimaryInfo.vue"),
);

const router = useRouter();
const menuRef = ref();

defineProps({
  person: {
    type: Object as PropType<PersonResponse>,
    required: true,
  },
});

const isDrawerOpen = ref(false);
const mainContentRef = ref<HTMLElement | null>(null);

const openDrawer = () => {
  isDrawerOpen.value = true;
};

const toggleMenu = (event: Event) => {
  menuRef.value.toggle(event);
};

const goBack = () => {
  router.back();
};

const menuItems = [
  {
    label: "Thông tin chi tiết",
    icon: "pi pi-user",
    command: openDrawer,
  },
];
</script>

<template>
  <div v-if="person" class="fixed inset-0 z-50 flex flex-col bg-white dark:bg-slate-900">
    <!-- Custom Header (similar to MobileSearchView) -->
    <header
      class="flex h-14 items-center border-b border-slate-200 bg-primary px-4 shadow-md dark:border-slate-700"
    >
      <div class="flex w-full items-center gap-2">
        <Button
          icon="pi pi-arrow-left"
          text
          rounded
          aria-label="Quay lại"
          class="flex-none bg-white/10 text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
          @click="goBack"
        />
        <h1 class="truncate text-base font-medium text-white">
          {{ person.full_name }}
        </h1>
        <div class="flex-grow"></div>
        <Button
          icon="pi pi-ellipsis-h"
          text
          rounded
          aria-label="Menu"
          aria-haspopup="true"
          aria-controls="person_profile_menu"
          class="flex-none bg-white/10 text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
          @click="toggleMenu"
        />
        <Menu
          ref="menuRef"
          id="person_profile_menu"
          :model="menuItems"
          :popup="true"
          :pt="{ content: { class: 'p-0' } }"
        />
      </div>
    </header>

    <!-- Main Content -->
    <main ref="mainContentRef" class="flex-1 overflow-y-auto bg-gray-50 p-4 dark:bg-slate-800">
      <PersonTabsMobile
        v-if="personTabsConfig && personTabsConfig.length > 0"
        :tabs="personTabsConfig"
        :person-id="person.id"
        :initial-tab-key="personTabsConfig.length > 0 ? personTabsConfig[0].key : null"
      />

      <Drawer v-model:visible="isDrawerOpen" position="bottom" header="Thông tin chi tiết">
        <PersonPrimaryInfo :person="person" />
      </Drawer>
    </main>

    <!-- Back to Top Button -->
    <ScrollToTop :target="mainContentRef" />
  </div>
  <Empty v-else />
</template>
