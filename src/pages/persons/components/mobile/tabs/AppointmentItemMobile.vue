<script setup lang="ts">
import { computed } from "vue";

import { AppointmentResponse, PersonResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import { NoteInfo } from "@/components/InfoText";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { formatCustom } from "@/utils/time-helper";
import useConstant from "@/hooks/useConstant";
import useColorHash from "@/utils/use-tag";
import PersonCard from "@/components/Person/PersonCard.vue";

const props = defineProps<{
  appointment: AppointmentResponse;
  isPast?: boolean;
  isMainPage?: boolean;
}>();

const { constants } = useConstant();

// Kiểm tra xem lịch hẹn đã đến chưa
const hasArrived = computed(() => {
  return Boolean(props.appointment.arrived_at && props.appointment.arrived_at.trim());
});

// Format thởi gian
const timeRange = computed(() => {
  return `${formatCustom(props.appointment.start_time, "HH:mm")} - ${formatCustom(props.appointment.end_time, "HH:mm")}`;
});

// Lấy ngày
const appointmentDate = computed(() => {
  return formatCustom(props.appointment.start_time, "DD/MM/YYYY");
});

// Lấy trạng thái
const statusClass = computed(() => {
  if (props.isPast) {
    return "bg-slate-200 text-slate-600";
  }

  if (hasArrived.value) {
    return "bg-success text-white";
  }

  switch (props.appointment.status) {
    case 1: // Đã khóa
      return "bg-red-500 text-white";
    case 2: // Đã đặt lịch
      return "bg-blue-500 text-white";
    case 3: // Đã đến
      return "bg-green-500 text-white";
    case 4: // Cảnh báo
      return "bg-yellow-500 text-white";
    case 5: // Đã cập nhật
      return "bg-green-500 text-white";
    case 6: // Đã thêm
      return "bg-blue-500 text-white";
    default:
      return "bg-slate-500 text-white";
  }
});

// Lấy tên trạng thái
const statusName = computed(() => {
  if (hasArrived.value) {
    return "Đã đến";
  }

  const status = constants.value?.appointment_status?.[props.appointment.status];
  return status || "Không xác định";
});
</script>

<template>
  <div
    class="appointment-item rounded-lg border border-slate-200 bg-white p-3 shadow-sm"
    :class="{ 'opacity-80': isPast }"
  >
    <!-- Header: Bác sĩ và trạng thái -->
    <div class="mb-2 flex items-center justify-between">
      <div class="flex items-center gap-2">
        <PersonCard
          v-if="isMainPage"
          :person="appointment.person as PersonResponse"
          showCode
          showAvatar
          size="normal"
        />
        <UserAvatar v-else :user="appointment.doctor" size="small" :muted="isPast" showName />
      </div>
      <div class="flex flex-col items-start gap-1">
        <div class="rounded-full px-2 py-1 text-xs font-medium" :class="statusClass">
          {{ statusName }}
        </div>
      </div>
    </div>

    <!-- Thời gian -->
    <div class="mb-2 flex flex-row flex-wrap justify-between gap-2">
      <div class="flex flex-wrap items-center gap-1 text-slate-600">
        <i class="pi pi-calendar text-sm"></i>
        <span class="text-sm">{{ appointmentDate }}</span>
      </div>

      <div class="flex flex-wrap items-center gap-1 text-slate-600">
        <i class="pi pi-clock text-sm"></i>
        <span class="text-sm">{{ timeRange }}</span>
      </div>
      <div class="flex flex-wrap items-center gap-1 text-slate-600">
        <i class="pi pi-tag text-sm"></i>
        <span class="text-sm">{{ constants?.appointment_type?.[appointment.type] }}</span>
      </div>
      <div v-if="hasArrived" class="flex flex-wrap items-center gap-1 text-green-600">
        <i class="pi pi-check-circle text-sm"></i>
        <DateTime :time="appointment.arrived_at" showTime size="sm" :showIcon="false" />
      </div>
    </div>

    <!-- Ghi chú -->
    <div v-if="appointment.notes || appointment.extra_notes" class="border-t border-slate-100 pt-2">
      <div v-if="appointment.extra_notes" class="mb-1 flex items-start">
        <NoteInfo
          :extra_notes="appointment.extra_notes"
          mode="list"
          size="normal"
          :muted="isPast"
        />
      </div>
      <div v-if="appointment.notes" class="flex items-start">
        <NoteInfo
          :notes="appointment.notes"
          mode="tag"
          size="normal"
          :muted="isPast"
          :editable="false"
        />
      </div>
    </div>
  </div>
</template>
