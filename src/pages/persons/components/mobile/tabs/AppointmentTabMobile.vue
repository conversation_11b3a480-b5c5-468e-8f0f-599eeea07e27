<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import dayjs from "dayjs";
import { AppointmentResponse } from "@/api/bcare-types-v2";
import { useAppointmentFilters, AppointmentFilterState } from "@/hooks/useAppointmentFilter";
import Empty from "@/base-components/Empty";
import AppointmentItemMobile from "./AppointmentItemMobile.vue";
import { useAuthStore } from "@/stores/auth-store";
import AppointmentFilterMobile from "./AppointmentFilterMobile.vue";

const props = defineProps<{
  personId?: number;
  isMainPage?: boolean;
}>();

const authStore = useAuthStore();

const isDrawerOpen = ref(false);

const initialFilterValues: Partial<AppointmentFilterState> = {
  person: "",
};

if (props.isMainPage) {
  initialFilterValues.start_time = dayjs().startOf("day").toDate();
  initialFilterValues.end_time = dayjs().endOf("day").toDate();
  if (authStore.currentUser?.id) {
    initialFilterValues.doctor_id = authStore.currentUser.id;
  }
  initialFilterValues.person_id = null;
} else if (props.personId) {
  initialFilterValues.person_id = props.personId;
}

const {
  appointments,
  isLoading,
  error,
  filterState,
  fetchFilteredAppointments,
  resetFiltersAndRefetch,
  searchQuery,
  activeFiltersCount,
  debouncedSearch,
  clearSearch,
} = useAppointmentFilters(initialFilterValues);

const fetchData = async (page = 1, pageSize = 200) => {
  const sortOrder = props.isMainPage ? "ASC" : "DESC";
  await fetchFilteredAppointments({
    page,
    pageSize,
    sort: [{ field: "start_time", order: sortOrder }],
  });
};

const handleApplyFiltersFromDrawer = () => {
  isDrawerOpen.value = false;
  fetchData();
};

const handleDrawerClearFilters = () => {
  isDrawerOpen.value = false;
  const clearOverrides: Partial<AppointmentFilterState> = {
    note: "",
    extra_notes: "",
    arrived: "both",
    person: "", // Clear person name filter
  };

  if (props.isMainPage) {
    clearOverrides.start_time = dayjs().startOf("day").toDate();
    clearOverrides.end_time = dayjs().endOf("day").toDate();
    if (authStore.currentUser?.id) {
      clearOverrides.doctor_id = authStore.currentUser.id;
    } else {
      clearOverrides.doctor_id = null;
    }
    clearOverrides.person_id = null;
  } else if (props.personId) {
    clearOverrides.person_id = props.personId;
    clearOverrides.start_time = undefined;
    clearOverrides.end_time = undefined;
    clearOverrides.doctor_id = null;
  }
  resetFiltersAndRefetch(clearOverrides);
};

onMounted(async () => {
  await fetchData();
});

const hasAppointments = computed(() => {
  return appointments.value && appointments.value.length > 0;
});

const isPastAppointment = (startTime: string | Date) => {
  return dayjs(startTime).startOf("day").isBefore(dayjs().startOf("day"));
};

const groupedAppointments = computed(() => {
  if (!appointments.value) return { upcoming: [], past: [] };
  const upcoming: AppointmentResponse[] = [];
  const past: AppointmentResponse[] = [];
  appointments.value.forEach((appointment) => {
    if (isPastAppointment(appointment.start_time)) {
      past.push(appointment);
    } else {
      upcoming.push(appointment);
    }
  });
  return { upcoming, past };
});
</script>

<template>
  <div class="appointment-tab-mobile bg-transparent p-4">
    <div v-if="isMainPage" class="mb-4 flex items-center gap-2">
      <IconField class="flex-1">
        <InputIcon class="pi pi-search" />
        <InputText
          v-model="searchQuery"
          placeholder="Tìm tên khách hàng..."
          @update:modelValue="debouncedSearch"
          fluid
        />
        <InputIcon v-if="searchQuery" class="pi pi-times cursor-pointer" @click="clearSearch" />
      </IconField>

      <Button
        aria-label="Bộ lọc"
        @click="isDrawerOpen = true"
        icon="pi pi-filter"
        :badge="activeFiltersCount > 0 ? activeFiltersCount.toString() : undefined"
        rounded
      />
    </div>

    <AppointmentFilterMobile
      v-model:visible="isDrawerOpen"
      :filterState="filterState"
      :isMainPage="props.isMainPage"
      @apply-filters="handleApplyFiltersFromDrawer"
      @clear-filters="handleDrawerClearFilters"
    />

    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <i class="pi pi-spin pi-spinner text-2xl text-primary"></i>
    </div>

    <div v-else-if="!hasAppointments && !isLoading" class="py-6">
      <Empty />
    </div>

    <div v-else class="appointments-container">
      <div v-if="groupedAppointments.upcoming.length > 0" class="mb-6">
        <h3
          class="mb-2 border-l-4 border-blue-500 py-1 pl-2 text-base font-semibold text-slate-800"
        >
          Lịch hẹn ({{ groupedAppointments.upcoming.length }})
        </h3>
        <DataView
          :value="groupedAppointments.upcoming"
          layout="list"
          :paginator="false"
          :rows="200"
          data-key="id"
          class="border-none bg-transparent p-0"
          lazy
          :pt="{
            content: { class: 'p-0 border-none bg-transparent' },
            grid: { class: 'p-0' },
          }"
        >
          <template #list="slotProps">
            <div class="mb-3" v-for="item in slotProps.items" :key="item.id">
              <AppointmentItemMobile :appointment="item" :isMainPage="isMainPage" />
            </div>
          </template>
        </DataView>
      </div>

      <div v-if="groupedAppointments.past.length > 0">
        <h3 class="mb-2 border-l-4 border-slate-400 py-1 pl-2 text-sm font-medium text-slate-500">
          Lịch hẹn cũ ({{ groupedAppointments.past.length }})
        </h3>
        <DataView
          :value="groupedAppointments.past"
          layout="list"
          :paginator="false"
          :rows="200"
          data-key="id"
          class="border-none bg-transparent p-0"
          lazy
          :pt="{
            content: { class: 'p-0 border-none bg-transparent' },
            grid: { class: 'p-0' },
          }"
        >
          <template #list="slotProps">
            <div class="mb-3" v-for="item in slotProps.items" :key="item.id">
              <AppointmentItemMobile :appointment="item" :is-past="true" :isMainPage="isMainPage" />
            </div>
          </template>
        </DataView>
      </div>
    </div>
    <div v-if="error && !isLoading" class="p-4 text-red-500">
      Lỗi khi tải dữ liệu: {{ error.message || error }}
    </div>
  </div>
</template>
