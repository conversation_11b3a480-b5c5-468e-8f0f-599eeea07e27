<script setup lang="ts">
import { AttachmentResponse } from "@/api/bcare-types-v2";
import DateTime from "@/base-components/DateTime.vue";
import Debug from "@/base-components/Debug.vue";
import AttachmentItem from "@/components/Attachment/AttachmentItem.vue";
import RootAttachment from "@/components/Attachment/RootAttachment.vue";
import UserAvatarGroup from "@/components/User/UserAvatarGroup.vue";
import useMetadataParser from "@/hooks/useAttachmentDataParser";
import { computed } from "vue";

const props = defineProps<{
  attachment: AttachmentResponse;
}>();

const computedAttachments = computed(() => {
  const attachment = { ...props.attachment };

  // Tìm metadata trong attachment data
  if (attachment.data) {
    const metaItem = attachment.data.find((data) => data.kind === "meta");
    if (metaItem && metaItem.data) {
      // Parse operation nếu là string
      if (metaItem.data.operation && typeof metaItem.data.operation === "string") {
        try {
          metaItem.data.operation = JSON.parse(metaItem.data.operation);
        } catch (e) {
          console.log("Error parsing operation", e);
        }
      }

      // Parse next_operation nếu là string
      if (metaItem.data.next_operation && typeof metaItem.data.next_operation === "string") {
        try {
          metaItem.data.next_operation = JSON.parse(metaItem.data.next_operation);
        } catch (e) {
          console.log("Error parsing next_operation", e);
        }
      }
    }
  }

  return attachment;
});

const { getSafeParticipants, teethDataByAttachment, metaDataByAttachment } = useMetadataParser();
</script>

<template>
  <div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
    <!-- Header with date and ID -->
    <div class="flex items-center justify-between border-b border-gray-100 pb-2">
      <div class="flex items-center gap-2">
        <DateTime :time="computedAttachments.created_at" size="sm" />
        <span class="text-xs font-medium text-slate-400">
          <i class="pi pi-hashtag pr-1 text-xs" />{{ computedAttachments.id }}
        </span>
      </div>
    </div>

    <!-- Main content -->
    <div class="py-2">
      <!-- Operations/Products -->
      <div class="mb-2">
        <Debug :value="computedAttachments" />
        <AttachmentItem
          v-if="metaDataByAttachment[computedAttachments.id]?.operation"
          :key="computedAttachments.id"
          :operations="metaDataByAttachment[computedAttachments.id].operation"
          :parent="computedAttachments"
          :show-operations="true"
          :teeth-data="teethDataByAttachment[computedAttachments.id] || {}"
        />
        <RootAttachment v-else :attachment="computedAttachments" />
      </div>
    </div>

    <!-- Footer with participants -->
    <div class="border-t border-gray-100 pt-2">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600">Phụ trách:</span>
        <UserAvatarGroup
          :users="[computedAttachments.user_id, ...getSafeParticipants(computedAttachments.id)]"
          :expand="true"
          size="small"
        />
      </div>
    </div>
  </div>
</template>
