<script setup lang="ts">
import { computed } from "vue";
import type { AppointmentFilterState } from "@/hooks/useAppointmentFilter";
import dayjs from "dayjs";

const props = defineProps<{
  filterState: AppointmentFilterState;
  visible: boolean;
  isMainPage: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "apply-filters"): void;
  (e: "clear-filters"): void;
}>();

const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const applyFilters = () => {
  emit("apply-filters");
};

const handleClearFilters = () => {
  emit("clear-filters");
};

const arrivedOptions = [
  { label: "Tất cả", value: "both" },
  { label: "Đã đến", value: "yes" },
  { label: "Chưa đến", value: "no" },
];

const handleDateChange = (
  currentValue: Date | Date[] | (Date | null)[] | null | undefined,
  target: "start_time" | "end_time",
) => {
  let newDate: Date | undefined = undefined;
  if (currentValue instanceof Date) {
    newDate = currentValue;
  } else if (Array.isArray(currentValue) && currentValue[0] instanceof Date) {
    newDate = currentValue[0]; // Take the first date if it's an array (for safety, though not expected in single mode)
  }

  props.filterState[target] = newDate;

  if (
    target === "start_time" &&
    props.filterState.start_time &&
    props.filterState.end_time &&
    dayjs(props.filterState.end_time).isBefore(dayjs(props.filterState.start_time))
  ) {
    props.filterState.end_time = props.filterState.start_time;
  } else if (
    target === "end_time" &&
    props.filterState.start_time &&
    props.filterState.end_time &&
    dayjs(props.filterState.end_time).isBefore(dayjs(props.filterState.start_time))
  ) {
    props.filterState.start_time = props.filterState.end_time;
  }
};
</script>

<template>
  <Drawer v-model:visible="drawerVisible" position="bottom" :header="'Bộ lọc lịch hẹn'" blockScroll>
    <div class="mb-4" v-if="props.isMainPage">
      <label for="personNameFilter" class="mb-1 block text-sm font-medium text-slate-700"
        >Tìm khách hàng</label
      >
      <InputText
        id="personNameFilter"
        v-model="props.filterState.person"
        placeholder="Nhập tên khách hàng..."
        class="w-full"
      />
    </div>

    <!-- Date Range Picker -->
    <div class="mb-4">
      <label for="fromDateDrawer" class="mb-1 block text-sm font-medium text-slate-700"
        >Từ ngày</label
      >
      <DatePicker
        id="fromDateDrawer"
        :modelValue="props.filterState.start_time"
        @update:modelValue="(value) => handleDateChange(value, 'start_time')"
        dateFormat="dd/mm/yy"
        placeholder="Từ ngày"
        class="w-full"
        :pt="{ input: { class: 'w-full border rounded' } }"
      />
    </div>

    <div class="mb-4">
      <label for="toDateDrawer" class="mb-1 block text-sm font-medium text-slate-700"
        >Đến ngày</label
      >
      <DatePicker
        id="toDateDrawer"
        :modelValue="props.filterState.end_time"
        @update:modelValue="(value) => handleDateChange(value, 'end_time')"
        dateFormat="dd/mm/yy"
        placeholder="Đến ngày"
        class="w-full"
        :pt="{ input: { class: 'w-full border rounded' } }"
        :minDate="props.filterState.start_time"
      />
    </div>

    <!-- Ghi chú -->
    <div class="mb-4">
      <label for="noteFilter" class="mb-1 block text-sm font-medium text-slate-700">Ghi chú</label>
      <InputText
        id="noteFilter"
        v-model="props.filterState.note"
        placeholder="Tìm theo ghi chú..."
        class="w-full"
      />
    </div>

    <!-- Công việc dự kiến -->
    <div class="mb-4">
      <label for="extraNotesFilter" class="mb-1 block text-sm font-medium text-slate-700"
        >Công việc dự kiến</label
      >
      <InputText
        id="extraNotesFilter"
        v-model="props.filterState.extra_notes"
        placeholder="Tìm theo công việc dự kiến..."
        class="w-full"
      />
    </div>

    <!-- Đã đến -->
    <div class="mb-4">
      <label for="arrivedFilter" class="mb-1 block text-sm font-medium text-slate-700"
        >Trạng thái đến</label
      >
      <SelectButton
        id="arrivedFilter"
        v-model="props.filterState.arrived"
        :options="arrivedOptions"
        optionLabel="label"
        optionValue="value"
        aria-labelledby="arrivedFilterLabel"
      />
    </div>

    <template #footer>
      <Button
        label="Xóa bộ lọc"
        severity="secondary"
        outlined
        @click="handleClearFilters"
        class="flex-1"
      />
      <Button label="Áp dụng" @click="applyFilters" class="flex-1" />
    </template>
  </Drawer>
</template>
