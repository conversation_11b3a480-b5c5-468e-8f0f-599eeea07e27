<script setup lang="ts">
import {onMounted, reactive, ref} from "vue";
import { useRouter } from "vue-router";

import { SettingAddRequest, SettingListRequest } from "@/api/bcare-types-v2";
import Button from "@/base-components/Button";
import { FormInput } from "@/base-components/Form";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import Table from "@/base-components/Table";
import useToggle from "@/hooks/useToggle";
import { useConfigurationsStore } from "@/stores/configuration-store";
import { useNotiStore } from "@/stores/notification";

import ModalAddConfiguration from "./components/ModalAddConfiguration.vue";
import ModalConfirm from "./components/ModalConfirm.vue";

const router = useRouter();
const configurationStore = useConfigurationsStore();
const modalDelete = useToggle(false);
const modalAdd = useToggle(false);
const notiStore = useNotiStore();
const search = ref("");
const filterParams = reactive<SettingListRequest>({ category: "", name: "" });

const paramId = ref<number>(0);

const handlerDelete = (id: number) => {
  paramId.value = id;
  modalDelete.show();
};

const deleteConfirm = async () => {
  const res = await configurationStore.deleteSetting({ id: paramId.value });
  modalDelete.hide();
  if (res) {
    configurationStore.getSettings(filterParams);
    notiStore.success({
      title: "Xoá cấu hình thành công.",
      message: "",
    });
  }
};

const handleAddConfiguration = async (request: SettingAddRequest) => {
  const res = await configurationStore.addSetting(request);
  modalAdd.hide();
  if (res) {
    configurationStore.getSettings(filterParams);
    notiStore.success({
      title: "Thêm cấu hình thành công.",
      message: "",
    });
  }
};

onMounted(() => {
  // Initialize settings when the component mounts
  configurationStore.init({ category: '', name: '' });
});
</script>

<template>
  <h2 class="intro-y mt-10 text-lg font-medium">Cấu hình</h2>
  <div class="intro-y mt-5 grid grid-cols-12 gap-6">
    <div class="col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
      <Button variant="primary" class="mr-2 shadow-md" @click="modalAdd.show">
        Thêm cấu hình
      </Button>
      <div class="mx-auto hidden text-slate-500 md:block"></div>
      <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
        <div class="relative w-56 text-slate-500">
          <FormInput v-model="search" type="text" class="!box w-56 pr-10" placeholder="Tìm kiếm" />
          <Lucide icon="Search" class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4" />
        </div>
      </div>
    </div>
    <div class="col-span-12 overflow-auto lg:overflow-visible">
      <Table class="-mt-2 border-separate border-spacing-y-[10px]">
        <Table.Thead>
          <Table.Tr>
            <Table.Th class="whitespace-nowrap border-b-0"> Tên cấu hình </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Loại </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Miêu tả </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <template v-for="item in configurationStore.state.configurations" :key="item.id">
            <Table.Tr
              v-if="
                item.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()) ||
                item.category.toLocaleLowerCase().includes(search.toLocaleLowerCase())
              "
              class="cursor-pointer hover:shadow-md"
              @click="
                router.push({
                  name: 'top-menu-configuration-detail',
                  params: { id: item.id },
                })
              "
            >
              <Table.Td
                class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <span class="whitespace-nowrap font-medium">
                  {{ item.name }}
                </span>
              </Table.Td>
              <Table.Td
                class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <div class="mr-3 flex items-center text-slate-500" href="#">
                  {{ item.category }}
                </div>
              </Table.Td>
              <Table.Td
                class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
              >
                <div class="mr-3 flex items-center text-slate-500" href="#">
                  {{ item.description }}
                </div>
              </Table.Td>
              <Table.Td
                class="w-3 border-b-0 bg-white py-0 shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
                @click.stop
              >
                <Menu class="ml-auto flex justify-end">
                  <Menu.Button as="a" class="-mr-2 block h-5 w-5" href="#">
                    <Lucide icon="MoreVertical" class="h-5 w-5 text-slate-500" />
                  </Menu.Button>
                  <Menu.Items class="w-40">
                    <router-link :to="{ name: 'top-menu-user-update', params: { id: item.id } }">
                      <Menu.Item class="mr-3 flex items-center">
                        <Lucide icon="Edit" class="mr-1 h-4 w-4" />
                        Sửa
                      </Menu.Item>
                    </router-link>

                    <Menu.Item
                      class="flex items-center text-danger"
                      @click="handlerDelete(item.id)"
                    >
                      <Lucide icon="Trash2" class="mr-1 h-4 w-4" /> Xóa
                    </Menu.Item>
                  </Menu.Items>
                </Menu>
              </Table.Td>
            </Table.Tr>
          </template>
        </Table.Tbody>
      </Table>
    </div>
  </div>
  <ModalConfirm
    :is-open="modalDelete.isVisible.value"
    title="Xác nhận xóa cấu hình"
    label-btn="Xóa"
    @on-close="modalDelete.hide"
    @confirm="deleteConfirm"
  />
  <ModalAddConfiguration
    :is-open="modalAdd.isVisible.value"
    @on-close="modalAdd.hide"
    @submit="handleAddConfiguration"
  />
</template>
