<script setup lang="ts">
import { ref, computed, watch } from "vue";
import useTag from "@/hooks/useTag";
import type { TagResponse, TagAddRequest, TagInfo } from "@/api/bcare-types-v2";
import TagChip from "@/components/Tags/TagChip.vue";
import InputText from "primevue/inputtext";
import Textarea from "primevue/textarea";
import Button from "primevue/button";
import ProgressSpinner from "primevue/progressspinner";
import Message from "primevue/message";
import Divider from "primevue/divider";
import Panel from "primevue/panel";
import Select from "primevue/select";
import Chip from "primevue/chip";

// --- State Management with useTag ---
// useStore: false ensures we always fetch fresh data, bypassing the Pinia store cache
const { tags, isLoading, error, loadTags, addTag, deleteTag, updateTag, getCategories } = useTag({
  useStore: false,
  autoLoad: true,
}); // autoLoad: true fetches tags on mount

// --- Add Tag Form State ---
const newTagName = ref("");
const newTagCategory = ref("");
const newTagDescription = ref("");
const isAdding = ref(false); // For showing loading state on add button
const addError = ref<string | null>(null);

// --- Computed Properties ---
// Group tags by category for display
const groupedTags = computed(() => {
  const groups: Record<string, TagResponse[]> = {};
  tags.value.forEach((tag) => {
    const category = tag.category || "Uncategorized"; // Group tags without category
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(tag);
  });
  // Sort categories alphabetically, keeping "Uncategorized" first if present
  const sortedGroups: Record<string, TagResponse[]> = {};
  if (groups.Uncategorized) {
    sortedGroups.Uncategorized = groups.Uncategorized.sort((a, b) => a.name.localeCompare(b.name));
    delete groups.Uncategorized;
  }
  Object.keys(groups)
    .sort()
    .forEach((category) => {
      sortedGroups[category] = groups[category].sort((a, b) => a.name.localeCompare(b.name));
    });
  return sortedGroups;
});

// Options for the category dropdown, including existing categories
const categoryOptions = computed(() => getCategories.value);

// --- Event Handlers ---
const handleAddTag = async () => {
  if (!newTagName.value || !newTagCategory.value) {
    addError.value = "Tên thẻ và Nhóm là bắt buộc.";
    return;
  }

  isAdding.value = true;
  addError.value = null;
  const request: TagAddRequest = {
    name: newTagName.value.trim(),
    category: newTagCategory.value.trim(),
    description: newTagDescription.value.trim() || undefined,
  };

  try {
    await addTag(request);
    // Reset form after successful add
    newTagName.value = "";
    newTagCategory.value = "";
    newTagDescription.value = "";
    // No need to manually call loadTags if useStore: false,
    // as addTag in the composable already updates the local 'tags' ref
  } catch (err: any) {
    addError.value = `Không thể thêm thẻ: ${err.message || "Lỗi không xác định"}`;
    console.error("Failed to add tag:", err);
  } finally {
    isAdding.value = false;
  }
};

const handleDeleteTag = async (tagId: number) => {
  // Optional: Add confirmation dialog here
  try {
    await deleteTag({ id: tagId });
    await loadTags();
    // 'tags' ref is updated automatically by deleteTag in the composable
  } catch (err: any) {
    console.error(`Failed to delete tag ${tagId}:`, err);
    // Optional: Show error message to user
  }
};

const handleUpdateTag = async (updatedTag: TagResponse | TagInfo) => {
  // Type guard to ensure we have the full TagResponse structure expected by updateTag
  if (!("status" in updatedTag && "version" in updatedTag)) {
    console.error("Invalid tag structure for update:", updatedTag);
    return; // Or fetch the full tag details first if necessary
  }

  try {
    // The TagChip emits the *updated* tag object directly
    await updateTag({
      id: updatedTag.id,
      name: updatedTag.name,
      category: updatedTag.category, // Assuming category is part of TagInfo/TagResponse
      description: updatedTag.description || undefined,
      // You might need to pass status and version if your API requires them for updates
      // status: updatedTag.status,
      // version: updatedTag.version,
    });
    // 'tags' ref is updated automatically by updateTag in the composable
  } catch (err: any) {
    console.error(`Failed to update tag ${updatedTag.id}:`, err);
    // Optional: Show error message to user, potentially revert UI change
  }
};

// Reset add error when user types in the form again
watch([newTagName, newTagCategory, newTagDescription], () => {
  if (addError.value) {
    addError.value = null;
  }
});
</script>

<template>
  <div class="space-y-6 p-4">
    <!-- Add Tag Form -->
    <Panel header="Thêm thẻ mới" toggleable>
      <div class="p-fluid grid grid-cols-1 gap-4 md:grid-cols-3">
        <div class="flex flex-col">
          <label for="new-tag-name" class="mb-1 text-sm font-medium text-gray-700">
            Tên thẻ <span class="text-red-500">*</span>
          </label>
          <InputText id="new-tag-name" v-model="newTagName" placeholder="VD: Tiềm năng" />
        </div>

        <div class="flex flex-col">
          <label for="new-tag-category" class="mb-1 text-sm font-medium text-gray-700">
            Nhóm <span class="text-red-500">*</span>
          </label>
          <Select
            id="new-tag-category"
            v-model="newTagCategory"
            :options="categoryOptions"
            placeholder="Chọn hoặc nhập nhóm mới"
            editable
            class="w-full"
          />
        </div>

        <div class="flex flex-col">
          <label for="new-tag-description" class="mb-1 text-sm font-medium text-gray-700">
            Mô tả
          </label>
          <Textarea
            id="new-tag-description"
            v-model="newTagDescription"
            rows="1"
            autoResize
            placeholder="Mô tả ngắn gọn (không bắt buộc)"
          />
        </div>
      </div>
      <div class="mt-4 flex items-center justify-end space-x-3">
        <Message v-if="addError" severity="error" :closable="false" class="text-sm">
          {{ addError }}
        </Message>
        <Button
          label="Thêm thẻ"
          icon="pi pi-plus"
          :loading="isAdding"
          :disabled="!newTagName || !newTagCategory || isAdding"
          @click="handleAddTag"
        />
      </div>
    </Panel>

    <Divider />

    <!-- Display Tags -->
    <div class="space-y-6">
      <h2 class="text-lg font-semibold text-gray-700">Danh sách thẻ</h2>

      <!-- Loading State -->
      <div v-if="isLoading && !tags.length" class="flex items-center justify-center py-8">
        <ProgressSpinner strokeWidth="4" style="width: 50px; height: 50px" />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="py-4">
        <Message severity="error" :closable="false">
          Không thể tải danh sách thẻ: {{ error.message || "Lỗi không xác định" }}
        </Message>
      </div>

      <!-- No Tags State -->
      <div v-else-if="!tags.length" class="py-8 text-center text-gray-500">
        <i class="pi pi-tags mb-3 text-4xl text-gray-400"></i>
        <p>Chưa có thẻ nào được tạo.</p>
        <p class="text-sm">Hãy thêm thẻ mới bằng biểu mẫu ở trên.</p>
      </div>

      <!-- Tags List Grouped by Category using Panels -->
      <div v-else class="space-y-4">
        <Panel
          v-for="(categoryTags, categoryName) in groupedTags"
          :key="categoryName"
          :toggleable="true"
          :collapsed="false"
        >
          <template #header>
            <div class="flex items-center gap-2">
              <i class="pi pi-folder"></i>
              <span class="font-semibold">{{ categoryName }}</span>
              <Chip :label="categoryTags.length.toString()" size="small" />
            </div>
          </template>
          <div class="flex flex-wrap gap-2 p-1">
            <TagChip
              v-for="tag in categoryTags"
              :key="tag.id"
              :tag="tag"
              :deletable="true"
              :modifiable="true"
              @delete="handleDeleteTag"
              @update="handleUpdateTag"
              size="normal"
            />
          </div>
        </Panel>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.p-panel .p-panel-header) {
  padding: 0.75rem 1rem;
}

:deep(.p-panel .p-panel-content) {
  padding: 0.5rem;
}

:deep(.p-select-panel) {
  z-index: 1100;
}
</style>
