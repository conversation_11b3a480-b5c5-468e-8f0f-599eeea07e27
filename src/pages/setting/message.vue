<script setup lang="ts">
import { ref } from "vue";

import Button from "@/base-components/Button";
import { FormCheck, FormInput, FormLabel, FormSwitch, FormTextarea } from "@/base-components/Form";
import Dialog from "@/base-components/Headless/Dialog";
import Lucide from "@/base-components/Lucide";
const modalTemplateSms = ref(false);
const isShowModalTemplateSms = (value: boolean) => {
  modalTemplateSms.value = value;
};
const modalConfirmStatus = ref(false);
const isShowConfirmStatus = (value: boolean) => {
  modalConfirmStatus.value = value;
};
const confirmStatus = () => {
  isShowConfirmStatus(true);
};
const messages = [
  {
    id: 1,
    name: "Nhắc hẹn thường",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH {{tenkhachhang}} ID {{id}} vào thời gian:21/09/2023 08:35-08:40.\nVui lòng đi đúng lịch hẹn để đảm bảo quá trình điều trị.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 2,
    name: "Nhắc hẹn tiểu phẫu",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH {{tenkhachhang}} ID {{id}} vào thời gian:21/09/2023 08:35-08:40.\nLưu ý: ĂN NO TRƯỚC KHI ĐẾN và bổ sung kết quả Xét nghiệm máu nếu chưa bổ sung (BẮT BUỘC).\nNẾU ĐANG TRONG GIAI ĐOẠN HÀNH KINH (KH nữ), vui lòng liên hệ để đổi hẹn.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 3,
    name: "Nhắc hẹn KH đã tháo mắc cài",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH {{tenkhachhang}} ID {{id}} vào thời gian:21/09/2023 08:35-08:40.\nLưu ý: MANG THEO KHAY DUY TRÌ KHI ĐẾN TÁI KHÁM (BẮT BUỘC).\nTrường hợp mất Khay duy trì, vui lòng liên hệ bộ phận CSKH trước lịch hẹn.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 4,
    name: "Nhắc hẹn KH chữa tủy",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH {{tenkhachhang}} ID {{id}} vào thời gian:21/09/2023 08:35-08:40.\nLưu ý: ĂN NO TRƯỚC KHI ĐẾN (BẮT BUỘC).\nNẾU ĐANG MANG THAI HOẶC NGHI NGỜ MANG THAI (KH nữ), vui lòng liên hệ để đổi hẹn.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 5,
    name: "Chăm sóc Sau nhổ răng",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi nhổ răng: Không dùng nước súc miệng/ nước muối trong 3- 4 ngày đầu. Không va chạm vào vùng nhổ răng. Ăn đồ nguội, uống nước nguội/lạnh trong 2-3 ngày đầu. Uống thuốc theo toa bác sĩ chỉ định.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 6,
    name: "Chăm sóc Sau Cắm vis",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Cắm vis: Không dùng nước súc miệng/ nước muối trong 3- 4 ngày đầu. Không va chạm vào vùng Cắm vis. Ăn đồ nguội, uống nước nguội/lạnh trong 2-3 ngày đầu. Uống thuốc theo toa bác",
    status: 1,
  },
  {
    id: 7,
    name: "Chăm sóc Sau Cắm implant",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Cắm implant: Không dùng nước súc miệng/ nước muối trong 3- 4 ngày đầu. Không va chạm vào vùng cắm implant. Ăn đồ nguội, uống nước nguội/lạnh trong 2-3 ngày đầu. Uống thuốc theo toa bác sĩ chỉ định.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 8,
    name: "Chăm sóc Sau Gọt xương- tỉa nướu",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Gọt xương- tỉa nướu: Không dùng nước súc miệng/ nước muối trong 3- 4 ngày đầu. Không va chạm vào vùng Gọt xương- tỉa nướu. Đeo lại khay duy trì để đảm bảo kết quả sau niềng. Ăn đồ nguội, uống nước nguội/lạnh trong 2-3 ngày đầu. Uống thuốc theo toa bác sĩ chỉ định.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 9,
    name: "Chăm sóc sau Gắn mắc cài + Gắn khí cụ",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Gắn mắc cài và Gắn khí cụ: Vệ sinh kĩ răng sau khi ăn uống bằng bàn chải, chỉ nha khoa, máy tăm nước. Sử dụng sáp nha khoa nếu cộm vướng mắc cài. Thường xuyên uống nước và tránh các thực phẩm dai, cứng.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 10,
    name: "Chăm sóc sau Tháo mắc cài",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Tháo mắc cài: Đeo khay duy trì liên tục trong 1-2 năm đầu, ngoại trừ khi ăn uống và vệ sinh răng miệng. Không ngâm khay duy trì vào nước nóng và chất tẩy rửa. Liên hệ ngay với phòng khám nếu hư hỏng hoặc mất khay duy trì.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 1,
  },
  {
    id: 11,
    name: "Chăm sóc sau Tách kẽ",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nNhững lưu ý sau khi Tách kẽ: Vệ sinh răng và ăn nhai nhẹ nhàng tránh làm rớt/ đứt thun. Không tự ý lấy thun tách kẽ ra khỏi vị trí. Liên hệ ngay với phòng khám nếu bị rớt/ đứt thun.\nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    status: 1,
  },
  {
    id: 12,
    name: "Chăm sóc nhóm KH Đã tháo mắc cài - Chăm sóc Online",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nLần cuối khách hàng tái khám sau niềng tại nha khoa Up Dental: 21/09/2023 08:35-08:40\nQuý khách vui lòng vẫn đeo khay duy trì theo đúng chỉ định của bác sĩ sau khi tháo mắc cài để duy trì kết quả tốt nhất.\nNếu tình trạng răng có vấn đề phát sinh, vui lòng liên hệ nha khoa. \nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 0,
  },
  {
    id: 13,
    name: "Thông báo KH nhận khay duy trì",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nHiện tại khay duy trì của quý khách đã có vào 21/09/2023 08:35-08:40.\nQuý khách vui lòng đến nha khoa để nhận khay duy trì. \nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 0,
  },
  {
    id: 14,
    name: "Hỗ trợ khách hàng",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nQuý khách vui lòng liên hệ nha khoa để được hỗ trợ các vấn đề phát sinh. \nLịch hẹn tiếp theo: 21/09/2023 08:35-08:40\nHotline 1900.0309 (phím 02)\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 0,
  },
  {
    id: 15,
    name: "Tin nhắn trễ hẹn",
    content:
      "Nha khoa Up Dental xin thông báo KH {{tenkhachhang}} ID {{id}} đang trễ lịch hẹn điều trị tại nha khoa.\nVui lòng liên hệ lại nha khoa sớm nhất để đặt lịch tái khám tránh ảnh hưởng đến quá trình và kết quả điều trị.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 0,
  },
  {
    id: 16,
    name: "TIN NHẮN HỦY HẸN",
    content:
      "Cảm ơn quý khách {{tenkhachhang}} ID {{id}} đã tin tưởng sử dụng dịch vụ tại Nha khoa Up Dental.\nVì bác sĩ điều trị có việc bận đột xuất nên lịch hẹn của quý khách tại Up tạm thời bị hủy. Nha khoa xin lỗi vì sự bất tiện này. Nha khoa đang đặt dự trù lịch cho mình vào ngày 21/09/2023 08:35-08:40.\nNhận được tin nhắn anh/ chị vui lòng liên hệ nha khoa để xác nhận lịch hẹn.\nXin cảm ơn.",
    schedule_time: "21/09/2023 08:35-08:40",
    status: 0,
  },
];
</script>
<template>
  <h2 class="intro-y mt-10 text-lg font-medium">Thiết lập tin nhắn</h2>
  <div class="intro-y mt-5 grid grid-cols-12 gap-5">
    <div class="intro-y col-span-12 mt-2 flex flex-wrap items-center sm:flex-nowrap">
      <Button variant="primary" class="mr-2 shadow-md" @click="isShowModalTemplateSms(true)">
        Thêm mới
      </Button>
      <div class="mx-auto hidden text-slate-500 md:block"></div>
      <div class="mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
        <div class="relative w-56 text-slate-500">
          <FormInput type="text" class="!box w-56 pr-10" placeholder="Tìm kiếm mẫu..." />
          <Lucide icon="Search" class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4" />
        </div>
      </div>
    </div>
    <div v-for="(message, key) in messages" :key="key" class="col-span-12 lg:col-span-4">
      <div class="box flex h-full flex-col overflow-hidden rounded-md">
        <div class="relative flex items-start justify-between bg-slate-200 p-5">
          <div class="flex items-center justify-between overflow-hidden">
            <div
              :class="[
                'absolute left-0 top-1/2 h-10 w-[2px] -translate-y-1/2',
                {
                  'bg-success': message.status === 1,
                },
                { 'bg-danger': message.status === 0 },
              ]"
            ></div>
            <div class="truncate text-base font-medium">
              <span class="mr-2">{{ message.id + "." }}</span> {{ message.name }}
            </div>
          </div>
          <FormCheck class="relative ml-auto mt-3 w-full sm:ml-auto sm:mt-0 sm:w-auto">
            <FormCheck.Label class="relative cursor-pointer">
              <FormCheck.Input
                class="peer sr-only z-30 ml-3 mr-0"
                type="checkbox"
                :checked="message.status == 1"
                @change="confirmStatus"
              />
              <div
                class="custom-checkbox peer peer-checked:bg-primary peer-checked:text-primary peer-checked:after:translate-x-full peer-checked:after:border-white peer-checked:after:content-['On']"
              ></div>
            </FormCheck.Label>
          </FormCheck>
          <!--          <Menu class="ml-auto">-->
          <!--            <Menu.Button as="a" class="block w-5 h-5 -mr-2" href="#">-->
          <!--              <Lucide-->
          <!--                  icon="MoreVertical"-->
          <!--                  class="w-5 h-5 text-slate-500"-->
          <!--              />-->
          <!--            </Menu.Button>-->
          <!--            <Menu.Items class="w-40">-->
          <!--              <Menu.Item>-->
          <!--                <Lucide icon="Edit" class="w-4 h-4 mr-2" />-->
          <!--                Chỉnh sửa-->
          <!--              </Menu.Item>-->
          <!--              <Menu.Item class="text-danger">-->
          <!--                <Lucide icon="Trash2" class="w-4 h-4 mr-2" />-->
          <!--                Xóa-->
          <!--              </Menu.Item>-->
          <!--            </Menu.Items>-->
          <!--          </Menu>-->
        </div>
        <div class="mt-5 flex-1 px-5">
          <div class="text-sm text-slate-600 dark:text-slate-400">
            {{ message.content }}
          </div>
        </div>
        <div class="flex items-center p-5 sm:flex-row">
          <div class="ml-auto">
            <Button class="border-primary bg-primary text-white"> Chỉnh sửa </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Dialog
    size="lg"
    :open="modalTemplateSms"
    @close="
      () => {
        isShowModalTemplateSms(false);
      }
    "
  >
    <Dialog.Panel class="overflow-hidden">
      <Dialog.Title class="p-5">
        <h2 class="mr-auto text-base font-medium">Thiết lập mẫu SMS</h2>
        <a class="absolute right-0 top-0 mr-3 mt-3" href="#" @click="isShowModalTemplateSms(false)">
          <Lucide icon="X" class="h-8 w-8 text-slate-400" />
        </a>
      </Dialog.Title>
      <Dialog.Description class="max-h-[80vh] overflow-y-auto p-5">
        <div>
          <FormLabel>Tiêu đề</FormLabel>
          <FormInput id="sms-title" type="number" placeholder="Tiêu đề SMS" />
        </div>
        <div class="mt-3">
          <FormLabel>Nội dung</FormLabel>
          <div class="news__input relative">
            <FormTextarea :rows="5" placeholder="Nội dung SMS" />
          </div>
        </div>
        <div class="mt-2 flex">
          <Button
            class="cursor-pointer rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600 hover:bg-primary hover:text-white dark:bg-darkmode-300 dark:text-slate-400"
          >
            Tên khách hàng
          </Button>
          <Button
            class="ml-2 cursor-pointer rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600 hover:bg-primary hover:text-white dark:bg-darkmode-300 dark:text-slate-400"
          >
            ID
          </Button>
          <Button
            class="ml-2 cursor-pointer rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600 hover:bg-primary hover:text-white dark:bg-darkmode-300 dark:text-slate-400"
          >
            Ngày hẹn
          </Button>
          <Button
            class="ml-2 cursor-pointer rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600 hover:bg-primary hover:text-white dark:bg-darkmode-300 dark:text-slate-400"
          >
            Ngày hẹn tiếp theo</Button
          >
          <Button
            class="ml-2 cursor-pointer rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600 hover:bg-primary hover:text-white dark:bg-darkmode-300 dark:text-slate-400"
          >
            Ngày đến cuối cùng
          </Button>
        </div>
        <div class="mt-3">
          <FormLabel>Trạng thái</FormLabel>
          <FormSwitch>
            <FormSwitch.Input id="product-status-active" type="checkbox" />
          </FormSwitch>
        </div>
      </Dialog.Description>
      <Dialog.Footer>
        <Button
          type="button"
          variant="outline-secondary"
          class="mr-1 w-20"
          @click="
            () => {
              isShowModalTemplateSms(false);
            }
          "
        >
          Đóng
        </Button>
        <Button ref="{sendButtonRef}" variant="primary" type="button" class="w-20"> Lưu </Button>
      </Dialog.Footer>
    </Dialog.Panel>
  </Dialog>
  <Dialog
    :open="modalConfirmStatus"
    @close="
      () => {
        isShowConfirmStatus(false);
      }
    "
  >
    <Dialog.Panel>
      <div class="p-5 text-center">
        <Lucide icon="CheckCircle2" class="mx-auto mt-3 h-16 w-16 text-success" />
        <div class="mt-5 text-3xl">Xác nhận</div>
        <div class="mt-2 text-slate-500">Chỉnh sửa trạng thái tin nhắn</div>
      </div>
      <div class="px-5 pb-8 text-center">
        <Button
          variant="outline-secondary"
          type="button"
          class="mr-1 w-24"
          @click="
            () => {
              isShowConfirmStatus(false);
            }
          "
        >
          Hủy
        </Button>
        <Button variant="primary" type="button" class="w-24"> Lưu </Button>
      </div>
    </Dialog.Panel>
  </Dialog>
  <!-- END: Delete Confirmation Modal -->
</template>
<style scoped>
.custom-checkbox {
  @apply flex h-7 w-[53px] items-center rounded-full bg-gray-300 text-[9px] font-extrabold text-gray-300 after:absolute after:left-[2px] after:flex after:h-6 after:w-6 after:items-center after:justify-center after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-['Off'];
}
</style>
