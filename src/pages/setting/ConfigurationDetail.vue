<script lang="ts" setup>
const JsonEditorVue = defineAsyncComponent(() => import("json-editor-vue"));

import { defineAsyncComponent, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { SettingUpdateRequest } from "@/api/bcare-types";
import Alert from "@/base-components/Alert";
import Button from "@/base-components/Button";
import { FormInput, FormLabel, FormTextarea } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import { TopBackButton } from "@/components";
import useToggle from "@/hooks/useToggle";
import { useConfigurationsStore } from "@/stores/configuration-store";
import { useNotiStore } from "@/stores/notification";
import { deepClone } from "@/utils/helper";

import ModalConfirm from "./components/ModalConfirm.vue";
import { defaultDataAddConfiguration } from "./constants";

const configurationStore = useConfigurationsStore();
const router = useRouter();
const route = useRoute();
const dataForm = ref<SettingUpdateRequest>(deepClone({ ...defaultDataAddConfiguration, id: 0 }));
const content = ref("");
const notiStore = useNotiStore();
const modalConfirm = useToggle(false);

const handleUpdateConfiguration = async () => {
  if (!dataForm.value.id) return;
  modalConfirm.hide();
  const res = await configurationStore.updateSetting({ ...dataForm.value, value: content.value });
  if (res) {
    notiStore.success({
      title: "Thay đổi cấu hình thành công.",
      message: "",
    });
  }
};

const formatJson = () => {
  try {
    if (!content.value) {
      return;
    }
    content.value = JSON.stringify(JSON.parse(content.value), null, "\t");
  } catch (error) {}
};

onMounted(async () => {
  Object.assign(
    dataForm.value,
    configurationStore.state.configurations.find((item) => item.id === +route.params.id) || {},
  );
  content.value = dataForm.value.value;
  formatJson();
});
</script>

<template>
  <div class="intro-y mt-6 flex flex-col gap-4">
    <TopBackButton title="Chi tiết cấu hình" @click="router.go(-1)" />

    <div class="flex flex-col-reverse gap-4 md:flex-row">
      <div class="flex flex-1 flex-col gap-2">
        <div class="flex flex-col gap-2 md:flex-row">
          <div class="flex-1">
            <FormLabel class="font-bold" html-for="input-name">Tên</FormLabel>
            <FormInput
              id="input-name"
              v-model:value="dataForm.name"
              class="flex-1"
              placeholder="Tên"
              type="text"
            />
          </div>
          <div class="flex-1">
            <FormLabel class="font-bold" html-for="input-category">Loại</FormLabel>
            <FormInput
              id="input-category"
              v-model:value="dataForm.category"
              class="flex-1"
              placeholder="Loại"
              type="text"
            />
          </div>
        </div>
        <div>
          <FormLabel class="font-bold" html-for="input-description">Mô tả</FormLabel>
          <FormTextarea
            id="input-description"
            v-model:value="dataForm.description"
            class="flex-1"
            placeholder="Mô tả"
            type="text"
          />
        </div>
        <div class="flex flex-1 flex-col">
          <FormLabel class="font-bold" html-for="input-content">Nội dung</FormLabel>
          <JsonEditorVue
            v-model="content"
            class="border-base-300 h-[400px] w-full rounded border text-sm md:flex-1"
            v-bind="{
              /* local props & attrs */
            }"
          />
        </div>
      </div>
      <div class="flex space-x-4 overflow-x-scroll">
        <div class="w-full flex-none md:w-[400px]">
          <Alert class="box flex items-center bg-white text-base font-medium text-primary/80">
            <Lucide class="mr-2 h-4 w-4" icon="AlignStartVertical" />
            Version
            <div
              class="ml-auto flex items-center rounded-full border border-primary/80 px-2 py-0.5 text-xs font-medium text-primary"
            >
              99
            </div>
          </Alert>
          <div
            class="scrollbar-hidden -mt-1 h-[80vh] overflow-hidden overflow-y-scroll rounded-md rounded-t-none bg-secondary/70 px-4 pb-4 pt-5"
          >
            <div class="list-group intro-y h-full">
              <div class="list-group-item relative mb-2 cursor-pointer first:mt-0 last:mb-0">
                <div class="box p-4">
                  <div class="flex items-start">
                    <div class="ml-4 flex-1">
                      <div class="flex items-center">
                        <div class="font-medium">1234</div>
                      </div>
                      <div class="mt-0.5 w-full truncate text-slate-500">
                        Ghi chú của khách hàng
                      </div>
                    </div>
                  </div>
                </div>
                <div class="absolute right-3 top-3 ml-auto flex text-xs">1234</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-end gap-2">
      <Button class="w-40" type="button" variant="outline-primary" @click="formatJson">
        Làm đẹp
      </Button>
      <Button class="w-40" type="button" variant="primary" @click="modalConfirm.show">
        Cập nhật
      </Button>
    </div>
  </div>
  <ModalConfirm
    :is-open="modalConfirm.isVisible.value"
    label-btn="Cập Nhật"
    title="Xác nhận thay đổi cấu hình"
    type="info"
    @confirm="handleUpdateConfiguration"
    @on-close="modalConfirm.hide"
  />
</template>
