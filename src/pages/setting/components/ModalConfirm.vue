<script setup lang="ts">
import { ref } from "vue";

import Button from "@/base-components/Button";
import { Dialog } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";

const props = defineProps<{
  isOpen: boolean;
  title: string;
  labelBtn: string;
  type?: "info" | "wraning";
}>();
const deleteButtonRef = ref(null);
const emits = defineEmits<{ (event: "onClose"): void; (event: "confirm"): void }>();
</script>

<template>
  <Dialog :open="props.isOpen" :initial-focus="deleteButtonRef" @close="emits('onClose')">
    <Dialog.Panel>
      <div class="p-5 text-center">
        <Lucide
          :icon="props.type === 'info' ? 'Info' : 'XCircle'"
          :class="['mx-auto mt-3 h-16 w-16 text-danger', { 'text-warning': props.type === 'info' }]"
        />
        <div class="mt-5 text-3xl">{{ props.title }}</div>
      </div>
      <div class="px-5 pb-8 text-center">
        <Button
          ref="{deleteButtonRef}"
          type="button"
          :variant="props.type === 'info' ? 'warning' : 'danger'"
          class="mr-2 w-24"
          @click="emits('confirm')"
        >
          {{ props.labelBtn }}
        </Button>
        <Button type="button" variant="outline-secondary" class="w-24" @click="emits('onClose')">
          Đóng
        </Button>
      </div>
    </Dialog.Panel>
  </Dialog>
</template>
