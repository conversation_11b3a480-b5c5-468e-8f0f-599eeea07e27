<script setup lang="ts">
import dayjs from "dayjs";
import DatePicker from "primevue/datepicker";
import { ref } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import FormInput from "@/base-components/Form/FormInput.vue";
import Lucide from "@/base-components/Lucide";
import TomSelect from "@/base-components/TomSelect";

interface Props {
  users: UserResponse[];
  filters: {
    creator: string;
    person: string;
    date: dayjs.Dayjs[];
  };
}

const props = defineProps<Props>();

const filterState = ref<Props["filters"]>(props.filters);
</script>

<template>
  <div class="mt-7 block h-10 justify-between sm:flex">
    <h2 class="mr-5 truncate text-lg font-medium">Danh sách cuộc gọi</h2>
    <div class="items-center sm:flex">
      <div class="flex">
        <div
          class="z-30 -mr-1 flex w-10 items-center justify-center rounded-l border bg-slate-100 text-slate-600 dark:border-darkmode-800 dark:bg-darkmode-700 dark:text-slate-400"
        >
          <Lucide icon="User" class="h-4 w-4" />
        </div>
        <TomSelect
          v-model="filterState.creator"
          :options="{ placeholder: 'Chọn người thực hiện' }"
          class="ove w-52 bg-white"
        >
          <option value="0">Tất cả</option>
          <option v-for="user of users" :key="user.id" :value="user.id">{{ user.name }}</option>
        </TomSelect>
      </div>

      <FormInput v-model="filterState.person" placeholder="Tìm kiếm khách hàng" class="ml-2 w-52" />

      <div class="relative ml-2 w-52">
        <DatePicker
          v-model="filterState.date"
          placeholder="Chọn thời gian"
          date-format="dd/mm/yy"
          size="small"
          show-icon
          icon-display="input"
          selection-mode="range"
          class="w-full"
        />
      </div>
    </div>
  </div>
</template>
