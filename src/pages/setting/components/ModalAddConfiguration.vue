<script setup lang="ts">
import { ref } from "vue";

import { SettingAddRequest } from "@/api/bcare-types";
import Button from "@/base-components/Button";
import { FormInput, FormLabel } from "@/base-components/Form";
import { Dialog } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import { deepClone } from "@/utils/helper";
import { defaultDataAddConfiguration } from "../constants";

const props = defineProps<{ isOpen: boolean }>();
const emits = defineEmits<{
  (event: "onClose"): void;
  (event: "submit", data: SettingAddRequest): void;
}>();
const dataForm = ref<SettingAddRequest>(deepClone(defaultDataAddConfiguration));
const handleClose = () => {
  emits("onClose");
  dataForm.value = deepClone(defaultDataAddConfiguration);
};
</script>

<template>
  <Dialog size="lg" :open="props.isOpen">
    <Dialog.Panel class="overflow-hidden">
      <Dialog.Title class="bg-white p-5">
        <h2 class="mr-auto text-base font-medium">Thêm cấu hình</h2>
        <a href="#" class="absolute right-0 top-0 mr-3 mt-3" @click="handleClose">
          <Lucide icon="X" class="h-8 w-8 text-slate-400" />
        </a>
      </Dialog.Title>
      <Dialog.Description>
        <div class="mb-3">
          <FormLabel html-for="input-name" class="font-bold">Tên</FormLabel>
          <FormInput
            id="input-name"
            v-model="dataForm.name"
            type="text"
            class="flex-1"
            placeholder="Tên"
          />
        </div>
        <div class="mb-3">
          <FormLabel html-for="input-category" class="font-bold">Loại</FormLabel>
          <FormInput
            id="input-category"
            v-model="dataForm.category"
            type="text"
            class="flex-1"
            placeholder="Loại"
          />
        </div>
        <div class="mb-3">
          <FormLabel html-for="input-description" class="font-bold">Mô tả</FormLabel>
          <FormInput
            id="input-description"
            v-model="dataForm.description"
            type="text"
            class="flex-1"
            placeholder="Mô tả"
          />
        </div>
      </Dialog.Description>
      <Dialog.Footer>
        <Button variant="primary" type="button" class="w-28" @click="emits('submit', dataForm)">
          Add
        </Button>
      </Dialog.Footer>
    </Dialog.Panel>
  </Dialog>
</template>
