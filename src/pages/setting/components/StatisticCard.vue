<script setup lang="ts">
import Lucide from "@/base-components/Lucide";
import { Icon } from "@/base-components/Lucide";
import Tippy from "@/base-components/Tippy";

interface Props {
  icon: Icon;
  label: string;
  total: number;
  percentage: number;
  isActive: boolean;
  tooltipContent: string;
  onClick: () => void;
}

defineProps<Props>();
</script>

<template>
  <div
    :class="[
      'zoom-in relative',
      'before:absolute before:inset-x-0 before:mx-auto before:mt-2.5 before:h-full before:w-[90%] before:rounded-md before:bg-white/60 before:shadow-[0px_3px_5px_#0000000b] before:content-[\'\'] before:dark:bg-darkmode-400/70',
    ]"
    @click="onClick"
  >
    <div :class="['box p-5', { 'border-2 border-success dark:border-darkmode-400': isActive }]">
      <div class="flex">
        <Lucide :icon="icon" class="h-[28px] w-[28px] text-primary" />
        <div class="ml-auto">
          <Tippy
            as="div"
            class="flex cursor-pointer items-center rounded-full py-[3px] pl-2 pr-1 text-xs font-medium text-white"
            :class="percentage > 0 ? 'bg-success' : 'bg-danger'"
            :content="tooltipContent"
          >
            {{ Math.abs(percentage) }}%
            <Lucide :icon="percentage > 0 ? 'ChevronUp' : 'ChevronDown'" class="ml-0.5 h-4 w-4" />
          </Tippy>
        </div>
      </div>
      <div class="mt-6 text-3xl font-medium leading-8">{{ total || 0 }}</div>
      <div class="mt-1 text-base text-slate-500">{{ label }}</div>
    </div>
  </div>
</template>

<style scoped>
/* Add any additional styles if needed */
</style>
