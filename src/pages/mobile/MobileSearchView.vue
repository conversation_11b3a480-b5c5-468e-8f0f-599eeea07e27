<template>
  <div class="mobile-search-view fixed inset-0 z-50 flex flex-col bg-white dark:bg-slate-900">
    <!-- Custom Header (not using MobileLayout header) -->
    <header
      class="flex h-14 items-center border-b border-slate-200 bg-primary px-4 shadow-md dark:border-slate-700"
    >
      <div class="-intro-x flex w-full items-center gap-2">
        <Button
          icon="pi pi-arrow-left"
          text
          rounded
          aria-label="Go back"
          class="flex-none bg-white/10 text-white/80 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/50"
          @click="handleBack"
        />

        <div class="flex-1" v-once>
          <IconField class="w-full">
            <InputIcon class="pi pi-search" />
            <InputText
              id="mobile-search-input"
              v-model="personPayload.search"
              autofocus
              class="w-full rounded-full border-none bg-white/20 text-white placeholder-white/70"
              type="text"
              placeholder="Tìm kiếm thông tin"
              @input="handleSearchPerson"
              @keydown="handleKeydown"
            />
            <InputIcon
              v-if="personPayload.search"
              class="pi pi-times cursor-pointer"
              @click="clearSearch"
            />
          </IconField>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-slate-800">
      <div v-if="isLoading" class="p-4">
        <div class="mb-3 text-base font-medium">Đang tìm kiếm...</div>
        <div class="space-y-2">
          <div
            v-for="i in 3"
            :key="i"
            class="rounded-lg border border-slate-200 bg-white p-2 shadow-sm dark:border-slate-700 dark:bg-slate-800"
          >
            <div class="flex items-center gap-2">
              <Skeleton shape="circle" size="2rem" class="flex-shrink-0" />
              <div class="flex-1">
                <Skeleton width="75%" height="0.875rem" class="mb-1.5" />
                <Skeleton width="50%" height="0.75rem" />
              </div>
              <Skeleton shape="circle" size="1.25rem" class="flex-shrink-0" />
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="personList.length > 0" class="p-4">
        <div class="mb-3 text-base font-medium">Kết quả tìm kiếm</div>
        <div class="space-y-2">
          <div
            v-for="person in personList"
            :key="person.id"
            class="flex cursor-pointer items-center rounded-lg border border-slate-200 bg-white p-2 shadow-sm hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-700"
            @click="handlePersonClick(person)"
          >
            <PersonAvatar :person="person" :issues="person.issues" class="h-9 w-9 flex-shrink-0" />
            <div class="ml-2 flex-1">
              <div class="font-medium leading-tight text-slate-800 dark:text-slate-200">
                <HighlightText :text="person.full_name" :highlight="personPayload.search" />
              </div>
              <div v-if="person.person_field?.code" class="mt-0.5 text-xs">
                <span
                  class="rounded border border-warning/50 bg-orange-50 px-0.5 text-xs font-semibold text-warning/80 dark:bg-secondary"
                >
                  <HighlightText
                    :text="person.person_field.code"
                    :highlight="personPayload.search"
                  />
                </span>
              </div>
            </div>
            <i
              class="pi pi-chevron-right flex-shrink-0 cursor-pointer p-2 text-slate-400"
              aria-label="View details"
            />
          </div>
        </div>
      </div>

      <div
        v-else-if="personPayload.search && !personList.length"
        class="flex h-full flex-col items-center justify-center p-4"
      >
        <Empty />
      </div>

      <div v-else class="flex h-full flex-col items-center justify-center p-4">
        <div
          class="w-full max-w-sm rounded-lg border border-slate-200 bg-white p-4 text-center shadow-sm dark:border-slate-700 dark:bg-slate-800"
        >
          <div
            class="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-700"
          >
            <i class="pi pi-search text-2xl text-slate-400 dark:text-slate-500" />
          </div>
          <h3 class="mb-1 text-base font-medium text-slate-800 dark:text-slate-200">
            Tìm kiếm khách hàng
          </h3>
          <p class="text-sm text-slate-500 dark:text-slate-400">
            Nhập từ khóa để tìm kiếm thông tin khách hàng
          </p>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
import _ from "lodash";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

import HighlightText from "@/base-components/HighlightText.vue";
import PersonAvatar from "@/components/Person/PersonAvatar.vue";
import usePerson from "@/hooks/usePerson";
import Empty from "@/base-components/Empty";

const router = useRouter();

const personPayload = reactive({
  page_size: 0,
  page: 0,
  filter: {
    status: 2,
  },
  search: "",
});

const { listPersons, persons: personList } = usePerson();

const isLoading = ref(false);

const performSearch = async () => {
  try {
    await listPersons({ ...personPayload });
  } finally {
    isLoading.value = false;
  }
};

const debouncedSearch = _.debounce(performSearch, 300);

const handleSearchPerson = (event: Event) => {
  const val = (event.target as HTMLInputElement).value;
  personPayload.search = val;

  debouncedSearch.cancel();

  if (val.length >= 2) {
    isLoading.value = true;
    personPayload.page = 1;
    personPayload.page_size = 20;
    debouncedSearch();
  } else {
    isLoading.value = false;
    personList.value = [];
  }
};

const clearSearch = () => {
  debouncedSearch.cancel();
  personPayload.search = "";
  isLoading.value = false;
  personList.value = [];
};

const handleBack = () => {
  router.back();
};

const handlePersonClick = (person: any) => {
  router.push({
    path: `/customer/profile/${person.id}`,
  });
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && personPayload.search) {
    event.preventDefault();
  }
};
</script>
