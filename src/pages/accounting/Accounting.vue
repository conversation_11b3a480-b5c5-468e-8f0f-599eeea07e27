<script setup lang="ts">
import { ref } from "vue";

import Litepicker from "@/base-components/Litepicker";
import Button from "../../base-components/Button";
import { FormInput, FormSelect } from "../../base-components/Form";
import Lucide from "../../base-components/Lucide";
import Pagination from "../../base-components/Pagination";
import Table from "../../base-components/Table";

const date = ref("");

const data = ref([
  {
    Order: 3,
    Service: "Chụp film toàn cảnh Panorex",
    Quantity: 1,
    Unit: "phim",
    Unit_Price: "150,000 VND",
    Total_Price: "150,000 VND",
    Discount: "0 VND",
    Grand_Total: "150,000 VND",
  },
  {
    Order: 7,
    Service: "Cạo vôi răng định kỳ độ 1 (1 lần)",
    Quantity: 3,
    Unit: "hàm",
    Unit_Price: "300,000 VND",
    Total_Price: "900,000 VND",
    Discount: "270,000 VND",
    Grand_Total: "630,000 VND",
  },
  {
    Order: 10,
    Service: "Điều trị viêm nướu mức độ 2 (CVR khoảng 3-4 lần)",
    Quantity: 1,
    Unit: "hàm",
    Unit_Price: "900,000 VND",
    Total_Price: "900,000 VND",
    Discount: "0 VND",
    Grand_Total: "900,000 VND",
  },
  {
    Order: 27,
    Service: "Nhổ răng cối nhỏ",
    Quantity: 4,
    Unit: "răng",
    Unit_Price: "800,000 VND",
    Total_Price: "3,200,000 VND",
    Discount: "960,000 VND",
    Grand_Total: "2,240,000 VND",
  },
  {
    Order: 29,
    Service: "Nhổ răng khôn hàm trên- Mức độ 1",
    Quantity: 2,
    Unit: "răng",
    Unit_Price: "1,500,000 VND",
    Total_Price: "3,000,000 VND",
    Discount: "900,000 VND",
    Grand_Total: "2,100,000 VND",
  },
  {
    Order: 86,
    Service: "Bàn chải lông mềm",
    Quantity: 1,
    Unit: "cái",
    Unit_Price: "35,000 VND",
    Total_Price: "35,000 VND",
    Discount: "0 VND",
    Grand_Total: "35,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 1,
    Unit: "",
    Unit_Price: "1,250,000 VND",
    Total_Price: "1,250,000 VND",
    Discount: "0 VND",
    Grand_Total: "1,250,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 4,
    Unit: "",
    Unit_Price: "1,300,000 VND",
    Total_Price: "5,200,000 VND",
    Discount: "0 VND",
    Grand_Total: "5,200,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 3,
    Unit: "",
    Unit_Price: "1,350,000 VND",
    Total_Price: "4,050,000 VND",
    Discount: "0 VND",
    Grand_Total: "4,050,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 5,
    Unit: "",
    Unit_Price: "1,400,000 VND",
    Total_Price: "7,000,000 VND",
    Discount: "0 VND",
    Grand_Total: "7,000,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 3,
    Unit: "",
    Unit_Price: "1,450,000 VND",
    Total_Price: "4,350,000 VND",
    Discount: "0 VND",
    Grand_Total: "4,350,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 1,
    Unit: "",
    Unit_Price: "1,500,000 VND",
    Total_Price: "1,500,000 VND",
    Discount: "0 VND",
    Grand_Total: "1,500,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 2,
    Unit: "",
    Unit_Price: "1,650,000 VND",
    Total_Price: "3,300,000 VND",
    Discount: "0 VND",
    Grand_Total: "3,300,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 1,
    Unit: "",
    Unit_Price: "1,700,000 VND",
    Total_Price: "1,700,000 VND",
    Discount: "0 VND",
    Grand_Total: "1,700,000 VND",
  },
  {
    Order: 127,
    Service: "Phí",
    Quantity: 1,
    Unit: "",
    Unit_Price: "1,800,000 VND",
    Total_Price: "1,800,000 VND",
    Discount: "0 VND",
    Grand_Total: "1,800,000 VND",
  },
  {
    Order: 141,
    Service: "Nước súc miệng Kin",
    Quantity: 1,
    Unit: "",
    Unit_Price: "140,000 VND",
    Total_Price: "140,000 VND",
    Discount: "0 VND",
    Grand_Total: "140,000 VND",
  },
]);
</script>

<template>
  <div class="intro-y col-span-12 mt-10 flex flex-wrap items-center sm:flex-nowrap">
    <h2 class="text-lg font-medium">Dịch vụ điều trị</h2>

    <div class="mx-auto hidden text-slate-500 md:block"></div>
    <div class="mt-3 flex sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
      <FormSelect class="!box sm:mt-0 lg:w-64">
        <option>Tất cả phòng khám</option>
        <option>Phòng khám Updental</option>
      </FormSelect>
      <div class="relative ml-2">
        <div
          class="absolute flex h-full w-10 items-center justify-center rounded-l border bg-slate-100 text-slate-500 dark:border-darkmode-800 dark:bg-darkmode-700 dark:text-slate-400"
        >
          <Lucide icon="Calendar" class="h-4 w-4" />
        </div>
        <Litepicker
          v-model="date"
          :options="{
            autoApply: true,
            singleMode: false,
            numberOfColumns: 2,
            numberOfMonths: 2,
            showWeekNumbers: true,
            format: 'DD/MM/YYYY',
            dropdowns: {
              minYear: 1990,
              maxYear: null,
              months: true,
              years: true,
            },
          }"
          class="pl-12 lg:w-64"
        />
      </div>
      <div class="relative">
        <FormInput type="text" class="!box ml-2 px-4 pr-10 lg:w-64" placeholder="Tìm kiếm" />
        <Lucide
          icon="Search"
          class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4 text-slate-500"
        />
      </div>
      <Button variant="primary" class="ml-2 shadow-md"> KT </Button>
      <Button variant="primary" class="ml-2 shadow-md">
        <Lucide icon="FileText" class="mr-2 h-4 w-4" />Excel
      </Button>
    </div>
  </div>
  <div class="mt-5 grid grid-cols-12 gap-6">
    <!-- BEGIN: Data List -->
    <div class="intro-y col-span-12 overflow-auto lg:overflow-visible">
      <Table class="-mt-2 border-separate border-spacing-y-[10px]">
        <Table.Thead>
          <Table.Tr>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Mã SP </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Tên sản phẩm </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Số lượng bán </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Đvt </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Giá </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Tổng tiền </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Tổng giảm giá </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Thành tiền </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <Table.Tr v-for="(item, index) in data" :key="index">
            <Table.Td
              class="w-20 border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Order }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Service }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <div class="mr-3 flex items-center text-slate-500" href="#">
                {{ item.Quantity }}
              </div>
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <div class="mr-3 flex items-center text-slate-500" href="#">
                {{ item.Unit }}
              </div>
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Unit_Price }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Total_Price }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Discount }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.Grand_Total }}
            </Table.Td>
          </Table.Tr>
        </Table.Tbody>
      </Table>
    </div>
    <!-- END: Data List -->
    <!-- BEGIN: Pagination -->
    <div class="intro-y col-span-12 flex flex-wrap items-center sm:flex-row sm:flex-nowrap">
      <Pagination class="w-full sm:mr-auto sm:w-auto">
        <Pagination.Link>
          <Lucide icon="ChevronsLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>1</Pagination.Link>
        <Pagination.Link active>2</Pagination.Link>
        <Pagination.Link>3</Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronRight" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronsRight" class="h-4 w-4" />
        </Pagination.Link>
      </Pagination>
      <FormSelect class="!box mt-3 w-20 sm:mt-0">
        <option>10</option>
        <option>25</option>
        <option>35</option>
        <option>50</option>
      </FormSelect>
    </div>
    <!-- END: Pagination -->
  </div>
  <!-- BEGIN: Delete Confirmation Modal -->
</template>
