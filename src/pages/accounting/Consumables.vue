<script setup lang="ts">
import { reactive, ref } from "vue";

import Litepicker from "@/base-components/Litepicker";
import Button from "../../base-components/Button";
import { FormInput, FormSelect } from "../../base-components/Form";
import Lucide from "../../base-components/Lucide";
import Pagination from "../../base-components/Pagination";
import Table from "../../base-components/Table";

const date = ref("");

const data = reactive([
  {
    order: "1",
    order_code: "423394",
    date: "28/11/2023",
    customer_code: "18014",
    customer_name: "<PERSON><PERSON>",
    items: [{ product: "Niti 016 X 022 U", quantity: "1 sợi" }],
  },
  {
    order: "2",
    order_code: "423395",
    date: "28/11/2023",
    customer_code: "16759",
    customer_name: "<PERSON><PERSON><PERSON>",
    items: [{ product: "Ss 016 X 022 U", quantity: "1 sợi" }],
  },
  {
    order: "3",
    order_code: "423396",
    date: "28/11/2023",
    customer_code: "11420",
    customer_name: "<PERSON><PERSON><PERSON><PERSON>ng",
    items: [{ product: "Ss 016 X 022 U", quantity: "1 sợi" }],
  },
  {
    order: "4",
    order_code: "423397",
    date: "28/11/2023",
    customer_code: "18984",
    customer_name: "Nguyễn Thành Trung",
    items: [
      { product: "Niti 014 U", quantity: "1 sợi" },
      { product: "Tube R36", quantity: "1 cái" },
      { product: "Tube R46", quantity: "1 cái" },
      { product: "Mắc Cài Kim Loại Hàm Dưới", quantity: "1 bộ" },
    ],
  },
  {
    order: "5",
    order_code: "423398",
    date: "28/11/2023",
    customer_code: "19247",
    customer_name: "Nguyễn Đình Bảo Ngọc",
    items: [
      { product: "Niti 014 U", quantity: "1 sợi" },
      { product: "Niti 016 U", quantity: "1 sợi" },
      { product: "Tube R36", quantity: "1 cái" },
      { product: "Tube R46", quantity: "1 cái" },
      { product: "Mắc Cài Kim Loại Hàm Dưới", quantity: "1 bộ" },
    ],
  },
]);
// const calculateRowspan = (order: any) => {
//   return order.items.length;
// };
</script>

<template>
  <div class="intro-y col-span-12 mt-10 flex flex-wrap items-center sm:flex-nowrap">
    <h2 class="text-lg font-medium">Vật tư tiêu hao</h2>

    <div class="mx-auto hidden text-slate-500 md:block"></div>
    <div class="mt-3 flex sm:ml-auto sm:mt-0 sm:w-auto md:ml-0">
      <FormSelect class="!box sm:mt-0 lg:w-64">
        <option>Tất cả phòng khám</option>
        <option>Phòng khám Updental</option>
      </FormSelect>
      <div class="relative ml-2">
        <div
          class="absolute flex h-full w-10 items-center justify-center rounded-l border bg-slate-100 text-slate-500 dark:border-darkmode-800 dark:bg-darkmode-700 dark:text-slate-400"
        >
          <Lucide icon="Calendar" class="h-4 w-4" />
        </div>
        <Litepicker
          v-model="date"
          :options="{
            autoApply: true,
            singleMode: false,
            numberOfColumns: 2,
            numberOfMonths: 2,
            showWeekNumbers: true,
            format: 'DD/MM/YYYY',
            dropdowns: {
              minYear: 1990,
              maxYear: null,
              months: true,
              years: true,
            },
          }"
          class="pl-12 lg:w-64"
        />
      </div>
      <div class="relative">
        <FormInput type="text" class="!box ml-2 px-4 pr-10 lg:w-64" placeholder="Tìm kiếm" />
        <Lucide
          icon="Search"
          class="absolute inset-y-0 right-0 my-auto mr-3 h-4 w-4 text-slate-500"
        />
      </div>
      <Button variant="primary" class="ml-2 shadow-md">
        <Lucide icon="FileText" class="mr-2 h-4 w-4" />Excel
      </Button>
    </div>
  </div>
  <div class="mt-5 grid grid-cols-12 gap-6">
    <!-- BEGIN: Data List -->
    <div class="intro-y col-span-12 overflow-auto lg:overflow-visible">
      <Table class="-mt-2 border-separate border-spacing-y-[10px]">
        <Table.Thead>
          <Table.Tr>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> STT </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Mã hóa đơn </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Ngày khám </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0"> Mã hồ sơ </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Tên khách hàng </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Tên sản phẩm </Table.Th>
            <Table.Th class="whitespace-nowrap border-b-0 text-center"> Số lượng </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          <Table.Tr v-for="(item, index) in data" :key="index">
            <Table.Td
              class="w-20 border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.order }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.order_code }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <div class="mr-3 flex items-center text-slate-500" href="#">
                {{ item.date }}
              </div>
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <div class="mr-3 flex items-center text-slate-500" href="#">
                {{ item.customer_code }}
              </div>
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              {{ item.customer_name }}
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <template v-for="product in item.items">
                <div>{{ product.product }}</div>
              </template>
            </Table.Td>
            <Table.Td
              class="border-b-0 bg-white text-center shadow-[20px_3px_20px_#0000000b] first:rounded-l-md last:rounded-r-md dark:bg-darkmode-600"
            >
              <template v-for="quantity in item.items">
                <div>{{ quantity.quantity }}</div>
              </template>
            </Table.Td>
          </Table.Tr>
        </Table.Tbody>
      </Table>
    </div>
    <!-- END: Data List -->
    <!-- BEGIN: Pagination -->
    <div class="intro-y col-span-12 flex flex-wrap items-center sm:flex-row sm:flex-nowrap">
      <Pagination class="w-full sm:mr-auto sm:w-auto">
        <Pagination.Link>
          <Lucide icon="ChevronsLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronLeft" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>1</Pagination.Link>
        <Pagination.Link active>2</Pagination.Link>
        <Pagination.Link>3</Pagination.Link>
        <Pagination.Link>...</Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronRight" class="h-4 w-4" />
        </Pagination.Link>
        <Pagination.Link>
          <Lucide icon="ChevronsRight" class="h-4 w-4" />
        </Pagination.Link>
      </Pagination>
      <FormSelect class="!box mt-3 w-20 sm:mt-0">
        <option>10</option>
        <option>25</option>
        <option>35</option>
        <option>50</option>
      </FormSelect>
    </div>
    <!-- END: Pagination -->
  </div>
</template>
