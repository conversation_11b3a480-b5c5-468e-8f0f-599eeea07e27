<script setup lang="ts">
import { FilterMatchMode } from "@primevue/core/api";
import { computed, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";

import { CommonStatus } from "@/api/bcare-enum";
import { UserListRequest, UserListResponse, UserResponse } from "@/api/bcare-types-v2";
import Term from "@/base-components/Term";
import { ColumnDefinition, DataTable } from "@/components/DataTable";
import UserAvatar from "@/components/User/UserAvatar.vue";
import { useConfirmTippy } from "@/composables/useConfirmTippy";
import useDepartment from "@/hooks/useDepartment";
import { useFilterList } from "@/hooks/useFilterList";
import useUser from "@/hooks/useUser";

import UserFormDrawer from "./components/UserFormDrawer.vue";

const { listUsersWithTotal, deleteUser, isLoading, paginatedUsers, totalRecords } = useUser({
  autoLoad: false,
});
const { getDepartmentNameById, departmentOptions } = useDepartment({ autoLoad: true });
const { confirm } = useConfirmTippy();
const router = useRouter();

// State
const tableState = ref<UserListResponse>({
  total: 0,
  total_page: 0,
  users: [],
});
const showDrawer = ref(false);
const selectedUserId = ref<number>();
const perPage = ref<number>(10);
const currentPage = ref(1);

// Column definitions
const columns = computed<ColumnDefinition<any>[]>(() => [
  {
    field: "username",
    header: "Thông tin nhân viên",
    filterType: "text",
    filterPlaceholder: "Tìm kiếm theo tên",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "status",
    header: "Trạng thái",
    filterType: "select",
    filterPlaceholder: "Chọn trạng thái",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: [
      { title: "Active", value: 2 },
      { title: "Inactive", value: 1 },
    ],
  },
  {
    field: "phone",
    header: "Số điện thoại",
    filterType: "text",
    filterPlaceholder: "Tìm kiếm SĐT",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "email",
    header: "Email",
    filterType: "text",
    filterPlaceholder: "Tìm kiếm email",
    filterMatchMode: FilterMatchMode.CONTAINS,
    showFilterMenu: false,
  },
  {
    field: "department_id",
    header: "Phòng ban",
    filterType: "select",
    filterPlaceholder: "Chọn phòng ban",
    filterMatchMode: FilterMatchMode.EQUALS,
    showFilterMenu: false,
    filterOptions: departmentOptions.value.map((dept) => ({
      title: dept.label,
      value: dept.value,
    })),
  },
]);

// Filter configurations
const filterConfigs = {
  username: {
    field: "search",
    isFilter: false,
  },
  status: {
    field: "status",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
  phone: {
    field: "phone",
    isFilter: true,
  },
  email: {
    field: "email",
    isFilter: true,
  },
  department_id: {
    field: "department_id",
    isFilter: true,
    valueTransform: (value: string | number) => Number(value),
  },
};

const defaultFilters: Partial<UserListRequest> = {
  page: 1,
  page_size: perPage.value,
  order_by: "",
};

const { filters, currentFilterPayload } = useFilterList(
  (filters) => {
    loadList(filters);
  },
  filterConfigs,
  defaultFilters,
);

// Thêm watcher để theo dõi sự thay đổi của filters
watch(
  filters,
  () => {
    // Khi filters thay đổi, reset về trang 1
    currentPage.value = 1;
  },
  { deep: true },
);

// Handlers
const handlePageChange = async (event: { first: number; rows: number }) => {
  try {
    const page = Math.floor(event.first / event.rows) + 1;
    currentPage.value = page;

    // Gọi trực tiếp API với page mới, không thông qua useFilterList
    const response = await listUsersWithTotal({
      ...currentFilterPayload.value,
      page,
      page_size: perPage.value,
    });

    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error changing page:", error);
  }
};

const handlePerPageChange = (newPerPage: number) => {
  perPage.value = newPerPage;
  loadList({ ...currentFilterPayload.value, page_size: newPerPage });
};

const handleEdit = (data: any) => {
  selectedUserId.value = data.id;
  showDrawer.value = true;
};

const onUserSaved = () => {
  loadList(currentFilterPayload.value);
};

// Update handleRowClick instead of handleEdit
const handleRowClick = (data: any) => {
  router.push({
    name: "top-menu-user-profile",
    params: { id: data.id },
  });
};

const loadList = async (filters: Partial<UserListRequest>) => {
  try {
    // Luôn sử dụng currentPage.value, vì nó đã được cập nhật bởi watcher nếu filter thay đổi
    const response = await listUsersWithTotal({
      ...filters,
      page: currentPage.value,
      page_size: perPage.value,
    });
    if (response) {
      tableState.value = response;
    }
  } catch (error) {
    console.error("Error loading users:", error);
  }
};

const handleDelete = async (data: UserResponse, e?: MouseEvent) => {
  confirm(e, {
    title: "Xác nhận xóa nhân viên",
    icon: "pi pi-trash",
    acceptLabel: "Xóa",
    rejectLabel: "Hủy",
    onAccept: async () => {
      await deleteUser({ id: data.id });
      loadList(currentFilterPayload.value);
    },
  });
};

// Watchers
watch(
  () => showDrawer.value,
  (newValue) => {
    if (!newValue) {
      selectedUserId.value = undefined;
    }
  },
);

watch(perPage, (newPerPage) => {
  handlePerPageChange(newPerPage);
});

// Đảm bảo load dữ liệu ban đầu
onMounted(() => {
  loadList(defaultFilters);
});
</script>

<template>
  <div class="intro-y mt-5">
    <DataTable
      title="Danh sách nhân viên"
      :columns="columns"
      :data="tableState.users"
      :loading="isLoading"
      :total-records="tableState.total"
      paginator
      :rows="perPage"
      v-model:filters="filters"
      @page="handlePageChange"
      :show-actions="{ edit: true, delete: true }"
      @on-edit="handleEdit"
      @on-delete="handleDelete"
      size="small"
      @row-click="handleRowClick"
      selection-mode="single"
      clickable
    >
      <template #left-header>
        <div class="flex items-center gap-2">
          <span class="text-base font-medium"> Danh sách nhân viên ({{ tableState.total }}) </span>
        </div>
      </template>

      <template #right-header>
        <Button
          severity="primary"
          icon="pi pi-user-plus"
          @click="showDrawer = true"
          label="Thêm nhân viên"
        />
      </template>

      <template #username="{ data }">
        <div class="flex items-center gap-3">
          <UserAvatar
            :user="data"
            :online="data.status === CommonStatus.ACTIVE"
            :muted="data.status !== CommonStatus.ACTIVE"
          />
          <div>
            <div class="flex items-center gap-2 whitespace-nowrap text-base font-medium">
              <span :class="{ 'text-slate-400': data.status !== CommonStatus.ACTIVE }">{{
                data.name
              }}</span>
              <span class="text-sm font-normal text-slate-500">#{{ data.id }}</span>
              <span v-if="data.status !== CommonStatus.ACTIVE" class="text-xs text-slate-500"
                >(Inactive)</span
              >
            </div>
            <div :class="{ 'text-slate-400': data.status !== CommonStatus.ACTIVE }">
              @{{ data.username }}
            </div>
            <div>
              <div
                v-for="role in data.roles"
                :key="role"
                class="mr-1 mt-0.5 inline-block whitespace-nowrap rounded-md px-2 py-0.5 text-xs"
                :class="
                  data.status !== CommonStatus.ACTIVE
                    ? 'bg-slate-100 text-slate-400'
                    : 'bg-slate-200 text-slate-600'
                "
              >
                {{ role }}
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #department_id="{ data }">
        <Term :color-key="getDepartmentNameById(data.department_id)" variant="soft">
          {{ getDepartmentNameById(data.department_id) }}
        </Term>
      </template>

      <template #status="{ data }">
        <div class="flex items-center gap-2">
          <i
            :class="[
              data.status === CommonStatus.ACTIVE
                ? 'pi pi-check-circle text-green-500'
                : 'pi pi-ban text-gray-500',
            ]"
          ></i>
          <span
            :class="[
              data.status === CommonStatus.ACTIVE ? 'text-green-600' : 'text-gray-600',
              'font-medium',
            ]"
          >
            {{ data.status === CommonStatus.ACTIVE ? "Active" : "Inactive" }}
          </span>
        </div>
      </template>
    </DataTable>
  </div>

  <!-- User Form Drawer -->
  <UserFormDrawer v-model:visible="showDrawer" :user-id="selectedUserId" @success="onUserSaved" />
</template>
