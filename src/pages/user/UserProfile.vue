<script setup lang="ts">
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";

import useUser from "@/hooks/useUser";
import { useAuthStore } from "@/stores/auth-store";

import UserProfileHeader from "./components/UserProfileHeader.vue";
import UserProfileInformation from "./components/UserProfileInformation.vue";
import UserProfilePerformanceChart from "./components/UserProfilePerformanceChart.vue";
import UserProfileTaskStats from "./components/UserProfileTaskStats.vue";

const route = useRoute();
const authStore = useAuthStore();
const currentUser = authStore.currentUser;

const { getUserById } = useUser({ autoLoad: true });

// Kiểm tra route để xác định context
const userId = computed(() => {
  const routeId = Number(route.params.id);
  return routeId || currentUser?.id || 0;
});

const user = computed(() => getUserById(userId.value));
</script>

<template>
  <div class="space-y-6 p-8" :key="route.fullPath">
    <!-- User Info Section -->
    <div class="flex flex-col gap-6 lg:flex-row">
      <!-- Left Column - Chiếm 40% -->
      <div class="flex-[3] space-y-6">
        <!-- User Profile Header -->
        <UserProfileHeader :user="user" />

        <!-- User Profile Information -->
        <UserProfileInformation :user="user" />
      </div>

      <!-- Right Column - Performance Chart - Chiếm 60% -->
      <div class="flex-[7]">
        <UserProfilePerformanceChart v-if="user?.id" :user-id="user.id" />
      </div>
    </div>

    <!-- Task Stats Section -->
    <div class="rounded-lg bg-white shadow-md">
      <UserProfileTaskStats :user-id="user?.id" v-if="user?.id" />
    </div>
  </div>
</template>
