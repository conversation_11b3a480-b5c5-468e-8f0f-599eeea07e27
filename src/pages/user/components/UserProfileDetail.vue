<script setup lang="ts">
import { computed, ref } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import Term from "@/base-components/Term";
import FormField from "@/components/Form/FormField.vue";
import { UserAvatar } from "@/components/User";
import useDepartment from "@/hooks/useDepartment";
import { useUpload } from "@/hooks/useUpload";
import useUser from "@/hooks/useUser";
import useUserDashboard from "@/hooks/useUserDashboard";

interface Props {
  user?: UserResponse;
}

const props = defineProps<Props>();
const { getDepartmentNameById } = useDepartment();
const { updateUser } = useUser();

const { uploadFile } = useUpload({
  maxFileSize: 5,
  allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  entity: props.user?.id
    ? {
        id: props.user.id,
        type: "user",
        usageType: "avatar",
      }
    : undefined,
});

const userFields = computed(() => [
  { label: "Email", value: props.user?.email, icon: "pi pi-envelope" },
  { label: "Số điện thoại", value: props.user?.phone, icon: "pi pi-phone" },
  {
    label: "Phòng ban",
    value: props.user?.department_id,
    icon: "pi pi-building",
    type: "department",
  },
  {
    label: "Role",
    value: props.user?.roles,
    icon: "pi pi-id-card",
    type: "roles",
  },
]);

const fileInput = ref<HTMLInputElement | null>(null);

const handleAvatarClick = () => {
  fileInput.value?.click();
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (!file || !props.user?.id) return;

  try {
    const fileResponse = await uploadFile(file);
    await updateUser({
      id: props.user.id,
      profile_image: fileResponse.path,
    });
  } catch (error) {
    console.error("Error uploading avatar:", error);
  }

  if (input) input.value = "";
};

// Thêm phần performance stats
const { performanceStats } = useUserDashboard({
  useStore: true,
  autoLoad: true,
  userId: props.user?.id,
  dateRange: {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date().toISOString(),
  },
});

// Dữ liệu cho mini chart
const miniChartData = computed(() => {
  if (!performanceStats.value?.time_series) return null;

  const { appointments, calls } = performanceStats.value.time_series;

  return {
    labels: appointments.map((item) => new Date(item.date).toLocaleDateString("vi-VN")),
    datasets: [
      {
        label: "Cuộc hẹn",
        data: appointments.map((item) => item.count),
        borderColor: "#4338ca", // indigo-700
        borderWidth: 2,
        tension: 0.4,
        fill: false,
        pointRadius: 0,
      },
      {
        label: "Cuộc gọi",
        data: calls.map((item) => item.count),
        borderColor: "#d1d5db", // gray-300
        borderWidth: 1,
        tension: 0.4,
        fill: false,
        pointRadius: 0,
        borderDash: [5, 5],
      },
    ],
  };
});

const miniChartOptions = computed(() => ({
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      enabled: false,
    },
  },
  scales: {
    x: {
      display: false,
    },
    y: {
      display: false,
    },
  },
  responsive: true,
  maintainAspectRatio: false,
}));

// Tính % tăng trưởng
const growthRate = computed(() => {
  if (!performanceStats.value?.time_series?.appointments) return 0;

  const appointments = performanceStats.value.time_series.appointments;
  const currentTotal = appointments.slice(-3).reduce((sum, item) => sum + item.count, 0);
  const previousTotal = appointments.slice(0, 3).reduce((sum, item) => sum + item.count, 0);

  if (previousTotal === 0) return 0;
  return ((currentTotal - previousTotal) / previousTotal) * 100;
});
</script>

<template>
  <div class="grid w-full grid-cols-1 gap-4">
    <!-- Cột thông tin cá nhân (giữ nguyên) -->
    <div v-if="user" class="w-full rounded-xl bg-white p-6 shadow-lg">
      <div class="-mx-5 flex flex-col lg:flex-row">
        <div class="flex flex-1 items-center px-5">
          <div class="relative">
            <UserAvatar :user="user" size="large" class="!size-24 ring-4 ring-primary/10" />
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handleFileChange"
            />
            <Button
              icon="pi pi-camera"
              rounded
              text
              severity="secondary"
              class="absolute -bottom-1 right-0 bg-white p-2 shadow-md hover:bg-gray-50"
              v-tooltip.bottom="'Change avatar'"
              @click="handleAvatarClick"
            />
          </div>
          <div class="ml-5">
            <div class="text-lg font-medium">{{ user.name }}</div>
            <div class="text-gray-500">@{{ user.username }}</div>
          </div>
        </div>

        <!-- User Fields Section -->
        <div class="mt-6 flex-1 border-l px-5 lg:mt-0 lg:pt-0">
          <div class="font-medium">Thông tin cá nhân</div>
          <div class="mt-4 flex flex-col space-y-3">
            <FormField
              v-for="field in userFields"
              :key="field.label"
              :label="field.label"
              :icon="field.icon"
              inline
              show-colon
            >
              <Term
                v-if="field.type === 'department'"
                :color-key="getDepartmentNameById(field.value as number)"
                variant="soft"
                size="sm"
              >
                {{ getDepartmentNameById(field.value as number) }}
              </Term>

              <div v-else-if="field.type === 'roles'" class="flex flex-wrap gap-1">
                <div
                  v-for="role in field.value"
                  :key="role"
                  class="inline-block whitespace-nowrap rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600"
                >
                  {{ role }}
                </div>
              </div>

              <span v-else class="text-gray-600">{{ field.value || "Not provided" }}</span>
            </FormField>
          </div>
        </div>

        <!-- Additional Section - Performance Mini Chart -->
        <div class="flex-1 px-5 pt-5 lg:mt-0 lg:border-l lg:pt-0">
          <div class="flex items-center gap-2">
            <span class="font-medium">Hiệu suất hoạt động</span>
            <Tag
              :value="`${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(0)}%`"
              :severity="growthRate >= 0 ? 'success' : 'danger'"
              class="text-xs"
            />
          </div>

          <!-- Mini Chart -->
          <div class="mt-4 h-full">
            <Chart
              v-if="miniChartData"
              type="line"
              :data="miniChartData"
              :options="miniChartOptions"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
