<script setup lang="ts">
import MultiSelect from "primevue/multiselect";
import Select from "primevue/select";

import type { UserAddRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";
import useDepartment from "@/hooks/useDepartment";
import useRole from "@/hooks/useRole";

interface Props {
  formData: UserAddRequest;
  errors: Partial<Record<string, string>>;
}

defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: UserAddRequest): void;
}>();

const { departmentOptions, positionOptions } = useDepartment();
const { roleOptions } = useRole();
</script>

<template>
  <div class="space-y-4">
    <div class="flex flex-wrap gap-4">
      <FormField label="Phòng ban" icon="pi pi-building" class="flex-1">
        <Select
          v-model="formData.department_id"
          :options="departmentOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Chọn phòng ban"
          class="w-full"
        />
      </FormField>

      <FormField label="Chức danh" icon="pi pi-id-card" class="flex-1">
        <Select
          v-model="formData.department_position"
          :options="positionOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="Chọn chức danh"
          class="w-full"
        />
      </FormField>
    </div>

    <FormField label="Vai trò" icon="pi pi-users">
      <MultiSelect
        v-model="formData.roles"
        :options="roleOptions"
        optionLabel="label"
        optionValue="value"
        placeholder="Chọn vai trò"
        class="w-full"
        filter
        display="chip"
        :class="{ 'p-invalid': errors.roles }"
      />
      <small class="p-error">{{ errors.roles }}</small>
    </FormField>
  </div>
</template>
