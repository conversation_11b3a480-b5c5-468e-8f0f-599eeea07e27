<script setup lang="ts">
import InputText from "primevue/inputtext";
import { ref, watch } from "vue";

import { USER_DATA_KIND } from "@/api/bcare-enum";
import { UserResponse } from "@/api/bcare-types-v2";
import <PERSON><PERSON>ield from "@/components/Form/FormField.vue";
import useUser from "@/hooks/useUser";

interface Props {
  userId?: number;
  isEdit?: boolean;
  userData?: UserResponse;
}

const props = defineProps<Props>();
const { getUserDataByKind } = useUser({ autoLoad: false });

const line = ref("");
const password_line = ref("");
const errors = ref({
  line: "",
  password_line: "",
});

watch(
  () => props.userData,
  (newUserData) => {
    if (newUserData) {
      const callCenterData = getUserDataByKind(newUserData, USER_DATA_KIND.CALL_CENTER);
      if (callCenterData?.data?.line) {
        try {
          const lineData = JSON.parse(callCenterData.data.line);
          const config = lineData.line || {};
          line.value = config.line || "";
          password_line.value = config.password_line || "";
        } catch (e) {
          console.error("Failed to parse call center config:", e);
        }
      }
    }
  },
  { immediate: true },
);

watch(
  () => props.isEdit,
  () => {
    if (!props.isEdit) {
      line.value = "";
      password_line.value = "";
      errors.value.line = "";
      errors.value.password_line = "";
    }
  },
);

const validate = () => true;

const getLineValue = () => {
  return {
    line: line.value,
    password_line: password_line.value,
  };
};

defineExpose({ validate, getLineValue });
</script>

<template>
  <div class="flex flex-wrap gap-4">
    <FormField label="Line" icon="pi pi-phone" class="min-w-[45%] flex-1">
      <InputText v-model="line" placeholder="Nhập line" class="w-full" />
      <small class="p-error">{{ errors.line }}</small>
    </FormField>

    <FormField label="Password Line" icon="pi pi-lock" class="min-w-[45%] flex-1">
      <InputText
        v-model="password_line"
        type="password"
        placeholder="Nhập password line"
        class="w-full"
      />
      <small class="p-error">{{ errors.password_line }}</small>
    </FormField>
  </div>
</template>
