<script setup lang="ts">
import InputText from "primevue/inputtext";
import SelectButton from "primevue/selectbutton";

import type { UserAddRequest } from "@/api/bcare-types-v2";
import <PERSON>Field from "@/components/Form/FormField.vue";

interface Props {
  formData: UserAddRequest;
  errors: Partial<Record<string, string>>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: UserAddRequest): void;
}>();

const genderOptions = [
  { label: "Nam", value: "male" },
  { label: "Nữ", value: "female" },
];

const handleFormatEmail = () => {
  if (props.formData.email && !props.formData.email.includes("@")) {
    props.formData.email = props.formData.email + "@gmail.com";
  }
};
</script>

<template>
  <div class="space-y-4">
    <div class="flex gap-4">
      <FormField label="Họ và tên" icon="pi pi-user" class="flex-[3]">
        <InputText
          v-model="formData.name"
          placeholder="<PERSON>uyễn Văn A"
          autocomplete="name"
          class="w-full"
          :class="{ 'p-invalid': errors.name }"
        />
        <small class="p-error">{{ errors.name }}</small>
      </FormField>

      <FormField label="Giới tính" icon="pi pi-user" class="flex-1">
        <SelectButton
          v-model="formData.gender"
          :options="genderOptions"
          optionLabel="label"
          optionValue="value"
          class="w-full"
        />
      </FormField>
    </div>

    <FormField label="Email" icon="pi pi-envelope">
      <InputText
        v-model="formData.email"
        type="email"
        placeholder="<EMAIL>"
        autocomplete="email"
        class="w-full"
        :class="{ 'p-invalid': errors.email }"
        @blur="handleFormatEmail"
      />
      <small class="p-error">{{ errors.email }}</small>
    </FormField>

    <FormField label="Số điện thoại" icon="pi pi-phone">
      <InputText
        v-model="formData.phone"
        placeholder="0123456789"
        autocomplete="tel"
        class="w-full"
        :class="{ 'p-invalid': errors.phone }"
      />
      <small class="p-error">{{ errors.phone }}</small>
    </FormField>
  </div>
</template>
