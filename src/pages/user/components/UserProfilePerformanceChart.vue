<script setup lang="ts">
import Chart from "chart.js/auto";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import { StatisticCard } from "@/components/Card";
import { useChart } from "@/composables/useChartConfig";
import useUserDashboard from "@/hooks/useUserDashboard";

interface TimeRangeOption {
  label: string;
  value: number; // số ngày
  key: string;
}

const TIME_RANGE_OPTIONS: TimeRangeOption[] = [
  { label: "7 ngày qua", value: 7, key: "7d" },
  { label: "14 ngày qua", value: 14, key: "14d" },
  { label: "30 ngày qua", value: 30, key: "30d" },
];

const props = defineProps<{
  userId: number;
}>();

const { performanceStats, fetchPerformanceStats, isLoading } = useUserDashboard({
  useStore: true,
  autoLoad: false,
  userId: props.userId,
});

// Prepare data for useChart
const chartDatasets = computed(() => {
  if (!performanceStats.value?.time_series) return [];
  return [
    {
      key: "appointment_count",
      label: "Đặt hẹn",
      data: performanceStats.value.time_series.appointments,
      dataKey: "count",
      color: "#60A5FA", // Blue 400
    },
    {
      key: "call_count",
      label: "Cuộc gọi",
      data: performanceStats.value.time_series.calls,
      dataKey: "count",
      color: "#34D399", // Emerald 400
    },
    {
      key: "note_count",
      label: "Ghi chú",
      data: performanceStats.value.time_series.notes,
      dataKey: "count",
      color: "#F472B6", // Pink 400
    },
    {
      key: "message_count",
      label: "Tin nhắn",
      data: performanceStats.value.time_series.messages,
      dataKey: "count",
      color: "#A78BFA", // Violet 400
    },
  ];
});

const chartLabels = computed(
  () =>
    performanceStats.value?.time_series?.appointments.map((item) =>
      new Date(item.date).toLocaleDateString("vi-VN"),
    ) ?? [],
);

const { chartData, chartOptions } = useChart(chartDatasets, chartLabels, {
  type: "line",
  colorScheme: "StatisticCard",
  enableAnimation: true,
  legendPosition: "bottom",
});

let chart: Chart | null = null;
const chartCanvas = ref<HTMLCanvasElement>();

const selectedTimeRange = ref<TimeRangeOption>(TIME_RANGE_OPTIONS[0]);

const handleTimeRangeChange = async (option: TimeRangeOption) => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - option.value);

  await fetchPerformanceStats(props.userId, startDate.toISOString(), endDate.toISOString());
};

// Watch cho việc thay đổi selectedTimeRange
watch(selectedTimeRange, async (newValue) => {
  await handleTimeRangeChange(newValue);
  if (chart) {
    chart.data = chartData.value;
    chart.update();
  }
});

onMounted(async () => {
  // Fetch data lần đầu với khoảng thời gian mặc định (7 ngày)
  await handleTimeRangeChange(TIME_RANGE_OPTIONS[0]);

  if (chartData.value && chartCanvas.value) {
    const ctx = chartCanvas.value.getContext("2d");
    if (ctx) {
      chart = new Chart(ctx, {
        type: "line",
        data: chartData.value,
        options: chartOptions.value,
      });
    }
  }
});

onUnmounted(() => {
  chart?.destroy();
});

// StatisticItems can reuse the same dataset config
const statisticItems = computed(() => [
  {
    label: "Đặt hẹn",
    key: "appointment_count",
    icon: "pi pi-calendar",
    color: "#60A5FA", // Blue 400
  },
  {
    label: "Cuộc gọi",
    key: "call_count",
    icon: "pi pi-phone",
    color: "#34D399", // Emerald 400
  },
  {
    label: "Ghi chú",
    key: "note_count",
    icon: "pi pi-file",
    color: "#F472B6", // Pink 400
  },
  {
    label: "Tin nhắn",
    key: "message_count",
    icon: "pi pi-comments",
    color: "#A78BFA", // Violet 400
  },
]);

// Thêm state để track dataset đang được chọn
const activeDatasetKey = ref<string | null>(null);

// Thêm hàm xử lý click
const handleCardClick = (chartKey: string) => {
  if (chart) {
    if (activeDatasetKey.value === chartKey) {
      // Nếu click lại item đang active, show tất cả
      activeDatasetKey.value = null;
      chart.data.datasets.forEach((dataset) => {
        dataset.hidden = false;
      });
    } else {
      // Ẩn tất cả tr�� dataset được chọn
      activeDatasetKey.value = chartKey;
      chart.data.datasets.forEach((dataset) => {
        dataset.hidden =
          dataset.label !== chartDatasets.value.find((d) => d.key === chartKey)?.label;
      });
    }
    chart.update();
  }
};
</script>

<template>
  <div class="h-full w-full space-y-4 rounded-lg bg-white p-6 pb-0 shadow-lg">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-800">Hoạt động</h3>
      <div class="flex items-center space-x-2">
        <Select
          v-model="selectedTimeRange"
          :options="TIME_RANGE_OPTIONS"
          optionLabel="label"
          class="w-40"
          :loading="isLoading"
        />
      </div>
    </div>

    <!-- StatisticCards Grid với gap lớn hơn -->
    <div v-if="performanceStats?.overview" class="grid grid-cols-2 gap-6 md:grid-cols-4">
      <StatisticCard
        v-for="item in statisticItems"
        :key="item.key"
        :label="item.label"
        :value="performanceStats.overview"
        :value-key="item.key"
        :icon="item.icon"
        :chart-key="item.key"
        :color="item.color"
        :is-active="activeDatasetKey === item.key"
        @click="handleCardClick(item.key)"
        class="cursor-pointer"
      />
    </div>

    <!-- Chart Container với padding và border -->
    <div class="bg-white px-0 py-4">
      <div ref="chartContainer" class="relative h-[calc(100%-300px)] w-full">
        <canvas ref="chartCanvas"></canvas>
      </div>
    </div>
  </div>
</template>
