<template>
  <div class="space-y-4">
    <div class="grid grid-cols-1 items-center gap-4" :class="{ 'md:grid-cols-3': isEdit }">
      <div :class="{ 'md:col-span-2': isEdit }">
        <FormField label="Tên đăng nhập" icon="pi pi-id-card">
          <InputText
            v-model="formData.username"
            placeholder="username"
            autocomplete="username"
            class="w-full"
            :class="{ 'p-invalid': errors.username }"
            :disabled="isEdit"
          />
          <small class="p-error">{{ errors.username }}</small>
        </FormField>
      </div>

      <div v-if="isEdit">
        <FormField label="Trạng thái" icon="pi pi-check-circle">
          <SelectButton
            v-model="userStatus"
            :options="statusOptions"
            optionLabel="label"
            optionValue="value"
            dataKey="value"
            class="w-full"
            @change="updateStatus"
          >
            <template #option="{ option }">
              <div class="flex items-center gap-2">
                <i
                  :class="[option.icon, option.value === 2 ? 'text-green-500' : 'text-red-500']"
                ></i>
                <span
                  :class="[option.value === 2 ? 'text-green-600' : 'text-red-600', 'font-medium']"
                >
                  {{ option.label }}
                </span>
              </div>
            </template>
          </SelectButton>
        </FormField>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
      <FormField :label="isEdit ? 'Mật khẩu mới (không bắt buộc)' : 'Mật khẩu'" icon="pi pi-lock">
        <Password
          v-model="formData.password"
          :feedback="false"
          toggleMask
          autocomplete="new-password"
          class="w-full"
          :class="{ 'p-invalid': errors.password }"
          inputClass="w-full"
        />
        <small class="p-error">{{ errors.password }}</small>
      </FormField>

      <FormField label="Nhập lại mật khẩu" icon="pi pi-lock">
        <Password
          v-model="confirmPassword"
          :feedback="false"
          toggleMask
          autocomplete="new-password"
          class="w-full"
          :class="{ 'p-invalid': !isPasswordMatch }"
          inputClass="w-full"
        />
        <small v-if="!isPasswordMatch" class="p-error">Mật khẩu không khớp</small>
      </FormField>
    </div>
  </div>
</template>

<script setup lang="ts">
import InputText from "primevue/inputtext";
import Password from "primevue/password";
import SelectButton from "primevue/selectbutton";
import { computed, ref, watch } from "vue";

import type { UserAddRequest, UserUpdateRequest } from "@/api/bcare-types-v2";
import FormField from "@/components/Form/FormField.vue";

interface Props {
  formData: UserAddRequest;
  errors: Partial<Record<string, string>>;
  isEdit?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:formData", value: UserAddRequest): void;
  (e: "statusChange", value: number): void;
}>();

const confirmPassword = ref("");
const userStatus = ref(2); // Default to active

// Status options
const statusOptions = [
  { label: "Active", value: 2, icon: "pi pi-check-circle" },
  { label: "Inactive", value: 1, icon: "pi pi-ban" },
];

// Update status without modifying formData directly
const updateStatus = () => {
  emit("statusChange", userStatus.value);
};

// Hiển thị confirm password khi có nhập password
const shouldShowConfirmPassword = computed(
  () => !props.isEdit || (props.isEdit && !!props.formData.password),
);

// Kiểm tra password match khi có nhập password
const isPasswordMatch = computed(
  () =>
    !shouldShowConfirmPassword.value ||
    !confirmPassword.value ||
    confirmPassword.value === props.formData.password,
);

defineExpose({
  isPasswordMatch,
  userStatus,
});
</script>
