<script setup lang="ts">
import { onMounted } from "vue";

import { TaskStateEnum } from "@/api/bcare-enum";
import { StatisticCard } from "@/components/Card";
import useUserDashboard from "@/hooks/useUserDashboard";
import TaskItem from "@/pages/customer/components/ActivityTab/components/TaskItem.vue";
import router from "@/router";

const props = defineProps<{
  userId: number;
}>();

const { taskStats, fetchTaskStats, isLoading } = useUserDashboard({
  useStore: true,
  autoLoad: false,
  userId: props.userId,
});

// Format helpers
const stateMap = {
  [TaskStateEnum.NEW_TASK]: { class: "bg-yellow-500", text: "Mới tạo" },
  [TaskStateEnum.IN_PROGRESS]: { class: "bg-green-500", text: "Đang thực hiện" },
  [TaskStateEnum.AWAITING_APPROVAL]: { class: "bg-purple-400", text: "Chờ phê duyệt" },
  [TaskStateEnum.COMPLETED]: { class: "bg-blue-500", text: "Hoàn thành" },
  [TaskStateEnum.CANCELLED]: { class: "bg-red-500", text: "Đã hủy" },
  [TaskStateEnum.COMPLETED_EARLY]: { class: "bg-teal-500", text: "Hoàn thành sớm" },
};

const formatStatus = (status: string) => {
  return stateMap[status as TaskStateEnum]?.text || "Không xác định";
};

const getStatusColor = (status: string) => {
  const baseColor = stateMap[status as TaskStateEnum]?.class || "bg-gray-500";
  // Chuyển từ class Tailwind sang mã màu HEX
  const colorMap = {
    "bg-yellow-500": "#EAB308",
    "bg-green-500": "#22C55E",
    "bg-purple-400": "#A78BFA",
    "bg-blue-500": "#3B82F6",
    "bg-red-500": "#EF4444",
    "bg-teal-500": "#14B8A6",
    "bg-gray-500": "#6B7280",
  };
  return colorMap[baseColor as keyof typeof colorMap] || "#6B7280";
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
};

onMounted(async () => {
  await fetchTaskStats(props.userId);
});

// Thêm các màu 400 mới cho StatisticCards
const statisticColors = {
  total: "#EAB308",
  completed: "#3B82F6",
  remaining: "#A78BFA",
  rate: "#14B8A6",
};

const handleTaskClick = () => {
  router.push(`/task/`);
};
</script>

<template>
  <div class="space-y-6 p-6">
    <template v-if="taskStats">
      <h3 class="text-lg font-semibold text-gray-800">Công việc</h3>

      <!-- Overview Cards -->
      <div class="grid grid-cols-2 gap-6 md:grid-cols-4">
        <StatisticCard
          label="Tổng công việc"
          :value="taskStats.overview.total_tasks"
          icon="pi pi-list"
          chart-key="total"
          :color="statisticColors.total"
        />
        <StatisticCard
          label="Đã hoàn thành"
          :value="taskStats.overview.completed_tasks"
          icon="pi pi-check-circle"
          chart-key="completed"
          :color="statisticColors.completed"
        />
        <StatisticCard
          label="Còn lại"
          :value="taskStats.overview.remaining_tasks"
          icon="pi pi-clock"
          chart-key="remaining"
          :color="statisticColors.remaining"
        />
        <StatisticCard
          label="Tỷ lệ hoàn thành"
          :value="`${taskStats.overview.completion_rate}%`"
          icon="pi pi-chart-line"
          chart-key="rate"
          :color="statisticColors.rate"
        />
      </div>

      <!-- Status Breakdown & Upcoming Tasks -->
      <div class="grid gap-6 md:grid-cols-2">
        <!-- Status Breakdown -->
        <div class="rounded-xl border border-gray-200 bg-white p-4 shadow-md">
          <h3 class="mb-4 text-base font-semibold text-gray-800">Trạng thái công việc</h3>
          <div v-if="taskStats?.status_breakdown?.length" class="space-y-4">
            <div
              v-for="status in taskStats.status_breakdown"
              :key="status.status"
              class="flex items-center space-x-4"
            >
              <div class="flex-1">
                <div class="mb-1 flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-600">
                    {{ formatStatus(status.status) }}
                  </span>
                  <span class="text-sm font-semibold text-gray-900">
                    {{ status.count }}
                  </span>
                </div>
                <div class="h-2 overflow-hidden rounded-full bg-gray-100">
                  <div
                    class="h-full rounded-full transition-all duration-500"
                    :style="{
                      width: `${(status.count / taskStats.overview.total_tasks) * 100}%`,
                      backgroundColor: getStatusColor(status.status),
                    }"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-else class="flex h-16 items-center justify-center text-gray-500">
            Không có dữ liệu
          </div>
        </div>

        <!-- Upcoming Deadlines -->
        <div class="rounded-xl border border-gray-200 bg-white p-4 shadow-md">
          <h3 class="mb-4 text-base font-semibold text-gray-800">Công việc sắp đến hạn</h3>
          <div v-if="taskStats?.upcoming_deadlines?.length">
            <TaskItem
              v-for="task in taskStats.upcoming_deadlines"
              :key="task.id"
              :task="task"
              :show-actions="false"
              :show-checkbox="false"
              class="!p-2 transition-all duration-200 first:!mt-0 last:!mb-0 hover:-translate-y-0.5 hover:!bg-gray-50 hover:shadow-md"
              @click="handleTaskClick()"
            />
          </div>
          <div v-else class="flex h-16 items-center justify-center text-gray-500">
            Không có dữ liệu
          </div>
        </div>
      </div>
    </template>

    <!-- No Data State -->
    <div
      v-else
      class="flex min-h-[200px] items-center justify-center rounded-xl border border-gray-200 bg-white"
    >
      <div class="text-center text-gray-500">Không có dữ liệu</div>
    </div>
  </div>
</template>
