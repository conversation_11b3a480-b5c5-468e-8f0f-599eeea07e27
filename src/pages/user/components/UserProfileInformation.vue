<script setup lang="ts">
import { computed } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import Term from "@/base-components/Term";
import FormField from "@/components/Form/FormField.vue";
import useDepartment from "@/hooks/useDepartment";

interface Props {
  user?: UserResponse;
}

const props = defineProps<Props>();

const userFields = computed(() => [
  { label: "Họ và tên", value: props.user?.name, icon: "pi pi-user" },
  { label: "Username", value: props.user?.username, icon: "pi pi-user" },
  { label: "Số điện thoại", value: props.user?.phone, icon: "pi pi-phone" },
  { label: "Email", value: props.user?.email, icon: "pi pi-envelope" },
]);
</script>

<template>
  <div v-if="user" class="w-full rounded-xl bg-white p-6 shadow-md">
    <h3 class="mb-6 text-base font-semibold">Thông tin cá nhân</h3>

    <!-- <div class="prose mb-8 max-w-none">
      <p class="text-gray-600">
        Hi I'm {{ user.name }}, has been the industry's standard dummy text To an English person, it
        will seem like simplified English, as a skeptical Cambridge.
      </p>
    </div> -->

    <div class="grid gap-6">
      <FormField
        v-for="field in userFields"
        :key="field.label"
        :label="field.label"
        :icon="field.icon"
        class="!min-h-0 border-b pb-2.5"
      >
        <span class="text-gray-600">{{ field.value || "Không có" }}</span>
      </FormField>
    </div>
  </div>
</template>
