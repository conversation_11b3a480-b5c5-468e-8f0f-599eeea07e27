<script setup lang="ts">
import Color from "color";
import { computed, ref } from "vue";

import { UserResponse } from "@/api/bcare-types-v2";
import Term from "@/base-components/Term";
import { UserAvatar } from "@/components/User";
import { getContrastTextColor, useHashColor } from "@/composables/useHashColor";
import useDepartment from "@/hooks/useDepartment";
import { useUpload } from "@/hooks/useUpload";
import useUser from "@/hooks/useUser";

import UserFormDrawer from "./UserFormDrawer.vue";

interface Props {
  user?: UserResponse;
  showUploadButton?: boolean;
  showRedirectButton?: boolean;
  showEditButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showUploadButton: true,
  showRedirectButton: false,
  showEditButton: true,
});
const { updateUser } = useUser();
const { getDepartmentNameById } = useDepartment();

const fileInput = ref<HTMLInputElement | null>(null);
const isEditDrawerVisible = ref(false);
const emit = defineEmits<{
  (e: "update"): void;
}>();

// Generate background color based on user name
const backgroundColor = computed(() => {
  if (!props.user?.name) return "#4F46E5"; // Default color
  return useHashColor(props.user.name, "UserAvatar");
});

const textColor = computed(() => getContrastTextColor(backgroundColor.value));

const { uploadFile } = useUpload({
  maxFileSize: 5,
  allowedMimeTypes: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  entity: props.user?.id
    ? {
        id: props.user.id,
        type: "user",
        usageType: "avatar",
      }
    : undefined,
});

const handleAvatarClick = () => {
  fileInput.value?.click();
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];

  if (!file || !props.user?.id) return;

  try {
    const fileResponse = await uploadFile(file);
    await updateUser({
      id: props.user.id,
      profile_image: fileResponse.path,
    });
  } catch (error) {
    console.error("Error uploading avatar:", error);
  }

  if (input) input.value = "";
};

const openEditDrawer = () => {
  isEditDrawerVisible.value = true;
};

const onEditSuccess = () => {
  emit("update");
};
</script>

<template>
  <div v-if="user" class="w-full rounded-xl bg-white shadow-md">
    <!-- Cover Section -->
    <div
      class="relative h-full rounded-t-xl p-6 pb-14"
      :style="{
        backgroundColor: backgroundColor,
        background: `linear-gradient(to right, ${backgroundColor}, ${Color(backgroundColor).lighten(0.2).hex()})`,
      }"
    >
      <!-- Edit button -->
      <div class="absolute -top-4 right-2">
        <Button
          v-if="showEditButton"
          icon="pi pi-user-edit"
          text
          raised
          rounded
          aria-label="User"
          class="!h-10 !w-10 !bg-white !shadow-xl transition-all duration-200 hover:-translate-y-0.5 hover:!bg-gray-100"
          :style="{
            color: backgroundColor,
          }"
          @click="openEditDrawer"
        />
      </div>
    </div>

    <!-- Avatar Section -->
    <div class="relative px-6">
      <div class="absolute -translate-y-1/2">
        <div class="relative">
          <UserAvatar :user="user" class="!size-20 !text-2xl ring-4 ring-white" />
          <input
            v-if="showUploadButton"
            ref="fileInput"
            type="file"
            accept="image/*"
            class="hidden"
            @change="handleFileChange"
          />
          <Button
            v-if="showUploadButton"
            icon="pi pi-camera"
            rounded
            text
            severity="secondary"
            class="absolute -bottom-1 right-0 h-8 w-8 bg-white p-2 shadow-md hover:bg-gray-100"
            v-tooltip.bottom="'Đổi ảnh đại diện'"
            @click="handleAvatarClick"
          />
        </div>
      </div>
    </div>

    <!-- Info Section -->
    <div class="px-6 pb-6 pt-14">
      <div class="flex items-center justify-between">
        <!-- left  -->
        <div>
          <h3 class="text-base font-semibold text-gray-900">{{ user.name }}</h3>
          <p class="text-sm text-gray-600">@{{ user.username }}</p>
        </div>

        <!-- right -->
        <div>
          <div class="flex items-center gap-2">
            <Button
              v-if="showRedirectButton"
              label="Xem thông tin"
              icon="pi pi-arrow-right"
              severity="primary"
              outlined
              size="small"
              :style="{
                border: `1px solid ${backgroundColor}`,
                color: textColor,
                backgroundColor: 'transparent',
              }"
            />
          </div>
          <!-- Display roles and department -->
          <div class="mt-2">
            <div v-if="user?.department_id" class="text-sm text-gray-600">
              <Term :color-key="getDepartmentNameById(user.department_id)" variant="soft" size="sm">
                {{ getDepartmentNameById(user.department_id) }}
              </Term>
            </div>
            <div v-if="user?.roles" class="mt-1 flex flex-wrap gap-1">
              <span
                v-for="role in user.roles"
                :key="role"
                class="inline-block whitespace-nowrap rounded-md bg-slate-200 px-2 py-0.5 text-xs text-slate-600"
              >
                {{ role }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <UserFormDrawer
    v-model:visible="isEditDrawerVisible"
    :user-id="user?.id"
    @success="onEditSuccess"
  />
</template>
