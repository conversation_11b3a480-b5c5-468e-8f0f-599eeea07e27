<template>
  <Drawer
    :closable="false"
    :modal="true"
    :pt="{
      header: 'border-b border-gray-200 px-3 py-2',
      footer: 'px-3 py-2 border-t border-gray-200',
    }"
    :style="{ width: '50rem' }"
    :visible="visible"
    blockScroll
    position="right"
    @hide="onClose"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <span class="text-lg font-medium">
        {{ isEdit ? "Cập nhật nhân viên" : "Thêm nhân viên" }}
      </span>
    </template>

    <form class="p-2" @submit.prevent="onSubmit">
      <div class="space-y-4">
        <Divider align="center">
          <span class="font-medium">Thông tin cơ bản</span>
        </Divider>
        <BasicInformation v-model:formData="formData" :errors="errors" />
        <template v-if="onlyAdmin()">
          <Divider align="center">
            <span class="font-medium">Công việc</span>
          </Divider>
          <WorkInformation v-model:formData="formData" :errors="errors" />
        </template>
        <Divider align="center">
          <span class="font-medium">Tổng đài</span>
        </Divider>
        <CallInfo
          ref="callInfoRef"
          :user-id="userId"
          :is-edit="isEdit"
          :user-data="user"
          @update="(value) => (callCenterConfig = value)"
        />
        <Divider align="center">
          <span class="font-medium">Tài khoản</span>
        </Divider>
        <Credentials
          ref="credentialsRef"
          v-model:formData="formData"
          :errors="errors"
          :is-edit="isEdit"
          @statusChange="handleStatusChange"
        />
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end gap-3 px-3 py-2">
        <Button icon="pi pi-times" label="Hủy" outlined severity="danger" @click="onClose" />
        <Button autofocus icon="pi pi-save" label="Lưu" @click="onSubmit" />
      </div>
    </template>
  </Drawer>
</template>

<script lang="ts" setup>
import Divider from "primevue/divider";
import { computed, ref, watch } from "vue";

import type { UserAddRequest, UserResponse, UserUpdateRequest } from "@/api/bcare-types-v2";
import { useFormValidation } from "@/composables/useFormValidation";
import { usePermissions } from "@/composables/usePermissions";
import useUser from "@/hooks/useUser";
import useUserData from "@/hooks/useUserData";

import BasicInformation from "./UserFormBasicInfo.vue";
import CallInfo from "./UserFormCallInfo.vue";
import Credentials from "./UserFormCredentials.vue";
import WorkInformation from "./UserFormWorkInfo.vue";

interface Props {
  visible: boolean;
  userId?: number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const { addUser, updateUser, getUser } = useUser();
const { setUserData } = useUserData();
const { onlyAdmin } = usePermissions();

const callCenterConfig = ref("");
// Add user ref
const user = ref<UserResponse>();

const credentialsRef = ref();
const callInfoRef = ref();

const isEdit = computed(() => !!props.userId);

// Add this interface to extend UserAddRequest for internal use
interface ExtendedUserData extends UserAddRequest {
  _status?: number; // Internal field not part of the API interface
}

// Form data using ref instead of reactive
const formData = ref<ExtendedUserData>({
  username: "",
  password: "",
  name: "",
  email: "",
  gender: "male",
  phone: "",
  department_id: undefined,
  department_position: "",
  roles: [],
  _status: 2, // Internal status field
});

// Validation rules
const { errors, validateForm } = useFormValidation({
  username: [
    {
      validate: () => true,
      message: "Tên đăng nhập phải có ít nhất 3 ký tự",
    },
  ],
  password: [
    {
      validate: (v) => {
        if (isEdit.value && !v) return true;
        return !!v && v.length >= 6;
      },
      message: "Mật khẩu phải có ít nhất 6 ký tự",
    },
  ],
  name: [
    {
      validate: (v) => !!v && v.length >= 2,
      message: "Họ tên phải có ít nhất 2 ký tự",
    },
  ],
  email: [
    {
      validate: (v) => !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v),
      message: "Email không hợp lệ",
    },
  ],
  phone: [
    {
      validate: (v) => !v || /^[0-9]{10,11}$/.test(v),
      message: "Số điện thoại không hợp lệ",
    },
  ],
  roles: [
    {
      validate: (v) => Array.isArray(v) && v.length > 0,
      message: "Phải chọn ít nhất một vai trò",
    },
  ],
});

// Reset form data to initial state
const resetForm = () => {
  formData.value = {
    username: "",
    password: "",
    name: "",
    email: "",
    gender: "male",
    phone: "",
    department_id: undefined,
    department_position: "",
    roles: [],
    _status: 2, // Internal status field
  };

  user.value = undefined;

  // Reset errors
  Object.keys(errors.value).forEach((key) => {
    errors.value[key] = "";
  });

  // Reset confirm password
  if (credentialsRef.value) {
    credentialsRef.value.confirmPassword = "";
  }
};

// Handle status change from the credentials component
const handleStatusChange = (status: number) => {
  formData.value._status = status;
};

// Watch for userId or visible changes
watch(
  [() => props.userId, () => props.visible],
  ([newUserId, isVisible]) => {
    resetForm();

    if (newUserId && isVisible) {
      getUser({ id: newUserId }).then((userData) => {
        if (userData) {
          user.value = userData; // Store user data
          const {
            username,
            name,
            email,
            gender,
            phone,
            department_id,
            department_position,
            roles,
            status,
          } = userData;

          formData.value = {
            username,
            name,
            email,
            gender: ["male", "female", "unknown"].includes(gender)
              ? (gender as "male" | "female" | "unknown")
              : "unknown",
            phone,
            department_id,
            department_position,
            roles,
            password: "",
            _status: status || 2, // Store status in internal field
          };

          // Update the status in the credentials component
          if (credentialsRef.value) {
            credentialsRef.value.userStatus = status || 2;
          }
        }
      });
    }
  },
  { immediate: true },
);

const onSubmit = async () => {
  if (formData.value.password && !credentialsRef.value?.isPasswordMatch) {
    errors.value.password = "Mật khẩu không khớp";
    return;
  }

  // Validate call info
  if (callInfoRef.value && !callInfoRef.value.validate()) {
    return;
  }

  if (!validateForm(formData.value)) return;

  try {
    if (props.userId) {
      // Update existing user
      const updateData: UserUpdateRequest = {
        id: props.userId,
        name: formData.value.name,
        email: formData.value.email,
        gender: formData.value.gender,
        phone: formData.value.phone,
        department_id: formData.value.department_id,
        department_position: formData.value.department_position,
        roles: formData.value.roles,
      };

      // Only add password if provided
      if (formData.value.password) {
        updateData.password = formData.value.password;
      }

      // Add status for update operations
      if (formData.value._status !== undefined) {
        updateData.status = formData.value._status;
      }

      const res = await updateUser(updateData);

      if (res) {
        // Save call center config
        const lineValue = callInfoRef.value?.getLineValue();
        if (lineValue) {
          try {
            await setUserData(res.id, "call_center", "line", JSON.stringify({ line: lineValue }));
          } catch (error) {
            console.error("Failed to save call center config:", error);
          }
        }

        emit("success");
        onClose();
      }
    } else {
      // Add new user - status not included in UserAddRequest
      const res = await addUser(formData.value);

      if (res) {
        // Save call center config
        const lineValue = callInfoRef.value?.getLineValue();
        if (lineValue) {
          try {
            await setUserData(res.id, "call_center", "line", JSON.stringify({ line: lineValue }));
          } catch (error) {
            console.error("Failed to save call center config:", error);
          }
        }

        emit("success");
        onClose();
      }
    }
  } catch (error) {
    console.error(error);
  }
};

const onClose = () => {
  emit("update:visible", false);
  resetForm();
};
</script>
