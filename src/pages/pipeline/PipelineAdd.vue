<script setup lang="ts">
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

import Alert from "@/base-components/Alert";
import Button from "@/base-components/Button";
import { FormInput, FormLabel } from "@/base-components/Form";
import Lucide from "@/base-components/Lucide";
import usePipeline from "@/pages/pipeline/config/use-pipeline";
import useStage from "@/pages/pipeline/config/use-stage";
const router = useRouter();
const selected = ref(0);

const pipelineData = reactive({
  id: 0,
  name: "",
  description: "",
  user_id: 0,
  status: 0,
});

const selectItem = (index: number) => {
  selected.value = index;
};
const { onAddPipeline, pipelineItem } = usePipeline();
const { onAddStage } = useStage();
const stageList = ref([
  {
    name: "Stage 1",
    description: "",
  },
  {
    name: "Stage 2",
    description: "",
  },
]);

const addStage = () => {
  stageList.value.push({ name: "Stage mới", description: "" });
};

const removeStage = (item: any) => {
  stageList.value.splice(item, 1);
};
const createPipelineAndStage = async () => {
  await onAddPipeline(pipelineData);
  const pipelineId = pipelineItem.value.id;
  if (pipelineId) {
    for (const stage of stageList.value) {
      const stageParam = {
        name: stage.name,
        description: stage.description,
        pipeline_id: pipelineId,
        order_number: 0,
        parent_stage_id: 0,
        status: 2,
      };
      await onAddStage(stageParam);
    }
  }
};
</script>
<template>
  <div class="mt-6 flex flex-col sm:flex-row sm:items-center">
    <div class="intro-x mt-5 text-slate-500 sm:mt-0">
      <FormInput
        v-model="pipelineData.name"
        type="text"
        class="w-full rounded-md"
        placeholder="Tên quy trình"
      />
    </div>
    <div class="ml-auto flex">
      <Button variant="outline-secondary" class="w-24" @click="router.go(-1)"> Hủy </Button>
      <Button variant="primary" class="ml-2 w-24" @click="createPipelineAndStage"> Lưu </Button>
    </div>
  </div>
  <div class="scrollbar-hidden mt-5 flex flex-col overflow-hidden overflow-scroll">
    <div class="inline-flex h-full">
      <div
        v-for="(item, index) in stageList"
        :key="index"
        :class="[
          'left-0 ml-2 w-[190px] min-w-[256px] basis-full first:ml-0  hover:opacity-100',
          { 'opacity-100': selected === index },
          { 'opacity-30': selected !== index },
        ]"
        @click="selectItem(index)"
      >
        <Alert
          class="drag-class box flex h-14 items-center bg-white px-4 text-base font-medium text-primary/80"
        >
          <div class="flex items-center">
            <Lucide icon="Settings" class="mr-2 h-4 w-4" />
            {{ item.name }}
          </div>
          <Lucide icon="X" class="ml-auto h-4 w-4 cursor-pointer" @click="removeStage(item)" />
        </Alert>
        <div
          class="scrollbar-hidden -mt-1 h-[80vh] overflow-hidden overflow-y-scroll rounded-md rounded-t-none bg-secondary px-2 pb-3 pt-3"
        >
          <div class="box p-3">
            <div class="">
              <FormLabel class="flex justify-between"> Tên giai đoạn </FormLabel>
              <FormInput v-model="item.name" placeholder="Tên giai đoạn" />
            </div>
            <div class="mt-3">
              <FormLabel class="flex justify-between"> Mô tả </FormLabel>
              <FormInput v-model="item.description" placeholder="Mô tả giai đoạn" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex-[1 1 0%] m-5 flex min-w-[256px] flex-col items-center justify-center">
        <div class="flex flex-col items-center text-center">
          <div class="mb-w text-lg font-bold">Thêm mới giai đoạn</div>
          <div class="text-md">Các giai đoạn thể hiện quy trình của bạn</div>
        </div>
        <Button variant="primary" class="mt-5" @click="addStage">
          <Lucide icon="Plus" class="mr-2 h-4 w-4" />
          Giai đoạn mới
        </Button>
      </div>
    </div>
  </div>
</template>
