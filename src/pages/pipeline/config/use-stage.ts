import { reactive, toRefs } from "vue";

import {
  StageAddRequest,
  StageDeleteRequest,
  StageGetRequest,
  StageListRequest,
  StageResponse,
  StageUpdateRequest,
} from "@/api/bcare-types-v2";
import { stageAdd, stageDelete, stageGet, stageList, stageUpdate } from "@/api/bcare-v2";
import { useNotiStore } from "@/stores/notification";

export default function useStage() {
  const notiStore = useNotiStore();
  const state = reactive<{
    stageItem?: StageResponse;
    stageList: StageResponse[];
  }>({
    stageList: [],
  });

  const fetchStageList = async (
    request: StageListRequest,
  ): Promise<StageResponse[] | undefined> => {
    try {
      const response = await stageList(request);
      if (response.code === 0) {
        state.stageList = response.data?.stages ?? [];
      } else {
        state.stageList = [];
      }
      return response.data?.stages;
    } catch (error) {
      return [];
    }
  };

  const fetchStage = async (request: StageGetRequest): Promise<StageResponse | undefined> => {
    try {
      const response = await stageGet(request);
      if (response.code === 0 && response.data) {
        state.stageItem = response.data;
        return response.data;
      }
    } catch (error) {
      return undefined;
    }
  };

  const onAddStage = async (request: StageAddRequest) => {
    try {
      const response = await stageAdd(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Thêm mới quy trình thành công",
          message: "",
        });
      }
      return response.code;
    } catch (error) {
      //
    }
  };

  const onUpdateStage = async (request: StageUpdateRequest) => {
    try {
      const response = await stageUpdate(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Chỉnh sửa giai đoạn thành công",
          message: "",
        });
      }
      return response.code;
    } catch (error) {
      //
    }
  };
  const onDeleteStage = async (request: StageDeleteRequest) => {
    try {
      const response = await stageDelete(request);
      if (response.code === 0) {
        notiStore.success({
          title: "Xóa quy trình thành công",
          message: "",
        });
      }
    } catch (error) {
      //
    }
  };

  return {
    ...toRefs(state),
    onAddStage,
    fetchStage,
    fetchStageList,
    onUpdateStage,
    onDeleteStage,
  };
}
