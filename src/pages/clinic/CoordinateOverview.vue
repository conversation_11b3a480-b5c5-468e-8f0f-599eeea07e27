<script setup lang="ts">
import _ from "lodash";
import { computed, onMounted, onUnmounted, ref } from "vue";
import { useRouter } from "vue-router";
import draggable from "vuedraggable";

import Alert from "@/base-components/Alert";
import Button from "@/base-components/Button";
import { FormInput, FormLabel } from "@/base-components/Form";
import { Disclosure } from "@/base-components/Headless";
import { Menu } from "@/base-components/Headless";
import Lucide from "@/base-components/Lucide";
import { useDragScroll } from "@/base-components/ScrollElement";
import { BUILDINGS, WAILTING } from "@/constants";
import fakerData from "@/utils/faker";
const router = useRouter();
const drag = ref(false);
const inputValue = ref("");
const log = function () {
  console.log(WAILTING);
};

const dragOptions = computed(() => {
  console.log("drag option");
  return {
    animation: 200,
    group: "overview",
    disabled: false,
    ghostClass: "ghost",
  };
});
const overview = ref<HTMLElement>();
const { addListeners, removeListeners, dragOver } = useDragScroll(overview);
onMounted(() => {
  addListeners();
});

onUnmounted(() => {
  removeListeners();
});

const onStart = () => (drag.value = true);
const onEnd = () => {
  drag.value = false;
  removeHighlightFromAllZones();
};

const highlightDropzone = (event: DragEvent) => {
  const dropzone = event.currentTarget as HTMLElement;
  dropzone.classList.add("highlight");
};

const unhighlightDropzone = (event: DragEvent, id: string) => {
  updateHighlightForZones(id, false);
};

const drop = (event: MouseEvent) => {
  event.preventDefault();
  removeHighlightFromAllZones();
};

const removeHighlightFromAllZones = () => {
  updateHighlightForZones("", false);
};

const updateHighlightForZones = (id: string, addHighlight: boolean) => {
  const divDropzone = document.querySelectorAll(".zone");
  divDropzone.forEach((dropzoneDiv) => {
    const attrValue = dropzoneDiv.getAttribute("data-dropzone-id");
    if (addHighlight) {
      if (attrValue === id) dropzoneDiv.classList.add("highlight");
    } else if (!id || attrValue !== id) dropzoneDiv.classList.remove("highlight");
  });
};
const person = _.take(fakerData, 6);
const person10 = ref([
  {
    name: "Ngô Văn Dương",
    phone: 9113535326,
    id: 1,
    type: 0,
    date: "07:20 - 12:00",
    doctor: "Bảo Lộc",
    task: "Nhắc ăn no+ nhắc bs xnm",
    photo: person[1].photos[0],
  },
  {
    name: "Phan Thị Trang",
    phone: 9112537272,
    id: 2,
    type: 1,
    date: "08:20 - 12:00",
    doctor: "Trí Lĩnh",
    task: "Dự kiến gắn implant",
    photo: person[2].photos[0],
  },
  {
    name: "Trần Minh Tuấn",
    phone: 9234237773,
    id: 3,
    type: 0,
    date: "09:20 - 12:00",
    doctor: "Hữu Vinh",
    task: "Dự kiến tháo niềng",
    photo: person[3].photos[0],
  },
  {
    name: "Hoàng Văn Thịnh",
    phone: 962838434,
    id: 4,
    type: 1,
    date: "10:20 - 12:00",
    doctor: "Lan Thuyên",
    task: "Tháo mắc cài kim loại",
    photo: person[4].photos[0],
  },
  {
    name: "Đinh Thị Hạnh",
    phone: 9782347772,
    type: 0,
    id: 5,
    date: "11:20 - 12:00",
    doctor: "Văn Quân",
    task: "Chỉnh nha",
    photo: person[5].photos[0],
  },
]);
</script>
<template>
  <div ref="overview" class="overview mt-5 flex space-x-4 overflow-x-scroll pb-4">
    <div>
      <div class="intro-x relative mt-5 lg:mt-0">
        <FormInput
          id="input-search"
          v-model="inputValue"
          type="text"
          :class="[
            'box h-14 w-14 w-full rounded-md border pr-10 shadow-none transition-[width] duration-300 ease-in-out focus:w-64 focus:border-secondary focus:border-opacity-100 focus:ring-0 dark:bg-darkmode-400',
            { 'w-64': inputValue },
          ]"
          placeholder="Tìm kiếm khách hàng"
        />
        <FormLabel
          html-for="input-search"
          class="absolute inset-y-0 right-0 my-auto mr-5 h-5 w-5 cursor-pointer text-xs"
        >
          <Lucide icon="Search" class="text-slate-600 dark:text-slate-500" />
        </FormLabel>
      </div>
      <div v-if="inputValue" class="mt-3">
        <draggable
          v-bind="dragOptions"
          v-model="person10"
          class="list-group scrollbar-hidden h-[75vh] overflow-y-hidden overflow-y-scroll"
          :component-data="{
            tag: 'ul',
            type: 'transition-group',
            name: !drag ? 'flip-list' : null,
          }"
          group="pipeline"
          item-key="id"
          @start="drag = true"
          @end="drag = false"
          @change="log"
        >
          <template #item="{ element }">
            <div class="list-group-item cursor-move">
              <div class="box mb-1 px-3 py-3">
                <div class="flex items-center">
                  <div class="image-fit mr-2 h-8 w-8 overflow-hidden rounded-md">
                    <img alt="Midone Tailwind HTML Admin Template" :src="element.photo" />
                  </div>
                  <div class="font-medium leading-none">
                    <a class="cursor-pointer">
                      {{ element.name }}
                    </a>
                    <div class="mt-1 text-xs text-slate-500">0{{ element.phone }}</div>
                  </div>
                </div>
                <Menu class="absolute right-3 top-3">
                  <Menu.Button
                    :as="Button"
                    class="cursor-pointer rounded-full bg-success bg-opacity-80 px-1 py-1 text-xs font-medium text-white"
                  >
                    <Lucide icon="ChevronRight" class="h-3 w-3" />
                  </Menu.Button>
                  <Menu.Items class="font-medium">
                    <Menu.Item>
                      <Lucide icon="ArrowRight" class="mr-2 h-4 w-4" />
                      <span class="truncate"> Chờ phục vụ </span>
                    </Menu.Item>
                    <Menu.Item>
                      <Lucide icon="Edit" class="mr-2 h-4 w-4" />
                      <span class="truncate"> Cập nhật </span>
                    </Menu.Item>
                  </Menu.Items>
                </Menu>
              </div>
            </div>
          </template>
        </draggable>
        <!--            <a-->
        <!--                href=""-->
        <!--                class="block mt-1 w-full py-4 text-center border border-dotted rounded-md intro-x border-slate-400 dark:border-darkmode-300 text-slate-500"-->
        <!--            >-->
        <!--              Xem thêm-->
        <!--            </a>-->
      </div>
    </div>
    <div
      v-for="building in WAILTING"
      :key="building.id"
      class="sticky left-0 z-50 w-[400px] flex-none"
    >
      <Alert class="box flex items-center bg-white px-4 text-base font-medium text-primary/80">
        <div class="flex items-center">
          <Lucide icon="Settings" class="mr-2 h-4 w-4" />
          {{ building.name }}
        </div>

        <div
          class="ml-auto flex items-center rounded-full border border-primary/80 px-2 py-0.5 text-xs font-medium text-primary"
        >
          03
          <Lucide icon="User" class="ml-1 h-3 w-3" />
        </div>
      </Alert>
      <div
        class="scrollbar-hidden -mt-1 h-[80vh] overflow-hidden overflow-y-scroll rounded-md rounded-t-none bg-secondary px-4 pb-4 pt-5"
      >
        <!--        <Disclosure.Group>-->
        <div
          v-for="doctor in building.doctors"
          :key="doctor.id"
          class="mt-4 rounded-md border-none leading-none first:mt-0 last:mb-0"
        >
          <!--            <Disclosure.Button class="px-4">-->
          <!--              <div class="list-group-item">-->
          <!--                <div class="flex items-center">-->
          <!--                  <Lucide-->
          <!--                      icon="ChevronDown"-->
          <!--                      :class="[-->
          <!--                    'w-4 h-4 mr-2 font-bold transition',-->
          <!--                    { 'transform rotate-180': open },-->
          <!--                  ]"-->
          <!--                  />-->
          <!--                  <div class="font-medium">{{ doctor.name }}</div>-->
          <!--                  <div-->
          <!--                      class="px-2 py-0.5 ml-auto text-xs font-medium flex items-center text-primary rounded-full border border-primary/80"-->
          <!--                  >-->
          <!--                    {{ doctor.customers.length }}-->
          <!--                    <Lucide icon="User" class="ml-1 w-3 h-3"/>-->
          <!--                  </div>-->
          <!--                </div>-->
          <!--              </div>-->
          <!--            </Disclosure.Button>-->
          <!--            <Disclosure.Panel-->
          <!--                class="leading-relaxed text-slate-600 dark:text-slate-500 border-t border-dashed px-2 py-2 -mb-4"-->
          <!--                v-if="doctor.customers.length"-->
          <!--            >-->
          <draggable
            v-bind="dragOptions"
            v-model="doctor.customers"
            class="list-group scrollbar-hidden overflow-y-hidden overflow-y-scroll"
            :component-data="{
              tag: 'ul',
              type: 'transition-group',
              name: !drag ? 'flip-list-move' : null,
            }"
            group="pipeline"
            item-key="id"
            @start="onStart"
            @end="onEnd"
            @change="log"
          >
            <template #item="{ element }">
              <div class="list-group-item cursor-move">
                <div class="box mb-1 px-3 py-3">
                  <div class="flex items-center">
                    <div class="image-fit mr-2 h-8 w-8 overflow-hidden rounded-md">
                      <img alt="Midone Tailwind HTML Admin Template" :src="element.photo" />
                    </div>
                    <div class="font-medium leading-none">
                      <a class="cursor-pointer">
                        {{ element.name }}
                        <span class="text-xs font-normal">(12:30)</span>
                      </a>
                      <div class="mt-0.5 text-xs text-slate-500">Bs. {{ element.doctor }}</div>
                    </div>
                  </div>
                  <div class="mt-1.5 text-xs leading-relaxed text-slate-500">
                    <span
                      class="mr-1 mt-0.5 inline-block whitespace-nowrap rounded-md bg-slate-300 px-2 py-0.5 leading-none text-slate-600 dark:bg-darkmode-300 dark:text-slate-400"
                    >
                      Cạo vôi răng
                    </span>
                    <span
                      class="mr-1 mt-0.5 inline-block whitespace-nowrap rounded-md bg-slate-200 px-2 py-0.5 leading-none text-slate-600 dark:bg-darkmode-300 dark:text-slate-400"
                    >
                      Trám răng
                    </span>
                    <span> {{ element.content }}</span>
                  </div>
                  <Menu class="absolute right-3 top-3">
                    <Menu.Button
                      :as="Button"
                      class="cursor-pointer rounded-full bg-success bg-opacity-80 px-1 py-1 text-xs font-medium text-white"
                    >
                      <Lucide icon="ChevronRight" class="h-3 w-3" />
                    </Menu.Button>
                    <Menu.Items class="font-medium">
                      <Menu.Item>
                        <Lucide icon="FileArchive" class="mr-2 h-4 w-4" />
                        <span class="truncate"> Khám khách mới </span>
                      </Menu.Item>
                      <Menu.Item>
                        <Lucide icon="ListStart" class="mr-2 h-4 w-4" />
                        <span class="truncate"> Lầu 4 </span>
                      </Menu.Item>
                      <Menu.Item>
                        <Lucide icon="ListStart" class="mr-2 h-4 w-4" />
                        <span class="truncate"> Lầu 5 </span>
                      </Menu.Item>
                      <Menu.Item>
                        <Lucide icon="ListStart" class="mr-2 h-4 w-4" />
                        <span class="truncate"> Lầu 6 </span>
                      </Menu.Item>
                    </Menu.Items>
                  </Menu>
                </div>
              </div>
            </template>
          </draggable>

          <!--            </Disclosure.Panel>-->
          <!--            <Disclosure.Panel v-else class="leading-relaxed text-slate-600 dark:text-slate-500 border-t border-dashed px-5 pt-4 -mb-4">-->
          <!--              <div class="text-center mb-4">-->
          <!--                Chưa có khách hàng-->
          <!--              </div>-->
          <!--            </Disclosure.Panel>-->
        </div>
        <!--        </Disclosure.Group>-->
      </div>
    </div>
    <div
      v-for="(building, index) in BUILDINGS"
      :key="building.id"
      class="intro-x z-[100] w-[400px] flex-none"
    >
      <Alert class="box flex items-center bg-white px-4 text-base font-medium text-primary/80">
        <div class="flex items-center">
          <Lucide icon="Settings" class="mr-2 h-4 w-4" />
          {{ building.name }}
        </div>

        <div
          class="ml-auto flex items-center rounded-full border border-primary/80 px-2 py-0.5 text-xs font-medium text-primary"
        >
          30
          <Lucide icon="User" class="ml-1 h-3 w-3" />
        </div>
        <Button
          class="ml-2 bg-primary/90 p-1 hover:bg-slate-800"
          @click="router.push({ name: 'top-menu-assistant' })"
        >
          <Lucide icon="ListStart" class="h-3 w-3 font-extrabold text-white" />
        </Button>
      </Alert>
      <div
        :data-dropzone-id="`dropzone-${index}`"
        :class="[
          'zone scrollbar-hidden -mt-1 h-[80vh] overflow-hidden overflow-y-scroll rounded-md rounded-t-none bg-secondary px-4 pb-4 pt-5',
          { highlight: building.highlight },
        ]"
        @dragover.prevent="dragOver"
        @dragenter="highlightDropzone($event)"
        @dragleave="unhighlightDropzone($event, `dropzone-${index}`)"
        @drop="drop($event)"
      >
        <Disclosure.Group>
          <Disclosure
            v-for="doctor in building.doctors"
            v-slot="{ open }"
            :key="doctor.id"
            class="box mt-4 rounded-md border-none leading-none first:mt-0 last:mb-0"
            :defaul-open="true"
          >
            <Disclosure.Button class="px-4">
              <div class="list-group-item">
                <div class="flex items-center">
                  <Lucide
                    icon="ChevronDown"
                    :class="['mr-2 h-4 w-4 font-bold transition', { 'rotate-180 transform': open }]"
                  />
                  <div class="font-medium">{{ doctor.name }}</div>
                  <div
                    class="ml-auto flex items-center rounded-full border border-primary/80 px-2 py-0.5 text-xs font-medium text-primary"
                  >
                    {{ doctor.customers.length }}
                    <Lucide icon="User" class="ml-1 h-3 w-3" />
                  </div>
                </div>
              </div>
            </Disclosure.Button>
            <Disclosure.Panel
              v-if="doctor.customers.length"
              class="-mb-4 border-t border-dashed px-2 py-2 leading-relaxed text-slate-600 dark:text-slate-500"
            >
              <div v-for="person in doctor.customers" :key="person.id" class="">
                <div class="flex items-center p-2">
                  <div class="image-fit h-10 w-10 flex-none overflow-hidden rounded-md">
                    <img alt="Midone Tailwind HTML Admin Template" :src="person.photo" />
                  </div>
                  <!--                <div class="ml-2 overflow-hidden">-->
                  <!--                  <div class="flex items-center">-->
                  <!--                    <a href="#" class="font-medium leading-none">-->
                  <!--                      {{ person.name }}-->
                  <!--                      <span class=" bg-pending/20 text-pending line-clamp-none"-->
                  <!--                            :class="[-->
                  <!--                                                'text-[11px] px-2 py-0.5 mt-1 lg:mt-0 rounded-full inline-block ml-0.5',-->
                  <!--                                                { 'text-pending bg-pending/20': person.type == 0 },-->
                  <!--                                                { 'text-success bg-success/20': person.type == 1 },-->
                  <!--                                              ]"-->
                  <!--                                              >{{ person.type == 1 ? "Đang điều trị" : "Đang chờ" }}</span>-->
                  <!--                    </a>-->
                  <!--                    <div class="ml-auto text-xs text-slate-400">06:05-->
                  <!--                    </div>-->
                  <!--                  </div>-->
                  <!--                  <div class="w-full text-xs truncate text-slate-500 mt-0.5">There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomi</div>-->
                  <!--                </div>-->
                  <div class="ml-2 flex-1">
                    <div class="flex items-center">
                      <div
                        :class="[
                          'flex items-center font-medium leading-none',
                          { 'text-danger': person.type == 1 },
                        ]"
                      >
                        {{ person.name }}
                        <span
                          class="line-clamp-none bg-pending/20 text-pending"
                          :class="[
                            'ml-1 mt-1 inline-block rounded-full px-2 py-0.5 text-[10px] lg:mt-0',
                            { 'bg-pending/20 text-pending': person.type == 0 },
                            { 'bg-success/20 text-success': person.type == 1 },
                          ]"
                          >{{ person.type == 1 ? "12:20" : "Chờ" }}</span
                        >
                      </div>
                      <div class="ml-auto text-xs">
                        {{ person.time }}
                      </div>
                    </div>
                    <div class="mt-1 text-xs text-slate-500">
                      <span
                        class="mr-1 mt-0.5 inline-block whitespace-nowrap rounded-md bg-slate-300 px-2 py-0.5 text-[10px] leading-none text-slate-600 dark:bg-darkmode-300 dark:text-slate-400"
                      >
                        Cạo vôi răng
                      </span>
                      <span
                        class="mr-1 mt-0.5 inline-block whitespace-nowrap rounded-md bg-slate-200 px-2 py-0.5 text-[10px] leading-none text-slate-600 dark:bg-darkmode-300 dark:text-slate-400"
                      >
                        Trám răng
                      </span>
                      <span>{{ person.content }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </Disclosure.Panel>
            <Disclosure.Panel
              v-else
              class="-mb-4 border-t border-dashed px-5 pt-4 leading-relaxed text-slate-600 dark:text-slate-500"
            >
              <div class="mb-4 text-center">Chưa có khách hàng</div>
            </Disclosure.Panel>
          </Disclosure>
        </Disclosure.Group>
      </div>
    </div>
  </div>
</template>
<style scoped>
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
.highlight {
  background-color: rgb(var(--color-primary) / 0.2);
}
</style>
