export const vLazyLoad = {
  mounted: (el: HTMLImageElement) => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && el.dataset.src) {
            el.src = el.dataset.src;
            el.classList.add("loaded");
            observer.unobserve(el);
          }
        });
      },
      { rootMargin: "50px" },
    );

    observer.observe(el);
  },
};
