<template>
  <Teleport to="body">
    <TransitionRoot appear :show="isOpen">
      <div class="fixed inset-0" @click="closeModal" style="z-index: 10000">
        <!-- Backdrop with fade -->
        <TransitionChild
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div
            class="fixed inset-0 bg-black/85 transition-opacity"
          />
        </TransitionChild>

        <!-- Modal content -->
        <div class="fixed inset-0 flex items-center justify-center p-4">
          <TransitionChild
            enter="ease-out duration-300"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <div class="relative" @click.stop>
              <!-- Close button -->
              <button
                @click="closeModal"
                class="absolute right-3 top-3 z-10 rounded-full p-2 text-white hover:bg-white/10 transition-colors"
                style="z-index: 10001"
              >
                <!-- Sử dụng icon của PrimeVue -->
                <i class="pi pi-times text-lg text-secondary"></i>
              </button>

              <!-- Image -->
              <img
                :src="imageSrc"
                :alt="imageAlt"
                class="max-h-[85vh] max-w-[85vw] rounded-lg object-contain"
              />
            </div>
          </TransitionChild>
        </div>
      </div>
    </TransitionRoot>
  </Teleport>
</template>

<script setup lang="ts">
import { TransitionChild,TransitionRoot } from '@headlessui/vue'
import { ref } from 'vue'

const isOpen = ref(false)
const imageSrc = ref('')
const imageAlt = ref('')

const openModal = (src: string, alt: string = '') => {
  imageSrc.value = src
  imageAlt.value = alt
  isOpen.value = true
}

const closeModal = () => {
  isOpen.value = false
}

defineExpose({
  openModal,
  closeModal
})
</script>

<style scoped>
/* Optional: Add custom transitions if needed */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
