import { type Directive } from 'vue'
import { createVNode, render } from 'vue'

import LightboxModal from "@/directives/Lightbox/LightboxModal.vue";

let lightboxInstance: any = null

const createLightboxModal = () => {
  const container = document.createElement('div')
  const vnode = createVNode(LightboxModal)
  render(vnode, container)
  document.body.appendChild(container)
  return vnode.component?.exposed
}

export const lightbox: Directive = {
  mounted(el) {
    if (!lightboxInstance) {
      lightboxInstance = createLightboxModal()
    }

    const images = el.getElementsByTagName('img')
    Array.from(images).forEach((img) => {
      const imgElement = img as HTMLImageElement
      imgElement.style.cursor = 'pointer'
      imgElement.addEventListener('click', (e) => {
        e.preventDefault()
        e.stopPropagation()
        lightboxInstance?.openModal(imgElement.src, imgElement.alt)
      })
    })
  },

  unmounted(el) {
    const images = el.getElementsByTagName('img')
    Array.from(images).forEach((img) => {
      const imgElement = img as HTMLImageElement
      imgElement.style.cursor = ''
    })
  }
}
