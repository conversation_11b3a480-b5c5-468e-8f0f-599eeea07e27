.tns-outer {
  position: relative;
  .tns-controls button {
    width: theme("spacing.5");
    height: theme("spacing.5");
    background-color: theme("colors.slate.200");
    color: transparent;
    border-radius: theme("borderRadius.full");
    position: absolute;
    top: 0;
    bottom: 0;
    margin-top: auto;
    margin-bottom: auto;
    z-index: theme("zIndex.10");
    outline: none;
    &:hover {
      background-color: theme("colors.slate.300");
    }
    &[data-controls="prev"] {
      left: 0;
      margin-left: calc(theme("spacing.6") * -1);
      &:before {
        content: "";
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(102 113 132)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='lucide lucide-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
        background-size: 80%;
        width: 100%;
        height: 100%;
        background-position: center;
        transform: rotate(90deg);
        position: absolute;
        margin: auto;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
      }
    }
    &[data-controls="next"] {
      right: 0;
      margin-right: calc(theme("spacing.6") * -1);
      &:before {
        content: "";
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(102 113 132)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='lucide lucide-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
        background-size: 80%;
        width: 100%;
        height: 100%;
        background-position: center;
        transform: rotate(-90deg);
        position: absolute;
        margin: auto;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
      }
    }
  }
  .tns-nav {
    margin-bottom: -1.8rem;
    width: 100%;
    position: absolute;
    bottom: 0;
    z-index: theme("zIndex.10");
    display: flex;
    justify-content: center;
    button {
      width: theme("spacing.3");
      height: theme("spacing.3");
      background-color: theme("colors.slate.300");
      border-radius: theme("borderRadius.full");
      margin-left: theme("spacing.1");
      margin-right: theme("spacing.1");
      outline: none;
      &.tns-nav-active {
        background-color: theme("colors.slate.500");
      }
    }
  }
  button[data-action] {
    display: none;
  }
  .tns-visually-hidden {
    display: none;
  }
}

.dark {
  .tns-outer {
    .tns-controls button {
      background-color: theme("colors.darkmode.300");
      &:hover {
        background-color: theme("colors.darkmode.200");
      }
    }
    .tns-nav {
      button {
        background-color: theme("colors.darkmode.300");
        &.tns-nav-active {
          background-color: theme("colors.darkmode.200");
        }
      }
    }
    button[data-action] {
      display: none;
    }
    .tns-visually-hidden {
      display: none;
    }
  }
}
