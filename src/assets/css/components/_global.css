html {
  scrollbar-gutter: stable;
  scroll-behavior: smooth;
  background-color: theme("colors.primary");

  @media screen(md) {
    background-color: theme("colors.slate.200");
  }

  &.dark {
    background-color: theme("colors.darkmode.800");

    body {
      color: theme("colors.slate.300");

      *,
      ::before,
      ::after {
        border-color: theme("colors.white" / 5%);
      }
    }
  }

  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
    font-family: theme("fontFamily.public-sans"), sans-serif;
    font-size: theme("fontSize.sm");
    line-height: theme("lineHeight.5");
    color: theme("colors.slate.800");
  }
}

@media print {

  .print-hide,
  .print-hide * {
    display: none !important;
  }
}