/* Base Tippy Styles */
.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0;
}

[data-tippy-root] {
  max-width: calc(100vw - 20px);
}

/* Light Theme (Default) - Transparent Background */
.tippy-box {
  font-family: theme('fontFamily.public-sans');
  font-weight: normal;
  position: relative;
  background-color: transparent;
  color: theme('colors.slate.800');
  border-radius: theme('borderRadius.lg');
  white-space: normal;
  outline: 0;
  box-shadow: none;
  border: none;
  transition: all 150ms ease-in-out;
  font-size: theme('fontSize.sm');
  line-height: theme('lineHeight.snug');
}

/* Tippy Content - No Padding */
.tippy-content {
  position: relative;
  padding: 0;
  z-index: 1;
}

/* Arrow Styles - Hidden */
.tippy-arrow {
  display: none;
}

/* Animation */
.tippy-box[data-inertia][data-state=visible] {
  transition-timing-function: cubic-bezier(0.2, 1, 0.2, 1);
}

/* Theme: Light */
.tippy-box[data-theme~='light'] {
  background-color: transparent;
  color: theme('colors.slate.800');
  box-shadow: none;
  border: none;
}

/* Theme: Primary */
.tippy-box[data-theme~='primary'] {
  background-color: transparent;
  color: theme('colors.slate.800');
  border: none;
}

/* Theme: Soft */
.tippy-box[data-theme~='soft'] {
  background-color: transparent;
  color: theme('colors.slate.800');
  border: none;
}

/* Dark Mode */
.dark .tippy-box {
  background-color: transparent;
  color: theme('colors.slate.200');
  border: none;
  box-shadow: none;
}

/* Dark Mode - Light Theme */
.dark .tippy-box[data-theme~='light'] {
  background-color: transparent;
  color: theme('colors.slate.200');
  border: none;
}

/* Dark Mode - Primary Theme */
.dark .tippy-box[data-theme~='primary'] {
  background-color: transparent;
  color: theme('colors.slate.200');
  border: none;
}

/* Dark Mode - Soft Theme */
.dark .tippy-box[data-theme~='soft'] {
  background-color: transparent;
  color: theme('colors.slate.200');
  border: none;
}
