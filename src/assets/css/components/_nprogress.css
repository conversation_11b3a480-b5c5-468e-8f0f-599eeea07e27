/* Make clicks pass-through */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: theme("colors.success");
  position: fixed;
  z-index: 99999;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 120px;
  height: 100%;
  box-shadow: 0 0 15px theme("colors.success"), 0 0 8px theme("colors.success");
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Spinner */
#nprogress .spinner {
  display: block;
  position: fixed;
  z-index: 99999;
  top: 20px;
  right: 20px;
}

#nprogress .spinner-icon {
  width: 22px;
  height: 22px;
  box-sizing: border-box;
  border: solid 3px transparent;
  border-top-color: theme("colors.success");
  border-left-color: theme("colors.success");
  border-radius: 50%;
  animation: nprogress-spinner 500ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Custom loading behavior */
#nprogress .bar {
  background: linear-gradient(to right, theme("colors.success"), theme("colors.success/80"));
  transition: all 200ms ease;
}

/* Optional: Add pulse effect to the bar */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

#nprogress .bar {
  animation: pulse 1s ease-in-out infinite;
}

/* Optional: Custom spinner glow */
#nprogress .spinner-icon {
  box-shadow: 0 0 10px theme("colors.success/30");
}
