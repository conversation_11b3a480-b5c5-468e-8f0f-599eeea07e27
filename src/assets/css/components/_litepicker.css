.litepicker {
  background-color: white;
  border-radius: theme("borderRadius.md");
  font-size: theme("fontSize.sm");
  z-index: 999999 !important;
  margin-top: 7px;
  box-shadow: 0px 3px 20px #00000014;
  @media (max-width: calc(theme("screens.lg") - 1px)) {
    width: 310px;
  }
  @media (max-width: calc(theme("screens.sm") - 1px)) {
    left: 0px !important;
    right: 0px;
    margin-left: auto;
    margin-right: auto;
  }
  &:before {
    content: "";
    left: 9px;
    top: -7px;
    border-bottom-color: #f7f7f7;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-bottom: 7px solid #e8e8e8;
    position: absolute;
    display: inline-block;
  }
  &:after {
    content: "";
    left: 10px;
    top: -6px;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
    position: absolute;
    display: inline-block;
  }
  .container__months,
  .container__footer {
    box-shadow: none;
    background-color: transparent;
    padding-top: 0;
  }
  .container__footer {
    border-top: 1px solid theme("colors.slate.200" / 60%);
    margin: 0;
    padding: theme("spacing.3");
    @media (max-width: calc(theme("screens.lg") - 1px)) {
      display: flex;
      flex-direction: column-reverse;
      text-align: center;
    }
    .preview-date-range {
      @media (max-width: calc(theme("screens.lg") - 1px)) {
        width: 100%;
        display: block;
        order: 1;
        margin-bottom: theme("spacing.[1.5]");
      }
    }
    .button-cancel {
      @media (max-width: calc(theme("screens.lg") - 1px)) {
        width: 100% !important;
        margin-left: 0px !important;
        padding-top: 0.375rem !important;
        padding-bottom: 0.375rem !important;
      }
    }
    .button-apply {
      @media (max-width: calc(theme("screens.lg") - 1px)) {
        width: 100% !important;
        margin-left: 0px !important;
        padding-top: 0.375rem !important;
        padding-bottom: 0.375rem !important;
        margin-bottom: theme("spacing.[1.5]");
      }
    }
    .button-apply,
    .button-cancel {
      width: theme("spacing.20");
      padding: theme("spacing.1") theme("spacing.2");
      border-radius: theme("borderRadius.md");
      font-weight: theme("fontWeight.medium");
      margin-left: theme("spacing.1");
      margin-right: 0;
    }
    .button-apply {
      background-color: theme("colors.primary");
      color: white;
    }
    .button-cancel {
      background-color: theme("colors.slate.200" / 70%);
      color: theme("colors.slate.500");
    }
  }
  .container__months {
    padding-left: theme("spacing.1");
    padding-right: theme("spacing.1");
    .month-item-weekdays-row {
      color: theme("colors.slate.400");
      margin-top: theme("spacing.3");
    }
    .month-item-header {
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 0;
      padding-top: theme("spacing.2");
      .button-previous-month {
        display: flex;
        align-items: center;
        justify-content: center;
        width: theme("spacing.8");
        height: theme("spacing.8");
        &:hover {
          background-color: theme("colors.slate.100");
        }
        svg {
          transform: rotate(135deg);
          margin-right: calc(theme("spacing.1") * -1);
        }
      }
      .button-next-month {
        display: flex;
        align-items: center;
        justify-content: center;
        width: theme("spacing.8");
        height: theme("spacing.8");
        &:hover {
          background-color: theme("colors.slate.100");
        }
        svg {
          transform: rotate(-45deg);
          margin-left: calc(theme("spacing.1") * -1);
        }
      }
      .button-previous-month:hover svg,
      .button-previous-month svg,
      .button-next-month:hover svg,
      .button-next-month svg {
        fill: transparent;
        border: solid black;
        border-width: 0 2px 2px 0;
        border-radius: 0;
        display: inline-block;
        border-color: #4a5568;
        width: theme("spacing.2");
        height: theme("spacing.2");
        display: block;
      }
      div > .month-item-name,
      div > .month-item-year {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(74, 85, 104)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='lucide lucide-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
        background-size: 15px;
        background-position: center right 0.6rem;
        background-color: transparent;
        font-size: theme("fontSize.sm");
        border-width: 1px;
        border-style: solid;
        background-repeat: no-repeat;
        padding-top: theme("spacing.1");
        padding-bottom: theme("spacing.1");
        padding-left: theme("spacing.2");
        padding-right: theme("spacing.8");
        border-radius: theme("borderRadius.md");
        border-color: theme("colors.slate.200");
        font-weight: theme("fontWeight.normal");
        &:focus {
          outline: none;
          border-color: theme("colors.slate.200");
          --tw-ring-color: transparent;
        }
      }
    }
  }
  .container__days {
    .day-item {
      color: theme("colors.slate.700");
      &:hover {
        box-shadow: none;
        background-color: theme("colors.slate.100");
        color: theme("colors.slate.700");
      }
      &.is-today,
      &.is-today:hover {
        font-weight: theme("fontWeight.medium");
        color: theme("colors.primary");
      }
      &.is-start-date,
      &.is-start-date:hover,
      &.is-end-date,
      &.is-end-date:hover {
        background-color: theme("colors.primary");
        color: white;
      }
      &.is-in-range,
      &.is-in-range:hover {
        background-color: theme("colors.slate.100");
      }
    }
    .week-number {
      color: theme("colors.slate.400");
    }
  }
}
@media (max-width: calc(theme("screens.lg") - 1px)) {
  :root {
    --litepicker-day-width: 35px;
  }
}

.dark {
  .litepicker {
    background-color: theme("colors.darkmode.600");
    &:before,
    &:after {
      border-right-color: transparent;
      border-left-color: transparent;
      border-bottom-color: theme("colors.darkmode.600");
    }
    .container__months {
      .button-previous-month:hover {
        background-color: theme("colors.darkmode.400");
      }
      .button-next-month:hover {
        background-color: theme("colors.darkmode.400");
      }
      .month-item-header {
        .button-previous-month:hover svg,
        .button-previous-month svg,
        .button-next-month:hover svg,
        .button-next-month svg {
          border-color: theme("colors.slate.200");
        }
        div > .month-item-name,
        div > .month-item-year {
          color: theme("colors.slate.200");
        }
        div > .month-item-name,
        div > .month-item-year {
          border-color: theme("colors.darkmode.400");
        }
      }
    }
    .container__days {
      .day-item {
        color: theme("colors.slate.200");
        &:hover {
          background-color: theme("colors.darkmode.400");
        }
        &.is-start-date,
        &.is-start-date:hover,
        &.is-end-date,
        &.is-end-date:hover {
          background-color: theme("colors.primary");
          color: theme("colors.slate.200");
        }
        &.is-in-range,
        &.is-in-range:hover {
          background-color: theme("colors.darkmode.400");
        }
        &.is-today,
        &.is-today:hover {
          color: theme("colors.slate.200");
        }
      }
    }
    .container__footer {
      border-color: theme("colors.darkmode.400");
      .button-cancel {
        background-color: theme("colors.darkmode.400");
        color: theme("colors.slate.300");
      }
    }
    .container__tooltip {
      background-color: theme("colors.darkmode.400");
      &:before,
      &:after {
        border-top-color: theme("colors.darkmode.600");
      }
    }
  }
}
