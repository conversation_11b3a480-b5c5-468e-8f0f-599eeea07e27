/// <reference types="vite/client" />

declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

// import type { RouteRecordInfo } from 'vue-router'
// import { MainRouteNamedMap } from './router/main-route'

// // Define an interface of routes

// // Last, you will need to augment the Vue Router types with this map of routes
// declare module 'vue-router' {
//   interface TypesConfig {
//     RouteNamedMap: MainRouteNamedMap
//   }
// }
