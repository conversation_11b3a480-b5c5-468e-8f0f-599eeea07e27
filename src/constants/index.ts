import _ from "lodash";

import fakerData from "@/utils/faker";

const person = _.take(fakerData, 6);

export const OPTION_PER_AGE = [10, 25, 35, 50];
export const STATUS = [
  { id: "11", value: "<PERSON><PERSON><PERSON> ký hợp đồng" },
  { id: "12", value: "<PERSON><PERSON> niềng" },
  { id: "13", value: "Đã tháo niềng" },
  { id: "14", value: "Đ<PERSON> gắn mắc cài và bỏ trị" },
  { id: "15", value: "Tạm ngưng điều trị" },
  { id: "16", value: "Từ chối điều trị" },
];
export const HEADER_PERSON = [
  {
    key: "id",
    name: "STT",
    isCheck: true,
  },
  {
    key: "full_name",
    name: "<PERSON>hô<PERSON> tin KH",
    isCheck: true,
  },
  {
    key: "email",
    name: "<PERSON><PERSON>",
    isCheck: false,
  },
  {
    key: "date_of_birth",
    name: "<PERSON><PERSON><PERSON>",
    isCheck: true,
  },
  {
    key: "job_id",
    name: "<PERSON><PERSON><PERSON>hi<PERSON>",
    isCheck: false,
  },
  {
    key: "person_field.description",
    name: "<PERSON><PERSON>",
    isCheck: false,
  },
  {
    key: "address_number",
    name: "Địa chỉ",
    isCheck: false,
  },
  {
    key: "source_id",
    name: "Nguồn khách hàng",
    isCheck: true,
  },
  {
    key: "person_field.treatment_id",
    name: "Loại điều trị",
    isCheck: true,
  },
  {
    key: "person_field.treatment_status_id",
    name: "Trạng thái điều trị",
    isCheck: true,
  },
];
export const HEADER_PRODUCT = [
  {
    id: 0,
    key: "code",
    name: "Mã đồng bộ",
  },
  {
    id: 1,
    key: "name",
    name: "Tên sản phẩm",
  },
  {
    id: 2,
    key: "num",
    name: "Số lượng",
  },
  {
    id: 3,
    key: "price",
    name: "Giá",
  },
  {
    id: 4,
    key: "group",
    name: "Nhóm",
  },
  {
    id: 5,
    key: "status",
    name: "Trạng thái",
  },
  {
    id: 6,
    key: "action",
    name: "Thao tác",
  },
];
export const TYPE_ADD_PRODUCT = [
  {
    key: "item",
    value: "Sản phẩm",
  },
  {
    key: "service",
    value: "Dịch vụ",
  },
  {
    key: "gift",
    value: "Quà tặng",
  },
];
export const APPOINTMENT_IN_OPTIONS = [
  { label: "3 phút", value: 3 },
  { label: "5 phút", value: 5 },
  {
    value: 10,
    label: "10 phút",
  },
  {
    value: 15,
    label: "15 phút",
  },
  {
    value: 20,
    label: "20 phút",
  },
  {
    value: 25,
    label: "25 phút",
  },
  {
    value: 30,
    label: "30 phút",
  },
  {
    value: 45,
    label: "45 phút",
  },
  {
    value: 60,
    label: "60 phút",
  },
  {
    value: 90,
    label: "90 phút",
  },
  {
    value: 120,
    label: "120 phút",
  },
];
export const STATUS_TYPE = [
  {
    value: 1,
    label: "Đúng hẹn",
  },
  {
    value: 2,
    label: "Trễ hẹn",
  },
  {
    value: 3,
    label: "Hẹn mới",
  },
  {
    value: 4,
    label: "Phát sinh",
  },
];
export const STAGE_ASSISTANT = [
  {
    id: 2,
    name: "Phòng điều trị",
  },
];
export const STAGE_IDS = {
  ASSISTANT: 2,
  CONSULTING: 3,
  PLANNING: 10,
  ROADMAP: 11,
  X_RAY: 12,
};

export const PIPELINE_IDS = {
  OFFLINE: 1,
};

export const DEAL_USER_ROLE = {
  ADVISOR: "advisor",
  ASSISTANT: "assistant",
  CONSULTANT_DOCTOR: "consultant_doctor",
  DOCTOR_ASSISTANT: "doctor_assistant",
  TREATMENT_DOCTOR: "treatment_doctor",
  X_RAY: "xray_technician",
};
export const HEADER_APPOINTMENT = [
  {
    id: 1,
    key: "doctor",
    name: "Bác sĩ hẹn",
  },
  {
    id: 2,
    key: "arrivalTime",
    name: "Lịch hẹn",
  },
  {
    id: 3,
    key: "status",
    name: "Trạng thái",
  },
  {
    id: 4,
    key: "anticipated",
    name: "CV dự kiến",
  },
  {
    id: 5,
    key: "note",
    name: "Ghi chú",
  },
];

export const APPOINTMENT_MESSAGES = [
  {
    name: "Nhắc hẹn thường",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH <{{customer_name}}> ID <{{customer_ID}}> vào thời gian:<{{schedule_time}}>. Vui lòng đi đúng lịch hẹn để đảm bảo quá trình điều trị. Xin cảm ơn.",
  },
  {
    name: "Nhắc hẹn tiểu phẫu",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH <{{customer_name}}> ID <{{customer_ID}}> vào thời gian:<{{schedule_time}}>. Lưu ý: ĂN NO TRƯỚC KHI ĐẾN và bổ sung kết quả Xét nghiệm máu nếu chưa bổ sung (BẮT BUỘC). NẾU ĐANG TRONG GIAI ĐOẠN HÀNH KINH (KH nữ), vui lòng liên hệ để đổi hẹn. Xin cảm ơn.",
  },
  {
    name: "Nhắc hẹn KH đã tháo mắc cài",
    content:
      "Nha khoa Up Dental xin thông báo lịch hẹn của KH <{{customer_name}}>} ID <{{customer_ID}}> vào thời gian:<{{schedule_time}}>. Lưu ý: MANG THEO KHAY DUY TRÌ KHI ĐẾN TÁI KHÁM (BẮT BUỘC). Trường hợp mất Khay duy trì, vui lòng liên hệ bộ phận CSKH trước lịch hẹn. Xin cảm ơn.",
  },
];

export const WAILTING = [
  {
    id: 1,
    name: "Chờ phục vụ",
    doctors: [
      {
        id: 3,
        name: "Khách đang chờ",
        customers: [
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            doctor: "Phan Đình Bửu Lộc",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            doctor: "Nguyễn Thị Quỳnh Huơng",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            doctor: "Nguyễn Kim Hoàng",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
    ],
  },
];
export const BUILDINGS = [
  {
    id: 2,
    name: "Phòng tư vấn",
    highlight: false,
    doctors: [
      {
        id: 4,
        name: "Chờ tư vấn",
        customers: [],
      },
      {
        id: 3,
        name: "Tư vấn kế hoạch",
        customers: [
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
      {
        id: 3,
        name: "Tư vấn lộ trình",
        customers: [
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
    ],
  },
  {
    id: 3,
    name: "Khám khách mới",
    highlight: false,
    doctors: [
      {
        id: 4,
        name: "Chờ khám",
        customers: [],
      },
      {
        id: 3,
        name: "Bs. Nguyễn Trọng Nguyễn",
        customers: [
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
      {
        id: 4,
        name: "Bs. Nguyễn Thị Quỳnh Hương",
        customers: [
          {
            id: 6,
            name: "Đặng Văn Dũng",
            time: "17:10",
            content: "Niềng răng",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 7,
            name: "Hoàng Thị Lan",
            time: "18:20",
            content: "Làm răng giả",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 8,
            name: "Phạm Thị Hương",
            time: "19:30",
            content: "Chỉnh nha",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
    ],
  },
  {
    id: 4,
    name: "Lầu 4",
    highlight: false,
    doctors: [
      {
        id: 1,
        name: "Chờ điều trị",
        customers: [
          {
            id: 1,
            name: "Vũ Hoàng Lâm",
            time: "11:20",
            content: "Khách này phải làm cái gì đó nữa",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 2,
            name: "Nguyễn Quốc Khánh Khánh",
            time: "12:30",
            content: "Tháo niềng",
            photo: person[2].photos[0],
            type: 0,
          },
        ],
      },
      {
        id: 1,
        name: "Bs. Nguyễn Kim Hoàng",
        customers: [
          {
            id: 1,
            name: "Hồ Ngọc Giàu",
            time: "15:30",
            content: "Khách này phải làm cái gì đó nữa",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 2,
            name: "Nguyễn Gia Thịnh",
            time: "14:30",
            content: "Gắn răng giả",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 2,
            name: "Nguyễn Quốc Khánh",
            time: "12:30",
            content: "Tháo niềng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
      {
        id: 2,
        name: "Bs. Trần Văn Quân",
        customers: [
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
    ],
  },
  {
    id: 5,
    name: "Lầu 5",
    highlight: false,
    doctors: [
      {
        id: 1,
        name: "Chờ điều trị",
        customers: [
          {
            id: 1,
            name: "Vũ Hoàng Lâm",
            time: "11:20",
            content: "Cạo vôi răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 2,
            name: "Nguyễn Quốc Khánh",
            time: "12:30",
            content: "Tháo niềng",
            photo: person[2].photos[0],
            type: 0,
          },
        ],
      },
      {
        id: 3,
        name: "Bs. Nguyễn Hoàn Thạnh Quân",
        customers: [
          {
            id: 9,
            name: "Nguyễn Văn Nam",
            time: "20:40",
            content: "Làm răng giả",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 10,
            name: "Nguyễn Thị Ngọc",
            time: "21:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 11,
            name: "Trần Thị Hằng",
            time: "23:00",
            content: "Cạo vôi răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 12,
            name: "Nguyễn Đức Minh",
            time: "23:10",
            content: "Niềng răng",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
      {
        id: 4,
        name: "Bs. Hoàng Hữu Vinh",
        customers: [
          {
            id: 13,
            name: "Lê Thị Thu",
            time: "23:20",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 14,
            name: "Đặng Thị Mỹ",
            time: "23:30",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
        ],
      },
    ],
  },
  {
    id: 6,
    name: "Lầu 6",
    highlight: false,
    doctors: [
      {
        id: 1,
        name: "Chờ điều trị",
        customers: [],
      },
      {
        id: 3,
        name: "Bs. Nguyễn Trọng Nguyễn",
        customers: [
          {
            id: 3,
            name: "Lê Thị Mai",
            time: "13:40",
            content: "Khám răng định kỳ",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 4,
            name: "Trần Văn Nam",
            time: "14:50",
            content: "Tẩy trắng răng",
            photo: person[2].photos[0],
            type: 0,
          },
          {
            id: 5,
            name: "Nguyễn Thị Hà",
            time: "16:00",
            content: "Bọc răng sứ",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
      {
        id: 4,
        name: "Bs. Nguyễn Thị Quỳnh Hương",
        customers: [
          {
            id: 6,
            name: "Đặng Văn Dũng",
            time: "17:10",
            content: "Niềng răng",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 7,
            name: "Hoàng Thị Lan",
            time: "18:20",
            content: "Làm răng giả",
            photo: person[2].photos[0],
            type: 1,
          },
          {
            id: 8,
            name: "Phạm Thị Hương",
            time: "19:30",
            content: "Chỉnh nha",
            photo: person[2].photos[0],
            type: 1,
          },
        ],
      },
    ],
  },
];

export const GLOBAL_TEXT = {
  COPIED: "Đã sao chép",
  ADD: "Thêm",
  ADD_APPOINTMENT: "Thêm lịch hẹn",
  ADD_CUSTOMER: "Thêm khách",
};

