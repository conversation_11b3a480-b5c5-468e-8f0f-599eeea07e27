export const DEAL_STAGE_HISTORY_QUERY = `
  WITH filtered_paginated_dsh AS (
    -- Bước 1: Lọc và phân trang trên bảng deal_stage_history trước
    SELECT
        dsh.id,
        dsh.deal_id,
        d.person_id
    FROM core.deal_stage_history dsh
    JOIN core.deal d ON dsh.deal_id = d.id
    JOIN core.person p ON d.person_id = p.id
    LEFT JOIN core.stage s_before ON s_before.id = dsh.before
    LEFT JOIN core.stage s_after ON s_after.id = dsh.after
    WHERE
        -- lọc theo ngày
        (dsh.changed_at > $1 OR $1 IS NULL)
        AND (dsh.changed_at < $2 OR $2 IS NULL)
        -- lọc theo khách hàng
        AND ((
               f_unaccent(p.full_name) LIKE ('%' || f_unaccent($3) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($3) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($3) || '%')
        ) OR $3 IS NULL )
        -- lọc theo stage cũ
        AND (dsh.before = ANY($4::int[]) OR s_before.parent_stage_id = ANY($4::int[]) OR $4 IS NULL)
        -- lọc theo stage mới
        AND (dsh.after = ANY($5::int[]) OR s_after.parent_stage_id = ANY($5::int[]) OR $5 IS NULL)
        -- lọc theo thẻ
        AND (EXISTS (
            SELECT 1
            FROM core.tag_deal tg
            WHERE tg.deal_id = d.id AND tg.tag_id = ANY($6::int[])
        ) OR $6 IS NULL)
        -- lọc theo nhân viên liên quan
        AND ((
            (SELECT pa.user_id FROM core.person_assignment pa WHERE pa.person_id = p.id AND pa.role = 'sale' LIMIT 1) = ANY($7::int[])
            OR EXISTS (
                SELECT 1
                FROM core.deal_user du
                WHERE du.deal_id = d.id AND du.user_id = ANY($7::int[])
            )
        ) OR $7 IS NULL)
    ORDER BY {{orderClause1}} -- Sắp xếp trước khi phân trang
    LIMIT $8
    OFFSET $9
)
SELECT
    dsh.*,
    p.full_name                                                  AS full_name,
    p.phone                                                      AS person_phone,
    p.person_field ->> 'code'                                    as person_code,
    p.source_id                                                  AS person_source_id,
    t_source.name                                                as person_source, -- Lấy tên source từ join
    CASE
        WHEN sp_before.id IS NULL THEN s_before.name
        ELSE sp_before.name || ' - ' || s_before.name
    END AS before_name,
    CASE
        WHEN sp_after.id IS NULL THEN s_after.name
        ELSE sp_after.name || ' - ' || s_after.name
    END AS after_name,
    s_before.parent_stage_id AS before_parent,
    s_after.parent_stage_id AS after_parent,
    sud.sale_user_id,
    dta.deal_tag_ids,
    daa.deal_assignment_user_ids,
    row_to_json(p)::jsonb AS person,
    jsonb_build_object(
        'id', d.id,
        'name', d.name,
        'state', d.state,
        'deal_assignment', COALESCE(daa.deal_assignment_json, '[]'::jsonb),
        'tags', COALESCE(dta.deal_tags_json, '[]'::jsonb)
    ) AS deal,
    rd.referrer_json AS referrer,
    rd.referral_info_json AS referral_info,
    sud.sale_user_json AS sale_user
FROM filtered_paginated_dsh fpd -- Bắt đầu từ kết quả đã lọc và phân trang
JOIN core.deal_stage_history dsh ON fpd.id = dsh.id -- Lấy đầy đủ thông tin dsh
LEFT JOIN core.deal d ON dsh.deal_id = d.id
LEFT JOIN core.person p ON d.person_id = p.id
LEFT JOIN core.stage s_before ON dsh.before = s_before.id
LEFT JOIN core.stage sp_before ON s_before.parent_stage_id = sp_before.id
LEFT JOIN core.stage s_after ON dsh.after = s_after.id
LEFT JOIN core.stage sp_after ON s_after.parent_stage_id = sp_after.id
LEFT JOIN core.term t_source ON p.source_id = t_source.id -- Join để lấy tên source

-- Lateral join để tổng hợp deal assignments cho từng deal_id liên quan
LEFT JOIN LATERAL (
    SELECT
        jsonb_agg(u.id::integer) AS deal_assignment_user_ids,
        jsonb_agg(
            jsonb_build_object(
                'id', du.id, 'user_id', u.id, 'username', u.username, 'email', u.email,
                'name', u.name, 'department_id', u.department_id, 'role', du.role, 'point', du.point,
                'ratings', COALESCE((
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', dur.id, 'category', dur.category, 'rating', dur.rating,
                        'created_at', dur.created_at, 'updated_at', dur.updated_at
                    ) ORDER BY dur.created_at)
                    FROM core.deal_user_rating dur WHERE dur.deal_user_id = du.id
                ), '[]'::jsonb)
            ) ORDER BY u.name
        ) AS deal_assignment_json
    FROM core.deal_user du
    JOIN core."user" u ON du.user_id = u.id
    WHERE du.deal_id = d.id -- Liên kết với deal_id của dòng hiện tại
) daa ON true -- Luôn join nếu có kết quả từ subquery

-- Lateral join để tổng hợp deal tags cho từng deal_id liên quan
LEFT JOIN LATERAL (
     SELECT
        jsonb_agg(tag.id::integer) AS deal_tag_ids,
        jsonb_agg(
            jsonb_build_object('id', tag.id, 'name', tag.name, 'category', tag.category)
            ORDER BY tag.name
        ) AS deal_tags_json
    FROM core.tag_deal tg
    JOIN core.tag tag ON tag.id = tg.tag_id
    WHERE tg.deal_id = d.id -- Liên kết với deal_id của dòng hiện tại
) dta ON true -- Luôn join nếu có kết quả từ subquery

-- Lateral join để lấy thông tin sale user cho từng person_id liên quan (giống DISTINCT ON)
LEFT JOIN LATERAL (
    SELECT
           pa.user_id AS sale_user_id,
           jsonb_build_object(
               'id', u.id, 'username', u.username, 'email', u.email,
               'name', u.name, 'department_id', u.department_id
           ) AS sale_user_json
    FROM core.person_assignment pa
    JOIN core."user" u ON u.id = pa.user_id
    WHERE pa.person_id = p.id -- Liên kết với person_id của dòng hiện tại
      AND pa.role = 'sale'
    -- Cần ORDER BY nếu muốn đảm bảo lấy user cụ thể khi có nhiều sale user cho 1 person
    -- ORDER BY u.id -- Ví dụ: lấy user có id nhỏ nhất
    LIMIT 1 -- Chỉ lấy 1 dòng (tương đương DISTINCT ON)
) sud ON true -- Luôn join nếu có kết quả từ subquery

-- Lateral join để lấy thông tin referral cho từng person_id liên quan (giống DISTINCT ON)
LEFT JOIN LATERAL (
    SELECT
           row_to_json(p_ref)::jsonb AS referrer_json,
           row_to_json(pr)::jsonb AS referral_info_json
    FROM core.person_referral pr
    JOIN core.person p_ref ON pr.referrer_id = p_ref.id
    WHERE pr.referred_id = p.id -- Liên kết với person_id của dòng hiện tại
    -- Cần ORDER BY nếu muốn đảm bảo lấy referral cụ thể khi có nhiều referral cho 1 person
    -- ORDER BY pr.created_at DESC -- Ví dụ: lấy referral mới nhất
    LIMIT 1 -- Chỉ lấy 1 dòng (tương đương DISTINCT ON)
) rd ON true -- Luôn join nếu có kết quả từ subquery

ORDER BY {{orderClause1}}
LIMIT $8;
`;

export const DEAL_STAGE_HISTORY_COUNT_QUERY = `
  SELECT COUNT(DISTINCT dsh.id) AS total_count
FROM core.deal_stage_history dsh
JOIN core.deal d ON dsh.deal_id = d.id
JOIN core.person p ON d.person_id = p.id
LEFT JOIN core.stage s_before ON s_before.id = dsh.before -- Join để kiểm tra before_parent
LEFT JOIN core.stage s_after ON s_after.id = dsh.after   -- Join để kiểm tra after_parent
WHERE
    -- lọc theo ngày
    (dsh.changed_at > $1 OR $1 IS NULL)
    AND (dsh.changed_at < $2 OR $2 IS NULL)

    -- lọc theo khách hàng
     AND ((
               f_unaccent(p.full_name) LIKE ('%' || f_unaccent($3) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($3) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($3) || '%')
        ) OR $3 IS NULL )

    -- lọc theo stage cũ
    AND (dsh.before = ANY($4::int[]) OR s_before.parent_stage_id = ANY($4::int[]) OR $4 IS NULL)

    -- lọc theo stage mới
    AND (dsh.after = ANY($5::int[]) OR s_after.parent_stage_id = ANY($5::int[]) OR $5 IS NULL)

    -- lọc theo thẻ
    AND (EXISTS (
        SELECT 1
        FROM core.tag_deal tg
        WHERE tg.deal_id = d.id AND tg.tag_id = ANY($6::int[])
    ) OR $6 IS NULL)

    -- lọc theo nhân viên liên quan
    AND ((
        -- Kiểm tra sale_user_id (lấy 1 người như logic gốc)
        (SELECT pa.user_id FROM core.person_assignment pa WHERE pa.person_id = p.id AND pa.role = 'sale' LIMIT 1) = ANY($7::int[])
        -- Kiểm tra deal_assignment_user_ids
        OR EXISTS (
            SELECT 1
            FROM core.deal_user du
            WHERE du.deal_id = d.id AND du.user_id = ANY($7::int[])
        )
    ) OR $7 IS NULL);
`;
