export const DEAL_QUERY = `WITH base_deal_ids AS (
    SELECT DISTINCT d.id, d.created_at
    FROM deal d
    LEFT JOIN person p ON d.person_id = p.id
    LEFT JOIN stage s ON d.stage_id = s.id
    LEFT JOIN deal_history dh ON d.id = dh.deal_id
    LEFT JOIN deal_stage_history dsh ON d.id = dsh.deal_id
    WHERE d.status > -1
      AND d.deleted_at IS NULL
      -- Lọc theo tên deal
      AND (f_unaccent(d.name) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
      -- lọc theo khách hàng
      AND ( (
        f_unaccent(p.full_name) LIKE ('%' || f_unaccent($2) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($2) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($2) || '%')
        ) OR $2 IS NULL)
      -- Lọc theo ngày tạo
      AND (d.created_at > $3 OR $3 IS NULL)
      AND (d.created_at < $4 OR $4 IS NULL)
      -- Lọc theo ngày cập nhật
      AND (d.updated_at > $5 OR $5 IS NULL)
      AND (d.updated_at < $6 OR $6 IS NULL)
      -- Lọc theo giá trị deal
      AND (d.total_value >= $7 OR $7 IS NULL)
      AND (d.total_value <= $8 OR $8 IS NULL)
      -- Lọc theo số tiền đặt cọc
      AND (d.deposit_amount >= $9 OR $9 IS NULL)
      AND (d.deposit_amount <= $10 OR $10 IS NULL)
      -- Lọc theo stage
      AND (d.stage_id = ANY($11::bigint[]) OR $11 IS NULL)
      -- Lọc theo trạng thái
      AND (d.state = $12 OR $12 IS NULL)
      -- Lọc theo người liên hệ
      AND (d.person_id = ANY($13::bigint[]) OR $13 IS NULL)

      -- Lọc theo deal_history - thay đổi state
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'state'
                AND dh_inner.after->>'state' = $14
          )) OR $14 IS NULL
      )

      -- Lọc theo deal_history - thay đổi total_value
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'total_value'
                AND (dh_inner.after->>'total_value')::double precision = $15::double precision
          )) OR $15 IS NULL
      )

      -- Lọc theo deal_history - thay đổi name
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'name'
                AND dh_inner.after->>'name' LIKE '%' || $16 || '%'
          )) OR $16 IS NULL
      )

      -- Lọc theo deal_history - thay đổi total_amount
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'total_amount'
                AND (dh_inner.after->>'total_amount')::double precision = $17::double precision
          )) OR $17 IS NULL
      )

      -- Lọc theo deal_history - thay đổi deposit_amount
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'deposit_amount'
                AND (dh_inner.after->>'deposit_amount')::double precision = $18::double precision
          )) OR $18 IS NULL
      )

      -- Lọc theo deal_history - operation
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.operation = $19
          )) OR $19 IS NULL
      )

      -- Lọc theo deal_stage_history - before stage
      AND (
          (EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.before = ANY($20::int[])
          )) OR $20 IS NULL
      )

      -- Lọc theo deal_stage_history - after stage
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.after = ANY($21::int[])
          )) OR $21 IS NULL
      )

      -- Lọc theo người thay đổi deal_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.user_id = $22
          )) OR $22 IS NULL
      )

      -- Lọc theo người thay đổi deal_stage_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.user_id = $23
          )) OR $23 IS NULL
      )

      -- Lọc theo thời gian thay đổi deal_history
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.changed_at BETWEEN $24 AND $25
          )) OR $24 IS NULL OR $25 IS NULL
      )

      -- Lọc theo thời gian thay đổi deal_stage_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.changed_at BETWEEN $26 AND $27
          )) OR $26 IS NULL OR $27 IS NULL
      )

      -- Lọc theo thẻ (tag)
      AND (EXISTS (
          SELECT 1
          FROM tag_deal tg
          WHERE tg.deal_id = d.id AND tg.tag_id = ANY($28::int[])
      ) OR $28 IS NULL)

      -- Lọc theo nhân viên liên quan
      AND ((
          (SELECT pa.user_id FROM person_assignment pa WHERE pa.person_id = p.id AND pa.role = 'sale' LIMIT 1) = ANY($29::int[])
          OR EXISTS (
              SELECT 1
              FROM deal_user du
              WHERE du.deal_id = d.id AND du.user_id = ANY($29::int[])
          )
      ) OR $29 IS NULL)

    ORDER BY {{orderClause1}}
    LIMIT $30 OFFSET $31
),
deal_data AS (
    SELECT
        d.id,
        d.status,
        d.version,
        d.created_at,
        d.updated_at,
        d.parent_deal_id,
        d.total_value,
        d.deposit_amount,
        d.total_installments,
        d.name,
        d.person_id,
        d.stage_id,
        d.stage_history,
        d.total_amount,
        d.state
    FROM deal d
    JOIN base_deal_ids bdi ON d.id = bdi.id
),
person_data AS (
    SELECT
        p.id,
        p.full_name,
        p.date_of_birth,
        p.gender,
        p.province_id,
        p.district_id,
        p.ward_id,
        p.address_number,
        p.phone,
        p.email,
        p.job_id,
        p.source_id,
        p.user_id,
        p.person_field
    FROM person p
    WHERE p.id IN (SELECT person_id FROM deal_data WHERE person_id IS NOT NULL)
),
stage_data AS (
    SELECT
        s.id,
        s.name,
        s.order_number,
        s.meta,
        s.parent_stage_id,
        s.pipeline_id
    FROM stage s
    WHERE s.id IN (SELECT stage_id FROM deal_data WHERE stage_id IS NOT NULL)
),
deal_history_data AS (
    SELECT
        dh.deal_id,
        jsonb_agg(
            jsonb_build_object(
                'id', dh.id,
                'created_at', dh.created_at,
                'before', dh.before,
                'after', dh.after,
                'operation', dh.operation,
                'changed_at', dh.changed_at,
                'user_id', dh.user_id
            )
        ) AS history_data
    FROM deal_history dh
    WHERE dh.deal_id IN (SELECT id FROM deal_data)
    GROUP BY dh.deal_id
),
deal_stage_history_data AS (
    SELECT
        dsh.deal_id,
        jsonb_agg(
            jsonb_build_object(
                'id', dsh.id,
                'created_at', dsh.created_at,
                'before', dsh.before,
                'after', dsh.after,
                'user_id', dsh.user_id,
                'changed_at', dsh.changed_at
            )
        ) AS stage_history_data
    FROM deal_stage_history dsh
    WHERE dsh.deal_id IN (SELECT id FROM deal_data)
    GROUP BY dsh.deal_id
),
deal_tag_data AS (
    SELECT
        tg.deal_id,
        jsonb_agg(tag.id::bigint) AS deal_tag_ids,
        jsonb_agg(
            jsonb_build_object(
                'id', tag.id,
                'name', tag.name,
                'category', tag.category
            ) ORDER BY tag.name
        ) AS deal_tags_json
    FROM tag_deal tg
    JOIN tag ON tag.id = tg.tag_id
    WHERE tg.deal_id IN (SELECT id FROM deal_data)
    GROUP BY tg.deal_id
),
deal_assignment_data AS (
    SELECT
        du.deal_id,
        jsonb_agg(u.id::bigint) AS deal_assignment_user_ids,
        jsonb_agg(
            jsonb_build_object(
                'id', du.id,
                'user_id', u.id,
                'username', u.username,
                'email', u.email,
                'name', u.name,
                'department_id', u.department_id,
                'role', du.role,
                'point', du.point,
                'ratings', COALESCE((
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', dur.id,
                        'category', dur.category,
                        'rating', dur.rating,
                        'created_at', dur.created_at,
                        'updated_at', dur.updated_at
                    ) ORDER BY dur.created_at)
                    FROM deal_user_rating dur
                    WHERE dur.deal_user_id = du.id
                ), '[]'::jsonb)
            ) ORDER BY u.name
        ) AS deal_assignment_json
    FROM deal_user du
    JOIN "user" u ON du.user_id = u.id
    WHERE du.deal_id IN (SELECT id FROM deal_data)
    GROUP BY du.deal_id
),
sale_user_data AS (
    SELECT
        p.id AS person_id,
        pa.user_id AS sale_user_id,
        jsonb_build_object(
            'id', u.id,
            'username', u.username,
            'email', u.email,
            'name', u.name,
            'department_id', u.department_id
        ) AS sale_user_json
    FROM person_data p
    JOIN person_assignment pa ON pa.person_id = p.id AND pa.role = 'sale'
    JOIN "user" u ON u.id = pa.user_id
    -- Lấy 1 sale user cho mỗi person
    WHERE (p.id, pa.created_at) IN (
        SELECT p_inner.id, MAX(pa_inner.created_at)
        FROM person_data p_inner
        JOIN person_assignment pa_inner ON pa_inner.person_id = p_inner.id AND pa_inner.role = 'sale'
        GROUP BY p_inner.id
    )
),
referrer_data AS (
    SELECT
        pr.referred_id AS person_id,
        jsonb_build_object(
            'id', p_ref.id,
            'full_name', p_ref.full_name,
            'phone', p_ref.phone,
            'email', p_ref.email
        ) AS referrer_json,
        row_to_json(pr)::jsonb AS referral_info_json
    FROM person_data p
    JOIN person_referral pr ON pr.referred_id = p.id
    JOIN person p_ref ON pr.referrer_id = p_ref.id
    -- Lấy 1 referrer cho mỗi person
    WHERE (p.id, pr.created_at) IN (
        SELECT p_inner.id, MAX(pr_inner.created_at)
        FROM person_data p_inner
        JOIN person_referral pr_inner ON pr_inner.referred_id = p_inner.id
        GROUP BY p_inner.id
    )
)

SELECT
    dd.id,
    dd.status,
    dd.version,
    dd.created_at,
    dd.updated_at,
    dd.parent_deal_id,
    dd.total_value,
    dd.deposit_amount,
    dd.total_installments,
    dd.name,
    dd.person_id,
    dd.stage_id,
    dd.stage_history,
    dd.total_amount,
    dd.state,
    jsonb_build_object(
        'id', p.id,
        'full_name', p.full_name,
        'phone', p.phone,
        'email', p.email,
        'gender', p.gender,
        'date_of_birth', p.date_of_birth,
        'address', jsonb_build_object(
            'province_id', p.province_id,
            'district_id', p.district_id,
            'ward_id', p.ward_id,
            'address_number', p.address_number
        ),
        'job_id', p.job_id,
        'source_id', p.source_id,
        'user_id', p.user_id,
        'person_field', p.person_field
    ) AS person,
    jsonb_build_object(
        'id', s.id,
        'name', s.name,
        'order_number', s.order_number,
        'meta', s.meta,
        'parent_stage_id', s.parent_stage_id,
        'pipeline_id', s.pipeline_id
    ) AS stage,
    dhd.history_data AS deal_history,
    dshd.stage_history_data AS stage_history,
    dtd.deal_tag_ids,
    dtd.deal_tags_json AS tags,
    dad.deal_assignment_user_ids,
    dad.deal_assignment_json AS deal_assignment,
    sud.sale_user_id,
    sud.sale_user_json AS sale_user,
    rd.referrer_json AS referrer,
    rd.referral_info_json AS referral_info
FROM deal_data dd
LEFT JOIN person_data p ON dd.person_id = p.id
LEFT JOIN stage_data s ON dd.stage_id = s.id
LEFT JOIN deal_history_data dhd ON dd.id = dhd.deal_id
LEFT JOIN deal_stage_history_data dshd ON dd.id = dshd.deal_id
LEFT JOIN deal_tag_data dtd ON dd.id = dtd.deal_id
LEFT JOIN deal_assignment_data dad ON dd.id = dad.deal_id
LEFT JOIN sale_user_data sud ON p.id = sud.person_id
LEFT JOIN referrer_data rd ON p.id = rd.person_id
ORDER BY {{orderClause2}}
LIMIT $30;
`;

export const DEAL_COUNT_QUERY = `SELECT COUNT(DISTINCT d.id) AS total_count
FROM deal d
LEFT JOIN person p ON d.person_id = p.id
LEFT JOIN stage s ON d.stage_id = s.id
LEFT JOIN deal_history dh ON d.id = dh.deal_id
LEFT JOIN deal_stage_history dsh ON d.id = dsh.deal_id
WHERE d.status > -1
  AND d.deleted_at IS NULL
  -- Lọc theo tên deal
  AND (f_unaccent(d.name) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
   -- lọc theo khách hàng
      AND ( (
        f_unaccent(p.full_name) LIKE ('%' || f_unaccent($2) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($2) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($2) || '%')
        ) OR $2 IS NULL)
  -- Lọc theo ngày tạo
  AND (d.created_at > $3 OR $3 IS NULL)
  AND (d.created_at < $4 OR $4 IS NULL)
  -- Lọc theo ngày cập nhật
  AND (d.updated_at > $5 OR $5 IS NULL)
  AND (d.updated_at < $6 OR $6 IS NULL)
  -- Lọc theo giá trị deal
  AND (d.total_value >= $7 OR $7 IS NULL)
  AND (d.total_value <= $8 OR $8 IS NULL)
  -- Lọc theo số tiền đặt cọc
  AND (d.deposit_amount >= $9 OR $9 IS NULL)
  AND (d.deposit_amount <= $10 OR $10 IS NULL)
  -- Lọc theo stage
  AND (d.stage_id = ANY($11::bigint[]) OR $11 IS NULL)
  -- Lọc theo trạng thái
  AND (d.state = $12 OR $12 IS NULL)
  -- Lọc theo người liên hệ
  AND (d.person_id = ANY($13::bigint[]) OR $13 IS NULL)

      -- Lọc theo deal_history - thay đổi state
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'state'
                AND dh_inner.after->>'state' = $14
          )) OR $14 IS NULL
      )

      -- Lọc theo deal_history - thay đổi total_value
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'total_value'
                AND (dh_inner.after->>'total_value')::double precision = $15::double precision
          )) OR $15 IS NULL
      )

      -- Lọc theo deal_history - thay đổi name
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'name'
                AND dh_inner.after->>'name' LIKE '%' || $16 || '%'
          )) OR $16 IS NULL
      )


      -- Lọc theo deal_history - thay đổi total_amount
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'total_amount'
                AND (dh_inner.after->>'total_amount')::double precision = $17::double precision
          )) OR $17 IS NULL
      )

      -- Lọc theo deal_history - thay đổi deposit_amount
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.after ? 'deposit_amount'
                AND (dh_inner.after->>'deposit_amount')::double precision = $18::double precision
          )) OR $18 IS NULL
      )

      -- Lọc theo deal_history - operation
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.operation = $19
          )) OR $19 IS NULL
      )

      -- Lọc theo deal_stage_history - before stage
      AND (
          (EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.before = ANY($20::int[])
          )) OR $20 IS NULL
      )
      -- Lọc theo deal_stage_history - after stage
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.after = ANY($21::int[])
          )) OR $21 IS NULL
      )

      -- Lọc theo người thay đổi deal_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.user_id = $22
          )) OR $22 IS NULL
      )

      -- Lọc theo người thay đổi deal_stage_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.user_id = $23
          )) OR $23 IS NULL
      )

      -- Lọc theo thời gian thay đổi deal_history
      AND (
          (EXISTS (
              SELECT 1 FROM deal_history dh_inner
              WHERE dh_inner.deal_id = d.id
                AND dh_inner.changed_at BETWEEN $24 AND $25
          )) OR $24 IS NULL OR $25 IS NULL
      )

      -- Lọc theo thời gian thay đổi deal_stage_history
      AND (
          ( EXISTS (
              SELECT 1 FROM deal_stage_history dsh_inner
              WHERE dsh_inner.deal_id = d.id
                AND dsh_inner.changed_at BETWEEN $26 AND $27
          )) OR $26 IS NULL OR $27 IS NULL
      )

      -- Lọc theo thẻ (tag)
      AND (EXISTS (
          SELECT 1
          FROM tag_deal tg
          WHERE tg.deal_id = d.id AND tg.tag_id = ANY($28::int[])
      ) OR $28 IS NULL)

      -- Lọc theo nhân viên liên quan
      AND ((
          (SELECT pa.user_id FROM person_assignment pa WHERE pa.person_id = p.id AND pa.role = 'sale' LIMIT 1) = ANY($29::int[])
          OR EXISTS (
              SELECT 1
              FROM deal_user du
              WHERE du.deal_id = d.id AND du.user_id = ANY($29::int[])
          )
      ) OR $29 IS NULL)
`;
