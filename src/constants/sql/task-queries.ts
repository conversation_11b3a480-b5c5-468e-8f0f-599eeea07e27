export const TASK_QUERY = `
  WITH base_task_ids AS (SELECT DISTINCT t.id, t.start_date, ta.serial
                     FROM core.task t
                              left join core.person p on t.person_id = p.id
                         -- JOIN cho role primary
                              LEFT JOIN core.task_assignment ta_primary ON t.id = ta_primary.task_id
                         AND ta_primary.role = 'primary'
                         AND ta_primary.status > -1
                         AND ta_primary.deleted_at IS NULL

                         -- JOIN cho role contributor
                              LEFT JOIN core.task_assignment ta_contributor ON t.id = ta_contributor.task_id
                         AND ta_contributor.role = 'contributor'
                         AND ta_contributor.status > -1
                         AND ta_contributor.deleted_at IS NULL

                         -- JOIN cho role reviewer
                              LEFT JOIN core.task_assignment ta_reviewer ON t.id = ta_reviewer.task_id
                         AND ta_reviewer.role = 'reviewer'
                         AND ta_reviewer.status > -1
                         AND ta_reviewer.deleted_at IS NULL

                         -- JOIN để lấy thông tin serial
                              LEFT JOIN core.task_assignment ta ON t.id = ta.task_id
                         AND ta.status > -1
                         AND ta.deleted_at IS NULL

                     WHERE t.status > -1
                       AND t.deleted_at IS NULL
                       --lọc theo tên task
                       AND (f_unaccent(t.title) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
                       --lọc theo tên khách hàng
                       AND (f_unaccent(p.full_name) LIKE '%' || f_unaccent($2) || '%' OR $2 IS NULL)
                       --lọc theo start_date
                       AND (t.start_date > $3 or $3 is null)
                       AND (t.start_date < $4 or $4 is null)
                       --lọc theo end_date
                       AND (t.end_date > $5 or $5 is null)
                       AND (t.end_date < $6 or $6 is null)
                       --lọc theo người nhận việc
                       AND (ta_primary.user_id = ANY($7::int[]) OR $7 IS NULL)
                       --lọc theo người giao việc
                       AND (t.creator_id = ANY($8::int[]) OR $8 IS NULL)
                       --lọc theo người liên quan
                       AND ((ta_contributor.user_id = ANY($9::int[]) OR ta_reviewer.user_id = ANY($9::int[])) OR $9 IS NULL)
                       --lọc task chậm tiến độ
                       AND ((t.due_date < $10 and t.completed_at is null) or $10 is null)
                       -- lọc task đã hoàn thành hoặc chưa hoàn thành
                        AND (
                            ($11 = 'completed' AND t.completed_at IS NOT NULL)
                            OR
                            ($11 = 'un_completed' AND t.completed_at IS NULL)
                            OR
                            ($11 NOT IN ('completed', 'un_completed') OR $11 IS NULL)
                        )
                       -- lọc theo độ ưu tiên
                       AND (t.priority = $12 or $12 is null)
                       -- lọc theo trạng thái task
                       AND (t.state = $13 OR $13 is null)
                     ORDER BY {{orderClause1}}
                     LIMIT $14 OFFSET $15),
   task_data AS (SELECT t.id,
                        t.due_date AS due_at,
                        tr.id      AS task_recurring_id,
                        t.created_at,
                        t.title,
                        t.note,
                        t.start_date,
                        t.due_date,
                        t.end_date,
                        t.type,
                        t.priority,
                        t.state,
                        t.completed_at,
                        t.parent_id,
                        t.person_id,
                        t.deal_id,
                        t.appointment_id,
                        t.creator_id,
                        t.department_id,
                        t.history,
                        ta.serial
                 FROM core.task t
                          JOIN base_task_ids bti ON t.id = bti.id
                          LEFT JOIN core.task_recurring tr ON t.id = tr.task_id
                          LEFT JOIN (SELECT DISTINCT ON (task_id) task_id, serial
                                     FROM core.task_assignment
                                     WHERE status > -1
                                       AND deleted_at IS NULL) ta ON t.id = ta.task_id),
   user_data AS (SELECT u.id,
                        u.username,
                        u.email,
                        u.name,
                        u.department_id
                 FROM core."user" u
                 WHERE u.id IN (SELECT creator_id
                                FROM task_data
                                WHERE creator_id IS NOT NULL
                                UNION
                                SELECT user_id
                                FROM core.task_assignment
                                WHERE task_id IN (SELECT id FROM task_data)
                                  AND status > -1
                                  AND deleted_at IS NULL
                                  AND user_id IS NOT NULL
                                UNION
                                SELECT complete_by
                                FROM core.task_department
                                WHERE task_id IN (SELECT id FROM task_data)
                                  AND status > -1
                                  AND deleted_at IS NULL
                                  AND complete_by IS NOT NULL)),
   assignments_data AS (SELECT ta.task_id,
                               ta.user_id,
                               ta.role,
                               jsonb_build_object(
                                       'user_id', ta.user_id,
                                       'due_at', ta.due_at,
                                       'started_at', ta.started_at,
                                       'completed_at', ta.completed_at,
                                       'state', ta.state,
                                       'role', ta.role,
                                       'user', jsonb_build_object(
                                               'id', u.id,
                                               'username', u.username,
                                               'email', u.email,
                                               'name', u.name,
                                               'department_id', u.department_id
                                               )
                               ) AS assignment_data
                        FROM core.task_assignment ta
                                 JOIN user_data u ON u.id = ta.user_id
                        WHERE ta.status > -1
                          AND ta.deleted_at IS NULL
                          AND ta.user_id IS NOT NULL
                          AND ta.task_id IN (SELECT id FROM task_data)),
   department_assignments_data AS (SELECT td.task_id,
                                          jsonb_build_object(
                                                  'department_id', td.department_id,
                                                  'due_at', td.due_at,
                                                  'started_at', td.started_at,
                                                  'completed_at', td.completed_at,
                                                  'status', td.status,
                                                  'role', td.role,
                                                  'complete_by', td.complete_by,
                                                  'complete_by_user', CASE
                                                                          WHEN td.complete_by IS NOT NULL THEN
                                                                              jsonb_build_object(
                                                                                      'id', u.id,
                                                                                      'username', u.username,
                                                                                      'email', u.email,
                                                                                      'name', u.name,
                                                                                      'department_id', u.department_id
                                                                              )
                                                                          ELSE NULL
                                                      END
                                          ) AS department_assignment_data
                                   FROM core.task_department td
                                            LEFT JOIN user_data u ON u.id = td.complete_by
                                   WHERE td.status > -1
                                     AND td.deleted_at IS NULL
                                     AND td.department_id IS NOT NULL
                                     AND td.task_id IN (SELECT id FROM task_data)),
   person_data AS (SELECT p.id,
                          p.full_name,
                          to_jsonb(p) AS person_json
                   FROM core.person p
                   WHERE p.id IN (SELECT person_id FROM task_data WHERE person_id IS NOT NULL))

SELECT td.id,
     td.serial,
     td.due_at,
     td.task_recurring_id,
     td.created_at,
     td.title,
     td.note,
     td.start_date,
     td.due_date,
     td.end_date,
     td.type,
     td.priority,
     td.state,
     td.completed_at,
     td.parent_id,
     td.person_id,
     td.deal_id,
     td.appointment_id,
     td.creator_id,
     td.department_id,
     td.history,
     CASE
         WHEN COUNT(a.assignment_data) = 0 THEN NULL
         ELSE jsonb_agg(DISTINCT a.assignment_data)
         END       AS assignments,
     CASE
         WHEN COUNT(da.department_assignment_data) = 0 THEN NULL
         ELSE jsonb_agg(DISTINCT da.department_assignment_data)
         END       AS department_assignments,
     jsonb_build_object(
             'id', u.id,
             'username', u.username,
             'email', u.email,
             'name', u.name,
             'department_id', u.department_id
     )             AS creator,
     p.person_json AS person,
     p.full_name   AS person_name,
     jsonb_build_object(
             'id', d.id,
             'name', d.name,
             'description', d.description
     )             AS department
FROM task_data td
       LEFT JOIN assignments_data a ON td.id = a.task_id
       LEFT JOIN department_assignments_data da ON td.id = da.task_id
       LEFT JOIN user_data u ON u.id = td.creator_id
       LEFT JOIN person_data p ON p.id = td.person_id
       LEFT JOIN core.department d ON d.id = td.department_id
GROUP BY td.id, td.serial, td.due_at, td.task_recurring_id, td.created_at, td.title,
       td.note, td.start_date, td.due_date, td.end_date, td.type, td.priority,
       td.state, td.completed_at, td.parent_id, td.person_id, td.deal_id,
       td.appointment_id, td.creator_id, td.department_id, td.history,
       u.id, u.username, u.email, u.name, u.department_id,
       p.person_json, p.full_name,
       d.id, d.name, d.description
ORDER BY {{orderClause2}}
LIMIT $14;
`;

export const TASK_COUNT_QUERY = `
  SELECT COUNT(DISTINCT t.id) AS total_count
  FROM core.task t
         LEFT JOIN core.person p ON t.person_id = p.id
-- JOIN cho role primary
         LEFT JOIN core.task_assignment ta_primary ON t.id = ta_primary.task_id
    AND ta_primary.role = 'primary'
    AND ta_primary.status > -1
    AND ta_primary.deleted_at IS NULL

-- JOIN cho role contributor
         LEFT JOIN core.task_assignment ta_contributor ON t.id = ta_contributor.task_id
    AND ta_contributor.role = 'contributor'
    AND ta_contributor.status > -1
    AND ta_contributor.deleted_at IS NULL

-- JOIN cho role reviewer
         LEFT JOIN core.task_assignment ta_reviewer ON t.id = ta_reviewer.task_id
    AND ta_reviewer.role = 'reviewer'
    AND ta_reviewer.status > -1
    AND ta_reviewer.deleted_at IS NULL

  WHERE t.status > -1
    AND t.deleted_at IS NULL
--lọc theo tên task
    AND (f_unaccent(t.title) LIKE '%' || f_unaccent($1) || '%' OR $1 IS NULL)
--lọc theo tên khách hàng
    AND (f_unaccent(p.full_name) LIKE '%' || f_unaccent($2) || '%' OR $2 IS NULL)
--lọc theo start_date
    AND (t.start_date > $3 OR $3 IS NULL)
    AND (t.start_date < $4 OR $4 IS NULL)
--lọc theo end_date
    AND (t.end_date > $5 OR $5 IS NULL)
    AND (t.end_date < $6 OR $6 IS NULL)
--lọc theo người nhận việc
    AND (ta_primary.user_id = ANY($7::int[]) OR $7 IS NULL)
--lọc theo người giao việc
    AND (t.creator_id = ANY($8::int[]) OR $8 IS NULL)
--lọc theo người liên quan
    AND ((ta_contributor.user_id = ANY($9::int[]) OR ta_reviewer.user_id = ANY($9::int[])) OR $9 IS NULL)
--lọc task chậm tiến độ
    AND ((t.due_date < $10 AND t.completed_at IS NULL) OR $10 IS NULL)
-- lọc task đã hoàn thành hoặc chưa hoàn thành
AND (
    ($11 = 'completed' AND t.completed_at IS NOT NULL)
    OR
    ($11 = 'un_completed' AND t.completed_at IS NULL)
    OR
    ($11 NOT IN ('completed', 'un_completed') OR $11 IS NULL)
)
-- lọc theo độ ưu tiên
    AND (t.priority = $12 OR $12 IS NULL)
    -- lọc theo trạng thái task
  AND (t.state = $13 OR $13 is null)
`;
