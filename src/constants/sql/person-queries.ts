export const PERSON_QUERY = `
WITH person_first_track_date AS (
    SELECT
        t.person_id,
        MIN(t.created_at) as first_track_created_at,
        MIN(t.end) as first_track_end
    FROM track t
    WHERE t.status <> -1 AND t.deleted_at IS NULL
    GROUP BY t.person_id
),
filtered_paginated_person AS (
    -- Bước 1: <PERSON>ọ<PERSON> và phân trang trên bảng person trước
    SELECT
        p.id AS person_id,
        p.status,
        p.deleted_at,
        pftd.first_track_created_at,
        pftd.first_track_end
    FROM person p
    LEFT JOIN person_first_track_date pftd ON p.id = pftd.person_id
    WHERE
        p.status <> -1
        AND p.deleted_at IS NULL
        -- Tìm kiếm theo tên/SĐT/mã
        AND ((
            f_unaccent(p.full_name) LIKE ('%' || f_unaccent($4) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($4) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($4) || '%')
        ) OR $4 IS NULL)
        -- Lọc theo thời gian tạo
        AND (p.created_at >= $7::timestamp OR $7 IS NULL)
        AND (p.created_at <= $8::timestamp OR $8 IS NULL)
        -- Lọc theo nguồn
        AND (p.source_id = $9 OR $9 IS NULL)
        -- Lọc theo tag (áp dụng tối ưu semi-join)
        AND (EXISTS (
            SELECT 1
            FROM deal d_tag
            JOIN tag_deal dt ON d_tag.id = dt.deal_id
            WHERE d_tag.person_id = p.id
              AND d_tag.deleted_at IS NULL
              AND dt.tag_id = ANY($5::int[])
            LIMIT 1
        ) OR $5 IS NULL)
        -- Lọc theo nhân viên liên quan
        AND (( (
            EXISTS (
                SELECT 1
                FROM deal d
                JOIN deal_user d_user ON d_user.deal_id = d.id
                WHERE d.person_id = p.id
                  AND d.deleted_at IS NULL
                  AND d_user.user_id = ANY($6::int[])
                LIMIT 1
            )
            OR EXISTS (
                SELECT 1
                FROM person_assignment pa_user
                WHERE pa_user.person_id = p.id
                  AND pa_user.role = 'sale'
                  AND pa_user.user_id = ANY($6::int[])
                LIMIT 1
            )
        )) OR $6 IS NULL)
        -- Lọc theo stage
        AND (EXISTS (
            SELECT 1
            FROM deal d_stage
            WHERE d_stage.person_id = p.id
              AND d_stage.deleted_at IS NULL
              AND d_stage.stage_id = $10
            LIMIT 1
        ) OR $10 IS NULL)
        -- Lọc theo deal.name
        AND (EXISTS (
            SELECT 1
            FROM deal d_name
            WHERE d_name.person_id = p.id
              AND d_name.deleted_at IS NULL
              AND f_unaccent(d_name.name) LIKE ('%' || f_unaccent($11) || '%')
            LIMIT 1
        ) OR $11 IS NULL)
        -- Lọc theo track (sử dụng $1, $2)
        AND (
            EXISTS (
                SELECT 1
                FROM track t_track
                WHERE t_track.person_id = p.id
                  AND t_track.status <> -1
                  AND t_track.deleted_at IS NULL
                  AND (t_track.created_at >= $1::timestamp OR $1 IS NULL)
                  AND (t_track.created_at <= $2::timestamp OR $2 IS NULL)
                LIMIT 1
            ) OR ($1 IS NULL AND $2 IS NULL)
        )
        -- Lọc theo loại khách hàng (new/returning/refunded) sử dụng first_track_created_at
        AND (
            ($3 = 'new' AND
                pftd.first_track_created_at IS NOT NULL AND
                (pftd.first_track_created_at >= $1::timestamp OR $1 IS NULL) AND
                (pftd.first_track_created_at <= $2::timestamp OR $2 IS NULL)
            )
            OR ($3 = 'returning' AND
                -- Có track trước đó
                pftd.first_track_created_at IS NOT NULL AND
                -- Có lịch sử đổi stage sang 'paying' trong khoảng thời gian lọc và sau khi track kết thúc
                EXISTS (
                    SELECT 1
                    FROM deal d
                    JOIN deal_history dh ON dh.deal_id = d.id
                    WHERE d.person_id = p.id
                      AND d.deleted_at IS NULL
                      AND dh.after ? 'state'
                      AND dh.after ->> 'state' = 'paying'
                      AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                      AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                      AND dh.created_at > pftd.first_track_end
                    LIMIT 1
                )
            )
            OR ($3 = 'refunded' AND EXISTS (
                SELECT 1
                FROM deal d
                JOIN deal_history dh ON dh.deal_id = d.id
                WHERE d.person_id = p.id
                  AND d.deleted_at IS NULL
                  AND dh.after ? 'state'
                  AND dh.after ->> 'state' = 'lost'
                  AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                  AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                LIMIT 1
            ))
            OR $3 IS NULL
        )
    ORDER BY p.id DESC
    LIMIT $12
    OFFSET $13
),
latest_appointment AS (
    -- Lấy cuộc hẹn mới nhất cho từng person_id đã lọc
    SELECT
        a.person_id,
        a.start_time
    FROM appointment a
    JOIN filtered_paginated_person fpp ON a.person_id = fpp.person_id
    WHERE (a.person_id, a.start_time) IN (
        SELECT person_id, MAX(start_time)
        FROM appointment
        WHERE person_id IN (SELECT person_id FROM filtered_paginated_person)
        GROUP BY person_id
    )
),
first_deal_info AS (
    -- Lấy thông tin deal đầu tiên cho từng person_id đã lọc
    SELECT
        d.person_id,
        d.id as deal_id,
        d.name as deal_name,
        d.state as deal_state,
        d.stage_id,
        s.name as stage_name_only,
        sp.id as stage_parent_id,
        sp.name as parent_stage_name
    FROM deal d
    JOIN filtered_paginated_person fpp ON d.person_id = fpp.person_id
    JOIN stage s ON s.id = d.stage_id
    LEFT JOIN stage sp ON sp.id = s.parent_stage_id
    WHERE d.deleted_at IS NULL
    AND (d.person_id, d.created_at) IN (
        SELECT person_id, MIN(created_at)
        FROM deal
        WHERE person_id IN (SELECT person_id FROM filtered_paginated_person)
        AND deleted_at IS NULL
        GROUP BY person_id
    )
),
targeted_deal_info AS (
    -- Lấy deal dựa trên điều kiện $3
    SELECT
        p.id as person_id,
        CASE
            WHEN $3 = 'new' THEN (
                -- Lấy deal liên quan đến track đầu tiên
                SELECT d.id
                FROM deal d
                JOIN track t ON d.person_id = t.person_id
                WHERE d.person_id = p.id
                AND d.deleted_at IS NULL
                AND t.status <> -1
                AND t.deleted_at IS NULL
                AND t.created_at = (
                    SELECT MIN(created_at)
                    FROM track
                    WHERE person_id = p.id
                    AND status <> -1
                    AND deleted_at IS NULL
                )
                ORDER BY d.created_at
                LIMIT 1
            )
            WHEN $3 = 'returning' THEN (
                -- Lấy deal đầu tiên được chuyển sang trạng thái 'paying' sau khi track kết thúc
                SELECT d.id
                FROM deal d
                JOIN deal_history dh ON dh.deal_id = d.id
                JOIN person_first_track_date pftd ON pftd.person_id = d.person_id
                WHERE d.person_id = p.id
                AND d.deleted_at IS NULL
                AND dh.after ? 'state'
                AND dh.after ->> 'state' = 'paying'
                AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                AND dh.created_at > pftd.first_track_end
                ORDER BY dh.created_at
                LIMIT 1
            )
            WHEN $3 = 'refunded' THEN (
                -- Lấy deal đầu tiên được chuyển sang trạng thái 'lost'
                SELECT d.id
                FROM deal d
                JOIN deal_history dh ON dh.deal_id = d.id
                WHERE d.person_id = p.id
                AND d.deleted_at IS NULL
                AND dh.after ? 'state'
                AND dh.after ->> 'state' = 'lost'
                AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                ORDER BY dh.created_at
                LIMIT 1
            )
            ELSE (
                -- Mặc định lấy first deal
                SELECT deal_id FROM first_deal_info WHERE person_id = p.id
            )
        END as targeted_deal_id
    FROM person p
    JOIN filtered_paginated_person fpp ON p.id = fpp.person_id
),
targeted_deal_full_info AS (
    -- Lấy thông tin đầy đủ của targeted deal
    SELECT
        tdi.person_id,
        d.id as deal_id,
        d.name as deal_name,
        d.state as deal_state,
        d.stage_id,
        s.name as stage_name_only,
        sp.id as stage_parent_id,
        sp.name as parent_stage_name
    FROM targeted_deal_info tdi
    JOIN deal d ON d.id = tdi.targeted_deal_id
    LEFT JOIN stage s ON s.id = d.stage_id
    LEFT JOIN stage sp ON sp.id = s.parent_stage_id
    WHERE d.deleted_at IS NULL
),
sale_assignment AS (
    -- Lấy thông tin sale cho từng person_id đã lọc
    SELECT
        pa.person_id,
        u.id as sale_id,
        u.name as sale_name,
        jsonb_build_object(
            'id', u.id,
            'username', u.username,
            'email', u.email,
            'name', u.name,
            'department_id', u.department_id
        ) as sale_json
    FROM person_assignment pa
    JOIN filtered_paginated_person fpp ON pa.person_id = fpp.person_id
    JOIN "user" u ON u.id = pa.user_id
    WHERE pa.role = 'sale'
    AND (pa.person_id, u.id) IN (
        SELECT person_id, MIN(user_id)
        FROM person_assignment
        WHERE person_id IN (SELECT person_id FROM filtered_paginated_person)
        AND role = 'sale'
        GROUP BY person_id
    )
),
doctor_assignment AS (
    -- Lấy thông tin bác sĩ chỉnh nha cho từng person
    SELECT
        pa.person_id,
        u.id as doctor_id,
        u.name as doctor_name
    FROM person_assignment pa
    JOIN filtered_paginated_person fpp ON pa.person_id = fpp.person_id
    JOIN "user" u ON u.id = pa.user_id
    WHERE pa.role = 'doctor'
    AND (pa.person_id, u.id) IN (
        SELECT person_id, MIN(user_id)
        FROM person_assignment
        WHERE person_id IN (SELECT person_id FROM filtered_paginated_person)
        AND role = 'doctor'
        GROUP BY person_id
    )
),
deal_tag_data AS (
    SELECT
        tg.deal_id,
        jsonb_agg(tag.id::bigint) AS deal_tag_ids,
        jsonb_agg(
            jsonb_build_object(
                'id', tag.id,
                'name', tag.name,
                'category', tag.category
            ) ORDER BY tag.name
        ) AS deal_tags_json,
        string_agg(tag.name, ', ' ORDER BY tag.name) AS deal_tag_names
    FROM tag_deal tg
    JOIN tag ON tag.id = tg.tag_id
    WHERE tg.deal_id IN (SELECT deal_id FROM targeted_deal_full_info)
    GROUP BY tg.deal_id
),
deal_assignment_data AS (
    SELECT
        du.deal_id,
        jsonb_agg(
            jsonb_build_object(
                'id', du.id,
                'user_id', u.id,
                'username', u.username,
                'email', u.email,
                'name', u.name,
                'department_id', u.department_id,
                'role', du.role,
                'point', du.point
            ) ORDER BY u.name
        ) AS deal_assignment_json,
        MAX(CASE WHEN du.role = 'consultant_doctor' THEN u.name ELSE NULL END) AS bs_tv_name,
        MAX(CASE WHEN du.role = 'consultant_doctor' THEN
                CASE
                    WHEN du.point ? 'averageScore' THEN du.point->>'averageScore'
                    ELSE NULL
                END
            ELSE NULL END) AS bs_tv_score,
        MAX(CASE WHEN du.role = 'treatment_doctor' THEN u.name ELSE NULL END) AS bs_nieng_name,
        MAX(CASE WHEN du.role = 'doctor_assistant' THEN u.name ELSE NULL END) AS tro_ly_name,
        MAX(CASE WHEN du.role = 'doctor_assistant' THEN
                CASE
                    WHEN du.point ? 'averageScore' THEN du.point->>'averageScore'
                    ELSE NULL
                END
            ELSE NULL END) AS tro_ly_score
    FROM deal_user du
    JOIN "user" u ON du.user_id = u.id
    WHERE du.deal_id IN (SELECT deal_id FROM targeted_deal_full_info)
    GROUP BY du.deal_id
),
referrer_info AS (
    -- Lấy thông tin người giới thiệu và thông tin giới thiệu
    SELECT
        pr.referred_id AS person_id,
        row_to_json(p_ref) AS referrer,
        row_to_json(pr) AS referral_info,
        p_ref.full_name AS referrer_name,
        p_ref.person_field ->> 'code' AS referrer_code
    FROM person_referral pr
    JOIN filtered_paginated_person fpp ON pr.referred_id = fpp.person_id
    JOIN person p_ref ON p_ref.id = pr.referrer_id
    WHERE (pr.referred_id, pr.created_at) IN (
        SELECT pr_inner.referred_id, MAX(pr_inner.created_at)
        FROM person_referral pr_inner
        JOIN filtered_paginated_person fpp_inner ON pr_inner.referred_id = fpp_inner.person_id
        GROUP BY pr_inner.referred_id
    )
)
SELECT DISTINCT ON (p.id)
    -- Fields cho cả hai function
    -- Common fields
    p.id,
    p.person_field ->> 'code' as person_code,
    p.full_name,
    p.person_field,
    p.gender,
   CASE
    WHEN p.phone IS NULL THEN NULL
    WHEN LENGTH(p.phone) <= 3 THEN p.phone
    ELSE CONCAT('****', RIGHT(p.phone, 3))
END as phone,
    t_source.name as person_source,

    -- Track Begin (NGÀY ĐẾN TƯ VẤN) - Cho function cũ
    CASE
        WHEN $3 = 'new' THEN pftd.first_track_created_at
        WHEN $3 = 'returning' THEN (
            SELECT MIN(dh.created_at)
            FROM deal d
            JOIN deal_history dh ON dh.deal_id = d.id
            WHERE d.person_id = p.id
              AND d.deleted_at IS NULL
              AND dh.after ? 'state'
              AND dh.after ->> 'state' = 'paying'
              AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
              AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
              AND dh.created_at > pftd.first_track_end
        )
        WHEN $3 = 'refunded' THEN (
            SELECT MIN(dh.created_at)
            FROM deal d
            JOIN deal_history dh ON dh.deal_id = d.id
            WHERE d.person_id = p.id
              AND d.deleted_at IS NULL
              AND dh.after ? 'state'
              AND dh.after ->> 'state' = 'lost'
              AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
              AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
        )
        ELSE (
            SELECT MAX(t.created_at)
            FROM track t
            WHERE t.person_id = p.id
              AND t.status <> -1
              AND t.deleted_at IS NULL
        )
    END as track_begin,

    -- Fields cho function cũ
    tdfi.deal_name,
    sa.sale_name as telesale_name,
    dad.bs_tv_name,
    dad.bs_tv_score,
    dad.bs_nieng_name,
    dad.tro_ly_name,
    dad.tro_ly_score,
    CASE
        WHEN tdfi.stage_parent_id IS NULL THEN tdfi.stage_name_only
        ELSE tdfi.parent_stage_name || ' - ' || tdfi.stage_name_only
    END as stage_name,
    dtd.deal_tag_names,
    ri.referrer_name,
    ri.referrer_code,

    -- Fields bổ sung cho function mới
    treatment.name as treatment_name,
    doc.doctor_name,
    la.start_time as appointment_time,
    creator.name AS creator_name,
    p.created_at,

    -- Fields cho kết quả truy vấn ban đầu
    sa.sale_json AS sale,
    sa.sale_id,
    tdfi.stage_id,
    tdfi.stage_parent_id,
    jsonb_build_object(
        'id', tdfi.deal_id,
        'name', tdfi.deal_name,
        'state', tdfi.deal_state,
        'deal_assignment', COALESCE(dad.deal_assignment_json, '[]'::jsonb),
        'tags', COALESCE(dtd.deal_tags_json, '[]'::jsonb)
    ) AS deal,
    ri.referrer,
    ri.referral_info
FROM person p
JOIN filtered_paginated_person fpp ON p.id = fpp.person_id
LEFT JOIN person_first_track_date pftd ON p.id = pftd.person_id
LEFT JOIN term t_source ON p.source_id = t_source.id
LEFT JOIN "user" creator ON p.user_id = creator.id
LEFT JOIN LATERAL (
    SELECT term.name
    FROM term term
    WHERE term.id = (p.person_field->>'treatment_id')::bigint
) treatment ON true
LEFT JOIN latest_appointment la ON p.id = la.person_id
LEFT JOIN targeted_deal_full_info tdfi ON p.id = tdfi.person_id
LEFT JOIN sale_assignment sa ON p.id = sa.person_id
LEFT JOIN doctor_assignment doc ON p.id = doc.person_id
LEFT JOIN deal_tag_data dtd ON tdfi.deal_id = dtd.deal_id
LEFT JOIN deal_assignment_data dad ON tdfi.deal_id = dad.deal_id
LEFT JOIN referrer_info ri ON p.id = ri.person_id
ORDER BY  p.id, {{orderClause1}}
LIMIT $12;
`;

export const PERSON_COUNT_QUERY = `
WITH PersonFirstTrackDate AS (
    SELECT
        t.person_id,
        MIN(t.created_at) as first_track_created_at,
        MIN(t.end) as first_track_end
    FROM track t
    WHERE t.status <> -1 AND t.deleted_at IS NULL
    GROUP BY t.person_id
)
SELECT COUNT(DISTINCT p.id) AS total_count
FROM person p
LEFT JOIN PersonFirstTrackDate pftd ON p.id = pftd.person_id
WHERE
        p.status <> -1
        AND p.deleted_at IS NULL
        -- Tìm kiếm theo tên/SĐT/mã
        AND ((
            f_unaccent(p.full_name) LIKE ('%' || f_unaccent($4) || '%')
            OR f_unaccent(p.phone) LIKE ('%' || f_unaccent($4) || '%')
            OR f_unaccent(p.person_field ->> 'code') LIKE ('%' || f_unaccent($4) || '%')
        ) OR $4 IS NULL)
        -- Lọc theo thời gian tạo
        AND (p.created_at >= $7::timestamp OR $7 IS NULL)
        AND (p.created_at <= $8::timestamp OR $8 IS NULL)
        -- Lọc theo nguồn
        AND (p.source_id = $9 OR $9 IS NULL)
        -- Lọc theo tag (áp dụng tối ưu semi-join)
        AND (EXISTS (
            SELECT 1
            FROM deal d_tag
            JOIN tag_deal dt ON d_tag.id = dt.deal_id
            WHERE d_tag.person_id = p.id
              AND d_tag.deleted_at IS NULL
              AND dt.tag_id = ANY($5::int[])
            LIMIT 1
        ) OR $5 IS NULL)
        -- Lọc theo nhân viên liên quan
        AND (((
            EXISTS (
                SELECT 1
                FROM deal d
                JOIN deal_user d_user ON d_user.deal_id = d.id
                WHERE d.person_id = p.id
                  AND d.deleted_at IS NULL
                  AND d_user.user_id = ANY($6::int[])
                LIMIT 1
            )
            OR EXISTS (
                SELECT 1
                FROM person_assignment pa_user
                WHERE pa_user.person_id = p.id
                  AND pa_user.role = 'sale'
                  AND pa_user.user_id = ANY($6::int[])
                LIMIT 1
            )
        )) OR $6 IS NULL)
        -- Lọc theo stage
        AND (EXISTS (
            SELECT 1
            FROM deal d_stage
            WHERE d_stage.person_id = p.id
              AND d_stage.deleted_at IS NULL
              AND d_stage.stage_id = $10
            LIMIT 1
        ) OR $10 IS NULL)
        -- Lọc theo deal.name
        AND (EXISTS (
            SELECT 1
            FROM deal d_name
            WHERE d_name.person_id = p.id
              AND d_name.deleted_at IS NULL
              AND f_unaccent(d_name.name) LIKE ('%' || f_unaccent($11) || '%')
            LIMIT 1
        ) OR $11 IS NULL)
        -- Lọc theo track (sử dụng $1, $2)
        AND (
            EXISTS (
                SELECT 1
                FROM track t_track
                WHERE t_track.person_id = p.id
                  AND t_track.status <> -1
                  AND t_track.deleted_at IS NULL
                  AND (t_track.created_at >= $1::timestamp OR $1 IS NULL)
                  AND (t_track.created_at <= $2::timestamp OR $2 IS NULL)
                LIMIT 1
            ) OR ($1 IS NULL AND $2 IS NULL)
        )
        -- Lọc theo loại khách hàng (new/returning/refunded) sử dụng first_track_created_at
        AND (
            ($3 = 'new' AND
                pftd.first_track_created_at IS NOT NULL AND
                (pftd.first_track_created_at >= $1::timestamp OR $1 IS NULL) AND
                (pftd.first_track_created_at <= $2::timestamp OR $2 IS NULL)
            )
            OR ($3 = 'returning' AND
                -- Có track trước đó
                pftd.first_track_created_at IS NOT NULL AND
                -- Có lịch sử đổi stage sang 'paying' trong khoảng thời gian lọc và sau khi track kết thúc
                EXISTS (
                    SELECT 1
                    FROM deal d
                    JOIN deal_history dh ON dh.deal_id = d.id
                    WHERE d.person_id = p.id
                      AND d.deleted_at IS NULL
                      AND dh.after ? 'state'
                      AND dh.after ->> 'state' = 'paying'
                      AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                      AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                      AND dh.created_at > pftd.first_track_end
                    LIMIT 1
                )
            )
            OR ($3 = 'refunded' AND EXISTS (
                SELECT 1
                FROM deal d
                JOIN deal_history dh ON dh.deal_id = d.id
                WHERE d.person_id = p.id
                  AND d.deleted_at IS NULL
                  AND dh.after ? 'state'
                  AND dh.after ->> 'state' = 'lost'
                  AND (dh.created_at >= $1::timestamp OR $1 IS NULL)
                  AND (dh.created_at <= $2::timestamp OR $2 IS NULL)
                LIMIT 1
            ))
            OR $3 IS NULL
        );
`;
