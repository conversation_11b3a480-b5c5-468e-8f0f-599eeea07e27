import { ColumnDefinition } from "@/components/DataTable";
import { Filter, FormSubmissionResponse } from "@/api/bcare-types-v2";
import { FilterOperator } from "@/api/bcare-enum";

// Define columns for the DataTable
export const formSubmissionColumns: ColumnDefinition<FormSubmissionResponse>[] = [
  {
    field: "person_id",
    header: "Khách hàng",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm khách hàng",
    style: { width: "20%", minWidth: "200px" },
  },
  {
    field: "email",
    header: "Email",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm email",
    style: { width: "20%", minWidth: "200px" },
  },
  {
    field: "form_name",
    header: "Tên form",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm tên form",
    style: { width: "23%", minWidth: "230px" },
  },
  {
    field: "referrer_url",
    header: "Referrer URL",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm referrer URL",
    style: { width: "11%" },
  },
  {
    field: "source_url",
    header: "Source URL",
    sortable: false,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Tìm source URL",
    style: { width: "11%" },
  },
  {
    field: "created_at",
    header: "Ngày tạo",
    sortable: false,
    showFilterMenu: false,
    filterType: "dateRange",
    filterPlaceholder: "Ngày tạo",
    style: { width: "12%", minWidth: "180px" },
  },
];

// Filter configuration
export const formSubmissionFilterConfigs = {
  full_name: {
    field: "full_name",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  person_id: {
    field: "full_name",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  email: {
    field: "email",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  form_name: {
    field: "form_name",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  source_url: {
    field: "source_url",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  referrer_url: {
    field: "referrer_url",
    operator: FilterOperator.LIKE,
    valueTransform: (v: string) => `%${v}%`,
  },
  // processed_at: {
  //   field: "processed_at",
  //   operator: FilterOperator.GTE,
  //   isDateRange: true,
  // },
  created_at: {
    field: "created_at",
    operator: FilterOperator.GTE,
    isDateRange: true,
  },
};

// Base filters
export const formSubmissionBaseFilters: Filter[] = [
  {
    field: "status",
    operator: FilterOperator.NEQ,
    value: "-1",
  },
];
