import type { ColumnDefinition } from "@/components/DataTable/constants";
import { FilterMatchMode } from "@primevue/core/api";

export const dealColumns: ColumnDefinition<any>[] = [
  {
    field: "created_at",
    header: "<PERSON><PERSON><PERSON> tạo",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "12%" },
  },
  {
    field: "name",
    header: "Tên Deal",
    sortable: true,
    showFilterMenu: false,
    filterType: "text",
    filterPlaceholder: "Nhập tên deal",
    style: { width: "23%" },
  },
  {
    field: "person_id", // Note: The filter logic in the store uses 'personInfo' param
    header: "<PERSON>h<PERSON>ch hàng",
    sortable: false,
    showFilterMenu: false,
    filterType: "text", // Simple text input, store handles combined search
    filterPlaceholder: "Tên, SĐT, Mã KH",
    style: { width: "23%" },
  },
  {
    field: "stage_id",
    header: "<PERSON><PERSON><PERSON> đ<PERSON>",
    sortable: false,
    showFilterMenu: false,
    style: { width: "14%" },
  },
  {
    field: "tags",
    header: "Tags",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false,
    filterType: "custom",
    filterPlaceholder: "Chọn tag",
  },
  {
    field: "related_users",
    header: "Người liên quan",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false,
    filterType: "custom",
    filterPlaceholder: "Chọn người liên quan",
  },
];

export const createDefaultDealFilters = () => ({
  created_at: { value: null, matchMode: FilterMatchMode.CONTAINS },
  name: { value: null, matchMode: FilterMatchMode.CONTAINS },
  person_id: { value: null, matchMode: FilterMatchMode.CONTAINS },
  stage_id: { value: null, matchMode: FilterMatchMode.CONTAINS },
  related_users: { value: null, matchMode: FilterMatchMode.CONTAINS },
  tags: { value: null, matchMode: FilterMatchMode.CONTAINS },
});
