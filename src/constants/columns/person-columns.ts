import { HeaderConfig } from "@/api/bcare-types-v2";
import type { ColumnDefinition } from "@/components/DataTable/constants";
import { FilterMatchMode } from "@primevue/core/api";
import type { DataTableFilterMetaData } from "primevue/datatable";

export const personColumns: ColumnDefinition<any>[] = [
  {
    field: "created_at",
    header: "Ngày tạo",
    sortable: false, // Sorting might need adjustments in the query/store if required
    showFilterMenu: false,
    filterType: "dateRange", // Matches 'createdDateRange' in store
    filterPlaceholder: "Ngày tạo",
    style: { width: "10%" },
  },
  {
    field: "track_begin",
    header: "Thời điểm",
    sortable: false, // Sorting might need adjustments in the query/store if required
    showFilterMenu: false,
    filterType: "dateRange", // Matches 'createdDateRange' in store
    filterPlaceholder: "<PERSON><PERSON><PERSON> tới",
    style: { width: "10%" },
  },
  {
    // Combined field for search, primary display likely name
    field: "full_name",
    header: "<PERSON>h<PERSON><PERSON> hàng",
    sortable: false,
    showFilterMenu: false,
    filterType: "text", // Matches 'searchString' in store (searches name, phone, code)
    filterPlaceholder: "Tên, SĐT, Mã KH",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "19%" },
    // Note: The filter input maps to 'searchString' param which searches multiple fields in SQL.
  },
  {
    field: "person_source", // Display source name
    header: "Nguồn",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom", // Likely needs a custom select component fetching sources
    filterPlaceholder: "Lọc nguồn",
    filterMatchMode: FilterMatchMode.EQUALS, // Maps to 'sourceId' in store
    style: { width: "10%" },
  },
  {
    field: "deal_name",
    header: "Deal",
    sortable: false,
    showFilterMenu: false,
    filterType: "text", // Matches 'searchString' in store (searches name, phone, code)
    filterPlaceholder: "Tên deal",
    filterMatchMode: FilterMatchMode.CONTAINS,
    style: { width: "19%" },
  },
  {
    field: "stage_name", // Display stage name
    header: "Giai đoạn",
    sortable: false,
    showFilterMenu: false,
    filterType: "custom", // Likely needs a custom select component fetching stages
    filterPlaceholder: "Lọc stage",
    filterMatchMode: FilterMatchMode.EQUALS, // Maps to 'stageId' in store
    style: { width: "11%" },
  },
  {
    field: "tags", // Placeholder for tags filter - data might not be directly on person row
    header: "Tags",
    sortable: false,
    style: { width: "11%" },
    showFilterMenu: false,
    filterType: "custom", // Needs a custom component (e.g., MultiSelect)
    filterPlaceholder: "Lọc tag",
    filterMatchMode: FilterMatchMode.CONTAINS, // Maps to 'tagIds' in store
  },
  {
    field: "related_users", // Represents the assigned sale user
    header: "Người liên quan",
    sortable: false,
    style: { width: "10%" },
    showFilterMenu: false,
    filterType: "custom", // Needs a custom user select component
    filterPlaceholder: "Lọc người liên quan",
    filterMatchMode: FilterMatchMode.CONTAINS, // Maps to 'relatedUserIds' in store
  },
  // Add other columns as needed, e.g., doctor, appointment_time, etc.
];

// Explicitly type the filter structure
// Adjust types based on expected filter component values (e.g., number, string[], Date[], etc.)
interface PersonDataTableFilters {
  created_at: DataTableFilterMetaData & { value: Date[] | null };
  track_begin: DataTableFilterMetaData & { value: Date[] | null };
  full_name: DataTableFilterMetaData & { value: string | null };
  person_source: DataTableFilterMetaData & { value: number | null }; // Assuming source filter uses ID
  stage_name: DataTableFilterMetaData & { value: number | null }; // Assuming stage filter uses ID
  tags: DataTableFilterMetaData & { value: number[] | null }; // Assuming tag filter uses array of IDs
  related_users: DataTableFilterMetaData & { value: number[] | null }; // Assuming user filter uses array of IDs
  deal_name: DataTableFilterMetaData & { value: string | null }; // Assuming deal filter uses string
  // Add other filters if needed
}

// Function to create the default filter state structure expected by PrimeVue DataTable
// The keys here should match the 'field' properties in personColumns where filtering is enabled.
export const createDefaultPersonFilters = (): PersonDataTableFilters => ({
  created_at: { value: null, matchMode: FilterMatchMode.CONTAINS }, // Use CONTAINS for dateRange internal logic
  track_begin: { value: null, matchMode: FilterMatchMode.CONTAINS }, // Use CONTAINS for dateRange internal logic
  full_name: { value: null, matchMode: FilterMatchMode.CONTAINS },
  deal_name: { value: null, matchMode: FilterMatchMode.CONTAINS },
  person_source: { value: null, matchMode: FilterMatchMode.EQUALS },
  stage_name: { value: null, matchMode: FilterMatchMode.EQUALS },
  tags: { value: null, matchMode: FilterMatchMode.CONTAINS }, // MatchMode might need adjustment based on custom component logic
  related_users: { value: null, matchMode: FilterMatchMode.CONTAINS }, // MatchMode might need adjustment based on custom component logic
});

// Define the headers for the person export
export const PERSON_EXPORT_HEADERS: HeaderConfig[] = [
  {
    field: "track_begin",
    display_name: "NGÀY ĐẾN TƯ VẤN",
    data_type: "date",
    formatter: "date:02/01/2006",
    width: 15, // Example width, adjust as needed
  },
  {
    field: "full_name",
    display_name: "TÊN KHÁCH HÀNG",
    data_type: "string",
    width: 25,
  },
  {
    field: "person_code",
    display_name: "ID KH", // Adjusted display_name for clarity
    data_type: "string",
    width: 15,
  },
  {
    field: "person_source",
    display_name: "NGUỒN KH",
    data_type: "string",
    width: 15,
  },
  {
    field: "phone",
    display_name: "SĐT",
    data_type: "string",
    width: 15,
  },
  {
    field: "deal_name",
    display_name: "TÊN DEAL",
    data_type: "string",
    width: 25,
  },
  {
    field: "telesale_name",
    display_name: "TELESALE",
    data_type: "string",
    width: 20,
  },
  {
    field: "bs_tv_name",
    display_name: "BÁC SĨ TƯ VẤN",
    data_type: "string",
    width: 20,
  },
  {
    field: "bs_tv_score",
    display_name: "ĐIỂM BS TV", // Adjusted display_name
    data_type: "float",
    width: 10,
  },
  {
    field: "bs_nieng_name",
    display_name: "BÁC SĨ NIỀNG",
    data_type: "string",
    width: 20,
  },
  {
    field: "tro_ly_name",
    display_name: "TRỢ LÝ BÁC SĨ",
    data_type: "string",
    width: 20,
  },
  {
    field: "tro_ly_score",
    display_name: "ĐIỂM TRỢ LÝ", // Adjusted display_name
    data_type: "float",
    width: 10,
  },
  {
    field: "stage_name",
    display_name: "TÌNH TRẠNG CHỐT SALE",
    data_type: "string",
    width: 25,
  },
  {
    field: "deal_tag_names",
    display_name: "NHÓM DEAL",
    data_type: "string",
    width: 20,
  },
  {
    field: "referrer_name",
    display_name: "TÊN NGƯỜI GIỚI THIỆU",
    data_type: "string",
    width: 25,
  },
  {
    field: "referrer_code",
    display_name: "ID NGƯỜI GIỚI THIỆU",
    data_type: "string",
    width: 20,
  },
  {
    field: "id",
    display_name: "PERSON ID", // Adjusted display_name
    data_type: "int",
    width: 15,
  },
  {
    field: "treatment_name",
    display_name: "Loại điều trị",
    data_type: "string",
    width: 20,
  },
  {
    field: "doctor_name",
    display_name: "Bác sĩ chỉnh nha",
    data_type: "string",
    width: 20,
  },
  {
    field: "appointment_time",
    display_name: "Lịch hẹn gần nhất",
    data_type: "datetime", // Changed to datetime to match formatter
    formatter: "date:02/01/2006 15:04:05",
    width: 20,
  },
  {
    field: "creator_name",
    display_name: "Người tạo",
    data_type: "string",
    width: 20,
  },
  {
    field: "created_at",
    display_name: "Ngày giờ tạo",
    data_type: "datetime", // Changed to datetime to match formatter
    formatter: "date:02/01/2006 15:04:05",
    width: 20,
  },
];
