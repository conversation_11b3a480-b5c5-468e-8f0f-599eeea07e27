import type { ColumnDefinition } from "@/components/DataTable/constants";
import { FilterMatchMode } from "@primevue/core/api";

export const dealStageHistoryColumns: ColumnDefinition<any>[] = [
  {
    field: "changed_at",
    header: "Th<PERSON>i gian",
    sortable: true,
    style: { width: "14%" },
    showFilterMenu: false, // Use filter row, not header menu
    filterType: "custom", // Use custom template
    filterMatchMode: FilterMatchMode.CONTAINS, // Custom range logic
    filterPlaceholder: "<PERSON>ọ<PERSON> theo ngày",
  },
  {
    field: "person",
    header: "<PERSON>h<PERSON><PERSON> hàng",
    style: { width: "15%" },
    showFilterMenu: false, // Use filter row, not header menu
    filterType: "custom", // Use custom template (contains InputText)
    filterMatchMode: FilterMatchMode.CONTAINS, // Text search logic
    filterPlaceholder: "Tìm tên, SĐT, mã KH",
  },
  {
    field: "deal",
    header: "Deal",
    sortable: false,
    style: { width: "15%" },
    showFilterMenu: false, // Use filter row, not header menu
    filterType: "custom", // Use custom template (PipelineStageSelect)
    filterMatchMode: FilterMatchMode.IN, // Multiple selection logic
  },
  {
    field: "before",
    header: "Stage cũ",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false, // No separate filter for 'before' stage yet
  },
  {
    field: "after",
    header: "Stage mới",
    sortable: false,
    style: { width: "14%" },
    showFilterMenu: false, // Use filter row, not header menu
    filterType: "custom", // Use custom template
    filterMatchMode: FilterMatchMode.IN, // Multiple selection logic
  },
  {
    field: "tags",
    header: "Thẻ",
    sortable: false,
    style: { width: "10%" },
    showFilterMenu: false,
    filterType: "custom",
    filterMatchMode: FilterMatchMode.IN,
    filterPlaceholder: "Chọn thẻ",
  },
  {
    field: "related_users",
    header: "Người liên quan",
    sortable: false,
    style: { width: "18%" },
    showFilterMenu: false,
    filterType: "custom",
    filterMatchMode: FilterMatchMode.IN,
    filterPlaceholder: "Chọn người liên quan",
  },
];
