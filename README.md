## BLAZY-VN Project
Developing an application for booking clinic for customers in the Dental industry.

## Setup Project:
**Step 1: Create SSH Key**
Open terminal and enter the following command:
-  ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

**Step 2: Add SSH Key to Git server**
- Open setting on git server.
- New SSH Key.

**Step 3: Clone Project with SSH Key**
- Copy link git project:
  **************:blazy-vn/bcare-fe.git
- Clone project with the following command
  **<NAME_EMAIL>:blazy-vn/bcare-fe.git**

## Build Project
**Step 1:** Open Project with VS code.
**Step 2:** Open terminal and enter the following command:
-  npx bun install
-  npx bun run dev

----------------------------------------------------------------
## Create .env.local file

VITE_API_URL=https://up.blazy.vn
VITE_WS_URL=wss://up.blazy.vn/ws
VITE_APP_TITLE=Updental

VITE_APP_VERSION=0.9.2
VITE_APP_AUTHOR=Blazy
VITE_APP_DESC=Blazy Care

VITE_APP_BRAND_NAME=Updental
VITE_APP_LOGO=Updental

----------------------------------------------------------------
## FOLDER STRUCTURE

--- src (Main app)
  --- API (Config method call api and service)
  --- Asset (Static file: CSS, Font, Image, Icons...)
  --- Base-components (components that reuse in all project)
  --- Components (Modify components use in specific)
  --- Composables (Reuse logic code)
  --- Hooks (Same with composables, move it into composables later)
  --- Pages (Page)
  --- ...
--- Config

## CODING CONVENTION
Follow all rules from 2 below guide:

https://learnvue.co/articles/vue-best-practices
https://vuejs.org/style-guide/



