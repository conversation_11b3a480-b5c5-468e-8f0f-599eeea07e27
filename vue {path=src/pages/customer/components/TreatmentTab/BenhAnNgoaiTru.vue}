<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { FormTextarea, FormInput } from "@/base-components/Form";
import useDynamicForm from "@/hooks/useDynamicForm";
import { Questions } from "@/pages/customer/components/MedicalExam/DynamicComponent/types";
import { joinRunes } from "@/utils/string";

// --- Interfaces ---
interface SectionQuestion {
  id: string;
  numericId?: number;
  question?: string;
  text?: boolean;
  input?: boolean;
  isSubHeader?: boolean;
  layout?: 'inline';
}
interface Section {
  id: string;
  title: string;
  displayTitle: string;
  questions: SectionQuestion[];
}
interface TemplateAnswer {
  id: number;
  value: string;
}
// --- Kết thúc Interfaces ---

const props = defineProps<{
  personId: number;
}>();

const FORM_ID = "bant";

// --- Cấu trúc Form Sections ---
const sections = ref<Section[]>([
  {
    id: 'ly_do',
    title: 'Lý do vào viện',
    displayTitle: 'II. Lý do vào viện',
    questions: [
      { id: 'ly_do_vao_vien', numericId: 1100, text: true }
    ]
  },
  {
    id: 'hoi_benh',
    title: 'Hỏi bệnh',
    displayTitle: 'III. Hỏi bệnh',
    questions: [
      { id: 'qua_trinh_benh_ly', numericId: 1110, question: '1. Quá trình bệnh lý', text: true },
      { id: 'tien_su_benh_header', question: '2. Tiền sử bệnh:', isSubHeader: true },
      { id: 'tien_su_ban_than', numericId: 1112, question: '+Bản thân', text: true },
      { id: 'tien_su_gia_dinh', numericId: 1113, question: '+Gia đình', text: true },
    ]
  },
  {
    id: 'kham_benh',
    title: 'Khám bệnh',
    displayTitle: 'IV. Khám bệnh',
    questions: [
      { id: 'mach', numericId: 1130, question: 'Mạch :', input: true },
      { id: 'nhip_tho', numericId: 1133, question: 'Nhịp thở :', input: true },
      { id: 'nhiet_do', numericId: 1131, question: 'Nhiệt độ :', input: true },
      { id: 'huyet_ap', numericId: 1132, question: 'Huyết áp :', input: true },
      { id: 'nhip_tim', numericId: 1134, question: 'Nhịp tim :', input: true },
      { id: 'can_nang', numericId: 1135, question: 'Cân nặng :', input: true },
      { id: 'chieu_cao', numericId: 1136, question: 'Chiều cao :', input: true },
      { id: 'toan_than', numericId: 1120, question: '1. Toàn thân :', text: true },
      { id: 'benh_chuyen_khoa', numericId: 1121, question: '2. Bệnh chuyên khoa :', text: true },
      { id: 'hinh_ve_mo_ta', numericId: 1122, question: '3. Hình vẽ mô tả tổn thương khi vào viện :', text: true },
      { id: 'tom_tat_benh_an', numericId: 1123, question: '4. Tóm tắt bệnh án :', text: true },
      { id: 'chan_doan_khoa', numericId: 1124, question: '5. Chẩn đoán của khoa khám bệnh :', text: true },
      { id: 'da_xu_ly_tuyen_duoi', numericId: 1125, question: '6. Đã xử lý của tuyến dưới :', text: true },
      { id: 'dieu_tri_ngoai_tru_header', question: '7. Điều trị ngoại trú :', isSubHeader: true },
      { id: 'dieu_tri_tu_ngay', numericId: 1127, question: 'từ ngày :', input: true },
      { id: 'dieu_tri_toi_ngay', numericId: 1128, question: 'tới ngày :', input: true },
    ]
  },
  {
    id: 'tong_ket_benh_an',
    title: 'Tổng kết bệnh án',
    displayTitle: 'V. Tổng kết bệnh án',
    questions: [
      { id: 'tk_qua_trinh_benh_ly', numericId: 1140, question: '1. Quá trình bệnh lý và diễn biến lâm sàn :', text: true },
      { id: 'tk_tom_tat_ket_qua', numericId: 1141, question: '2. Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chuẩn đoán :', text: true },
      { id: 'tk_chuan_doan_header', question: '3. Chuẩn đoán ra viện :', isSubHeader: true },
      { id: 'tk_chuan_doan_benh_chinh', numericId: 1143, question: '+Bệnh chính :', input: true },
      { id: 'tk_chuan_doan_benh_kem_theo', numericId: 1144, question: '+Bệnh kèm theo :', input: true },
      { id: 'tk_phuong_phap_dieu_tri', numericId: 1145, question: '4. Phương pháp điều trị :', text: true },
      { id: 'tk_tinh_trang_ra_vien', numericId: 1146, question: '5. Tình trạng người bệnh ra viện :', input: true },
      { id: 'tk_huong_dieu_tri_tiep', numericId: 1147, question: '6. Hướng điều trị và các chế độ tiếp theo :', text: true },
      { id: 'tk_x_quang', numericId: 1150, question: 'X - quang', input: true },
      { id: 'tk_ct_scanner', numericId: 1151, question: 'CT Scanner', input: true },
      { id: 'tk_sieu_am', numericId: 1152, question: 'Siêu âm', input: true },
      { id: 'tk_xet_nghiem', numericId: 1153, question: 'Xét nghiệm', input: true },
      { id: 'tk_khac', numericId: 1154, question: 'Khác .......', input: true },
      { id: 'tk_toan_bo_ho_so', numericId: 1155, question: 'Toàn bộ hồ sơ', input: true },
      { id: 'tk_bac_si_kham', numericId: 1200, question: 'Bác sĩ khám bệnh :', input: true },
      { id: 'tk_so_ngoai_tru', numericId: 1201, question: 'Số ngoại trú :', input: true },
      { id: 'tk_so_luu_tru', numericId: 1202, question: 'Số lưu trữ :', input: true },
    ]
  }
]);
// --- Kết thúc Cấu trúc Form Sections ---

// --- Dữ liệu mẫu bệnh án ---
const medicalTemplates = ref<Record<string, TemplateAnswer[]>>({
  "Cạo vôi răng": [ { id: 1100, value: "Chảy máu nướu" }, { id: 1110, value: "Bệnh nhân thường bị chảy máu nướu khi chải răng. bệnh nhân đến khám tại phòng khám." }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "Vôi răng viêm nướu (++).Vôi răng trên nướu + mảng bám nhiều.Vôi răng dưới nướu ít.Không có túi nha chu." }, { id: 1123, value: "Chảy máu nướu do vôi răng" }, { id: 1124, value: "Viêm nướu" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "84" }, { id: 1133, value: "20" }, { id: 1131, value: "37" }, { id: 1132, value: "110/70" }, { id: 1134, value: "84" }, { id: 1135, value: "50" }, { id: 1136, value: "160" }, { id: 1140, value: "Bệnh nhân thường bị chảy máu nướu khi chải răng. Bệnh nhân đến khám tại phòng khám và được chẩn đoán là viêm nướu" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: "Viêm nướu" }, { id: 1144, value: "" }, { id: 1145, value: "Cạo vôi răng" }, { id: 1146, value: "Ổn" }, { id: 1147, value: "Hướng dẫn vệ sinh răng miệng" }, ],
  "Trám răng": [ { id: 1100, value: "ê buốt răng" }, { id: 1110, value: "bệnh nhân thường bị ê buốt răng khi ăn nhai. Bệnh nhân đến khám tại phòng khám" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "" }, { id: 1123, value: "ê buốt răng khi ăn nhai" }, { id: 1124, value: "sâu răng" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "80" }, { id: 1133, value: "23" }, { id: 1131, value: "37" }, { id: 1132, value: "120/80" }, { id: 1134, value: "80" }, { id: 1135, value: "55" }, { id: 1136, value: "160" }, { id: 1140, value: "bệnh nhân thường bị ê buốt răng khi ăn nhai, Bệnh nhân đến khám tại phòng khám và được chẩn đoán là sâu răng" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: "Sâu răng" }, { id: 1144, value: "" }, { id: 1145, value: " trám composite" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
  "Nhổ răng": [ { id: 1100, value: " đau nhức răng" }, { id: 1110, value: "bệnh nhân thường bị đau nhức răng. Bệnh nhân đến khám tại phòng khám" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "đau nhức răng vùng hàm dưới" }, { id: 1123, value: " đau nhức răng vùng hàm dưới" }, { id: 1124, value: "sâu răng" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "85" }, { id: 1133, value: "21" }, { id: 1131, value: "37" }, { id: 1132, value: "100/60" }, { id: 1134, value: "78" }, { id: 1135, value: "60" }, { id: 1136, value: "160" }, { id: 1140, value: "bệnh nhân bị đau nhức răng vùng hàm dưới. Bệnh nhân đến khám tại phòng khám và được chẩn đoán là sâu răng" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: "Sâu răng" }, { id: 1144, value: "" }, { id: 1145, value: "nhổ răng+ toa thuốc: efferalgan 500mg: số lượng 03 viên( sáng:1 , trưa:1 , chiều:1).transamin 500mg: số lượng 2 viên (uống sau khi nhả gòn)" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
  "Tẩy trắng răng": [ { id: 1100, value: "răng ố vàng" }, { id: 1110, value: " bệnh nhân thấy răng ố vàng +  xỉn màu nên đến khám và điều trị" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: " VRVN (++).mảng bám ít" }, { id: 1123, value: "bệnh nhân thấy răng ố vàng+ xỉn màu" }, { id: 1124, value: "răng ố vàng" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "83" }, { id: 1133, value: "23" }, { id: 1131, value: "37" }, { id: 1132, value: "115/75" }, { id: 1134, value: "83" }, { id: 1135, value: "57" }, { id: 1136, value: "160" }, { id: 1140, value: "bệnh nhân thấy răng ố vàng+ xỉn màu. cạo vôi răng+ đáng bóng. tẩy trắng" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: "răng ố vàng" }, { id: 1144, value: "" }, { id: 1145, value: "tẩy trắng" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
  "Nhổ răng 48": [ { id: 1100, value: "đau nhức răng" }, { id: 1110, value: "bệnh nhân thường bị đau nhức răng vùng hàm dưới, bệnh nhân đến khám tại phòng khám\n" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "đau nhức răng vùng hàm dưới, răng 48 chưa mọc" }, { id: 1123, value: "đau nhức răng vùng hàm dưới, răng 48 chưa mọc" }, { id: 1124, value: "răng 48 mọc ngầm" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "83" }, { id: 1133, value: "23" }, { id: 1131, value: "37" }, { id: 1132, value: "115/75" }, { id: 1134, value: "83" }, { id: 1135, value: "57" }, { id: 1136, value: "160" }, { id: 1140, value: "bệnh nhân thường bị đau nhức răng vùng hàm dưới, răng 48 chưa mọc" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: " răng 48 mọc ngầm" }, { id: 1144, value: "" }, { id: 1145, value: "tiểu phẫu thuật răng 48+ toa thuốc. augmentin 625mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). medrol 16mg: 5 viên ( sáng: 1). efferalgan 500mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). transamin 500mg: 03 viên ( sáng: 1, trưa: 1, chiều: 1)\n" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
  "Nhổ răng 38": [ { id: 1100, value: "đau nhức răng" }, { id: 1110, value: "bệnh nhân thường bị đau nhức răng vùng hàm dưới, bệnh nhân đến khám tại phòng khám" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "đau nhức răng vùng hàm dưới, răng 38 chưa mọc" }, { id: 1123, value: "đau nhức răng vùng hàm dưới, răng 38 chưa mọc" }, { id: 1124, value: "răng 38 mọc ngầm" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "83" }, { id: 1133, value: "23" }, { id: 1131, value: "37" }, { id: 1132, value: "115/75" }, { id: 1134, value: "83" }, { id: 1135, value: "57" }, { id: 1136, value: "160" }, { id: 1140, value: "bệnh nhân thường bị đau nhức răng vùng hàm dưới, răng 38 chưa mọc" }, { id: 1141, value: "Chưa ghi nhận bất thường" }, { id: 1143, value: "răng 38 mọc ngầm" }, { id: 1144, value: "" }, { id: 1145, value: " tiểu phẫu thuật răng 38+ toa thuốc. augmentin 625mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). medrol 16mg: 5 viên ( sáng: 1). efferalgan 500mg: 15 viên (sáng: 1, trưa: 1, chiều: 1). transamin 500mg: 03 viên ( sáng: 1, trưa: 1, chiều: 1)\n" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
  "Nội nha": [ { id: 1100, value: "đau răng" }, { id: 1110, value: "răng bị sâu, cách đây 3 ngày bị bể, đau nhiều không ngủ được\n" }, { id: 1112, value: "Bình thường" }, { id: 1113, value: "Bình thường" }, { id: 1120, value: "Ổn" }, { id: 1121, value: "đau răng" }, { id: 1123, value: "răng vỡ lớn đến tủy" }, { id: 1124, value: "viêm quanh chóp" }, { id: 1125, value: "" }, { id: 1127, value: "Thời gian hiện tại" }, { id: 1128, value: "Ngày hiện tại" }, { id: 1130, value: "83" }, { id: 1133, value: "23" }, { id: 1131, value: "37" }, { id: 1132, value: "115/75" }, { id: 1134, value: "83" }, { id: 1135, value: "57" }, { id: 1136, value: "160" }, { id: 1140, value: "răng… vỡ lớn, đau. mở tủy. đo CD: 21mm, SSOT/ F2 - bơm rửa. quay canxi, trám tạm cavic. trám bít ống tủy. trám kết thúc" }, { id: 1141, value: "Thấy quang quanh chóp" }, { id: 1143, value: "viêm quanh chóp" }, { id: 1144, value: "" }, { id: 1145, value: "điều trị tủy răng và trám bít ống tủy bằng gutta pencha\n" }, { id: 1146, value: "Ổn" }, { id: 1147, value: " hướng dẫn vệ sinh răng miệng" }, ],
});
// --- Kết thúc Dữ liệu mẫu ---

const selectedTemplateName = ref<string>("");
const numericIdToFlatIdMap = ref<Map<number, string>>(new Map());

const getFlatId = (sectionId: string, questionId: string): string => {
  return `${sectionId}_${questionId}`;
};

onMounted(() => {
  sections.value.forEach(section => {
    section.questions.forEach(q => {
      if (q.numericId && !q.isSubHeader) {
        const flatId = getFlatId(section.id, q.id);
        numericIdToFlatIdMap.value.set(q.numericId, flatId);
      }
    });
  });
  console.log("ID Map created:", numericIdToFlatIdMap.value);
});

const { answers, syncData, debouncedSyncData } = useDynamicForm(
  FORM_ID,
  computed(() => {
    const result: Questions = {};
    sections.value.forEach(section => {
      section.questions.forEach(q => {
        if (q.isSubHeader) return;
        const flatId = getFlatId(section.id, q.id);
        result[flatId] = {
          question: q.question || section.displayTitle,
          text: q.text || q.input,
        };
      });
    });
    return result;
  }).value, // .value có thể không cần thiết nếu useDynamicForm nhận trực tiếp computed ref
  props.personId,
);

watch(selectedTemplateName, (newTemplateName) => {
  // Guard 1: Đảm bảo map ID đã sẵn sàng (thường là sau onMounted)
  if (numericIdToFlatIdMap.value.size === 0) {
      console.warn("ID map not ready yet in watch callback.");
      return; // Thoát nếu map chưa sẵn sàng
  }

  // Guard 2: Đảm bảo cấu trúc answers.value đã sẵn sàng từ useDynamicForm (không phải undefined/null và là object)
  if (!answers.value || typeof answers.value !== 'object') {
    // Log này bạn đã thấy in ra "undefined" và "Reactive<Object>" ban đầu
    console.warn("Answers structure not ready yet in watch callback (value is undefined or not an object). Waiting for hook...");
    return; // Thoát nếu answers.value chưa phải là object
  }

  // Guard 3: Kiểm tra xem answers.value đã chứa dữ liệu (ít nhất là một field dự kiến) chưa
  // Chúng ta dùng một ID chắc chắn có trong map để kiểm tra sự tồn tại của field đó trong answers.value
  const firstNumericId = 1100; // ID của 'ly_do_vao_vien'
  const firstExpectedFlatId = numericIdToFlatIdMap.value.get(firstNumericId);
  // Kiểm tra xem firstExpectedFlatId có tồn tại và answers.value có chứa key này không
  if (!firstExpectedFlatId || !answers.value.hasOwnProperty(firstExpectedFlatId)) {
       console.warn(`Answers content not ready yet. Expected field '${firstExpectedFlatId}' (for numeric ID ${firstNumericId}) not found in answers.value. Waiting for data population...`);
       // Có thể answers.value đã là object {} nhưng chưa được điền dữ liệu. Thoát ra và đợi lần chạy watch tiếp theo.
       return;
  }

  // --- Nếu đến đây, cả map ID và answers.value đều đã sẵn sàng và chứa dữ liệu ---
  console.log("Watch triggered: ID map and answers structure/content are ready. Proceeding with template logic.");

  const templateData = medicalTemplates.value[newTemplateName];
  const allFlatIds = Object.keys(answers.value); // Lấy các key hiện có trong answers

  // 1. Xóa tất cả giá trị hiện có trong các field của answers.value
  console.log("Clearing existing answer fields...");
  allFlatIds.forEach(flatId => {
    if (answers.value[flatId]) { // Kiểm tra field tồn tại trước khi gán
      answers.value[flatId].text = '';
    } else {
      console.warn(`Flat ID ${flatId} found in keys but missing in answers object during clear.`);
    }
  });

  // 2. Nếu không có template nào được chọn (newTemplateName rỗng) hoặc template không tồn tại
  if (!newTemplateName || !templateData) {
    console.log("No template selected or template data not found. Fields cleared.");
    // Đồng bộ hóa các trường đã bị xóa rỗng
    allFlatIds.forEach(flatId => { if (answers.value[flatId]) syncData(flatId); });
    return; // Kết thúc xử lý
  }

  // 3. Áp dụng dữ liệu từ template đã chọn
  console.log(`Applying template: ${newTemplateName}`);
  templateData.forEach(item => {
    const flatId = numericIdToFlatIdMap.value.get(item.id);
    if (flatId && answers.value[flatId]) { // Kiểm tra cả flatId và sự tồn tại của field trong answers
      let valueToSet = item.value;
      // Xử lý giá trị đặc biệt "Thời gian hiện tại" và "Ngày hiện tại"
      if (valueToSet === "Thời gian hiện tại") {
        const now = new Date();
        valueToSet = now.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' }) + ' ' + now.toLocaleDateString('vi-VN');
      } else if (valueToSet === "Ngày hiện tại") {
        const now = new Date();
        valueToSet = now.toLocaleDateString('vi-VN');
      }
      answers.value[flatId].text = valueToSet;
    } else {
      // Ghi log chi tiết hơn tại sao không áp dụng được
      if (!flatId) {
         console.warn(`Could not find flat ID mapping for template numeric ID: ${item.id}`);
      } else /* if (!answers.value[flatId]) */ {
         console.warn(`Field with flat ID ${flatId} (for template ID ${item.id}) not found in answers.value during template application. Skipping.`);
      }
    }
  });

  // 4. Đồng bộ hóa tất cả các trường sau khi áp dụng template (bao gồm cả các trường được set và các trường bị xóa rỗng)
  console.log("Syncing all fields after applying template...");
  allFlatIds.forEach(flatId => {
    if (answers.value[flatId]) { // Kiểm tra lại trước khi sync
      syncData(flatId);
    }
  });
  console.log("Template application and sync complete.");

}, { flush: 'post' }); // Giữ flush: 'post' để đảm bảo chạy sau các cập nhật DOM, dù nó không giải quyết hoàn toàn vấn đề bất đồng bộ của hook

const shouldIndent = (section: Section, q: SectionQuestion, index: number): boolean => {
  if (index === 0) return false;
  const previousQuestion = section.questions[index - 1];
  return !!(previousQuestion?.isSubHeader && !q.isSubHeader);
};
</script>

<template>
  <div>
    <div class="mb-4">
      <label for="template-select" class="block text-sm font-medium text-gray-700 mb-1">Chọn mẫu bệnh án:</label>
      <select
        id="template-select"
        v-model="selectedTemplateName"
        class="block w-full max-w-xs rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
      >
        <option value="">-- Chọn mẫu --</option>
        <option v-for="(template, name) in medicalTemplates" :key="name" :value="name">
          {{ name }}
        </option>
      </select>
    </div>

    <div class="overflow-hidden rounded-md border border-gray-200 bg-white shadow-sm">
      <div v-for="section in sections" :key="section.id">
        <div class="border-b border-gray-200 bg-gray-50 px-4 py-3 font-semibold text-gray-700 sm:px-6">
          {{ section.displayTitle }}
        </div>
        <div
          v-for="(q, index) in section.questions"
          :key="q.id"
          class="px-4 py-4 sm:px-6"
          :class="{ 'pl-8': shouldIndent(section, q, index) }"
        >
          <div v-if="q.isSubHeader" class="text-sm font-medium text-gray-600 mt-2 mb-1">
            {{ q.question }}
          </div>

          <div v-else>
             <!-- Hiển thị label trừ khi nó trùng với displayTitle của section -->
            <label v-if="q.question && q.question !== section.displayTitle" :for="joinRunes(FORM_ID, getFlatId(section.id, q.id))" class="mb-1 block text-sm font-medium text-gray-600">
              {{ q.question }}
            </label>
            <!-- Container div để chứa input/textarea -->
            <div>
              <FormInput
                v-if="q.input && answers[getFlatId(section.id, q.id)]"
                :id="joinRunes(FORM_ID, getFlatId(section.id, q.id))"
                type="text"
                v-model="answers[getFlatId(section.id, q.id)].text"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                @input="debouncedSyncData(getFlatId(section.id, q.id))"
                @change="syncData(getFlatId(section.id, q.id))"
              />
              <FormTextarea
                v-else-if="q.text && answers[getFlatId(section.id, q.id)]"
                :id="joinRunes(FORM_ID, getFlatId(section.id, q.id))"
                v-model="answers[getFlatId(section.id, q.id)].text"
                :min-rows="['hinh_ve_mo_ta', 'tom_tat_benh_an', 'benh_chuyen_khoa', 'chan_doan_khoa', 'da_xu_ly_tuyen_duoi', 'qua_trinh_benh_ly', 'ly_do_vao_vien', 'toan_than', 'tk_qua_trinh_benh_ly', 'tk_tom_tat_ket_qua', 'tk_phuong_phap_dieu_tri', 'tk_huong_dieu_tri_tiep', 'tien_su_ban_than', 'tien_su_gia_dinh'].includes(q.id) ? 3 : 2"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                @input="debouncedSyncData(getFlatId(section.id, q.id))"
                @change="syncData(getFlatId(section.id, q.id))"
              />
              <!-- Optional: Thêm placeholder hoặc thông báo lỗi nếu answers[flatId] không tồn tại -->
               <div v-else class="text-xs text-red-500 italic">
                 <!-- Field data not ready: {{ getFlatId(section.id, q.id) }} -->
               </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Thêm style nếu cần */
</style> 